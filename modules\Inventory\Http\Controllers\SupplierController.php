<?php

namespace Modules\Inventory\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Supplier;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Modules\Inventory\Http\Requests\StoreSupplierRequest;
use Modules\Inventory\Http\Requests\UpdateSupplierRequest;
use Modules\Inventory\Services\SupplierService;

class SupplierController extends Controller
{
    protected $supplierService;

    public function __construct(SupplierService $supplierService)
    {
        $this->supplierService = $supplierService;
    }

    /**
     * Display a listing of suppliers.
     */
    public function index(Request $request)
    {
        $suppliers = $this->supplierService->getAllSuppliers($request->all());
        return response()->json($suppliers);
    }

    /**
     * Get suppliers data for DataTable using Yajra DataTables
     */
    public function datatable(Request $request)
    {
        $user = Auth::user();

        if (!$user->branch_id) {
            return response()->json([
                'error' => 'User is not assigned to any branch'
            ], 400);
        }

        // Note: Suppliers are typically tenant-wide, not branch-specific
        // But we filter by tenant_id for multi-tenancy
        $query = Supplier::where('tenant_id', $user->tenant_id ?? 1);

        // Apply filters
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', "%{$searchTerm}%")
                  ->orWhere('email', 'like', "%{$searchTerm}%")
                  ->orWhere('phone', 'like', "%{$searchTerm}%")
                  ->orWhere('contact_person', 'like', "%{$searchTerm}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        return \Yajra\DataTables\Facades\DataTables::of($query)
            ->addIndexColumn()
            ->addColumn('status', function ($row) {
                $status = $row->is_active ? 'نشط' : 'غير نشط';
                $class = $row->is_active ? 'badge-success' : 'badge-danger';
                return "<span class='badge {$class}'>{$status}</span>";
            })
            ->addColumn('contact_info', function ($row) {
                $info = '';
                if ($row->email) {
                    $info .= "<div><i class='fas fa-envelope'></i> {$row->email}</div>";
                }
                if ($row->phone) {
                    $info .= "<div><i class='fas fa-phone'></i> {$row->phone}</div>";
                }
                return $info ?: 'لا يوجد';
            })
            ->addColumn('rating', function ($row) {
                $rating = $row->rating ?? 0;
                $stars = '';
                for ($i = 1; $i <= 5; $i++) {
                    $class = $i <= $rating ? 'fas fa-star text-warning' : 'far fa-star text-muted';
                    $stars .= "<i class='{$class}'></i>";
                }
                return $stars;
            })
            ->addColumn('actions', function ($row) {
                return "
                    <div class='btn-group' role='group'>
                        <button type='button' class='btn btn-sm btn-info' onclick='viewSupplier({$row->id})' title='عرض'>
                            <i class='fas fa-eye'></i>
                        </button>
                        <button type='button' class='btn btn-sm btn-warning' onclick='editSupplier({$row->id})' title='تعديل'>
                            <i class='fas fa-edit'></i>
                        </button>
                        <button type='button' class='btn btn-sm btn-primary' onclick='viewPurchaseOrders({$row->id})' title='أوامر الشراء'>
                            <i class='fas fa-shopping-cart'></i>
                        </button>
                        <button type='button' class='btn btn-sm btn-danger' onclick='deleteSupplier({$row->id})' title='حذف'>
                            <i class='fas fa-trash'></i>
                        </button>
                    </div>
                ";
            })
            ->rawColumns(['status', 'contact_info', 'rating', 'actions'])
            ->make(true);
    }

    /**
     * Store a newly created supplier.
     */
    public function store(StoreSupplierRequest $request)
    {
        $supplier = $this->supplierService->createSupplier($request->validated());
        return response()->json($supplier, 201);
    }

    /**
     * Display the specified supplier.
     */
    public function show(string $id)
    {
        $supplier = $this->supplierService->getSupplierById($id);
        return response()->json($supplier);
    }

    /**
     * Update the specified supplier.
     */
    public function update(UpdateSupplierRequest $request, string $id)
    {
        $supplier = $this->supplierService->updateSupplier($id, $request->validated());
        return response()->json($supplier);
    }

    /**
     * Remove the specified supplier.
     */
    public function destroy(string $id)
    {
        $this->supplierService->deleteSupplier($id);
        return response()->json(null, 204);
    }

    /**
     * Get supplier products
     */
    public function getProducts(string $id)
    {
        $products = $this->supplierService->getSupplierProducts($id);
        return response()->json($products);
    }

    /**
     * Get supplier purchase orders
     */
    public function getPurchaseOrders(string $id)
    {
        $orders = $this->supplierService->getSupplierPurchaseOrders($id);
        return response()->json($orders);
    }

    /**
     * Get supplier performance metrics
     */
    public function getPerformance(string $id)
    {
        $performance = $this->supplierService->getSupplierPerformance($id);
        return response()->json($performance);
    }

    /**
     * Get supplier products
     */
    public function getSupplierProducts(string $id)
    {
        $products = $this->supplierService->getSupplierProducts($id);
        return response()->json($products);
    }

    /**
     * Get supplier purchase orders
     */
    public function getSupplierPurchaseOrders(string $id)
    {
        $orders = $this->supplierService->getSupplierPurchaseOrders($id);
        return response()->json($orders);
    }

    /**
     * Add product to supplier
     */
    public function addProductToSupplier(Request $request, string $id)
    {
        $request->validate([
            'product_id' => 'required|integer|exists:products,id',
            'supplier_sku' => 'nullable|string|max:100',
            'cost_per_unit' => 'required|numeric|min:0',
            'minimum_order_quantity' => 'nullable|integer|min:1',
            'lead_time_days' => 'nullable|integer|min:0'
        ]);

        $result = $this->supplierService->addProductToSupplier($id, $request->validated());
        return response()->json($result, 201);
    }

    /**
     * Get supplier categories
     */
    public function getSupplierCategories()
    {
        $categories = [
            'food_ingredients' => 'Food Ingredients',
            'beverages' => 'Beverages',
            'packaging' => 'Packaging Materials',
            'cleaning_supplies' => 'Cleaning Supplies',
            'equipment' => 'Equipment & Tools',
            'maintenance' => 'Maintenance Services',
            'utilities' => 'Utilities',
            'general' => 'General Supplies'
        ];
        return response()->json($categories);
    }
}