<?php $__env->startSection('title', 'تفاصيل الباقة'); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h1 class="text-2xl font-bold text-gray-900 flex items-center gap-3">
                    <i class="fas fa-box text-purple-600"></i>
                    <?php echo e($package->name); ?>

                </h1>
                <div class="flex gap-2">
                    <a href="<?php echo e(route('packages.edit', $package)); ?>" 
                       class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                        <i class="fas fa-edit"></i>
                        تعديل
                    </a>
                    <a href="<?php echo e(route('packages.index')); ?>" 
                       class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                        <i class="fas fa-arrow-right"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Status and Quick Info -->
        <div class="px-6 py-4 bg-gray-50">
            <div class="flex flex-wrap items-center gap-4">
                <div class="flex items-center gap-2">
                    <span class="text-sm font-medium text-gray-700">الحالة:</span>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($package->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                        <i class="fas fa-circle text-xs mr-1"></i>
                        <?php echo e($package->is_active ? 'نشط' : 'غير نشط'); ?>

                    </span>
                </div>
                <div class="flex items-center gap-2">
                    <span class="text-sm font-medium text-gray-700">السعر:</span>
                    <span class="text-lg font-bold text-purple-600"><?php echo e(number_format($package->price, 2)); ?> ر.س</span>
                </div>
                <div class="flex items-center gap-2">
                    <span class="text-sm font-medium text-gray-700">دورة الفوترة:</span>
                    <span class="text-sm text-gray-600"><?php echo e($package->billing_cycle === 'monthly' ? 'شهري' : ($package->billing_cycle === 'yearly' ? 'سنوي' : 'ربع سنوي')); ?></span>
                </div>
                <div class="flex items-center gap-2">
                    <span class="text-sm font-medium text-gray-700">عدد المشتركين:</span>
                    <span class="text-sm font-semibold text-blue-600"><?php echo e($package->subscriptions_count ?? 0); ?></span>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="fas fa-info-circle text-blue-600"></i>
                        المعلومات الأساسية
                    </h2>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">اسم الباقة</label>
                            <p class="text-gray-900 font-medium"><?php echo e($package->name); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">السعر</label>
                            <p class="text-gray-900 font-medium"><?php echo e(number_format($package->price, 2)); ?> ريال سعودي</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">دورة الفوترة</label>
                            <p class="text-gray-900">
                                <?php if($package->billing_cycle === 'monthly'): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        <i class="fas fa-calendar-alt mr-1"></i>
                                        شهري
                                    </span>
                                <?php elseif($package->billing_cycle === 'yearly'): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-calendar-alt mr-1"></i>
                                        سنوي
                                    </span>
                                <?php else: ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                        <i class="fas fa-calendar-alt mr-1"></i>
                                        ربع سنوي
                                    </span>
                                <?php endif; ?>
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">ترتيب العرض</label>
                            <p class="text-gray-900"><?php echo e($package->sort_order ?? 'غير محدد'); ?></p>
                        </div>
                        <?php if($package->description): ?>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-1">الوصف</label>
                            <p class="text-gray-900 leading-relaxed"><?php echo e($package->description); ?></p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Limitations -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="fas fa-cogs text-orange-600"></i>
                        القيود والحدود
                    </h2>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="text-center p-4 bg-blue-50 rounded-lg">
                            <div class="text-2xl font-bold text-blue-600"><?php echo e($package->max_branches ?? '∞'); ?></div>
                            <div class="text-sm text-gray-600 mt-1">الحد الأقصى للفروع</div>
                        </div>
                        <div class="text-center p-4 bg-green-50 rounded-lg">
                            <div class="text-2xl font-bold text-green-600"><?php echo e($package->max_users ?? '∞'); ?></div>
                            <div class="text-sm text-gray-600 mt-1">الحد الأقصى للمستخدمين</div>
                        </div>
                        <div class="text-center p-4 bg-purple-50 rounded-lg">
                            <div class="text-2xl font-bold text-purple-600">
                                <?php echo e(isset($package->limitations['storage_limit']) ? $package->limitations['storage_limit'] . ' GB' : '∞'); ?>

                            </div>
                            <div class="text-sm text-gray-600 mt-1">مساحة التخزين</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Features -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="fas fa-star text-yellow-600"></i>
                        الميزات المتاحة
                    </h2>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                        <?php
                            $features = $package->features ?? [];
                            $allFeatures = [
                                'pos' => ['name' => 'نظام نقاط البيع', 'icon' => 'fas fa-cash-register', 'color' => 'green'],
                                'inventory' => ['name' => 'إدارة المخزون', 'icon' => 'fas fa-boxes', 'color' => 'blue'],
                                'reports' => ['name' => 'التقارير', 'icon' => 'fas fa-chart-bar', 'color' => 'indigo'],
                                'delivery' => ['name' => 'خدمة التوصيل', 'icon' => 'fas fa-truck', 'color' => 'orange'],
                                'hr' => ['name' => 'إدارة الموارد البشرية', 'icon' => 'fas fa-users', 'color' => 'red'],
                                'api' => ['name' => 'واجهة برمجة التطبيقات', 'icon' => 'fas fa-code', 'color' => 'gray']
                            ];
                        ?>
                        
                        <?php $__currentLoopData = $allFeatures; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex items-center p-4 border border-gray-200 rounded-lg <?php echo e(in_array($key, $features) ? 'bg-green-50 border-green-200' : 'bg-gray-50'); ?>">
                                <div class="flex-shrink-0">
                                    <?php if(in_array($key, $features)): ?>
                                        <i class="fas fa-check-circle text-green-600 text-lg"></i>
                                    <?php else: ?>
                                        <i class="fas fa-times-circle text-gray-400 text-lg"></i>
                                    <?php endif; ?>
                                </div>
                                <div class="mr-3">
                                    <div class="flex items-center">
                                        <i class="<?php echo e($feature['icon']); ?> text-<?php echo e($feature['color']); ?>-600 mr-2"></i>
                                        <span class="text-sm font-medium <?php echo e(in_array($key, $features) ? 'text-gray-900' : 'text-gray-500'); ?>">
                                            <?php echo e($feature['name']); ?>

                                        </span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Statistics -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="fas fa-chart-pie text-indigo-600"></i>
                        الإحصائيات
                    </h2>
                </div>
                <div class="p-6 space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">عدد المشتركين</span>
                        <span class="font-semibold text-blue-600"><?php echo e($package->subscriptions_count ?? 0); ?></span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">تاريخ الإنشاء</span>
                        <span class="text-sm text-gray-900"><?php echo e($package->created_at->format('Y-m-d')); ?></span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">آخر تحديث</span>
                        <span class="text-sm text-gray-900"><?php echo e($package->updated_at->format('Y-m-d')); ?></span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">الإيرادات المتوقعة</span>
                        <span class="font-semibold text-green-600">
                            <?php echo e(number_format(($package->subscriptions_count ?? 0) * $package->price, 2)); ?> ر.س
                        </span>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="fas fa-tools text-gray-600"></i>
                        الإجراءات
                    </h2>
                </div>
                <div class="p-6 space-y-3">
                    <!-- Status Toggle -->
                    <?php if($package->is_active): ?>
                        <button onclick="changePackageStatus(<?php echo e($package->id); ?>, 'deactivate')" 
                                class="w-full bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                            <i class="fas fa-pause"></i>
                            إلغاء تفعيل الباقة
                        </button>
                    <?php else: ?>
                        <button onclick="changePackageStatus(<?php echo e($package->id); ?>, 'activate')" 
                                class="w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                            <i class="fas fa-play"></i>
                            تفعيل الباقة
                        </button>
                    <?php endif; ?>
                    
                    <!-- Edit -->
                    <a href="<?php echo e(route('packages.edit', $package)); ?>" 
                       class="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                        <i class="fas fa-edit"></i>
                        تعديل الباقة
                    </a>
                    
                    <!-- Delete -->
                    <button onclick="deletePackage(<?php echo e($package->id); ?>)" 
                            class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                        <i class="fas fa-trash"></i>
                        حذف الباقة
                    </button>
                </div>
            </div>

            <!-- Quick Links -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="fas fa-link text-blue-600"></i>
                        روابط سريعة
                    </h2>
                </div>
                <div class="p-6 space-y-3">
                    <a href="<?php echo e(route('subscriptions.index', ['package_id' => $package->id])); ?>" 
                       class="w-full bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                        <i class="fas fa-users"></i>
                        عرض المشتركين
                    </a>
                    
                    <a href="<?php echo e(route('packages.index')); ?>" 
                       class="w-full bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                        <i class="fas fa-list"></i>
                        جميع الباقات
                    </a>
                    
                    <a href="<?php echo e(route('packages.create')); ?>" 
                       class="w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                        <i class="fas fa-plus"></i>
                        إضافة باقة جديدة
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status Change Confirmation Modal -->
<div id="statusModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100">
                <i class="fas fa-exclamation-triangle text-yellow-600"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mt-4" id="statusModalTitle">تأكيد العملية</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500" id="statusModalMessage">
                    هل أنت متأكد من تغيير حالة هذه الباقة؟
                </p>
            </div>
            <div class="items-center px-4 py-3">
                <button id="confirmStatusChange" 
                        class="px-4 py-2 bg-yellow-500 text-white text-base font-medium rounded-md w-24 mr-2 hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-yellow-300">
                    تأكيد
                </button>
                <button onclick="closeStatusModal()" 
                        class="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md w-24 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300">
                    إلغاء
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <i class="fas fa-trash text-red-600"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mt-4">تأكيد الحذف</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500">
                    هل أنت متأكد من حذف هذه الباقة؟ هذا الإجراء لا يمكن التراجع عنه.
                </p>
            </div>
            <div class="items-center px-4 py-3">
                <button id="confirmDelete" 
                        class="px-4 py-2 bg-red-500 text-white text-base font-medium rounded-md w-24 mr-2 hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-300">
                    حذف
                </button>
                <button onclick="closeDeleteModal()" 
                        class="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md w-24 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300">
                    إلغاء
                </button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
let currentPackageId = null;
let currentAction = null;

function changePackageStatus(packageId, action) {
    currentPackageId = packageId;
    currentAction = action;
    
    const modal = document.getElementById('statusModal');
    const title = document.getElementById('statusModalTitle');
    const message = document.getElementById('statusModalMessage');
    
    if (action === 'activate') {
        title.textContent = 'تفعيل الباقة';
        message.textContent = 'هل أنت متأكد من تفعيل هذه الباقة؟';
    } else {
        title.textContent = 'إلغاء تفعيل الباقة';
        message.textContent = 'هل أنت متأكد من إلغاء تفعيل هذه الباقة؟';
    }
    
    modal.classList.remove('hidden');
}

function closeStatusModal() {
    document.getElementById('statusModal').classList.add('hidden');
    currentPackageId = null;
    currentAction = null;
}

function deletePackage(packageId) {
    currentPackageId = packageId;
    document.getElementById('deleteModal').classList.remove('hidden');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
    currentPackageId = null;
}

// Confirm status change
document.getElementById('confirmStatusChange').addEventListener('click', function() {
    if (currentPackageId && currentAction) {
        const url = currentAction === 'activate' 
            ? `/admin/packages/${currentPackageId}/activate`
            : `/admin/packages/${currentPackageId}/deactivate`;
            
        fetch(url, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء تغيير حالة الباقة');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء تغيير حالة الباقة');
        });
    }
    closeStatusModal();
});

// Confirm delete
document.getElementById('confirmDelete').addEventListener('click', function() {
    if (currentPackageId) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/packages/${currentPackageId}`;
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';
        
        form.appendChild(csrfToken);
        form.appendChild(methodField);
        document.body.appendChild(form);
        form.submit();
    }
    closeDeleteModal();
});

// Close modals when clicking outside
window.onclick = function(event) {
    const statusModal = document.getElementById('statusModal');
    const deleteModal = document.getElementById('deleteModal');
    
    if (event.target === statusModal) {
        closeStatusModal();
    }
    if (event.target === deleteModal) {
        closeDeleteModal();
    }
}
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Tenant\Providers/../resources/views/packages/show.blade.php ENDPATH**/ ?>