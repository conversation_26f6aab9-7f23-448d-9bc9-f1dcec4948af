<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tenants', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code', 50)->unique()->comment('For subdomain/routing');
            $table->string('business_type', 100)->nullable()->comment('Restaurant, Cafe, Fast Food, etc.');
            $table->foreignId('country_id')->nullable()->constrained('countries')->onDelete('set null');
            $table->string('primary_contact_name');
            $table->string('contact_email');
            $table->string('contact_phone', 20)->nullable();
            $table->text('business_address')->nullable();
            $table->decimal('latitude', 10, 7)->nullable();
            $table->decimal('longitude', 10, 7)->nullable();
            $table->string('tax_number', 100)->nullable();
            $table->string('business_license', 100)->nullable();
            $table->string('logo_url', 500)->nullable();
            $table->string('website_url', 500)->nullable();
            $table->json('social_media')->nullable()->comment('facebook, instagram, twitter');
            $table->json('business_hours')->nullable()->comment('Weekly schedule');
            $table->string('timezone', 50)->default('UTC');
            $table->string('date_format', 20)->default('Y-m-d');
            $table->char('currency_code', 3)->default('USD');
            $table->char('language_code', 2)->default('en');
            $table->enum('status', ['active', 'suspended', 'cancelled'])->default('active');
            $table->timestamp('trial_ends_at')->nullable();
            $table->timestamps();
            $table->softDeletes();
            
            // Performance indexes
            $table->index(['status', 'created_at']);
            $table->index(['business_type', 'status']);
            $table->index(['trial_ends_at']);
            $table->index(['contact_email']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tenants');
    }
};