<?php

namespace Modules\Reservation\Services;

use App\Models\Reservation;
use App\Models\ReservationStatus;
use App\Models\Table;
use App\Models\Branch;
use Modules\Reservation\Contracts\ReservationServiceInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Exception;

class ReservationService implements ReservationServiceInterface
{
    /**
     * Get all reservations with optional filters.
     */
    public function getAllReservations(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = Reservation::with([
            'branch',
            'customer',
            'table',
            'area',
            'reservationStatus',
            'createdBy'
        ]);

        // Apply filters
        if (!empty($filters['branch_id'])) {
            $query->where('branch_id', $filters['branch_id']);
        }

        if (!empty($filters['status'])) {
            $query->whereHas('reservationStatus', function ($q) use ($filters) {
                $q->where('name', $filters['status']);
            });
        }

        if (!empty($filters['date_from'])) {
            $query->whereDate('reservation_datetime', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->whereDate('reservation_datetime', '<=', $filters['date_to']);
        }

        if (!empty($filters['customer_phone'])) {
            $query->where('customer_phone', 'like', '%' . $filters['customer_phone'] . '%');
        }

        if (!empty($filters['customer_name'])) {
            $query->where('customer_name', 'like', '%' . $filters['customer_name'] . '%');
        }

        if (!empty($filters['table_id'])) {
            $query->where('table_id', $filters['table_id']);
        }

        if (!empty($filters['area_id'])) {
            $query->where('area_id', $filters['area_id']);
        }

        return $query->orderBy('reservation_datetime', 'desc')->paginate($perPage);
    }

    /**
     * Create a new reservation.
     */
    public function createReservation(array $data): Reservation
    {
        DB::beginTransaction();
        
        try {
            // Generate reservation number
            $data['reservation_number'] = $this->generateReservationNumber($data['branch_id']);
            
            // Set default status to pending
            if (!isset($data['reservation_status_id'])) {
                $pendingStatus = ReservationStatus::where('name', 'pending')->first();
                $data['reservation_status_id'] = $pendingStatus?->id;
            }

            // Set default duration if not provided
            if (!isset($data['duration_minutes'])) {
                $data['duration_minutes'] = config('reservation.default_duration', 120);
            }

            // Validate availability if table is specified
            if (!empty($data['table_id'])) {
                $isAvailable = $this->checkTableAvailability(
                    $data['table_id'],
                    $data['reservation_datetime'],
                    $data['duration_minutes']
                );

                if (!$isAvailable) {
                    throw new Exception('Table is not available for the selected time.');
                }
            }

            $reservation = Reservation::create($data);
            
            DB::commit();
            
            return $reservation->load([
                'branch',
                'customer',
                'table',
                'area',
                'reservationStatus',
                'createdBy'
            ]);
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get reservation by ID.
     */
    public function getReservationById(int $id): ?Reservation
    {
        return Reservation::with([
            'branch',
            'customer',
            'table',
            'area',
            'reservationStatus',
            'createdBy'
        ])->find($id);
    }

    /**
     * Update reservation.
     */
    public function updateReservation(int $id, array $data): Reservation
    {
        DB::beginTransaction();
        
        try {
            $reservation = Reservation::findOrFail($id);
            
            // If table or datetime is being changed, check availability
            if (isset($data['table_id']) || isset($data['reservation_datetime'])) {
                $tableId = $data['table_id'] ?? $reservation->table_id;
                $datetime = $data['reservation_datetime'] ?? $reservation->reservation_datetime;
                $duration = $data['duration_minutes'] ?? $reservation->duration_minutes;
                
                if ($tableId) {
                    $isAvailable = $this->checkTableAvailability($tableId, $datetime, $duration, $id);
                    
                    if (!$isAvailable) {
                        throw new Exception('Table is not available for the selected time.');
                    }
                }
            }
            
            $reservation->update($data);
            
            DB::commit();
            
            return $reservation->load([
                'branch',
                'customer',
                'table',
                'area',
                'reservationStatus',
                'createdBy'
            ]);
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Cancel reservation.
     */
    public function cancelReservation(int $id, string $reason = null): Reservation
    {
        $reservation = Reservation::findOrFail($id);
        
        $cancelledStatus = ReservationStatus::where('name', 'cancelled')->first();
        
        $updateData = [
            'reservation_status_id' => $cancelledStatus?->id,
        ];
        
        if ($reason) {
            $updateData['notes'] = ($reservation->notes ? $reservation->notes . "\n" : '') . "Cancelled: " . $reason;
        }
        
        $reservation->update($updateData);
        
        return $reservation->load([
            'branch',
            'customer',
            'table',
            'area',
            'reservationStatus',
            'createdBy'
        ]);
    }

    /**
     * Confirm reservation.
     */
    public function confirmReservation(int $id): Reservation
    {
        $reservation = Reservation::findOrFail($id);
        
        $confirmedStatus = ReservationStatus::where('name', 'confirmed')->first();
        
        $reservation->update([
            'reservation_status_id' => $confirmedStatus?->id,
        ]);
        
        return $reservation->load([
            'branch',
            'customer',
            'table',
            'area',
            'reservationStatus',
            'createdBy'
        ]);
    }

    /**
     * Mark reservation as seated.
     */
    public function seatReservation(int $id, int $tableId = null): Reservation
    {
        DB::beginTransaction();
        
        try {
            $reservation = Reservation::findOrFail($id);
            
            $seatedStatus = ReservationStatus::where('name', 'seated')->first();
            
            $updateData = [
                'reservation_status_id' => $seatedStatus?->id,
                'seated_at' => now(),
            ];
            
            if ($tableId) {
                $updateData['table_id'] = $tableId;
                
                // Update table status to occupied
                Table::where('id', $tableId)->update(['status' => 'occupied']);
            }
            
            $reservation->update($updateData);
            
            DB::commit();
            
            return $reservation->load([
                'branch',
                'customer',
                'table',
                'area',
                'reservationStatus',
                'createdBy'
            ]);
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Complete reservation.
     */
    public function completeReservation(int $id): Reservation
    {
        DB::beginTransaction();
        
        try {
            $reservation = Reservation::findOrFail($id);
            
            $completedStatus = ReservationStatus::where('name', 'completed')->first();
            
            $reservation->update([
                'reservation_status_id' => $completedStatus?->id,
                'completed_at' => now(),
            ]);
            
            // Update table status to available if it was occupied
            if ($reservation->table_id) {
                Table::where('id', $reservation->table_id)->update(['status' => 'available']);
            }
            
            DB::commit();
            
            return $reservation->load([
                'branch',
                'customer',
                'table',
                'area',
                'reservationStatus',
                'createdBy'
            ]);
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Mark reservation as no-show.
     */
    public function markAsNoShow(int $id): Reservation
    {
        DB::beginTransaction();
        
        try {
            $reservation = Reservation::findOrFail($id);
            
            $noShowStatus = ReservationStatus::where('name', 'no_show')->first();
            
            $reservation->update([
                'reservation_status_id' => $noShowStatus?->id,
            ]);
            
            // Update table status to available if it was reserved
            if ($reservation->table_id) {
                Table::where('id', $reservation->table_id)->update(['status' => 'available']);
            }
            
            DB::commit();
            
            return $reservation->load([
                'branch',
                'customer',
                'table',
                'area',
                'reservationStatus',
                'createdBy'
            ]);
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Check if table is available for given time.
     */
    public function checkAvailability(int $branchId, string $datetime, int $duration = 120, int $partySize = 1): array
    {
        $startTime = Carbon::parse($datetime);
        $endTime = $startTime->copy()->addMinutes($duration);
        
        // Get all tables in the branch that can accommodate the party size
        $tables = Table::where('branch_id', $branchId)
            ->where('seating_capacity', '>=', $partySize)
            ->where('is_active', true)
            ->get();
        
        $availableTables = [];
        
        foreach ($tables as $table) {
            if ($this->checkTableAvailability($table->id, $datetime, $duration)) {
                $availableTables[] = $table;
            }
        }
        
        return $availableTables;
    }

    /**
     * Get reservation statistics.
     */
    public function getReservationStats(int $branchId = null, string $dateFrom = null, string $dateTo = null): array
    {
        $query = Reservation::query();
        
        if ($branchId) {
            $query->where('branch_id', $branchId);
        }
        
        if ($dateFrom) {
            $query->whereDate('reservation_datetime', '>=', $dateFrom);
        }
        
        if ($dateTo) {
            $query->whereDate('reservation_datetime', '<=', $dateTo);
        }
        
        $stats = [
            'total_reservations' => $query->count(),
            'by_status' => $query->join('reservation_statuses', 'reservations.reservation_status_id', '=', 'reservation_statuses.id')
                ->groupBy('reservation_statuses.name')
                ->selectRaw('reservation_statuses.name as status, count(*) as count')
                ->pluck('count', 'status')
                ->toArray(),
            'average_party_size' => round($query->avg('party_size'), 1),
            'total_guests' => $query->sum('party_size'),
        ];
        
        return $stats;
    }

    /**
     * Generate reservation number.
     */
    public function generateReservationNumber(int $branchId): string
    {
        $branch = Branch::find($branchId);
        $branchCode = $branch ? strtoupper(substr($branch->name, 0, 3)) : 'RES';
        
        $date = now()->format('Ymd');
        $sequence = Reservation::where('branch_id', $branchId)
            ->whereDate('created_at', now())
            ->count() + 1;
        
        return $branchCode . $date . str_pad($sequence, 3, '0', STR_PAD_LEFT);
    }

    /**
     * Check if a specific table is available for given time.
     */
    private function checkTableAvailability(int $tableId, string $datetime, int $duration, int $excludeReservationId = null): bool
    {
        $startTime = Carbon::parse($datetime);
        $endTime = $startTime->copy()->addMinutes($duration);
        $bufferMinutes = config('reservation.table_turnover_buffer', 30);
        
        // Check for overlapping reservations
        $query = Reservation::where('table_id', $tableId)
            ->whereIn('reservation_status_id', function ($q) {
                $q->select('id')
                    ->from('reservation_statuses')
                    ->whereIn('name', ['confirmed', 'seated']);
            })
            ->where(function ($q) use ($startTime, $endTime, $bufferMinutes) {
                $q->where(function ($subQ) use ($startTime, $endTime, $bufferMinutes) {
                    // Reservation starts before our end time and ends after our start time
                    $subQ->where('reservation_datetime', '<', $endTime->addMinutes($bufferMinutes))
                        ->whereRaw('DATE_ADD(reservation_datetime, INTERVAL duration_minutes + ? MINUTE) > ?', 
                            [$bufferMinutes, $startTime->subMinutes($bufferMinutes)]);
                });
            });
        
        if ($excludeReservationId) {
            $query->where('id', '!=', $excludeReservationId);
        }
        
        return $query->count() === 0;
    }


}