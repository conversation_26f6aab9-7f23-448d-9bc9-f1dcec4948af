<?php

namespace Modules\Menu\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class VariantResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'menu_item_id' => $this->menu_item_id,
            'name' => $this->name,
            'code' => $this->code,
            'price_modifier' => $this->price_modifier,
            'cost_modifier' => $this->cost_modifier,
            'is_default' => $this->is_default,
            'sort_order' => $this->sort_order,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            
            // Relationships
            'menu_item' => new MenuItemResource($this->whenLoaded('menuItem')),
            
            // Computed attributes
            'formatted_price_modifier' => ($this->price_modifier >= 0 ? '+' : '') . '$' . number_format($this->price_modifier, 2),
            'formatted_cost_modifier' => $this->cost_modifier ? (($this->cost_modifier >= 0 ? '+' : '') . '$' . number_format($this->cost_modifier, 2)) : null,
        ];
    }
}