<?php

namespace Modules\Menu\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AddonResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'menu_item_id' => $this->menu_item_id,
            'addon_group_name' => $this->addon_group_name,
            'name' => $this->name,
            'code' => $this->code,
            'price' => $this->price,
            'cost' => $this->cost,
            'is_required' => $this->is_required,
            'max_quantity' => $this->max_quantity,
            'sort_order' => $this->sort_order,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            
            // Relationships
            'menu_item' => new MenuItemResource($this->whenLoaded('menuItem')),
            
            // Computed attributes
            'formatted_price' => '$' . number_format($this->price, 2),
            'formatted_cost' => $this->cost ? '$' . number_format($this->cost, 2) : null,
        ];
    }
}