<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

/**
 * Event Model
 * 
 * Manages restaurant events including special occasions, promotions, 
 * seasonal events, and holiday celebrations.
 * 
 * @package App\Models
 * <AUTHOR> POS Team
 * @version 1.0.0
 */
class Event extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'branch_id',
        'name',
        'code',
        'description',
        'short_description',
        'event_type',
        'image_urls',
        'banner_image',
        'start_date',
        'end_date',
        'start_time',
        'end_time',
        'available_days',
        'location',
        'price',
        'max_participants',
        'current_participants',
        'requires_reservation',
        'menu_items',
        'special_menu',
        'discount_percentage',
        'discount_amount',
        'discount_type',
        'terms_conditions',
        'contact_info',
        'booking_url',
        'is_featured',
        'is_active',
        'is_recurring',
        'recurrence_pattern',
        'sort_order',
        'metadata',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'image_urls' => 'array',
            'start_date' => 'datetime',
            'end_date' => 'datetime',
            'start_time' => 'datetime:H:i',
            'end_time' => 'datetime:H:i',
            'available_days' => 'array',
            'price' => 'decimal:2',
            'max_participants' => 'integer',
            'current_participants' => 'integer',
            'requires_reservation' => 'boolean',
            'menu_items' => 'array',
            'special_menu' => 'array',
            'discount_percentage' => 'decimal:2',
            'discount_amount' => 'decimal:2',
            'terms_conditions' => 'array',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'is_recurring' => 'boolean',
            'sort_order' => 'integer',
            'metadata' => 'array',
        ];
    }

    // Relationships

    /**
     * Get the tenant that owns the event.
     */
    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the branch that owns the event.
     */
    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    // Scopes

    /**
     * Scope to get events for a specific branch
     */
    public function scopeForBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * Scope to get active events
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get featured events
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope to get current events (happening now)
     */
    public function scopeCurrent($query)
    {
        $now = now();
        return $query->where('start_date', '<=', $now)
                    ->where('end_date', '>=', $now)
                    ->where('is_active', true);
    }

    /**
     * Scope to get upcoming events
     */
    public function scopeUpcoming($query)
    {
        return $query->where('start_date', '>', now())
                    ->where('is_active', true);
    }

    /**
     * Scope to get events by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('event_type', $type);
    }

    // Helper Methods

    /**
     * Check if event is currently active
     */
    public function isCurrentlyActive()
    {
        if (!$this->is_active) {
            return false;
        }

        $now = now();
        
        // Check date range
        if ($now < $this->start_date || $now > $this->end_date) {
            return false;
        }

        // Check time range if specified
        if ($this->start_time && $this->end_time) {
            $currentTime = $now->format('H:i');
            if ($currentTime < $this->start_time->format('H:i') || 
                $currentTime > $this->end_time->format('H:i')) {
                return false;
            }
        }

        // Check day availability
        if ($this->available_days && !in_array($now->dayOfWeek, $this->available_days)) {
            return false;
        }

        return true;
    }

    /**
     * Check if event is upcoming
     */
    public function isUpcoming()
    {
        return $this->is_active && $this->start_date > now();
    }

    /**
     * Check if event has ended
     */
    public function hasEnded()
    {
        return $this->end_date < now();
    }

    /**
     * Check if event has available spots
     */
    public function hasAvailableSpots()
    {
        if (!$this->max_participants) {
            return true;
        }

        return $this->current_participants < $this->max_participants;
    }

    /**
     * Get remaining spots
     */
    public function getRemainingSpots()
    {
        if (!$this->max_participants) {
            return null;
        }

        return max(0, $this->max_participants - $this->current_participants);
    }

    /**
     * Check if event requires reservation
     */
    public function requiresReservation()
    {
        return $this->requires_reservation;
    }

    /**
     * Get event status
     */
    public function getStatus()
    {
        if (!$this->is_active) {
            return 'inactive';
        }

        if ($this->hasEnded()) {
            return 'ended';
        }

        if ($this->isCurrentlyActive()) {
            return 'active';
        }

        if ($this->isUpcoming()) {
            return 'upcoming';
        }

        return 'scheduled';
    }

    /**
     * Get formatted duration
     */
    public function getFormattedDuration()
    {
        $start = $this->start_date;
        $end = $this->end_date;

        if ($start->isSameDay($end)) {
            return $start->format('M j, Y') . 
                   ($this->start_time ? ' ' . $this->start_time->format('g:i A') : '') .
                   ($this->end_time ? ' - ' . $this->end_time->format('g:i A') : '');
        }

        return $start->format('M j, Y') . ' - ' . $end->format('M j, Y');
    }
}