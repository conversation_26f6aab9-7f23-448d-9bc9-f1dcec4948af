<?php

use Illuminate\Support\Facades\Route;
use Modules\Menu\Http\Controllers\Web\MenuController;
use Modules\Menu\Http\Controllers\Web\CategoryController;
use Modules\Menu\Http\Controllers\Web\MenuItemController;
use Modules\Menu\Http\Controllers\Web\AddonController;
use Modules\Menu\Http\Controllers\Web\VariantController;
use Modules\Menu\Http\Controllers\Web\BannerController;
use Modules\Menu\Http\Controllers\Web\EventController;
use Modules\Menu\Http\Controllers\Web\OfferController;

/*
|--------------------------------------------------------------------------
| Menu Module Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for the Menu module.
| All routes are prefixed with 'menu' and require authentication.
|
*/

Route::prefix('menu')->middleware(['auth'])->group(function () {
    
    // Menu Routes
    Route::get('/menus', [MenuController::class, 'index'])->name('menus.index');
    Route::post('/menus', [MenuController::class, 'store'])->name('menus.store');
    Route::get('/menus/{id}/show', [MenuController::class, 'show'])->name('menus.show');
    Route::get('/menus/{id}/edit', [MenuController::class, 'edit'])->name('menus.edit');
    Route::put('/menus/{id}', [MenuController::class, 'update'])->name('menus.update');
    Route::delete('/menus/{id}', [MenuController::class, 'destroy'])->name('menus.destroy');
    Route::get('/menus/list', [MenuController::class, 'getMenusList'])->name('menus.list');
    
    // Category Routes
    Route::get('/categories', [CategoryController::class, 'index'])->name('categories.index');
    Route::post('/categories', [CategoryController::class, 'store'])->name('categories.store');
    Route::get('/categories/{id}/show', [CategoryController::class, 'show'])->name('categories.show');
    Route::get('/categories/{id}/edit', [CategoryController::class, 'edit'])->name('categories.edit');
    Route::put('/categories/{id}', [CategoryController::class, 'update'])->name('categories.update');
    Route::delete('/categories/{id}', [CategoryController::class, 'destroy'])->name('categories.destroy');
    Route::get('/categories/list', [CategoryController::class, 'getCategoriesList'])->name('categories.categories-list');
    Route::get('/categories/menus-list', [CategoryController::class, 'getMenusList'])->name('categories.menus-list');
    
    // Menu Item Routes
    Route::get('/menu-items', [MenuItemController::class, 'index'])->name('menu-items.index');
    Route::post('/menu-items', [MenuItemController::class, 'store'])->name('menu-items.store');
    Route::get('/menu-items/{id}/show', [MenuItemController::class, 'show'])->name('menu-items.show');
    Route::get('/menu-items/{id}/edit', [MenuItemController::class, 'edit'])->name('menu-items.edit');
    Route::put('/menu-items/{id}', [MenuItemController::class, 'update'])->name('menu-items.update');
    Route::delete('/menu-items/{id}', [MenuItemController::class, 'destroy'])->name('menu-items.destroy');
    Route::get('/menu-items/list', [MenuItemController::class, 'getMenuItemsList'])->name('menu-items.list');
    Route::get('/menu-items/categories-list', [MenuItemController::class, 'getCategoriesList'])->name('menu-items.categories-list');
    Route::get('/menu-items/menus-list', [MenuItemController::class, 'getMenusList'])->name('menu-items.menus-list');
    
    // Addon Routes
    Route::get('/addons', [AddonController::class, 'index'])->name('addons.index');
    Route::post('/addons', [AddonController::class, 'store'])->name('addons.store');
    Route::get('/addons/{id}/show', [AddonController::class, 'show'])->name('addons.show');
    Route::get('/addons/{id}/edit', [AddonController::class, 'edit'])->name('addons.edit');
    Route::put('/addons/{id}', [AddonController::class, 'update'])->name('addons.update');
    Route::delete('/addons/{id}', [AddonController::class, 'destroy'])->name('addons.destroy');
    Route::get('/addons/menu-items-list', [AddonController::class, 'getMenuItemsList'])->name('addons.menu-items-list');
    
    // Variant Routes
    Route::get('/variants', [VariantController::class, 'index'])->name('menu.variants.index');
    Route::post('/variants', [VariantController::class, 'store'])->name('menu.variants.store');
    Route::get('/variants/{id}/show', [VariantController::class, 'show'])->name('menu.variants.show');
    Route::get('/variants/{id}/edit', [VariantController::class, 'edit'])->name('menu.variants.edit');
    Route::put('/variants/{id}', [VariantController::class, 'update'])->name('menu.variants.update');
    Route::delete('/variants/{id}', [VariantController::class, 'destroy'])->name('menu.variants.destroy');
    Route::get('/variants/menu-items-list', [VariantController::class, 'getMenuItemsList'])->name('menu.variants.menu-items-list');

    // Legacy route for variations (redirect to variants)
    Route::get('/variations', [VariantController::class, 'index'])->name('variations.index');
    Route::post('/variations', [VariantController::class, 'store'])->name('variations.store');
    Route::get('/variations/{id}/show', [VariantController::class, 'show'])->name('variations.show');
    Route::get('/variations/{id}/edit', [VariantController::class, 'edit'])->name('variations.edit');
    Route::put('/variations/{id}', [VariantController::class, 'update'])->name('variations.update');
    Route::delete('/variations/{id}', [VariantController::class, 'destroy'])->name('variations.destroy');
    Route::get('/variations/menu-items-list', [VariantController::class, 'getMenuItemsList'])->name('variations.menu-items-list');
    
    // Banner Routes
    Route::get('/banners', [BannerController::class, 'index'])->name('banners.index');
    Route::post('/banners', [BannerController::class, 'store'])->name('banners.store');
    Route::get('/banners/{id}/show', [BannerController::class, 'show'])->name('banners.show');
    Route::get('/banners/{id}/edit', [BannerController::class, 'edit'])->name('banners.edit');
    Route::put('/banners/{id}', [BannerController::class, 'update'])->name('banners.update');
    Route::delete('/banners/{id}', [BannerController::class, 'destroy'])->name('banners.destroy');
    Route::get('/banners/display', [BannerController::class, 'display'])->name('banners.display');
    
    // Event Routes
    Route::get('/events', [EventController::class, 'index'])->name('events.index');
    Route::post('/events', [EventController::class, 'store'])->name('events.store');
    Route::get('/events/{id}/show', [EventController::class, 'show'])->name('events.show');
    Route::get('/events/{id}/edit', [EventController::class, 'edit'])->name('events.edit');
    Route::put('/events/{id}', [EventController::class, 'update'])->name('events.update');
    Route::delete('/events/{id}', [EventController::class, 'destroy'])->name('events.destroy');
    Route::get('/events/featured', [EventController::class, 'featured'])->name('events.featured');
    
    // Offer Routes
    Route::get('/offers', [OfferController::class, 'index'])->name('offers.index');
    Route::post('/offers', [OfferController::class, 'store'])->name('offers.store');
    Route::get('/offers/{id}/show', [OfferController::class, 'show'])->name('offers.show');
    Route::get('/offers/{id}/edit', [OfferController::class, 'edit'])->name('offers.edit');
    Route::put('/offers/{id}', [OfferController::class, 'update'])->name('offers.update');
    Route::delete('/offers/{id}', [OfferController::class, 'destroy'])->name('offers.destroy');
    Route::get('/offers/featured', [OfferController::class, 'featured'])->name('offers.featured');
    
});
