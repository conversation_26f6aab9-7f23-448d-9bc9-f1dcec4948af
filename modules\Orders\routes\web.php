<?php

use Illuminate\Support\Facades\Route;
use Modules\Orders\Http\Controllers\Web\OrderController;
use Modules\Orders\Http\Controllers\Web\POSController;

/*
|--------------------------------------------------------------------------
| Web Routes for Orders Module
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for the Orders module.
|
*/

Route::middleware(['auth'])->group(function () {
    // POS System Routes
    Route::prefix('pos')->name('pos.')->group(function () {
        Route::get('/', [POSController::class, 'index'])->name('index');
        Route::get('/create', [POSController::class, 'create'])->name('create');
        Route::post('/orders', [POSController::class, 'store'])->name('orders.store');
        Route::get('/orders/{order}/kot', [POSController::class, 'generateKOT'])->name('orders.kot');
        Route::post('/orders/{order}/kot/print', [POSController::class, 'printKOT'])->name('orders.kot.print');

        // AJAX endpoints
        Route::get('/menu-items/{menuItem}/addons', [POSController::class, 'getMenuItemAddons'])->name('menu-items.addons');
        Route::get('/menu-items/{menuItem}/details', [POSController::class, 'getMenuItemDetails'])->name('menu-items.details');
        Route::post('/calculate-totals', [POSController::class, 'calculateOrderTotals'])->name('calculate-totals');
    });

    // Order Management Routes
    Route::prefix('orders')->name('orders.')->group(function () {
        Route::get('/', [OrderController::class, 'indexView'])->name('index');
        Route::get('/create', [OrderController::class, 'create'])->name('create');
        Route::post('/', [OrderController::class, 'store'])->name('store');
        Route::get('/{order}', [OrderController::class, 'showView'])->name('show');
        Route::get('/{order}/edit', [OrderController::class, 'edit'])->name('edit');
        Route::put('/{order}', [OrderController::class, 'update'])->name('update');
        Route::delete('/{order}', [OrderController::class, 'destroy'])->name('destroy');

        // DataTable AJAX endpoint
        Route::get('/data/orders', [OrderController::class, 'getOrdersData'])->name('data');
        
        // Statistics endpoint for web view
        Route::get('/data/statistics', [OrderController::class, 'getStatistics'])->name('statistics');
        
        // Cards data endpoint for web view
        Route::get('/data/cards', [OrderController::class, 'getCardsData'])->name('cards');
    });
});
