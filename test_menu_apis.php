<?php

/**
 * Menu Module API Testing Script
 * 
 * This script tests all API endpoints in the Menu module to ensure they work correctly.
 * Run this script from the command line: php test_menu_apis.php
 */

require_once __DIR__ . '/vendor/autoload.php';

class MenuApiTester
{
    private $baseUrl;
    private $authToken;
    private $testResults = [];
    
    public function __construct()
    {
        $this->baseUrl = 'http://localhost:8000/api/menu';
        $this->authToken = null; // Will be set after authentication
    }
    
    /**
     * Run all API tests
     */
    public function runAllTests()
    {
        echo "🚀 Starting Menu Module API Tests...\n\n";
        
        // First, authenticate to get a token
        if (!$this->authenticate()) {
            echo "❌ Authentication failed. Cannot proceed with API tests.\n";
            return;
        }
        
        // Test all endpoints
        $this->testMenuApis();
        $this->testCategoryApis();
        $this->testMenuItemApis();
        $this->testAddonApis();
        $this->testVariantApis();
        $this->testBannerApis();
        $this->testEventApis();
        $this->testOfferApis();
        $this->testPublicApis();
        
        // Display results
        $this->displayResults();
    }
    
    /**
     * Authenticate and get token
     */
    private function authenticate()
    {
        echo "🔐 Authenticating...\n";
        
        $loginData = [
            'email' => '<EMAIL>', // Update with actual test credentials
            'password' => 'password'
        ];
        
        $response = $this->makeRequest('POST', 'http://localhost:8000/api/auth/login', $loginData);
        
        if ($response && isset($response['token'])) {
            $this->authToken = $response['token'];
            echo "✅ Authentication successful\n\n";
            return true;
        }
        
        echo "❌ Authentication failed\n";
        return false;
    }
    
    /**
     * Test Menu APIs
     */
    private function testMenuApis()
    {
        echo "📋 Testing Menu APIs...\n";
        
        // Test GET /api/menu/menus
        $this->testEndpoint('GET', '/menus', [], 'Get all menus');
        
        // Test POST /api/menu/menus
        $menuData = [
            'name' => 'Test Menu',
            'description' => 'Test menu description',
            'menu_type' => 'main',
            'is_active' => true,
            'sort_order' => 1
        ];
        $createdMenu = $this->testEndpoint('POST', '/menus', $menuData, 'Create menu');
        
        if ($createdMenu && isset($createdMenu['data']['id'])) {
            $menuId = $createdMenu['data']['id'];
            
            // Test GET /api/menu/menus/{id}
            $this->testEndpoint('GET', "/menus/{$menuId}", [], 'Get menu by ID');
            
            // Test PUT /api/menu/menus/{id}
            $updateData = [
                'name' => 'Updated Test Menu',
                'description' => 'Updated description'
            ];
            $this->testEndpoint('PUT', "/menus/{$menuId}", $updateData, 'Update menu');
            
            // Test DELETE /api/menu/menus/{id}
            $this->testEndpoint('DELETE', "/menus/{$menuId}", [], 'Delete menu');
        }
        
        echo "\n";
    }
    
    /**
     * Test Category APIs
     */
    private function testCategoryApis()
    {
        echo "📂 Testing Category APIs...\n";
        
        // Test GET /api/menu/categories
        $this->testEndpoint('GET', '/categories', [], 'Get all categories');
        
        // Test POST /api/menu/categories
        $categoryData = [
            'name' => 'Test Category',
            'code' => 'TEST_CAT',
            'description' => 'Test category description',
            'is_active' => true,
            'sort_order' => 1
        ];
        $createdCategory = $this->testEndpoint('POST', '/categories', $categoryData, 'Create category');
        
        if ($createdCategory && isset($createdCategory['data']['id'])) {
            $categoryId = $createdCategory['data']['id'];
            
            // Test GET /api/menu/categories/{id}
            $this->testEndpoint('GET', "/categories/{$categoryId}", [], 'Get category by ID');
            
            // Test PUT /api/menu/categories/{id}
            $updateData = [
                'name' => 'Updated Test Category',
                'description' => 'Updated description'
            ];
            $this->testEndpoint('PUT', "/categories/{$categoryId}", $updateData, 'Update category');
            
            // Test DELETE /api/menu/categories/{id}
            $this->testEndpoint('DELETE', "/categories/{$categoryId}", [], 'Delete category');
        }
        
        echo "\n";
    }
    
    /**
     * Test Menu Item APIs
     */
    private function testMenuItemApis()
    {
        echo "🍽️ Testing Menu Item APIs...\n";
        
        // Test GET /api/menu/menu-items
        $this->testEndpoint('GET', '/menu-items', [], 'Get all menu items');
        
        // Test POST /api/menu/menu-items
        $menuItemData = [
            'name' => 'Test Menu Item',
            'description' => 'Test menu item description',
            'price' => 15.99,
            'is_active' => true,
            'is_featured' => false
        ];
        $createdMenuItem = $this->testEndpoint('POST', '/menu-items', $menuItemData, 'Create menu item');
        
        if ($createdMenuItem && isset($createdMenuItem['data']['id'])) {
            $menuItemId = $createdMenuItem['data']['id'];
            
            // Test GET /api/menu/menu-items/{id}
            $this->testEndpoint('GET', "/menu-items/{$menuItemId}", [], 'Get menu item by ID');
            
            // Test PUT /api/menu/menu-items/{id}
            $updateData = [
                'name' => 'Updated Test Menu Item',
                'price' => 18.99
            ];
            $this->testEndpoint('PUT', "/menu-items/{$menuItemId}", $updateData, 'Update menu item');
            
            // Test DELETE /api/menu/menu-items/{id}
            $this->testEndpoint('DELETE', "/menu-items/{$menuItemId}", [], 'Delete menu item');
        }
        
        echo "\n";
    }
    
    /**
     * Test Addon APIs
     */
    private function testAddonApis()
    {
        echo "🧩 Testing Addon APIs...\n";
        
        // Test GET /api/menu/addons
        $this->testEndpoint('GET', '/addons', [], 'Get all addons');
        
        // Test POST /api/menu/addons
        $addonData = [
            'name' => 'Test Addon',
            'addon_group_name' => 'Test Group',
            'price' => 2.99,
            'is_required' => false
        ];
        $createdAddon = $this->testEndpoint('POST', '/addons', $addonData, 'Create addon');
        
        if ($createdAddon && isset($createdAddon['data']['id'])) {
            $addonId = $createdAddon['data']['id'];
            
            // Test GET /api/menu/addons/{id}
            $this->testEndpoint('GET', "/addons/{$addonId}", [], 'Get addon by ID');
            
            // Test PUT /api/menu/addons/{id}
            $updateData = [
                'name' => 'Updated Test Addon',
                'price' => 3.99
            ];
            $this->testEndpoint('PUT', "/addons/{$addonId}", $updateData, 'Update addon');
            
            // Test DELETE /api/menu/addons/{id}
            $this->testEndpoint('DELETE', "/addons/{$addonId}", [], 'Delete addon');
        }
        
        echo "\n";
    }
    
    /**
     * Test Variant APIs
     */
    private function testVariantApis()
    {
        echo "🔄 Testing Variant APIs...\n";
        
        // Test GET /api/menu/variants
        $this->testEndpoint('GET', '/variants', [], 'Get all variants');
        
        // Test POST /api/menu/variants
        $variantData = [
            'name' => 'Test Variant',
            'variant_group_name' => 'Size',
            'price_adjustment' => 1.50,
            'is_required' => false
        ];
        $createdVariant = $this->testEndpoint('POST', '/variants', $variantData, 'Create variant');
        
        if ($createdVariant && isset($createdVariant['data']['id'])) {
            $variantId = $createdVariant['data']['id'];
            
            // Test GET /api/menu/variants/{id}
            $this->testEndpoint('GET', "/variants/{$variantId}", [], 'Get variant by ID');
            
            // Test PUT /api/menu/variants/{id}
            $updateData = [
                'name' => 'Updated Test Variant',
                'price_adjustment' => 2.00
            ];
            $this->testEndpoint('PUT', "/variants/{$variantId}", $updateData, 'Update variant');
            
            // Test DELETE /api/menu/variants/{id}
            $this->testEndpoint('DELETE', "/variants/{$variantId}", [], 'Delete variant');
        }
        
        echo "\n";
    }
    
    /**
     * Test Banner APIs
     */
    private function testBannerApis()
    {
        echo "🎯 Testing Banner APIs...\n";
        
        // Test GET /api/menu/banners
        $this->testEndpoint('GET', '/banners', [], 'Get all banners');
        
        // Test GET /api/menu/banners/featured
        $this->testEndpoint('GET', '/banners/featured', [], 'Get featured banners');
        
        // Test POST /api/menu/banners
        $bannerData = [
            'title' => 'Test Banner',
            'description' => 'Test banner description',
            'banner_type' => 'promotional',
            'display_location' => 'homepage',
            'is_active' => true,
            'is_featured' => false
        ];
        $createdBanner = $this->testEndpoint('POST', '/banners', $bannerData, 'Create banner');
        
        if ($createdBanner && isset($createdBanner['data']['id'])) {
            $bannerId = $createdBanner['data']['id'];
            
            // Test GET /api/menu/banners/{id}
            $this->testEndpoint('GET', "/banners/{$bannerId}", [], 'Get banner by ID');
            
            // Test PUT /api/menu/banners/{id}
            $updateData = [
                'title' => 'Updated Test Banner',
                'is_featured' => true
            ];
            $this->testEndpoint('PUT', "/banners/{$bannerId}", $updateData, 'Update banner');
            
            // Test DELETE /api/menu/banners/{id}
            $this->testEndpoint('DELETE', "/banners/{$bannerId}", [], 'Delete banner');
        }
        
        echo "\n";
    }
    
    /**
     * Test Event APIs
     */
    private function testEventApis()
    {
        echo "🎉 Testing Event APIs...\n";
        
        // Test GET /api/menu/events
        $this->testEndpoint('GET', '/events', [], 'Get all events');
        
        // Test GET /api/menu/events/featured
        $this->testEndpoint('GET', '/events/featured', [], 'Get featured events');
        
        // Test GET /api/menu/events/upcoming
        $this->testEndpoint('GET', '/events/upcoming', [], 'Get upcoming events');
        
        // Test POST /api/menu/events
        $eventData = [
            'title' => 'Test Event',
            'description' => 'Test event description',
            'event_type' => 'special_dinner',
            'event_date' => date('Y-m-d H:i:s', strtotime('+1 week')),
            'is_featured' => false,
            'max_participants' => 50
        ];
        $createdEvent = $this->testEndpoint('POST', '/events', $eventData, 'Create event');
        
        if ($createdEvent && isset($createdEvent['data']['id'])) {
            $eventId = $createdEvent['data']['id'];
            
            // Test GET /api/menu/events/{id}
            $this->testEndpoint('GET', "/events/{$eventId}", [], 'Get event by ID');
            
            // Test PUT /api/menu/events/{id}
            $updateData = [
                'title' => 'Updated Test Event',
                'is_featured' => true
            ];
            $this->testEndpoint('PUT', "/events/{$eventId}", $updateData, 'Update event');
            
            // Test POST /api/menu/events/{id}/register
            $registrationData = [
                'customer_name' => 'John Doe',
                'customer_email' => '<EMAIL>',
                'party_size' => 2
            ];
            $this->testEndpoint('POST', "/events/{$eventId}/register", $registrationData, 'Register for event');
            
            // Test DELETE /api/menu/events/{id}
            $this->testEndpoint('DELETE', "/events/{$eventId}", [], 'Delete event');
        }
        
        echo "\n";
    }
    
    /**
     * Test Offer APIs
     */
    private function testOfferApis()
    {
        echo "💰 Testing Offer APIs...\n";
        
        // Test GET /api/menu/offers
        $this->testEndpoint('GET', '/offers', [], 'Get all offers');
        
        // Test GET /api/menu/offers/featured
        $this->testEndpoint('GET', '/offers/featured', [], 'Get featured offers');
        
        // Test GET /api/menu/offers/active
        $this->testEndpoint('GET', '/offers/active', [], 'Get active offers');
        
        // Test POST /api/menu/offers
        $offerData = [
            'title' => 'Test Offer',
            'description' => 'Test offer description',
            'offer_code' => 'TEST10',
            'offer_type' => 'discount',
            'discount_type' => 'percentage',
            'discount_value' => 10,
            'is_active' => true,
            'valid_from' => date('Y-m-d'),
            'valid_until' => date('Y-m-d', strtotime('+1 month'))
        ];
        $createdOffer = $this->testEndpoint('POST', '/offers', $offerData, 'Create offer');
        
        if ($createdOffer && isset($createdOffer['data']['id'])) {
            $offerId = $createdOffer['data']['id'];
            
            // Test GET /api/menu/offers/{id}
            $this->testEndpoint('GET', "/offers/{$offerId}", [], 'Get offer by ID');
            
            // Test PUT /api/menu/offers/{id}
            $updateData = [
                'title' => 'Updated Test Offer',
                'discount_value' => 15
            ];
            $this->testEndpoint('PUT', "/offers/{$offerId}", $updateData, 'Update offer');
            
            // Test POST /api/menu/offers/validate
            $validateData = [
                'offer_code' => 'TEST10',
                'order_total' => 50.00
            ];
            $this->testEndpoint('POST', '/offers/validate', $validateData, 'Validate offer');
            
            // Test POST /api/menu/offers/apply
            $applyData = [
                'offer_code' => 'TEST10',
                'order_total' => 50.00,
                'menu_items' => []
            ];
            $this->testEndpoint('POST', '/offers/apply', $applyData, 'Apply offer');
            
            // Test DELETE /api/menu/offers/{id}
            $this->testEndpoint('DELETE', "/offers/{$offerId}", [], 'Delete offer');
        }
        
        echo "\n";
    }
    
    /**
     * Test Public APIs
     */
    private function testPublicApis()
    {
        echo "🌐 Testing Public APIs...\n";
        
        // Test GET /menu/public/menus (no auth required)
        $this->testEndpoint('GET', '/public/menus', [], 'Get public menus', false);
        
        echo "\n";
    }
    
    /**
     * Test a specific endpoint
     */
    private function testEndpoint($method, $endpoint, $data = [], $description = '', $requireAuth = true)
    {
        $url = $this->baseUrl . $endpoint;
        $response = $this->makeRequest($method, $url, $data, $requireAuth);
        
        $success = $response !== false && (!isset($response['error']) || $response['success'] === true);
        
        $this->testResults[] = [
            'method' => $method,
            'endpoint' => $endpoint,
            'description' => $description,
            'success' => $success,
            'response' => $response
        ];
        
        $status = $success ? '✅' : '❌';
        echo "  {$status} {$method} {$endpoint} - {$description}\n";
        
        if (!$success && $response) {
            echo "     Error: " . (isset($response['message']) ? $response['message'] : 'Unknown error') . "\n";
        }
        
        return $response;
    }
    
    /**
     * Make HTTP request
     */
    private function makeRequest($method, $url, $data = [], $requireAuth = true)
    {
        $ch = curl_init();
        
        $headers = [
            'Content-Type: application/json',
            'Accept: application/json'
        ];
        
        if ($requireAuth && $this->authToken) {
            $headers[] = 'Authorization: Bearer ' . $this->authToken;
        }
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CUSTOMREQUEST => $method
        ]);
        
        if (in_array($method, ['POST', 'PUT', 'PATCH']) && !empty($data)) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($response === false) {
            return false;
        }
        
        $decodedResponse = json_decode($response, true);
        
        // Consider 2xx status codes as success
        if ($httpCode >= 200 && $httpCode < 300) {
            return $decodedResponse;
        }
        
        return $decodedResponse ?: ['error' => 'HTTP ' . $httpCode];
    }
    
    /**
     * Display test results
     */
    private function displayResults()
    {
        echo "📊 Test Results Summary:\n";
        echo str_repeat("=", 50) . "\n";
        
        $totalTests = count($this->testResults);
        $passedTests = array_filter($this->testResults, function($result) {
            return $result['success'];
        });
        $passedCount = count($passedTests);
        $failedCount = $totalTests - $passedCount;
        
        echo "Total Tests: {$totalTests}\n";
        echo "Passed: {$passedCount} ✅\n";
        echo "Failed: {$failedCount} ❌\n";
        echo "Success Rate: " . round(($passedCount / $totalTests) * 100, 2) . "%\n\n";
        
        if ($failedCount > 0) {
            echo "Failed Tests:\n";
            echo str_repeat("-", 30) . "\n";
            foreach ($this->testResults as $result) {
                if (!$result['success']) {
                    echo "❌ {$result['method']} {$result['endpoint']} - {$result['description']}\n";
                    if (isset($result['response']['message'])) {
                        echo "   Error: {$result['response']['message']}\n";
                    }
                }
            }
        }
        
        echo "\n🎉 Menu API testing completed!\n";
    }
}

// Run the tests
$tester = new MenuApiTester();
$tester->runAllTests();