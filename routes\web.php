<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\PublicRestaurantController;
use Illuminate\Support\Facades\Route;

Route::get('/restaurant/table/{hashe}', [PublicRestaurantController::class, 'showByTableQRCode']);
Route::get('/restaurant/{tenantName}', [PublicRestaurantController::class, 'showByTenantName'])->name('public.restaurant.show');
Route::post('/restaurant/{tenantName}/request-waiter', [PublicRestaurantController::class, 'requestWaiter'])->name('restaurant.request-waiter');
Route::post('/restaurant/{tenantName}/submit-order', [PublicRestaurantController::class, 'submitOrder'])->name('restaurant.submit-order');


Route::get('/', function () {
    return view('welcome');
})->name('index');
Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth'])->name('dashboard');




Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
      // Include Module Routes
    // require __DIR__.'/../modules/Menu/routes/web.php';
    // require __DIR__.'/../modules/Orders/routes/web.php';
    // require __DIR__.'/../modules/Customer/routes/web.php';
    // require __DIR__.'/../modules/Reservation/routes/web.php';
});

require __DIR__.'/auth.php';

// Include public menu routes (no authentication required)
require __DIR__.'/../modules/Menu/routes/public.php';
