<?php $__env->startSection('title', 'Kitchen Details'); ?>

<?php $__env->startPush('styles'); ?>
<meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
<!-- Bootstrap CSS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<!-- SweetAlert2 CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
<?php $__env->stopPush(); ?>

<?php $__env->startSection('page-header'); ?>
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">Kitchen</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ <?php echo e($kitchen->name); ?></span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <a href="<?php echo e(route('kitchens.edit', $kitchen)); ?>" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit Kitchen
            </a>
            <a href="<?php echo e(route('kitchens.index')); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Kitchens
            </a>
        </div>
    </div>
</div>
<!-- breadcrumb -->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Kitchen Information -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Kitchen Information</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Name:</strong></td>
                                    <td><?php echo e($kitchen->name); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Code:</strong></td>
                                    <td><span class="badge badge-info"><?php echo e($kitchen->code); ?></span></td>
                                </tr>
                                <tr>
                                    <td><strong>Station Type:</strong></td>
                                    <td><?php echo e(ucfirst($kitchen->station_type)); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Branch:</strong></td>
                                    <td><?php echo e($kitchen->branch?->name ?? 'N/A'); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Manager:</strong></td>
                                    <td><?php echo e($kitchen->manager?->name ?? 'Unassigned'); ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        <span class="badge badge-<?php echo e($kitchen->is_active ? 'success' : 'danger'); ?>">
                                            <?php echo e($kitchen->is_active ? 'Active' : 'Inactive'); ?>

                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Operating:</strong></td>
                                    <td>
                                        <span class="badge badge-<?php echo e($kitchen->isOperating() ? 'success' : 'warning'); ?>">
                                            <?php echo e($kitchen->isOperating() ? 'Operating' : 'Closed'); ?>

                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Max Orders:</strong></td>
                                    <td><?php echo e($kitchen->max_concurrent_orders ?? '∞'); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Current Workload:</strong></td>
                                    <td><?php echo e($kitchen->getCurrentWorkload()); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Created:</strong></td>
                                    <td><?php echo e($kitchen->created_at->format('M d, Y H:i')); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <?php if($kitchen->description): ?>
                    <div class="row">
                        <div class="col-12">
                            <hr>
                            <h6>Description:</h6>
                            <p><?php echo e($kitchen->description); ?></p>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Quick Stats</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-primary"><?php echo e($kitchen->kitchenMenuItems->count()); ?></h4>
                                <small class="text-muted">Menu Items</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-warning"><?php echo e($kitchen->kotOrders->count()); ?></h4>
                                <small class="text-muted">Active KOTs</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Menu Items -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Assigned Menu Items</h3>
                    <div class="card-options">
                        <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#assignMenuItemModal">
                            <i class="fas fa-plus"></i> Assign Menu Item
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="menu-items-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Menu Item</th>
                                    <th>Category</th>
                                    <th>Price</th>
                                    <th>Status</th>
                                    <th>Assigned Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $kitchen->kitchenMenuItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $kitchenMenuItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($loop->iteration); ?></td>
                                    <td><?php echo e($kitchenMenuItem->menuItem->name); ?></td>
                                    <td><?php echo e($kitchenMenuItem->menuItem->category->name ?? 'N/A'); ?></td>
                                    <td>$<?php echo e(number_format($kitchenMenuItem->menuItem->base_price, 2)); ?></td>
                                    <td>
                                        <span class="badge badge-<?php echo e($kitchenMenuItem->is_active ? 'success' : 'danger'); ?>">
                                            <?php echo e($kitchenMenuItem->is_active ? 'Active' : 'Inactive'); ?>

                                        </span>
                                    </td>
                                    <td><?php echo e($kitchenMenuItem->created_at->format('M d, Y')); ?></td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-danger remove-menu-item" 
                                                data-menu-item-id="<?php echo e($kitchenMenuItem->menu_item_id); ?>">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent KOT Orders -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Recent KOT Orders</h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>KOT #</th>
                                    <th>Order #</th>
                                    <th>Status</th>
                                    <th>Priority</th>
                                    <th>Items</th>
                                    <th>Created</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $kitchen->kotOrders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $kotOrder): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($kotOrder->kot_number); ?></td>
                                    <td><?php echo e($kotOrder->order->order_number); ?></td>
                                    <td>
                                        <span class="badge badge-<?php echo e($kotOrder->status == 'pending' ? 'warning' : ($kotOrder->status == 'preparing' ? 'info' : 'success')); ?>">
                                            <?php echo e(ucfirst($kotOrder->status)); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-<?php echo e($kotOrder->priority == 'urgent' ? 'danger' : ($kotOrder->priority == 'high' ? 'warning' : 'secondary')); ?>">
                                            <?php echo e(ucfirst($kotOrder->priority)); ?>

                                        </span>
                                    </td>
                                    <td><?php echo e($kotOrder->kotOrderItems->count()); ?></td>
                                    <td><?php echo e($kotOrder->created_at->format('M d, H:i')); ?></td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="6" class="text-center text-muted">No recent KOT orders</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Assign Menu Item Modal -->
<div class="modal fade" id="assignMenuItemModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Assign Menu Item</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="assignMenuItemForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="menu_item_id">Menu Item <span class="text-danger">*</span></label>
                        <select class="form-control select2" id="menu_item_id" name="menu_item_id" required>
                            <option value="">Select Menu Item</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Assign</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
$(document).ready(function() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Initialize Select2
    $('.select2').select2();

    // Initialize DataTable
    $('#menu-items-table').DataTable();

    // Load available menu items when modal opens
    $('#assignMenuItemModal').on('shown.bs.modal', function() {
        $.ajax({
            url: '<?php echo e(route("kitchens.available-menu-items", $kitchen)); ?>',
            method: 'GET',
            success: function(response) {
                const select = $('#menu_item_id');
                select.empty().append('<option value="">Select Menu Item</option>');
                
                response.forEach(function(item) {
                    select.append(`<option value="${item.id}">${item.name} - ${item.category_name}</option>`);
                });
            }
        });
    });

    // Handle assign menu item form submission
    $('#assignMenuItemForm').on('submit', function(e) {
        e.preventDefault();

        const submitButton = $(this).find('button[type="submit"]');
        const originalText = submitButton.text();
        submitButton.prop('disabled', true).text('Assigning...');

        $.ajax({
            url: '<?php echo e(route("kitchens.assign-menu-item", $kitchen)); ?>',
            method: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                if (response.success) {
                    Swal.fire({
                        title: 'Success!',
                        text: 'Menu item assigned successfully',
                        icon: 'success',
                        confirmButtonColor: '#3b82f6'
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: response.message || 'Failed to assign menu item',
                        icon: 'error',
                        confirmButtonColor: '#3b82f6'
                    });
                }
            },
            error: function(xhr) {
                let errorMessage = "Failed to assign menu item";
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                Swal.fire({
                    title: 'Error!',
                    text: errorMessage,
                    icon: 'error',
                    confirmButtonColor: '#3b82f6'
                });
            },
            complete: function() {
                submitButton.prop('disabled', false).text(originalText);
            }
        });
    });

    // Handle remove menu item
    $(document).on('click', '.remove-menu-item', function() {
        const menuItemId = $(this).data('menu-item-id');
        
        Swal.fire({
            title: 'Are you sure?',
            text: 'This will remove the menu item from this kitchen.',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, remove it!',
            cancelButtonText: 'Cancel',
            confirmButtonColor: '#dc2626',
            cancelButtonColor: '#6b7280'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: `<?php echo e(url('kitchens/' . $kitchen->id . '/menu-items')); ?>/${menuItemId}`,
                    method: 'DELETE',
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'Success!',
                                text: 'Menu item removed successfully',
                                icon: 'success',
                                confirmButtonColor: '#3b82f6'
                            }).then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: response.message || 'Failed to remove menu item',
                                icon: 'error',
                                confirmButtonColor: '#3b82f6'
                            });
                        }
                    },
                    error: function(xhr) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Failed to remove menu item',
                            icon: 'error',
                            confirmButtonColor: '#3b82f6'
                        });
                    }
                });
            }
        });
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Kitchen\Providers/../resources/views/kitchens/show.blade.php ENDPATH**/ ?>