<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\SubscriptionPlan>
 */
class SubscriptionPlanFactory extends Factory
{
    public function definition(): array
    {
        return [
            'name' => $name = $this->faker->word() . ' Plan',
            'code' => \Illuminate\Support\Str::slug($name) . '-' . $this->faker->unique()->randomNumber(4),
            'description' => $this->faker->sentence(),
            'price' => $this->faker->randomFloat(2, 10, 500),
            'billing_cycle' => $this->faker->randomElement(['monthly', 'yearly', 'quarterly']),
            'trial_days' => $this->faker->randomElement([0, 7, 14, 30]),
            'max_branches' => $this->faker->numberBetween(1, 10),
            'max_users' => $this->faker->numberBetween(1, 100),
            'max_products' => $this->faker->numberBetween(10, 1000),
            'max_orders_per_month' => $this->faker->numberBetween(100, 10000),
            'features' => ['feature1', 'feature2'],
            'limitations' => ['orders' => 1000],
            'is_active' => true,
            'is_popular' => $this->faker->boolean(),
            'sort_order' => $this->faker->numberBetween(1, 10),
        ];
    }
}