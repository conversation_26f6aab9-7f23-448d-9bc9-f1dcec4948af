@extends('layouts.master')

@section('page-header')
<div class="mb-6">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="mb-4 sm:mb-0">
            <nav class="flex mb-2" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <span class="text-gray-700 text-sm font-medium">إدارة النظام</span>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <i class="fas fa-chevron-left text-gray-400 mx-2"></i>
                            <span class="text-gray-500 text-sm">الأدوار</span>
                        </div>
                    </li>
                </ol>
            </nav>
            <h1 class="text-2xl font-bold text-gray-900">إدارة الأدوار</h1>
        </div>
        <div class="flex space-x-2">
            <button type="button" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 active:bg-blue-900 focus:outline-none focus:border-blue-900 focus:ring ring-blue-300 disabled:opacity-25 transition ease-in-out duration-150" onclick="openModal('addRoleModal')">
                <i class="fas fa-plus ml-2"></i>
                إضافة دور جديد
            </button>
        </div>
    </div>
</div>
@endsection

@section('content')
<div class="bg-white rounded-lg shadow">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex justify-between items-center">
            <div>
                <h3 class="text-lg font-medium text-gray-900">قائمة الأدوار</h3>
                <p class="mt-1 text-sm text-gray-600">إدارة أدوار النظام وصلاحياتها</p>
            </div>
            <div class="flex items-center space-x-2">
                <i class="fas fa-ellipsis-h text-gray-400"></i>
            </div>
        </div>
    </div>
    <div class="p-6">
        <!-- DataTable Controls -->
        <div class="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div class="flex flex-col sm:flex-row gap-4">
                <!-- Search -->
                <div class="relative">
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" id="globalSearch" placeholder="البحث في الأدوار..." class="w-full sm:w-64 pl-3 pr-10 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- Guard Filter -->
                <select id="guardFilter" class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">جميع الحراس</option>
                    <option value="web">ويب</option>
                    <option value="api">API</option>
                    <option value="sanctum">Sanctum</option>
                </select>
            </div>

            <!-- Export Buttons -->
            <div class="flex gap-2">
                <button type="button" id="exportExcel" class="px-3 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 flex items-center gap-2">
                    <i class="fas fa-file-excel"></i>
                    Excel
                </button>
                <button type="button" id="exportPdf" class="px-3 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 flex items-center gap-2">
                    <i class="fas fa-file-pdf"></i>
                    PDF
                </button>
                <button type="button" id="printTable" class="px-3 py-2 bg-gray-600 text-white text-sm font-medium rounded-md hover:bg-gray-700 flex items-center gap-2">
                    <i class="fas fa-print"></i>
                    طباعة
                </button>
            </div>
        </div>

        <!-- DataTable Container -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
            <div class="overflow-x-auto">
                <table id="rolesTable" class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">#</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">اسم الدور</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">الحارس</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">عدد الصلاحيات</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">عدد المستخدمين</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">تاريخ الإنشاء</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                    </tbody>
                </table>
            </div>

            <!-- DataTable Info and Pagination -->
            <div class="bg-gray-50 px-6 py-3 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700" id="tableInfo">
                        <!-- DataTable info will be inserted here -->
                    </div>
                    <div id="tablePagination">
                        <!-- DataTable pagination will be inserted here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Role Modal -->
<div id="addRoleModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center pb-3">
            <h3 class="text-lg font-medium text-gray-900">إضافة دور جديد</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeModal('addRoleModal')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <form id="addRoleForm" action="{{ route('roles.store') }}" method="POST">
            @csrf
            <div class="mt-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">اسم الدور <span class="text-red-500">*</span></label>
                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="name" name="name" required>
                    </div>
                    <div>
                        <label for="guard_name" class="block text-sm font-medium text-gray-700 mb-2">الحارس <span class="text-red-500">*</span></label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="guard_name" name="guard_name" required>
                            <option value="web">ويب</option>
                            <option value="api">API</option>
                            <option value="sanctum">Sanctum</option>
                        </select>
                    </div>
                </div>

                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700 mb-3">الصلاحيات:</label>
                    <div class="permissions-container max-h-80 overflow-y-auto border border-gray-200 rounded-md p-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            @foreach($permissions as $group => $groupPermissions)
                                <div class="bg-gray-50 rounded-lg p-3">
                                    <div class="flex items-center mb-3 pb-2 border-b border-gray-200">
                                        <input type="checkbox" class="group-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" data-group="{{ $group }}">
                                        <label class="mr-2 text-sm font-medium text-gray-900">{{ ucfirst($group) }}</label>
                                    </div>
                                    <div class="space-y-2">
                                        @foreach($groupPermissions as $permission)
                                            <div class="flex items-center">
                                                <input class="permission-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" type="checkbox"
                                                       name="permissions[]" value="{{ $permission->name }}"
                                                       id="perm_{{ $permission->id }}" data-group="{{ $group }}">
                                                <label class="mr-2 text-sm text-gray-700" for="perm_{{ $permission->id }}">
                                                    {{ $permission->name }}
                                                </label>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex justify-end space-x-2 pt-4">
                <button type="button" class="px-4 py-2 bg-gray-500 text-white text-sm font-medium rounded-md hover:bg-gray-600" onclick="closeModal('addRoleModal')">إلغاء</button>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700">حفظ</button>
            </div>
        </form>
    </div>
</div>

<!-- Edit Role Modal -->
<div id="editRoleModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center pb-3">
            <h3 class="text-lg font-medium text-gray-900">تعديل الدور</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeModal('editRoleModal')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <form id="editRoleForm" method="POST">
            @csrf
            @method('PUT')
            <div class="mt-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="edit_name" class="block text-sm font-medium text-gray-700 mb-2">اسم الدور <span class="text-red-500">*</span></label>
                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="edit_name" name="name" required>
                    </div>
                    <div>
                        <label for="edit_guard_name" class="block text-sm font-medium text-gray-700 mb-2">الحارس <span class="text-red-500">*</span></label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="edit_guard_name" name="guard_name" required>
                            <option value="web">ويب</option>
                            <option value="api">API</option>
                            <option value="sanctum">Sanctum</option>
                        </select>
                    </div>
                </div>

                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700 mb-3">الصلاحيات:</label>
                    <div class="edit-permissions-container max-h-80 overflow-y-auto border border-gray-200 rounded-md p-4">
                        <!-- Permissions will be loaded dynamically -->
                    </div>
                </div>
            </div>
            <div class="flex justify-end space-x-2 pt-4">
                <button type="button" class="px-4 py-2 bg-gray-500 text-white text-sm font-medium rounded-md hover:bg-gray-600" onclick="closeModal('editRoleModal')">إلغاء</button>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700">تحديث</button>
            </div>
        </form>
    </div>
</div>

<!-- Assign Permissions Modal -->
<div id="assignPermissionsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center pb-3">
            <h3 class="text-lg font-medium text-gray-900">إدارة صلاحيات الدور</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeModal('assignPermissionsModal')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <form id="assignPermissionsForm" method="POST">
            @csrf
            <div class="mt-4">
                <div class="assign-permissions-container max-h-96 overflow-y-auto border border-gray-200 rounded-md p-4">
                    <!-- Permissions will be loaded dynamically -->
                </div>
            </div>
            <div class="flex justify-end space-x-2 pt-4">
                <button type="button" class="px-4 py-2 bg-gray-500 text-white text-sm font-medium rounded-md hover:bg-gray-600" onclick="closeModal('assignPermissionsModal')">إلغاء</button>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700">حفظ الصلاحيات</button>
            </div>
        </form>
    </div>
</div>

@endsection

@push('scripts')
<!-- DataTables CDN -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.js"></script>
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap4.min.js"></script>
<!-- Sweet Alert -->
<script src="{{URL::asset('assets/plugins/sweet-alert/sweetalert.min.js')}}"></script>

<script>
// Modal Functions
function openModal(modalId) {
    document.getElementById(modalId).classList.remove('hidden');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

$(document).ready(function() {
    // Initialize DataTable
    var table = $('#rolesTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: "{{ route('roles.data') }}",
        columns: [
            {data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false},
            {data: 'name', name: 'name'},
            {data: 'guard_badge', name: 'guard_name'},
            {data: 'permissions_count', name: 'permissions_count', orderable: false},
            {data: 'users_count', name: 'users_count', orderable: false},
            {data: 'created_at', name: 'created_at'},
            {data: 'action', name: 'action', orderable: false, searchable: false}
        ],
        dom: 'Bfrtip',
        buttons: [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
        }
    });

    // Group checkbox functionality
    $(document).on('change', '.group-checkbox', function() {
        var group = $(this).data('group');
        var isChecked = $(this).is(':checked');
        $('.permission-checkbox[data-group="' + group + '"]').prop('checked', isChecked);
    });

    // Individual permission checkbox functionality
    $(document).on('change', '.permission-checkbox', function() {
        var group = $(this).data('group');
        var groupCheckbox = $('.group-checkbox[data-group="' + group + '"]');
        var groupPermissions = $('.permission-checkbox[data-group="' + group + '"]');
        var checkedPermissions = $('.permission-checkbox[data-group="' + group + '"]:checked');
        
        if (checkedPermissions.length === groupPermissions.length) {
            groupCheckbox.prop('checked', true);
        } else {
            groupCheckbox.prop('checked', false);
        }
    });

    // Enhanced Search and Filter Functionality
    $('#globalSearch').on('keyup', function() {
        table.search(this.value).draw();
    });

    $('#guardFilter').on('change', function() {
        var guard = this.value;
        table.column(2).search(guard).draw();
    });

    // Export Functions
    $('#exportExcel').on('click', function() {
        table.button('.buttons-excel').trigger();
    });

    $('#exportPdf').on('click', function() {
        table.button('.buttons-pdf').trigger();
    });

    $('#printTable').on('click', function() {
        table.button('.buttons-print').trigger();
    });

    // Store permissions data for JavaScript use
    var permissionsData = @json($permissions ?? []);

    // Handle Add Role Form Submission
    $('#addRoleForm').on('submit', function(e) {
        e.preventDefault();

        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                closeModal('addRoleModal');
                $('#addRoleForm')[0].reset();
                swal("تم الحفظ!", "تم إضافة الدور بنجاح.", "success");
                table.draw();
            },
            error: function(xhr) {
                var errors = xhr.responseJSON.errors;
                var errorMessage = 'حدث خطأ أثناء إضافة الدور:\n';
                if (errors) {
                    Object.keys(errors).forEach(function(key) {
                        errorMessage += errors[key][0] + '\n';
                    });
                }
                swal("خطأ!", errorMessage, "error");
            }
        });
    });

    // Handle Edit Role Form Submission
    $('#editRoleForm').on('submit', function(e) {
        e.preventDefault();

        $.ajax({
            url: $(this).attr('action'),
            type: 'PUT',
            data: $(this).serialize(),
            success: function(response) {
                closeModal('editRoleModal');
                swal("تم التحديث!", "تم تحديث الدور بنجاح.", "success");
                table.draw();
            },
            error: function(xhr) {
                var errors = xhr.responseJSON.errors;
                var errorMessage = 'حدث خطأ أثناء تحديث الدور:\n';
                if (errors) {
                    Object.keys(errors).forEach(function(key) {
                        errorMessage += errors[key][0] + '\n';
                    });
                }
                swal("خطأ!", errorMessage, "error");
            }
        });
    });

    // Handle Assign Permissions Form Submission
    $('#assignPermissionsForm').on('submit', function(e) {
        e.preventDefault();

        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                closeModal('assignPermissionsModal');
                swal("تم الحفظ!", "تم تحديث صلاحيات الدور بنجاح.", "success");
                table.draw();
            },
            error: function(xhr) {
                swal("خطأ!", "حدث خطأ أثناء تحديث الصلاحيات.", "error");
            }
        });
    });

    // Edit role
    window.editRole = function(id) {
        $.get("{{ url('admin/roles') }}/" + id, function(data) {
            $('#edit_name').val(data.name);
            $('#edit_guard_name').val(data.guard_name);
            $('#editRoleForm').attr('action', "{{ url('admin/roles') }}/" + id);
            
            // Load permissions for editing
            loadPermissionsForEdit(data.permissions);
            
            openModal('editRoleModal');
        });
    };

    // Load permissions for edit modal
    function loadPermissionsForEdit(rolePermissions) {
        var permissionsHtml = '';

        Object.keys(permissionsData).forEach(function(group) {
            var groupPermissions = permissions[group];
            permissionsHtml += '<div class="bg-gray-50 rounded-lg p-3 mb-3">';
            permissionsHtml += '<div class="flex items-center mb-3 pb-2 border-b border-gray-200">';
            permissionsHtml += '<input type="checkbox" class="group-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" data-group="' + group + '">';
            permissionsHtml += '<label class="mr-2 text-sm font-medium text-gray-900">' + group.charAt(0).toUpperCase() + group.slice(1) + '</label>';
            permissionsHtml += '</div>';
            permissionsHtml += '<div class="space-y-2">';

            groupPermissions.forEach(function(permission) {
                var isChecked = rolePermissions.some(p => p.name === permission.name);
                permissionsHtml += '<div class="flex items-center">';
                permissionsHtml += '<input class="permission-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" type="checkbox" ';
                permissionsHtml += 'name="permissions[]" value="' + permission.name + '" ';
                permissionsHtml += 'id="edit_perm_' + permission.id + '" data-group="' + group + '"';
                if (isChecked) permissionsHtml += ' checked';
                permissionsHtml += '>';
                permissionsHtml += '<label class="mr-2 text-sm text-gray-700" for="edit_perm_' + permission.id + '">';
                permissionsHtml += permission.name;
                permissionsHtml += '</label>';
                permissionsHtml += '</div>';
            });

            permissionsHtml += '</div>';
            permissionsHtml += '</div>';
        });

        $('.edit-permissions-container').html(permissionsHtml);
    }

    // Assign permissions to role
    window.assignPermissions = function(id) {
        $.get("{{ url('admin/roles') }}/" + id + "/permissions", function(data) {
            $('#assignPermissionsForm').attr('action', "{{ url('admin/roles') }}/" + id + "/permissions");

            var permissionsHtml = '';

            Object.keys(permissionsData).forEach(function(group) {
                var groupPermissions = permissionsData[group];
                permissionsHtml += '<div class="bg-gray-50 rounded-lg p-3 mb-3">';
                permissionsHtml += '<div class="flex items-center mb-3 pb-2 border-b border-gray-200">';
                permissionsHtml += '<input type="checkbox" class="group-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" data-group="' + group + '">';
                permissionsHtml += '<label class="mr-2 text-sm font-medium text-gray-900">' + group.charAt(0).toUpperCase() + group.slice(1) + '</label>';
                permissionsHtml += '</div>';
                permissionsHtml += '<div class="space-y-2">';

                groupPermissions.forEach(function(permission) {
                    var isChecked = data.rolePermissions.includes(permission.name);
                    permissionsHtml += '<div class="flex items-center">';
                    permissionsHtml += '<input class="permission-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" type="checkbox" ';
                    permissionsHtml += 'name="permissions[]" value="' + permission.name + '" ';
                    permissionsHtml += 'id="assign_perm_' + permission.id + '" data-group="' + group + '"';
                    if (isChecked) permissionsHtml += ' checked';
                    permissionsHtml += '>';
                    permissionsHtml += '<label class="mr-2 text-sm text-gray-700" for="assign_perm_' + permission.id + '">';
                    permissionsHtml += permission.name;
                    permissionsHtml += '</label>';
                    permissionsHtml += '</div>';
                });

                permissionsHtml += '</div>';
                permissionsHtml += '</div>';
            });

            $('.assign-permissions-container').html(permissionsHtml);

            openModal('assignPermissionsModal');
        });
    };

    // Delete role
    window.deleteRole = function(id) {
        swal({
            title: "هل أنت متأكد؟",
            text: "لن تتمكن من التراجع عن هذا الإجراء!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "نعم، احذف!",
            cancelButtonText: "إلغاء",
            closeOnConfirm: false,
            closeOnCancel: false
        }, function(isConfirm) {
            if (isConfirm) {
                $.ajax({
                    url: "{{ url('admin/roles') }}/" + id,
                    type: 'DELETE',
                    data: {
                        _token: "{{ csrf_token() }}"
                    },
                    success: function(response) {
                        swal("تم الحذف!", "تم حذف الدور بنجاح.", "success");
                        table.draw();
                    },
                    error: function(xhr) {
                        swal("خطأ!", "حدث خطأ أثناء حذف الدور.", "error");
                    }
                });
            } else {
                swal("تم الإلغاء", "لم يتم حذف الدور.", "error");
            }
        });
    };

    // View role users
    window.viewRoleUsers = function(id) {
        window.location.href = "{{ url('admin/roles') }}/" + id + "/users";
    };
});
</script>
@endpush