<?php

namespace App\Helpers;

class BranchHelper
{
    /**
     * Get the current user's branch ID
     */
    public static function getCurrentBranchId(): ?int
    {
        $user = auth()->user();
        return $user ? $user->branch_id : null;
    }

    /**
     * Get the current user's tenant ID
     */
    public static function getCurrentTenantId(): ?int
    {
        $user = auth()->user();
        return $user ? $user->tenant_id : null;
    }

    /**
     * Check if the current user has a branch assigned
     */
    public static function hasCurrentBranch(): bool
    {
        return self::getCurrentBranchId() !== null;
    }

    /**
     * Check if the current user has a tenant assigned
     */
    public static function hasCurrentTenant(): bool
    {
        return self::getCurrentTenantId() !== null;
    }

    /**
     * Validate that the current user has both branch and tenant
     */
    public static function validateCurrentUserAssignment(): array
    {
        $user = auth()->user();
        
        if (!$user) {
            return ['valid' => false, 'message' => 'User not authenticated'];
        }

        if (!$user->branch_id) {
            return ['valid' => false, 'message' => 'User is not assigned to any branch'];
        }

        if (!$user->tenant_id) {
            return ['valid' => false, 'message' => 'User is not assigned to any tenant'];
        }

        return ['valid' => true, 'branch_id' => $user->branch_id, 'tenant_id' => $user->tenant_id];
    }

    /**
     * Add branch and tenant IDs to data array
     */
    public static function addCurrentBranchAndTenant(array $data): array
    {
        $user = auth()->user();
        
        if ($user && $user->branch_id) {
            $data['branch_id'] = $user->branch_id;
        }
        
        if ($user && $user->tenant_id) {
            $data['tenant_id'] = $user->tenant_id;
        }
        
        return $data;
    }

    /**
     * Add branch and tenant IDs to data array (alias for consistency)
     */
    public static function addBranchAndTenantIds(array $data): array
    {
        return self::addCurrentBranchAndTenant($data);
    }

    /**
     * Generate a unique code with prefix
     */
    public static function generateCode(string $prefix = 'ITEM'): string
    {
        return $prefix . '-' . time() . '-' . rand(1000, 9999);
    }
}