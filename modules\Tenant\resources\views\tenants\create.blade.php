@extends('layouts.master')

@section('title', 'إضافة مستأجر جديد')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h1 class="text-2xl font-bold text-gray-900 flex items-center gap-3">
                    <i class="fas fa-user-plus text-blue-600"></i>
                    إضافة مستأجر جديد
                </h1>
                <a href="{{ route('tenants.index') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                    <i class="fas fa-arrow-left text-sm"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">معلومات المستأجر</h2>
        </div>
        
        <form method="POST" action="{{ route('tenants.store') }}" class="p-6">
            @csrf
            
            <!-- Basic Information -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                    <i class="fas fa-info-circle text-blue-600"></i>
                    المعلومات الأساسية
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                            اسم المستأجر <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="name" id="name" value="{{ old('name') }}" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('name') border-red-500 @enderror">
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label for="code" class="block text-sm font-medium text-gray-700 mb-2">
                            الكود
                        </label>
                        <input type="text" name="code" id="code" value="{{ old('code') }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('code') border-red-500 @enderror">
                        @error('code')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label for="business_type" class="block text-sm font-medium text-gray-700 mb-2">
                            نوع النشاط <span class="text-red-500">*</span>
                        </label>
                        <select name="business_type" id="business_type" required 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('business_type') border-red-500 @enderror">
                            <option value="">اختر نوع النشاط</option>
                            <option value="restaurant" {{ old('business_type') == 'restaurant' ? 'selected' : '' }}>مطعم</option>
                            <option value="cafe" {{ old('business_type') == 'cafe' ? 'selected' : '' }}>مقهى</option>
                            <option value="bakery" {{ old('business_type') == 'bakery' ? 'selected' : '' }}>مخبز</option>
                            <option value="fast_food" {{ old('business_type') == 'fast_food' ? 'selected' : '' }}>وجبات سريعة</option>
                            <option value="catering" {{ old('business_type') == 'catering' ? 'selected' : '' }}>تموين</option>
                        </select>
                        @error('business_type')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label for="country_id" class="block text-sm font-medium text-gray-700 mb-2">
                            الدولة <span class="text-red-500">*</span>
                        </label>
                        <select name="country_id" id="country_id" required 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('country_id') border-red-500 @enderror">
                            <option value="">اختر الدولة</option>
                            @foreach($countries as $country)
                                <option value="{{ $country->id }}" {{ old('country_id') == $country->id ? 'selected' : '' }}>
                                    {{ $country->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('country_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>
            
            <!-- Contact Information -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                    <i class="fas fa-address-book text-blue-600"></i>
                    معلومات الاتصال
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="primary_contact_name" class="block text-sm font-medium text-gray-700 mb-2">
                            اسم جهة الاتصال <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="primary_contact_name" id="primary_contact_name" value="{{ old('primary_contact_name') }}" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('primary_contact_name') border-red-500 @enderror">
                        @error('primary_contact_name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label for="contact_email" class="block text-sm font-medium text-gray-700 mb-2">
                            البريد الإلكتروني <span class="text-red-500">*</span>
                        </label>
                        <input type="email" name="contact_email" id="contact_email" value="{{ old('contact_email') }}" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('contact_email') border-red-500 @enderror">
                        @error('contact_email')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label for="contact_phone" class="block text-sm font-medium text-gray-700 mb-2">
                            رقم الهاتف <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="contact_phone" id="contact_phone" value="{{ old('contact_phone') }}" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('contact_phone') border-red-500 @enderror">
                        @error('contact_phone')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label for="website_url" class="block text-sm font-medium text-gray-700 mb-2">
                            الموقع الإلكتروني
                        </label>
                        <input type="url" name="website_url" id="website_url" value="{{ old('website_url') }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('website_url') border-red-500 @enderror">
                        @error('website_url')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
                
                <div class="mt-6">
                    <label for="business_address" class="block text-sm font-medium text-gray-700 mb-2">
                        عنوان النشاط <span class="text-red-500">*</span>
                    </label>
                    <textarea name="business_address" id="business_address" rows="3" required 
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('business_address') border-red-500 @enderror">{{ old('business_address') }}</textarea>
                    @error('business_address')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>
            
            <!-- Business Information -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                    <i class="fas fa-building text-blue-600"></i>
                    معلومات النشاط
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="tax_number" class="block text-sm font-medium text-gray-700 mb-2">
                            الرقم الضريبي
                        </label>
                        <input type="text" name="tax_number" id="tax_number" value="{{ old('tax_number') }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('tax_number') border-red-500 @enderror">
                        @error('tax_number')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label for="business_license" class="block text-sm font-medium text-gray-700 mb-2">
                            رخصة النشاط
                        </label>
                        <input type="text" name="business_license" id="business_license" value="{{ old('business_license') }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('business_license') border-red-500 @enderror">
                        @error('business_license')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>
            
            <!-- System Settings -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                    <i class="fas fa-cog text-blue-600"></i>
                    إعدادات النظام
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label for="timezone" class="block text-sm font-medium text-gray-700 mb-2">
                            المنطقة الزمنية <span class="text-red-500">*</span>
                        </label>
                        <select name="timezone" id="timezone" required 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('timezone') border-red-500 @enderror">
                            <option value="Asia/Riyadh" {{ old('timezone') == 'Asia/Riyadh' ? 'selected' : '' }}>Asia/Riyadh</option>
                            <option value="Asia/Dubai" {{ old('timezone') == 'Asia/Dubai' ? 'selected' : '' }}>Asia/Dubai</option>
                            <option value="Asia/Kuwait" {{ old('timezone') == 'Asia/Kuwait' ? 'selected' : '' }}>Asia/Kuwait</option>
                            <option value="Asia/Qatar" {{ old('timezone') == 'Asia/Qatar' ? 'selected' : '' }}>Asia/Qatar</option>
                            <option value="Asia/Bahrain" {{ old('timezone') == 'Asia/Bahrain' ? 'selected' : '' }}>Asia/Bahrain</option>
                        </select>
                        @error('timezone')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label for="currency_code" class="block text-sm font-medium text-gray-700 mb-2">
                            العملة <span class="text-red-500">*</span>
                        </label>
                        <select name="currency_code" id="currency_code" required 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('currency_code') border-red-500 @enderror">
                            <option value="SAR" {{ old('currency_code') == 'SAR' ? 'selected' : '' }}>ريال سعودي (SAR)</option>
                            <option value="AED" {{ old('currency_code') == 'AED' ? 'selected' : '' }}>درهم إماراتي (AED)</option>
                            <option value="KWD" {{ old('currency_code') == 'KWD' ? 'selected' : '' }}>دينار كويتي (KWD)</option>
                            <option value="QAR" {{ old('currency_code') == 'QAR' ? 'selected' : '' }}>ريال قطري (QAR)</option>
                            <option value="BHD" {{ old('currency_code') == 'BHD' ? 'selected' : '' }}>دينار بحريني (BHD)</option>
                            <option value="USD" {{ old('currency_code') == 'USD' ? 'selected' : '' }}>دولار أمريكي (USD)</option>
                        </select>
                        @error('currency_code')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label for="language_code" class="block text-sm font-medium text-gray-700 mb-2">
                            اللغة <span class="text-red-500">*</span>
                        </label>
                        <select name="language_code" id="language_code" required 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('language_code') border-red-500 @enderror">
                            <option value="ar" {{ old('language_code') == 'ar' ? 'selected' : '' }}>العربية</option>
                            <option value="en" {{ old('language_code') == 'en' ? 'selected' : '' }}>English</option>
                        </select>
                        @error('language_code')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>
            
            <!-- Submit Buttons -->
            <div class="flex justify-end gap-3 pt-6 border-t border-gray-200">
                <a href="{{ route('tenants.index') }}" class="px-6 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                    إلغاء
                </a>
                <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2">
                    <i class="fas fa-save text-sm"></i>
                    إضافة المستأجر
                </button>
            </div>
        </form>
    </div>
</div>

@if(session('success'))
    <script>
        Swal.fire({
            icon: 'success',
            title: 'تم بنجاح!',
            text: '{{ session('success') }}',
            confirmButtonText: 'موافق'
        });
    </script>
@endif

@if(session('error'))
    <script>
        Swal.fire({
            icon: 'error',
            title: 'خطأ!',
            text: '{{ session('error') }}',
            confirmButtonText: 'موافق'
        });
    </script>
@endif
@endsection