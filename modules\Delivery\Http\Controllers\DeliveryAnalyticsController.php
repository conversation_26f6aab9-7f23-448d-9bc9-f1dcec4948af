<?php

namespace Modules\Delivery\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\Delivery\Services\DeliveryAnalyticsService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class DeliveryAnalyticsController extends Controller
{
    public function __construct(
        private DeliveryAnalyticsService $analyticsService
    ) {}

    /**
     * Get tenant ID from authenticated user
     */
    private function getTenantId(): int
    {
        return Auth::user()->tenant_id;
    }

    /**
     * Get delivery overview metrics
     */
    public function overview(Request $request): JsonResponse
    {
        $request->validate([
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
            'branch_id' => 'nullable|exists:branches,id',
        ]);

        try {
            $overview = $this->analyticsService->getOverviewMetrics(
                $request->date_from,
                $request->date_to,
                $request->branch_id
            );

            return response()->json([
                'success' => true,
                'data' => $overview,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve overview metrics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get delivery performance metrics
     */
    public function performance(Request $request): JsonResponse
    {
        $request->validate([
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
            'branch_id' => 'nullable|exists:branches,id',
            'personnel_id' => 'nullable|exists:delivery_personnel,id',
        ]);

        try {
            $performance = $this->analyticsService->getPerformanceMetrics(
                $request->date_from,
                $request->date_to,
                $request->branch_id,
                $request->personnel_id
            );

            return response()->json([
                'success' => true,
                'data' => $performance,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve performance metrics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get personnel analytics
     */
    public function personnel(Request $request): JsonResponse
    {
        $request->validate([
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
            'branch_id' => 'nullable|exists:branches,id',
        ]);

        try {
            $personnelMetrics = $this->analyticsService->getPersonnelMetrics(
                $request->date_from,
                $request->date_to,
                $request->branch_id
            );

            return response()->json([
                'success' => true,
                'data' => $personnelMetrics,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve personnel metrics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get zone analytics
     */
    public function zones(Request $request): JsonResponse
    {
        $request->validate([
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
            'branch_id' => 'nullable|exists:branches,id',
        ]);

        try {
            $zoneMetrics = $this->analyticsService->getZoneMetrics(
                $request->date_from,
                $request->date_to,
                $request->branch_id
            );

            return response()->json([
                'success' => true,
                'data' => $zoneMetrics,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve zone metrics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get daily trends
     */
    public function dailyTrends(Request $request): JsonResponse
    {
        $request->validate([
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
            'branch_id' => 'nullable|exists:branches,id',
        ]);

        try {
            $trends = $this->analyticsService->getDailyTrends(
                $request->date_from,
                $request->date_to,
                $request->branch_id
            );

            return response()->json([
                'success' => true,
                'data' => $trends,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve daily trends',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get hourly trends
     */
    public function hourlyTrends(Request $request): JsonResponse
    {
        $request->validate([
            'date' => 'nullable|date',
            'branch_id' => 'nullable|exists:branches,id',
        ]);

        try {
            $trends = $this->analyticsService->getHourlyTrends(
                $request->date ?? now()->toDateString(),
                $request->branch_id
            );

            return response()->json([
                'success' => true,
                'data' => $trends,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve hourly trends',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get weekly trends
     */
    public function weeklyTrends(Request $request): JsonResponse
    {
        $request->validate([
            'weeks' => 'nullable|integer|min:1|max:52',
            'branch_id' => 'nullable|exists:branches,id',
        ]);

        try {
            $weeks = $request->get('weeks', 12);
            $trends = $this->analyticsService->getWeeklyTrends($weeks, $request->branch_id);

            return response()->json([
                'success' => true,
                'data' => $trends,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve weekly trends',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get real-time dashboard data
     */
    public function dashboard(Request $request): JsonResponse
    {
        $request->validate([
            'branch_id' => 'nullable|exists:branches,id',
        ]);

        try {
            $dashboardData = $this->analyticsService->getRealTimeDashboard(
                $request->branch_id
            );

            return response()->json([
                'success' => true,
                'data' => $dashboardData,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve dashboard data',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get delivery time analysis
     */
    public function deliveryTimeAnalysis(Request $request): JsonResponse
    {
        $request->validate([
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
            'branch_id' => 'nullable|exists:branches,id',
            'zone_id' => 'nullable|exists:delivery_zones,id',
        ]);

        try {
            $analysis = $this->analyticsService->getDeliveryTimeAnalysis(
                $request->date_from,
                $request->date_to,
                $request->branch_id,
                $request->zone_id
            );

            return response()->json([
                'success' => true,
                'data' => $analysis,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve delivery time analysis',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get customer satisfaction metrics
     */
    public function customerSatisfaction(Request $request): JsonResponse
    {
        $request->validate([
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
            'branch_id' => 'nullable|exists:branches,id',
        ]);

        try {
            $satisfaction = $this->analyticsService->getCustomerSatisfactionMetrics(
                $request->date_from,
                $request->date_to,
                $request->branch_id
            );

            return response()->json([
                'success' => true,
                'data' => $satisfaction,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve customer satisfaction metrics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Export analytics data
     */
    public function export(Request $request): JsonResponse
    {
        $request->validate([
            'type' => 'required|in:overview,performance,personnel,zones,trends',
            'format' => 'required|in:csv,excel,pdf',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
            'branch_id' => 'nullable|exists:branches,id',
        ]);

        try {
            $exportData = $this->analyticsService->exportAnalyticsData(
                $request->type,
                $request->format,
                $request->date_from,
                $request->date_to,
                $request->branch_id
            );

            return response()->json([
                'success' => true,
                'data' => $exportData,
                'message' => 'Export generated successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export analytics data',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}