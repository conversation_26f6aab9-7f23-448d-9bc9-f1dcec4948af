@extends('layouts.master')

@section('title', 'Notification Settings')

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
@endpush

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg shadow-lg p-6 mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                            <i class="fas fa-bell text-white text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <h1 class="text-2xl font-bold text-white">Notification Settings</h1>
                        <p class="text-indigo-100">Configure alerts, notifications, and communication preferences</p>
                    </div>
                </div>
            </div>
            <div class="mt-4 md:mt-0">
                <button class="px-4 py-2 bg-white text-indigo-600 rounded-lg hover:bg-indigo-50 transition-colors duration-200 font-medium">
                    <i class="fas fa-test-tube mr-2"></i>
                    Test Notifications
                </button>
            </div>
        </div>
    </div>

    <!-- Notification Status Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-envelope text-green-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Email Sent Today</p>
                    <p class="text-2xl font-bold text-gray-900">47</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-sms text-blue-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">SMS Sent Today</p>
                    <p class="text-2xl font-bold text-gray-900">23</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-mobile-alt text-yellow-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Push Notifications</p>
                    <p class="text-2xl font-bold text-gray-900">156</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Failed Deliveries</p>
                    <p class="text-2xl font-bold text-gray-900">2</p>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Email Notifications -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-envelope mr-2 text-indigo-600"></i>
                Email Notifications
            </h3>
            
            <form class="space-y-4">
                <div>
                    <label for="email_from_name" class="block text-sm font-medium text-gray-700 mb-2">
                        From Name
                    </label>
                    <input type="text" id="email_from_name" name="email_from_name" value="Restaurant POS System"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                </div>

                <div>
                    <label for="email_from_address" class="block text-sm font-medium text-gray-700 mb-2">
                        From Email Address
                    </label>
                    <input type="email" id="email_from_address" name="email_from_address" value="<EMAIL>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                </div>

                <div>
                    <label for="email_template" class="block text-sm font-medium text-gray-700 mb-2">
                        Email Template
                    </label>
                    <select id="email_template" name="email_template" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="modern" selected>Modern Template</option>
                        <option value="classic">Classic Template</option>
                        <option value="minimal">Minimal Template</option>
                        <option value="branded">Branded Template</option>
                    </select>
                </div>

                <div class="space-y-3">
                    <h4 class="text-sm font-medium text-gray-900">Email Notifications For:</h4>
                    
                    <div class="flex items-center justify-between">
                        <div>
                            <label for="email_new_orders" class="text-sm font-medium text-gray-700">New Orders</label>
                            <p class="text-sm text-gray-500">Send email when new order is placed</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="email_new_orders" name="email_new_orders" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="email_order_ready" class="text-sm font-medium text-gray-700">Order Ready</label>
                            <p class="text-sm text-gray-500">Notify customer when order is ready</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="email_order_ready" name="email_order_ready" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="email_payment_received" class="text-sm font-medium text-gray-700">Payment Received</label>
                            <p class="text-sm text-gray-500">Send receipt via email</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="email_payment_received" name="email_payment_received" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="email_daily_reports" class="text-sm font-medium text-gray-700">Daily Reports</label>
                            <p class="text-sm text-gray-500">Send daily sales summary</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="email_daily_reports" name="email_daily_reports" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="email_low_stock" class="text-sm font-medium text-gray-700">Low Stock Alerts</label>
                            <p class="text-sm text-gray-500">Alert when inventory is low</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="email_low_stock" name="email_low_stock" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                        </label>
                    </div>
                </div>

                <div class="pt-4">
                    <button type="submit" class="w-full px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-lg hover:bg-indigo-700">
                        <i class="fas fa-save mr-2"></i>
                        Save Email Settings
                    </button>
                </div>
            </form>
        </div>

        <!-- SMS Notifications -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-sms mr-2 text-blue-600"></i>
                SMS Notifications
            </h3>
            
            <form class="space-y-4">
                <div>
                    <label for="sms_provider" class="block text-sm font-medium text-gray-700 mb-2">
                        SMS Provider
                    </label>
                    <select id="sms_provider" name="sms_provider" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="twilio" selected>Twilio</option>
                        <option value="nexmo">Nexmo</option>
                        <option value="aws_sns">AWS SNS</option>
                        <option value="local_gateway">Local Gateway</option>
                    </select>
                </div>

                <div>
                    <label for="sms_sender_id" class="block text-sm font-medium text-gray-700 mb-2">
                        Sender ID
                    </label>
                    <input type="text" id="sms_sender_id" name="sms_sender_id" value="RESTAURANT" maxlength="11"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <p class="text-xs text-gray-500 mt-1">Maximum 11 characters</p>
                </div>

                <div>
                    <label for="sms_country_code" class="block text-sm font-medium text-gray-700 mb-2">
                        Default Country Code
                    </label>
                    <select id="sms_country_code" name="sms_country_code" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="+966" selected>+966 (Saudi Arabia)</option>
                        <option value="+971">+971 (UAE)</option>
                        <option value="+1">+1 (USA/Canada)</option>
                        <option value="+44">+44 (UK)</option>
                        <option value="+91">+91 (India)</option>
                    </select>
                </div>

                <div class="space-y-3">
                    <h4 class="text-sm font-medium text-gray-900">SMS Notifications For:</h4>
                    
                    <div class="flex items-center justify-between">
                        <div>
                            <label for="sms_order_confirmation" class="text-sm font-medium text-gray-700">Order Confirmation</label>
                            <p class="text-sm text-gray-500">Confirm order placement</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="sms_order_confirmation" name="sms_order_confirmation" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="sms_order_ready" class="text-sm font-medium text-gray-700">Order Ready</label>
                            <p class="text-sm text-gray-500">Notify when order is ready for pickup</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="sms_order_ready" name="sms_order_ready" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="sms_delivery_updates" class="text-sm font-medium text-gray-700">Delivery Updates</label>
                            <p class="text-sm text-gray-500">Send delivery status updates</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="sms_delivery_updates" name="sms_delivery_updates" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="sms_promotional" class="text-sm font-medium text-gray-700">Promotional Messages</label>
                            <p class="text-sm text-gray-500">Send offers and promotions</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="sms_promotional" name="sms_promotional" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                </div>

                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 class="text-sm font-medium text-blue-900 mb-2">SMS Credit Balance</h4>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-blue-800">Remaining Credits:</span>
                        <span class="text-lg font-bold text-blue-900">2,847</span>
                    </div>
                    <button class="mt-2 w-full px-3 py-2 text-sm font-medium text-blue-700 bg-blue-100 border border-blue-300 rounded-lg hover:bg-blue-200">
                        Purchase More Credits
                    </button>
                </div>

                <div class="pt-4">
                    <button type="submit" class="w-full px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700">
                        <i class="fas fa-save mr-2"></i>
                        Save SMS Settings
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Push Notifications & System Alerts -->
    <div class="mt-6 grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Push Notifications -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-mobile-alt mr-2 text-green-600"></i>
                Push Notifications
            </h3>
            
            <form class="space-y-4">
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div>
                            <label for="push_new_orders" class="text-sm font-medium text-gray-700">New Orders</label>
                            <p class="text-sm text-gray-500">Push notification for new orders</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="push_new_orders" name="push_new_orders" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="push_kitchen_alerts" class="text-sm font-medium text-gray-700">Kitchen Alerts</label>
                            <p class="text-sm text-gray-500">Alert kitchen staff of urgent orders</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="push_kitchen_alerts" name="push_kitchen_alerts" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="push_payment_alerts" class="text-sm font-medium text-gray-700">Payment Alerts</label>
                            <p class="text-sm text-gray-500">Notify of payment confirmations</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="push_payment_alerts" name="push_payment_alerts" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="push_system_updates" class="text-sm font-medium text-gray-700">System Updates</label>
                            <p class="text-sm text-gray-500">Notify of system maintenance</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="push_system_updates" name="push_system_updates" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                        </label>
                    </div>
                </div>

                <div>
                    <label for="push_sound" class="block text-sm font-medium text-gray-700 mb-2">
                        Notification Sound
                    </label>
                    <select id="push_sound" name="push_sound" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                        <option value="default" selected>Default</option>
                        <option value="chime">Chime</option>
                        <option value="bell">Bell</option>
                        <option value="ding">Ding</option>
                        <option value="silent">Silent</option>
                    </select>
                </div>

                <div>
                    <label for="push_quiet_hours" class="block text-sm font-medium text-gray-700 mb-2">
                        Quiet Hours
                    </label>
                    <div class="grid grid-cols-2 gap-3">
                        <div>
                            <label class="text-xs text-gray-500">From</label>
                            <input type="time" id="quiet_start" name="quiet_start" value="22:00"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                        </div>
                        <div>
                            <label class="text-xs text-gray-500">To</label>
                            <input type="time" id="quiet_end" name="quiet_end" value="08:00"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                        </div>
                    </div>
                </div>

                <div class="pt-4">
                    <button type="submit" class="w-full px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-lg hover:bg-green-700">
                        <i class="fas fa-save mr-2"></i>
                        Save Push Settings
                    </button>
                </div>
            </form>
        </div>

        <!-- System Alerts -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-exclamation-triangle mr-2 text-red-600"></i>
                System Alerts
            </h3>
            
            <form class="space-y-4">
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div>
                            <label for="alert_system_errors" class="text-sm font-medium text-gray-700">System Errors</label>
                            <p class="text-sm text-gray-500">Alert on critical system errors</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="alert_system_errors" name="alert_system_errors" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="alert_low_stock" class="text-sm font-medium text-gray-700">Low Stock</label>
                            <p class="text-sm text-gray-500">Alert when inventory is running low</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="alert_low_stock" name="alert_low_stock" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="alert_failed_payments" class="text-sm font-medium text-gray-700">Failed Payments</label>
                            <p class="text-sm text-gray-500">Alert on payment processing failures</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="alert_failed_payments" name="alert_failed_payments" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="alert_security_issues" class="text-sm font-medium text-gray-700">Security Issues</label>
                            <p class="text-sm text-gray-500">Alert on security breaches</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="alert_security_issues" name="alert_security_issues" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                        </label>
                    </div>
                </div>

                <div>
                    <label for="alert_recipients" class="block text-sm font-medium text-gray-700 mb-2">
                        Alert Recipients
                    </label>
                    <textarea id="alert_recipients" name="alert_recipients" rows="3" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                              placeholder="<EMAIL>, <EMAIL>"><EMAIL>
<EMAIL></textarea>
                    <p class="text-xs text-gray-500 mt-1">Enter email addresses separated by commas</p>
                </div>

                <div>
                    <label for="alert_escalation_time" class="block text-sm font-medium text-gray-700 mb-2">
                        Escalation Time (minutes)
                    </label>
                    <input type="number" id="alert_escalation_time" name="alert_escalation_time" value="15" min="1" max="60"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500">
                    <p class="text-xs text-gray-500 mt-1">Time before escalating unacknowledged alerts</p>
                </div>

                <div class="pt-4">
                    <button type="submit" class="w-full px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-lg hover:bg-red-700">
                        <i class="fas fa-save mr-2"></i>
                        Save Alert Settings
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Recent Notifications Log -->
    <div class="mt-6 bg-white rounded-lg shadow-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fas fa-history mr-2 text-gray-600"></i>
            Recent Notifications
        </h3>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Recipient</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Message</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2:45 PM</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 py-1 text-xs font-medium text-blue-800 bg-blue-100 rounded-full">Email</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><EMAIL></td>
                        <td class="px-6 py-4 text-sm text-gray-900">Order #ORD-1234 confirmation</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 py-1 text-xs font-medium text-green-800 bg-green-100 rounded-full">Delivered</span>
                        </td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2:30 PM</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 py-1 text-xs font-medium text-green-800 bg-green-100 rounded-full">SMS</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">+966501234567</td>
                        <td class="px-6 py-4 text-sm text-gray-900">Your order is ready for pickup</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 py-1 text-xs font-medium text-green-800 bg-green-100 rounded-full">Delivered</span>
                        </td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2:15 PM</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 py-1 text-xs font-medium text-yellow-800 bg-yellow-100 rounded-full">Push</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Kitchen Staff</td>
                        <td class="px-6 py-4 text-sm text-gray-900">New order received - Table 5</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 py-1 text-xs font-medium text-green-800 bg-green-100 rounded-full">Delivered</span>
                        </td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1:45 PM</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 py-1 text-xs font-medium text-red-800 bg-red-100 rounded-full">Alert</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><EMAIL></td>
                        <td class="px-6 py-4 text-sm text-gray-900">Low stock alert: Chicken Breast</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 py-1 text-xs font-medium text-red-800 bg-red-100 rounded-full">Failed</span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="mt-4 flex justify-between items-center">
            <p class="text-sm text-gray-700">Showing 4 of 47 notifications</p>
            <div class="flex space-x-2">
                <button class="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                    Previous
                </button>
                <button class="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                    Next
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Form submission handlers
    $('form').on('submit', function(e) {
        e.preventDefault();
        
        const formType = $(this).closest('.bg-white').find('h3').text().trim();
        
        Swal.fire({
            icon: 'success',
            title: 'Success!',
            text: `${formType} updated successfully.`,
            timer: 2000,
            showConfirmButton: false
        });
    });

    // Test notifications handler
    $('button:contains("Test Notifications")').on('click', function() {
        Swal.fire({
            title: 'Test Notifications',
            text: 'This will send test notifications to verify your settings.',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Send Test',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                Swal.fire({
                    title: 'Sending Test Notifications...',
                    text: 'Please wait while we send test notifications.',
                    icon: 'info',
                    showConfirmButton: false,
                    timer: 3000
                }).then(() => {
                    Swal.fire({
                        icon: 'success',
                        title: 'Test Complete',
                        text: 'Test notifications sent successfully. Check your devices.',
                        timer: 3000,
                        showConfirmButton: false
                    });
                });
            }
        });
    });

    // Purchase SMS credits handler
    $('button:contains("Purchase More Credits")').on('click', function() {
        Swal.fire({
            title: 'Purchase SMS Credits',
            text: 'This will redirect you to the billing page to purchase more SMS credits.',
            icon: 'info',
            confirmButtonText: 'Continue'
        });
    });

    // Sound preview for push notifications
    $('#push_sound').on('change', function() {
        const sound = $(this).val();
        if (sound !== 'silent') {
            // In a real implementation, you would play the selected sound
            console.log(`Playing sound: ${sound}`);
        }
    });

    // Validate quiet hours
    $('#quiet_start, #quiet_end').on('change', function() {
        const start = $('#quiet_start').val();
        const end = $('#quiet_end').val();
        
        if (start && end && start === end) {
            Swal.fire({
                icon: 'warning',
                title: 'Invalid Quiet Hours',
                text: 'Start and end times cannot be the same.',
                timer: 3000,
                showConfirmButton: false
            });
        }
    });

    // Email template preview
    $('#email_template').on('change', function() {
        const template = $(this).val();
        console.log(`Email template changed to: ${template}`);
        // In a real implementation, you might show a preview of the template
    });
});
</script>
@endpush