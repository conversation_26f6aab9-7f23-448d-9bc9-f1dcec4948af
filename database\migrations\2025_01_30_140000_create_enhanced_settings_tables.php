<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create printer_settings table
        Schema::create('printer_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('tenants')->onDelete('cascade');
            $table->foreignId('branch_id')->nullable()->constrained('branches')->onDelete('cascade');
            $table->string('name', 100)->comment('Printer name/identifier');
            $table->enum('type', ['receipt', 'kitchen', 'label', 'invoice'])->default('receipt');
            $table->enum('connection_type', ['usb', 'network', 'bluetooth', 'serial'])->default('usb');
            $table->json('connection_config')->comment('IP, port, device path, etc.');
            $table->string('paper_size', 20)->default('80mm')->comment('Paper width');
            $table->json('print_template')->nullable()->comment('Custom print template');
            $table->boolean('auto_cut')->default(true);
            $table->boolean('cash_drawer')->default(false);
            $table->integer('copies')->default(1);
            $table->boolean('is_default')->default(false);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['tenant_id', 'branch_id', 'type']);
        });

        // Create system_settings table for application-wide settings
        Schema::create('system_settings', function (Blueprint $table) {
            $table->id();
            $table->string('key', 100)->unique();
            $table->text('value')->nullable();
            $table->string('data_type', 20)->default('string');
            $table->text('description')->nullable();
            $table->boolean('is_public')->default(false);
            $table->timestamps();
        });

        // Create branch_settings table for branch-specific configurations
        Schema::create('branch_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('tenants')->onDelete('cascade');
            $table->foreignId('branch_id')->constrained('branches')->onDelete('cascade');
            $table->string('category', 50);
            $table->string('key', 100);
            $table->text('value')->nullable();
            $table->string('data_type', 20)->default('string');
            $table->text('description')->nullable();
            $table->timestamps();
            
            $table->unique(['tenant_id', 'branch_id', 'category', 'key']);
            $table->index(['tenant_id', 'branch_id', 'category']);
        });

        // Create payment_settings table
        Schema::create('payment_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('tenants')->onDelete('cascade');
            $table->foreignId('branch_id')->nullable()->constrained('branches')->onDelete('cascade');
            $table->string('payment_method', 50)->comment('cash, card, apple_pay, etc.');
            $table->boolean('is_enabled')->default(true);
            $table->json('gateway_config')->nullable()->comment('API keys, merchant IDs, etc.');
            $table->decimal('min_amount', 10, 2)->nullable();
            $table->decimal('max_amount', 10, 2)->nullable();
            $table->decimal('transaction_fee', 5, 2)->default(0);
            $table->enum('fee_type', ['fixed', 'percentage'])->default('fixed');
            $table->integer('display_order')->default(0);
            $table->timestamps();
            
            $table->unique(['tenant_id', 'branch_id', 'payment_method']);
        });

        // Create inventory_settings table
        Schema::create('inventory_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('tenants')->onDelete('cascade');
            $table->foreignId('branch_id')->nullable()->constrained('branches')->onDelete('cascade');
            $table->boolean('auto_deduct_stock')->default(true);
            $table->boolean('low_stock_alerts')->default(true);
            $table->integer('low_stock_threshold')->default(10);
            $table->boolean('negative_stock_allowed')->default(false);
            $table->enum('costing_method', ['fifo', 'lifo', 'average'])->default('fifo');
            $table->boolean('batch_tracking')->default(false);
            $table->boolean('expiry_tracking')->default(false);
            $table->integer('expiry_alert_days')->default(7);
            $table->timestamps();
            
            $table->unique(['tenant_id', 'branch_id']);
        });

        // Create user_settings table for user preferences
        Schema::create('user_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('tenant_id')->constrained('tenants')->onDelete('cascade');
            $table->string('category', 50);
            $table->string('key', 100);
            $table->text('value')->nullable();
            $table->string('data_type', 20)->default('string');
            $table->timestamps();
            
            $table->unique(['user_id', 'tenant_id', 'category', 'key']);
        });

        // Create report_settings table
        Schema::create('report_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('tenants')->onDelete('cascade');
            $table->foreignId('branch_id')->nullable()->constrained('branches')->onDelete('cascade');
            $table->string('report_type', 50);
            $table->json('default_parameters')->nullable();
            $table->enum('default_format', ['pdf', 'excel', 'csv'])->default('pdf');
            $table->boolean('auto_generate')->default(false);
            $table->string('schedule_frequency', 20)->nullable()->comment('daily, weekly, monthly');
            $table->time('schedule_time')->nullable();
            $table->json('email_recipients')->nullable();
            $table->timestamps();
            
            $table->unique(['tenant_id', 'branch_id', 'report_type']);
        });

        // Create restaurant_settings table
        Schema::create('restaurant_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('tenants')->onDelete('cascade');
            $table->foreignId('branch_id')->nullable()->constrained('branches')->onDelete('cascade');
            $table->boolean('table_service')->default(true);
            $table->boolean('takeaway_service')->default(true);
            $table->boolean('delivery_service')->default(false);
            $table->boolean('online_ordering')->default(false);
            $table->integer('table_reservation_duration')->default(120)->comment('minutes');
            $table->boolean('require_customer_info')->default(false);
            $table->boolean('allow_split_bills')->default(true);
            $table->boolean('auto_print_kitchen_orders')->default(true);
            $table->json('service_charges')->nullable();
            $table->json('operating_hours')->nullable();
            $table->timestamps();
            
            $table->unique(['tenant_id', 'branch_id']);
        });

        // Create security_settings table
        Schema::create('security_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('tenants')->onDelete('cascade');
            $table->integer('session_timeout')->default(30)->comment('minutes');
            $table->integer('max_login_attempts')->default(5);
            $table->integer('lockout_duration')->default(15)->comment('minutes');
            $table->boolean('require_password_change')->default(false);
            $table->integer('password_expiry_days')->nullable();
            $table->boolean('two_factor_auth')->default(false);
            $table->boolean('audit_logging')->default(true);
            $table->json('ip_whitelist')->nullable();
            $table->timestamps();
            
            $table->unique(['tenant_id']);
        });

        // Create backup_settings table
        Schema::create('backup_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('tenants')->onDelete('cascade');
            $table->boolean('auto_backup')->default(true);
            $table->enum('backup_frequency', ['daily', 'weekly', 'monthly'])->default('daily');
            $table->time('backup_time')->default('02:00:00');
            $table->integer('retention_days')->default(30);
            $table->json('backup_types')->comment('database, files, settings');
            $table->string('storage_location', 100)->default('local');
            $table->json('cloud_config')->nullable();
            $table->timestamps();
            
            $table->unique(['tenant_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('backup_settings');
        Schema::dropIfExists('security_settings');
        Schema::dropIfExists('restaurant_settings');
        Schema::dropIfExists('report_settings');
        Schema::dropIfExists('user_settings');
        Schema::dropIfExists('inventory_settings');
        Schema::dropIfExists('payment_settings');
        Schema::dropIfExists('branch_settings');
        Schema::dropIfExists('system_settings');
        Schema::dropIfExists('printer_settings');
    }
};
