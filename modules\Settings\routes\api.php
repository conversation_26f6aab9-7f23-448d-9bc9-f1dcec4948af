<?php

use Illuminate\Support\Facades\Route;
use Modules\Settings\Http\Controllers\SettingController;
use Modules\Settings\Http\Controllers\PrinterSettingController;
use Modules\Settings\Http\Controllers\PaymentSettingController;
use Modules\Settings\Http\Controllers\SecuritySettingController;
use Modules\Settings\Http\Controllers\BranchSettingController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:api')->group(function () {
    Route::prefix('settings')->group(function () {
        // General settings endpoints
        Route::get('/', [SettingController::class, 'index']);
        Route::get('/{key}', [SettingController::class, 'show']);
        Route::post('/', [SettingController::class, 'store']);
        Route::put('/{key}', [SettingController::class, 'update']);
        Route::delete('/{key}', [SettingController::class, 'destroy']);
        
        // Specialized category endpoints
        Route::get('/categories/available', [SettingController::class, 'getAvailableCategories']);
        Route::post('/categories/{category}/bulk-update', [SettingController::class, 'bulkUpdateCategory']);
        
        // KOT settings
        Route::get('/kot', [SettingController::class, 'getKotSettings']);
        Route::post('/kot', [SettingController::class, 'setKotSettings']);
        Route::delete('/kot/{key}', [SettingController::class, 'deleteKotSetting']);
        
        // Language settings
        Route::get('/language', [SettingController::class, 'getLanguageSettings']);
        Route::post('/language', [SettingController::class, 'setLanguageSettings']);
        Route::delete('/language/{key}', [SettingController::class, 'deleteLanguageSetting']);
        
        // Notification settings
        Route::get('/notification', [SettingController::class, 'getNotificationSettings']);
        Route::post('/notification', [SettingController::class, 'setNotificationSettings']);
        Route::delete('/notification/{key}', [SettingController::class, 'deleteNotificationSetting']);
        
        // Pusher settings
        Route::get('/pusher', [SettingController::class, 'getPusherSettings']);
        Route::post('/pusher', [SettingController::class, 'setPusherSettings']);
        Route::delete('/pusher/{key}', [SettingController::class, 'deletePusherSetting']);
        
        // Receipt settings
        Route::get('/receipt', [SettingController::class, 'getReceiptSettings']);
        Route::post('/receipt', [SettingController::class, 'setReceiptSettings']);
        Route::delete('/receipt/{key}', [SettingController::class, 'deleteReceiptSetting']);
        
        // Reservation settings
        Route::get('/reservation', [SettingController::class, 'getReservationSettings']);
        Route::post('/reservation', [SettingController::class, 'setReservationSettings']);
        Route::delete('/reservation/{key}', [SettingController::class, 'deleteReservationSetting']);
        
        // Branch delivery settings
        Route::get('/branch-delivery', [SettingController::class, 'getBranchDeliverySettings']);
        Route::post('/branch-delivery', [SettingController::class, 'setBranchDeliverySettings']);
        Route::delete('/branch-delivery/{key}', [SettingController::class, 'deleteBranchDeliverySetting']);
        
        // Email settings
        Route::get('/email', [SettingController::class, 'getEmailSettings']);
        Route::post('/email', [SettingController::class, 'setEmailSettings']);
        Route::delete('/email/{key}', [SettingController::class, 'deleteEmailSetting']);
        
        // File storage settings
        Route::get('/file-storage', [SettingController::class, 'getFileStorageSettings']);
        Route::post('/file-storage', [SettingController::class, 'setFileStorageSettings']);
        Route::delete('/file-storage/{key}', [SettingController::class, 'deleteFileStorageSetting']);
        
        // Front FAQ settings
        Route::get('/front-faq', [SettingController::class, 'getFrontFaqSettings']);
        Route::post('/front-faq', [SettingController::class, 'setFrontFaqSettings']);
        Route::delete('/front-faq/{key}', [SettingController::class, 'deleteFrontFaqSetting']);
        
        // Front review settings
        Route::get('/front-review', [SettingController::class, 'getFrontReviewSettings']);
        Route::post('/front-review', [SettingController::class, 'setFrontReviewSettings']);
        Route::delete('/front-review/{key}', [SettingController::class, 'deleteFrontReviewSetting']);

        // Printer Settings API Routes
        Route::prefix('printers')->group(function () {
            Route::get('/', [PrinterSettingController::class, 'index']);
            Route::post('/', [PrinterSettingController::class, 'store']);
            Route::get('/{id}', [PrinterSettingController::class, 'show']);
            Route::put('/{id}', [PrinterSettingController::class, 'update']);
            Route::delete('/{id}', [PrinterSettingController::class, 'destroy']);
            Route::post('/{id}/test-connection', [PrinterSettingController::class, 'testConnection']);
            Route::get('/{id}/default-template', [PrinterSettingController::class, 'getDefaultTemplate']);
            Route::get('/types/available', [PrinterSettingController::class, 'getAvailableTypes']);
            Route::get('/connections/types', [PrinterSettingController::class, 'getConnectionTypes']);
        });

        // Payment Settings API Routes
        Route::prefix('payments')->group(function () {
            Route::get('/', [PaymentSettingController::class, 'index']);
            Route::post('/', [PaymentSettingController::class, 'store']);
            Route::get('/{id}', [PaymentSettingController::class, 'show']);
            Route::put('/{id}', [PaymentSettingController::class, 'update']);
            Route::delete('/{id}', [PaymentSettingController::class, 'destroy']);
            Route::post('/{id}/calculate-fee', [PaymentSettingController::class, 'calculateFee']);
            Route::get('/methods/available', [PaymentSettingController::class, 'getAvailablePaymentMethods']);
            Route::post('/bulk-update', [PaymentSettingController::class, 'bulkUpdate']);
            Route::post('/initialize-defaults', [PaymentSettingController::class, 'initializeDefaults']);
        });

        // Security Settings API Routes
        Route::prefix('security')->group(function () {
            Route::get('/', [SecuritySettingController::class, 'index']);
            Route::get('/show', [SecuritySettingController::class, 'show']);
            Route::put('/', [SecuritySettingController::class, 'update']);
            Route::post('/ip-whitelist/add', [SecuritySettingController::class, 'addIpToWhitelist']);
            Route::post('/ip-whitelist/remove', [SecuritySettingController::class, 'removeIpFromWhitelist']);
            Route::post('/password/validate', [SecuritySettingController::class, 'validatePassword']);
            Route::get('/password/policy', [SecuritySettingController::class, 'getPasswordPolicy']);
            Route::post('/ip/check', [SecuritySettingController::class, 'checkIpWhitelist']);
            Route::get('/report', [SecuritySettingController::class, 'getSecurityReport']);
        });

        // Branch Settings API Routes
        Route::prefix('branches')->group(function () {
            Route::get('/', [BranchSettingController::class, 'index']);
            Route::post('/', [BranchSettingController::class, 'store']);
            Route::get('/{id}', [BranchSettingController::class, 'show']);
            Route::put('/{id}', [BranchSettingController::class, 'update']);
            Route::delete('/{id}', [BranchSettingController::class, 'destroy']);
            Route::get('/{branchId}/category/{category}', [BranchSettingController::class, 'getByCategory']);
            Route::post('/bulk-update', [BranchSettingController::class, 'bulkUpdate']);
            Route::get('/categories/available', [BranchSettingController::class, 'getCategories']);
            Route::post('/initialize-defaults', [BranchSettingController::class, 'initializeDefaults']);
            Route::get('/{branchId}/operating-hours', [BranchSettingController::class, 'getOperatingHours']);
            Route::put('/{branchId}/operating-hours', [BranchSettingController::class, 'updateOperatingHours']);
        });
    });
});
