<?php

namespace Modules\Customer\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class LoyaltyTransactionCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
            'meta' => [
                'total_transactions' => $this->total(),
                'total_points_earned' => $this->collection->where('type', 'earned')->sum('points'),
                'total_points_redeemed' => $this->collection->where('type', 'redeemed')->sum('points'),
                'net_points' => $this->collection->where('type', 'earned')->sum('points') - 
                              $this->collection->where('type', 'redeemed')->sum('points'),
                'earned_transactions' => $this->collection->where('type', 'earned')->count(),
                'redeemed_transactions' => $this->collection->where('type', 'redeemed')->count(),
                'expired_points' => $this->collection->where('expires_at', '<', now())->sum('points'),
            ],
        ];
    }

    /**
     * Get additional data that should be returned with the resource array.
     *
     * @return array<string, mixed>
     */
    public function with(Request $request): array
    {
        return [
            'success' => true,
            'message' => 'Loyalty transactions retrieved successfully',
        ];
    }
}