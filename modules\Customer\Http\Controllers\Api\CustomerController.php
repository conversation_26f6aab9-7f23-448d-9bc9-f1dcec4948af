<?php

namespace Modules\Customer\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use Modules\Customer\Services\CustomerService;
use Modules\Customer\Http\Requests\StoreCustomerRequest;
use Modules\Customer\Http\Requests\UpdateCustomerRequest;
use Modules\Customer\Http\Resources\CustomerResource;
use Modules\Customer\Http\Resources\CustomerCollection;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Helpers\ResponseHelper;

class CustomerController extends Controller
{
    protected CustomerService $customerService;

    public function __construct(CustomerService $customerService)
    {
        $this->customerService = $customerService;
    }

    /**
     * Display a listing of customers
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $filters = $request->only(['search', 'is_active', 'per_page']);
            $customers = $this->customerService->getPaginatedCustomers($filters);
            
            return ResponseHelper::success(
                'Customers retrieved successfully',
                new CustomerCollection($customers)
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to retrieve customers: ' . $e->getMessage()
            );
        }
    }

    /**
     * Store a newly created customer
     */
    public function store(StoreCustomerRequest $request): JsonResponse
    {
        try {
            $customer = $this->customerService->createCustomer($request->validated());
            
            return ResponseHelper::success(
                'Customer created successfully',
                new CustomerResource($customer),
                201
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to create customer: ' . $e->getMessage()
            );
        }
    }

    /**
     * Display the specified customer
     */
    public function show(Customer $customer): JsonResponse
    {
        try {
            return ResponseHelper::success(
                'Customer retrieved successfully',
                new CustomerResource($customer)
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to retrieve customer: ' . $e->getMessage()
            );
        }
    }

    /**
     * Update the specified customer
     */
    public function update(UpdateCustomerRequest $request, Customer $customer): JsonResponse
    {
        try {
            $customer = $this->customerService->updateCustomer($customer, $request->validated());
            
            return ResponseHelper::success(
                'Customer updated successfully',
                new CustomerResource($customer)
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to update customer: ' . $e->getMessage()
            );
        }
    }

    /**
     * Remove the specified customer
     */
    public function destroy(Customer $customer): JsonResponse
    {
        try {
            $this->customerService->deleteCustomer($customer);
            
            return ResponseHelper::success('Customer deleted successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to delete customer: ' . $e->getMessage()
            );
        }
    }

    /**
     * Search customers
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $query = $request->get('q', '');
            $customers = $this->customerService->searchCustomers($query);
            
            return ResponseHelper::success(
                'Customers found',
                CustomerResource::collection($customers)
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to search customers: ' . $e->getMessage()
            );
        }
    }

    /**
     * Activate customer
     */
    public function activate(Customer $customer): JsonResponse
    {
        try {
            $customer = $this->customerService->activateCustomer($customer);
            
            return ResponseHelper::success(
                'Customer activated successfully',
                new CustomerResource($customer)
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to activate customer: ' . $e->getMessage()
            );
        }
    }

    /**
     * Deactivate customer
     */
    public function deactivate(Customer $customer): JsonResponse
    {
        try {
            $customer = $this->customerService->deactivateCustomer($customer);
            
            return ResponseHelper::success(
                'Customer deactivated successfully',
                new CustomerResource($customer)
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to deactivate customer: ' . $e->getMessage()
            );
        }
    }

    /**
     * Update customer last visit
     */
    public function updateLastVisit(Customer $customer): JsonResponse
    {
        try {
            $customer = $this->customerService->updateLastVisit($customer);
            
            return ResponseHelper::success(
                'Last visit updated successfully',
                new CustomerResource($customer)
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to update last visit: ' . $e->getMessage()
            );
        }
    }

    /**
     * Get active customers
     */
    public function activeCustomers(): JsonResponse
    {
        try {
            $customers = $this->customerService->getActiveCustomers();
            
            return ResponseHelper::success(
                'Active customers retrieved',
                CustomerResource::collection($customers)
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to get active customers: ' . $e->getMessage()
            );
        }
    }

    /**
     * Get customer statistics
     */
    public function statistics(): JsonResponse
    {
        try {
            $statistics = $this->customerService->getStatistics();
            
            return ResponseHelper::success('Statistics retrieved successfully', $statistics);
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to get statistics: ' . $e->getMessage()
            );
        }
    }
}