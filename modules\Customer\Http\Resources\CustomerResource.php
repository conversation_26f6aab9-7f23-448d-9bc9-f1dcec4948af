<?php

namespace Modules\Customer\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CustomerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'full_name' => $this->first_name . ' ' . $this->last_name,
            'email' => $this->email,
            'phone' => $this->phone,
            'date_of_birth' => $this->date_of_birth?->format('Y-m-d'),
            'age' => $this->date_of_birth ? $this->date_of_birth->age : null,
            'gender' => $this->gender,
            'address' => $this->address,
            'city' => $this->city,
            'postal_code' => $this->postal_code,
            'loyalty_points' => (float) $this->loyalty_points,
            'total_orders' => $this->whenCounted('orders'),
            'total_spent' => $this->when(
                $this->relationLoaded('orders'),
                fn() => (float) $this->orders->sum('total_amount')
            ),
            'preferences' => $this->preferences,
            'notes' => $this->notes,
            'last_visit' => $this->last_visit?->format('Y-m-d H:i:s'),
            'is_active' => (bool) $this->is_active,
            'tenant_id' => $this->tenant_id,
            'branch_id' => $this->branch_id,
            'branch' => $this->whenLoaded('branch', function () {
                return [
                    'id' => $this->branch->id,
                    'name' => $this->branch->name,
                ];
            }),
            'recent_orders' => $this->whenLoaded('orders', function () {
                return $this->orders->take(5)->map(function ($order) {
                    return [
                        'id' => $order->id,
                        'order_number' => $order->order_number,
                        'total_amount' => (float) $order->total_amount,
                        'status' => $order->status,
                        'created_at' => $order->created_at->format('Y-m-d H:i:s'),
                    ];
                });
            }),
            'loyalty_transactions_count' => $this->whenCounted('loyaltyTransactions'),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
        ];
    }
}