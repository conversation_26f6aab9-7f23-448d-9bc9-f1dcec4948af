<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class Reservation extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'branch_id',
        'customer_id',
        'table_id',
        'area_id',
        'reservation_status_id',
        'reservation_number',
        'customer_name',
        'customer_phone',
        'customer_email',
        'party_size',
        'reservation_datetime',
        'duration_minutes',
        'special_requests',
        'notes',
        'created_by',
        'seated_at',
        'completed_at',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'reservation_datetime' => 'datetime',
            'seated_at' => 'datetime',
            'completed_at' => 'datetime',
            'party_size' => 'integer',
            'duration_minutes' => 'integer',
        ];
    }

    /**
     * The attributes that should be appended to arrays.
     *
     * @var array
     */
    protected $appends = [
        'estimated_end_time',
        'is_overdue',
        'can_cancel',
        'can_seat',
        'formatted_duration',
    ];

    // Relationships
    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function table()
    {
        return $this->belongsTo(Table::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function area()
    {
        return $this->belongsTo(Area::class);
    }

    public function reservationStatus()
    {
        return $this->belongsTo(ReservationStatus::class);
    }

    // Scopes
    public function scopeForBranch(Builder $query, $branchId): Builder
    {
        return $query->where('branch_id', $branchId);
    }

    public function scopeByStatus(Builder $query, $statusId): Builder
    {
        return $query->where('reservation_status_id', $statusId);
    }

    public function scopeForDate(Builder $query, $date): Builder
    {
        return $query->whereDate('reservation_datetime', $date);
    }

    public function scopeDateRange(Builder $query, $startDate, $endDate): Builder
    {
        return $query->whereBetween('reservation_datetime', [$startDate, $endDate]);
    }

    public function scopeActive(Builder $query): Builder
    {
        return $query->whereHas('reservationStatus', function ($q) {
            $q->whereIn('name', ['pending', 'confirmed', 'seated']);
        });
    }

    public function scopeUpcoming(Builder $query): Builder
    {
        return $query->where('reservation_datetime', '>', now());
    }

    public function scopeToday(Builder $query): Builder
    {
        return $query->whereDate('reservation_datetime', today());
    }

    // Computed Attributes
    public function getEstimatedEndTimeAttribute()
    {
        if (!$this->reservation_datetime || !$this->duration_minutes) {
            return null;
        }

        return $this->reservation_datetime->addMinutes($this->duration_minutes);
    }

    public function getIsOverdueAttribute(): bool
    {
        if (!$this->estimated_end_time || $this->completed_at) {
            return false;
        }

        return now()->gt($this->estimated_end_time);
    }

    public function getCanCancelAttribute(): bool
    {
        if (!$this->reservationStatus) {
            return false;
        }

        $cancellableStatuses = ['pending', 'confirmed'];
        return in_array($this->reservationStatus->name, $cancellableStatuses);
    }

    public function getCanSeatAttribute(): bool
    {
        if (!$this->reservationStatus) {
            return false;
        }

        return $this->reservationStatus->name === 'confirmed';
    }

    public function getFormattedDurationAttribute(): string
    {
        if (!$this->duration_minutes) {
            return '0 minutes';
        }

        $hours = intval($this->duration_minutes / 60);
        $minutes = $this->duration_minutes % 60;

        if ($hours > 0 && $minutes > 0) {
            return "{$hours}h {$minutes}m";
        } elseif ($hours > 0) {
            return "{$hours}h";
        } else {
            return "{$minutes}m";
        }
    }

    // Helper Methods
    public function isActive(): bool
    {
        if (!$this->reservationStatus) {
            return false;
        }

        return in_array($this->reservationStatus->name, ['pending', 'confirmed', 'seated']);
    }

    public function canBeModified(): bool
    {
        if (!$this->reservationStatus) {
            return false;
        }

        $modifiableStatuses = ['pending', 'confirmed'];
        return in_array($this->reservationStatus->name, $modifiableStatuses);
    }

    public function markAsSeated(): bool
    {
        if (!$this->can_seat) {
            return false;
        }

        $seatedStatus = ReservationStatus::where('name', 'seated')->first();
        if (!$seatedStatus) {
            return false;
        }

        $this->update([
            'reservation_status_id' => $seatedStatus->id,
            'seated_at' => now(),
        ]);

        return true;
    }

    public function markAsCompleted(): bool
    {
        $completedStatus = ReservationStatus::where('name', 'completed')->first();
        if (!$completedStatus) {
            return false;
        }

        $this->update([
            'reservation_status_id' => $completedStatus->id,
            'completed_at' => now(),
        ]);

        return true;
    }

    public function markAsCancelled(): bool
    {
        if (!$this->can_cancel) {
            return false;
        }

        $cancelledStatus = ReservationStatus::where('name', 'cancelled')->first();
        if (!$cancelledStatus) {
            return false;
        }

        $this->update([
            'reservation_status_id' => $cancelledStatus->id,
        ]);

        return true;
    }

    public function markAsNoShow(): bool
    {
        $noShowStatus = ReservationStatus::where('name', 'no_show')->first();
        if (!$noShowStatus) {
            return false;
        }

        $this->update([
            'reservation_status_id' => $noShowStatus->id,
        ]);

        return true;
    }
}