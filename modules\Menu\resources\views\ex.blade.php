 public function index()
    {
        $business_id = request()->session()->get('user.business_id');

        if (! (auth()->user()->can('superadmin') || ($this->moduleUtil->hasThePermissionInSubscription($business_id, 'repair_module') && (auth()->user()->can('repair.view') || auth()->user()->can('repair.view_own'))))) {
            abort(403, 'Unauthorized action.');
        }

        $is_admin = $this->commonUtil->is_admin(auth()->user(), $business_id);

        if (request()->ajax()) {
            $sells = Transaction::leftJoin('contacts', 'transactions.contact_id', '=', 'contacts.id')
                ->leftJoin('transaction_payments as tp', 'transactions.id', '=', 'tp.transaction_id')
                ->join(
                    'business_locations AS bl',
                    'transactions.location_id',
                    '=',
                    'bl.id'
                )
      
                ->leftJoin(
                    'users as ss',
                    'ss.id',
                    '=',
                    'transactions.res_waiter_id'
                )
                ->leftJoin(
                    'warranties as rw',
                    'rw.id',
                    '=',
                    'transactions.repair_warranty_id'
                )
                ->leftJoin(
                    'transactions AS SR',
                    'transactions.id',
                    '=',
                    'SR.return_parent_id'
                )
                ->leftJoin(
                    'repair_job_sheets AS rjs',
                    'transactions.repair_job_sheet_id',
                    '=',
                    'rjs.id'
                    )
                    ->join(
                        'users',
                        'rjs.created_by',
                        '=',
                        'users.id'
                        )
                                ->leftJoin('repair_statuses as rs', 'rjs.status_id', '=', 'rs.id')

                                // Add join to contact_device table to get VIN number
                                ->leftJoin('bookings', 'rjs.booking_id', '=', 'bookings.id')
                                ->leftJoin('contact_device', 'bookings.device_id', '=', 'contact_device.id')
                                ->leftJoin(
                                    'categories AS b',
                                    'contact_device.device_id',
                                    '=',
                                    'b.id'
                                )
                                ->leftJoin(
                       'repair_device_models as rdm',
                       'rdm.id',
                       '=',
                       'contact_device.models_id'
                   )
                   
                   ->where('transactions.business_id', $business_id)
              
                ->select(
                    'transactions.id',
                    'transactions.Exit_permission',
                    'transactions.transaction_date',
                    'transactions.is_direct_sale',
                    'transactions.invoice_no',
                    'transactions.status as transactions_status',
                    'contacts.name',
                    'transactions.payment_status',
                    'transactions.final_total',
                    'transactions.tax_amount',
                    'transactions.discount_amount',
                    'transactions.discount_type',
                    'transactions.total_before_tax',
                    'transactions.repair_status_id',
                    'rs.name as repair_status',
                    'rs.color as status_color',
                    'rs.is_completed_status',
                    'transactions.repair_serial_no',
                    DB::raw('SUM(IF(tp.is_return = 1,-1*tp.amount,tp.amount)) as total_paid'),
                    'bl.name as business_location',
                    // DB::raw('CONCAT(COALESCE(ss.first_name, ""), COALESCE(ss.last_name, "")) as service_staff'),
                    'transactions.repair_completed_on',
                    'rw.name as warranty_name',
                    'rw.duration',
                    'rw.duration_type',
                    'transactions.repair_due_date',
                    DB::raw('COUNT(SR.id) as return_exists'),
                    DB::raw('(SELECT SUM(tp1.amount) FROM transaction_payments AS tp1 WHERE
                        tp1.transaction_id=SR.id ) as return_paid'),
                    DB::raw('COALESCE(SR.final_total, 0) as amount_return'),
                    'SR.id as return_transaction_id',
                    'rdm.name as device_model',
                    'b.name as brand',
                    'transactions.repair_updates_notif',
                    DB::raw("CONCAT(COALESCE(users.surname, ''),' ',COALESCE(users.first_name, ''),' ',COALESCE(users.last_name,'')) as added_by"),
                    'rjs.job_sheet_no as job_sheet_no',
                    'rjs.service_staff', // Select the service_staff JSON field
                    'rjs.id as job_sheet_id',
                    'rjs.due_date',
                    'contact_device.chassis_number as vin_number' // Add VIN number from contact_device table

                );

                $permitted_locations = auth()->user()->permitted_locations();
                if ($permitted_locations != 'all') {
                    $sells->whereIn('transactions.location_id', $permitted_locations);
            }

            if (! auth()->user()->can('repair.view') && auth()->user()->can('repair.view_own')) {
                $sells->where(function ($q) {
                    $q->where('transactions.created_by', auth()->user()->id)
                    ->orWhere('transactions.res_waiter_id', auth()->user()->id);
                });
            }

            //Add condition for created_by,used in sales representative sales report
            if (request()->has('created_by')) {
                $created_by = request()->get('created_by');
                if (! empty($created_by)) {
                    $sells->where('transactions.created_by', $created_by);
                }
            }

            if (! empty(request()->input('payment_status'))) {
                $sells->where('transactions.payment_status', request()->input('payment_status'));
            }

            //Add condition for location,used in sales representative expense report
            if (request()->has('location_id')) {
                $location_id = request()->get('location_id');
                if (! empty($location_id)) {
                    $sells->where('transactions.location_id', $location_id);
                }
            }

            if (! empty(request()->customer_id)) {
                $customer_id = request()->customer_id;
                $sells->where('contacts.id', $customer_id);
            }
            if (! empty(request()->start_date) && ! empty(request()->end_date)) {
                $start = request()->start_date;
                $end = request()->end_date;

                $sells->whereDate('transactions.transaction_date', '>=', $start)
                ->whereDate('transactions.transaction_date', '<=', $end);
            }

            // if (! empty(request()->service_staff_id)) {
            //     $sells->where('transactions.res_waiter_id', request()->service_staff_id);
            // }

            if (! empty(request()->repair_status_id)) {
                $sells->where('transactions.repair_status_id', request()->repair_status_id);
            }

            //filter out mark as completed status
            // $sells->where('rs.is_completed_status', request()->get('is_completed_status'));
                // Check if we need to filter by completed or pending repairs
                // Check if we need to filter by completed or pending repairs
                if (request()->get('is_completed_status')) {
                    $sells->where('transactions.status', 'final'); // Completed repairs
                } else {
                    $sells->where('transactions.status', 'under processing'); // Pending repairs
        }

        $sells->groupBy('transactions.id');

            // Order by created_at descending (newest first)
        $sells->orderBy('transactions.created_at', 'desc');

                $datatable = Datatables::of($sells)
                ->addColumn(
                        'action',
                        function ($row) {
                            $html = '<div class="btn-group">
                                        <button type="button" class="tw-dw-btn tw-dw-btn-xs tw-dw-btn-outline tw-dw-btn-info tw-w-max dropdown-toggle"
                                            data-toggle="dropdown" aria-expanded="false">'.
                                            __('messages.actions').
                                            '<span class="caret"></span><span class="sr-only">Toggle Dropdown
                                            </span>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-left" role="menu">';

                            if (auth()->user()->can('repair.view') || auth()->user()->can('direct_sell.access')) {
                                $html .= '<li><a href="#" data-href="'.action([\Modules\Repair\Http\Controllers\RepairController::class, 'show'], [$row->id]).'" class="btn-modal" data-container=".view_modal"><i class="fas fa-eye" aria-hidden="true"></i> '.__('messages.view').'</a></li>';
                            }

                            if (auth()->user()->can('repair.update')) {
                                $html .= '<li><a target="_blank" href="'.action([\App\Http\Controllers\SellController::class, 'edit'], [$row->id]).'"><i class="fas fa-edit"></i> '.__('messages.edit').'</a></li>';
                            }

                            if (auth()->user()->can('repair.delete')) {
                                $html .= '<li><a href="'.action([\App\Http\Controllers\SellPosController::class, 'destroy'], [$row->id]).'" class="delete-sale"><i class="fa fa-trash"></i> '.__('messages.delete').'</a></li>';
                            }

                            if (auth()->user()->can('repair.view') || auth()->user()->can('direct_sell.access')) {
                              $html .= '
                            <li>
                                <a href="' . route('sell.printCleanInvoice', ['transaction_id' => $row->id]) . '" class="btn btn-info no-print" target="_blank">
                                    <i class="fas fa-file-alt"></i> ' . __('messages.print') . '
                                </a>
                            </li>';
                            }
                            $html .= '<li class="divider"></li>';

                            if (auth()->user()->can('repair.create')) {
                                $html .= '<li><a href="'.action([\App\Http\Controllers\SellReturnController::class, 'add'], [$row->id]).'"><i class="fas fa-undo"></i> '.__('lang_v1.sell_return').'</a></li>';
                            }

                            if (auth()->user()->can('repair_status.update')) {
                                $html .= '<li><a data-href="'.action([\Modules\Repair\Http\Controllers\RepairController::class, 'editRepairStatus'], [$row->id]).'" class="edit_repair_status"><i class="fa fa-edit"></i> '.__('repair::lang.change_status').'</a></li>';
                            }

                            if ($row->payment_status != 'paid' && (auth()->user()->can('repair.create') || auth()->user()->can('direct_sell.access'))) {
                                $html .= '<li><a href="'.action([\App\Http\Controllers\TransactionPaymentController::class, 'addPayment'], [$row->id]).'" class="add_payment_modal"><i class="fas fa-money-bill-alt"></i> '.__('purchase.add_payment').'</a></li>';
                            }

                            $html .= '<li><a href="'.action([\App\Http\Controllers\TransactionPaymentController::class, 'show'], [$row->id]).'" class="view_payment_modal"><i class="fas fa-money-bill-alt"></i> '.__('purchase.view_payments').'</a></li>';

                            if (auth()->user()->can('send_notification')) {
                                $html .= '<li><a href="#" data-href="'.action([\App\Http\Controllers\NotificationController::class, 'getTemplate'], ['transaction_id' => $row->id, 'template_for' => 'new_sale']).'" class="btn-modal" data-container=".view_modal"><i class="fa fa-envelope" aria-hidden="true"></i>'.__('lang_v1.new_sale_notification').'</a></li>';
                            }

                            // Add Exit Permission toggle button
                            if (auth()->user()->can('repair.update')) {
                                $exit_permission = $row->Exit_permission ?? 'Exit not allowed';
                                $is_checked = ($exit_permission == 'Exit allowed' || $exit_permission == 'Exited') ? true : false;

                                $html .= '<li class="divider"></li>';
                                $html .= '<li>
                                    <a href="javascript:void(0);" class="toggle-exit-permission" data-id="'.$row->id.'" data-status="'.($is_checked ? 'true' : 'false').'">
                                        <i class="fa fa-sign-out-alt"></i>
                                        <span class="exit-permission-label">'.($is_checked ? __('repair::lang.exit_allowed') : __('repair::lang.exit_not_allowed')).'</span>
                                    </a>
                                </li>';
                            }

                            $html .= '</ul></div>';

                            return $html;
                        }
                    )
                    ->removeColumn('id')
                    ->editColumn(
                        'final_total',
                        '<span class="display_currency final-total" data-currency_symbol="true" data-orig-value="{{$final_total}}">{{$final_total}}</span>'
                    )
                    ->editColumn(
                        'tax_amount',
                        '<span class="display_currency total-tax" data-currency_symbol="true" data-orig-value="{{$tax_amount}}">{{$tax_amount}}</span>'
                    )
                    ->editColumn(
                        'total_before_tax',
                        '<span class="display_currency total_before_tax" data-currency_symbol="true" data-orig-value="{{$total_before_tax}}">{{$total_before_tax}}</span>'
                    )
                    ->editColumn(
                        'discount_amount',
                        function ($row) {
                            $discount = ! empty($row->discount_amount) ? $row->discount_amount : 0;

                            if (! empty($discount) && $row->discount_type == 'percentage') {
                                $discount = $row->total_before_tax * ($discount / 100);
                            }

                            return '<span class="display_currency total-discount" data-currency_symbol="true" data-orig-value="'.$discount.'">'.$discount.'</span>';
                        }
                    )
                    ->editColumn('due_date', '
                            @if(!empty($due_date))
                                {{@format_datetime($due_date)}}
                            @endif
                    ')
                    ->editColumn('transaction_date', '{{@format_date($transaction_date)}}')
                    ->editColumn(
                        'payment_status',
                        '<a href="{{ action([\App\Http\Controllers\TransactionPaymentController::class, \'show\'], [$id])}}"
                            class="view_payment_modal payment-status-label no-print"
                            data-orig-value="{{ $payment_status ?? \' \' }}"
                            data-status-name="{{ $payment_status ? __(\'lang_v1.\' . $payment_status) : \' \' }}">
                            <span class="label @payment_status($payment_status)">{{ $payment_status ? __(\'lang_v1.\' . $payment_status) : \' \' }}
                            </span>
                        </a>
                        <span class="print_section">{{ $payment_status ? __(\'lang_v1.\' . $payment_status) : \' \' }}</span>'
                    )

                    ->addColumn('total_remaining', function ($row) {
                        $total_remaining = $row->final_total - $row->total_paid;
                        $total_remaining_html = '<span class="display_currency payment_due" data-currency_symbol="true" data-orig-value="'.$total_remaining.'">'.$total_remaining.'</span>';

                        return $total_remaining_html;
                    })
                    ->editColumn('invoice_no', function ($row) {
                        $invoice_no = $row->invoice_no;

                        if (! empty($row->return_exists)) {
                            $invoice_no .= ' &nbsp;<small class="label bg-red label-round no-print" title="'.__('lang_v1.some_qty_returned_from_sell').'"><i class="fas fa-undo"></i></small>';
                        }

                        return $invoice_no;
                    })
                    ->editColumn(
                        'repair_status',
                        function ($row) {
                            $html = '<a data-href="'.action([\Modules\Repair\Http\Controllers\RepairController::class, 'editRepairStatus'], [$row->id]).'" class="edit_repair_status" data-orig-value="'.$row->repair_status.'" data-status-name="'.$row->repair_status.'">
                                    <span class="label " style="background-color:'.$row->status_color.';" >
                                        '.$row->repair_status.'
                                    </span>
                                </a>
                            ';

                            if ($row->repair_updates_notif) {
                                $tooltip = __('repair::lang.sms_sent');
                                $html .= '<br><i class="fas fa-check-double text-success"
                                    data-toggle="tooltip" title="'.$tooltip.'"></i>';
                            }

                            return $html;
                        }
                    )
                    ->addColumn('technecian', function ($row) {
                        $service_staff_ids = json_decode($row->service_staff, true);

                        if (is_array($service_staff_ids) && !empty($service_staff_ids)) {
                            $technician = DB::table('users')
                                ->whereIn('id', $service_staff_ids)
                                ->pluck('first_name') // Assuming you want the first names
                                ->implode(', '); // Concatenate names with a comma
                        } else {
                            $technician = ''; // No service staff
                        }

                        return $technician;
                    })
                    ->addColumn('exit_permission', function ($row) {
                        $exit_permission = $row->Exit_permission ?? 'Exit not allowed';

                        $color = match ($exit_permission) {
                            'Exit allowed' => '#28a745', // Green
                            'Exited' => '#007bff',       // Blue
                            default => '#dc3545'         // Red (Exit not allowed)
                        };

                        return '<span style="background-color: ' . $color . '; color: #fff; padding: 5px 10px; border-radius: 6px; font-size: 14px; min-width: 80px; text-align: center; display: inline-block;">' .
                            __('repair::lang.' . strtolower(str_replace(' ', '_', $exit_permission))) .
                        '</span>';
                    })
                    ->addColumn('return_due', function ($row) {
                        $return_due_html = '';
                        if (! empty($row->return_exists)) {
                            $return_due = $row->amount_return - $row->return_paid;
                            $return_due_html .= '<a href="'.action([\App\Http\Controllers\TransactionPaymentController::class, 'show'], [$row->return_transaction_id]).'" class="view_purchase_return_payment_modal"><span class="display_currency sell_return_due" data-currency_symbol="true" data-orig-value="'.$return_due.'">'.$return_due.'</span></a>';
                        }

                        return $return_due_html;
                    })
                    ->editColumn('transactions_status', function ($row) {
                        $status = strtolower($row->transactions_status);

                        $translations = [
                            'under processing' => 'قيد العمل',
                            'final' => 'منتهي'
                        ];

                        $color = match (true) {
                            str_contains($status, 'under processing') => '#f39c12', // Orange
                            str_contains($status, 'final') => '#dc3545', // Red
                            default => '#6c757d' // Gray (default)
                        };

                        return !empty($row->transactions_status)
                            ? '<span style="background-color: ' . $color . '; color: #fff; padding: 5px 10px; border-radius: 6px; font-size: 14px; min-width: 80px; text-align: center;">'
                                . ($translations[$status] ?? e($row->transactions_status)) .
                            '</span>'
                            : ' ';
                    })

                    ->editColumn('job_sheet_no', function ($row) {
                        $html = $row->job_sheet_no;
                        if (!empty($row->job_sheet_id)
                            && (auth()->user()->can('job_sheet.view_assigned')
                            || auth()->user()->can('job_sheet.view_all')
                            || auth()->user()->can('job_sheet.create')))
                        {
                            $html = '<a href="'.action([\Modules\Repair\Http\Controllers\JobSheetController::class, 'show'], [$row->job_sheet_id]).'" class="cursor-pointer" target="_blank">
                                        '.$row->job_sheet_no.'
                                    </a>';
                        }

                        return $html;
                    })
                    ->setRowAttr([
                        'data-href' => function ($row) {
                            if (auth()->user()->can('sell.view')) {
                                return  action([\Modules\Repair\Http\Controllers\RepairController::class, 'show'], [$row->id]);
                            } else {
                                return '';
                            }
                        }, ]);

                $rawColumns = ['transactions_status','final_total', 'repair_due_date', 'action', 'total_paid', 'total_remaining', 'payment_status', 'invoice_no', 'discount_amount', 'tax_amount', 'total_before_tax', 'repair_status', 'warranty_name', 'return_due', 'job_sheet_no', 'exit_permission'];

                return $datatable->rawColumns($rawColumns)
                        ->make(true);
            }

            $business_locations = BusinessLocation::forDropdown($business_id, false);
            $customers = Contact::customersDropdown($business_id, false);
            $service_staffs = $this->transactionUtil->serviceStaffDropdown($business_id);
            $repair_status_dropdown = RepairStatus::forDropdown($business_id);
            $sales_representative = User::forDropdown($business_id, false, false, true);
            $user_role_as_service_staff = auth()->user()->roles()
                                        ->where('is_service_staff', 1)
                                        ->get()
                                        ->toArray();
            $is_service_staff = false;
            if (! empty($user_role_as_service_staff) && ! $is_admin) {
                $is_service_staff = true;
            }

            return view('repair::repair.index')->with(compact('business_locations', 'customers', 'service_staffs', 'repair_status_dropdown', 'sales_representative', 'is_service_staff'));
    }