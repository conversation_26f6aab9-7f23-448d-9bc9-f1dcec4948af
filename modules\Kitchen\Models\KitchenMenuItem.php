<?php

namespace Modules\Kitchen\Models;

use App\Models\MenuItem;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class KitchenMenuItem extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'kitchen_id',
        'menu_item_id',
        'prep_time_minutes',
        'priority_level',
        'is_active',
        'special_instructions',
        'assigned_by',
        'assigned_at',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'assigned_at' => 'datetime',
        ];
    }

    /**
     * Get the kitchen that owns the assignment.
     */
    public function kitchen(): BelongsTo
    {
        return $this->belongsTo(Kitchen::class);
    }

    /**
     * Get the menu item that is assigned.
     */
    public function menuItem(): BelongsTo
    {
        return $this->belongsTo(MenuItem::class);
    }

    /**
     * Get the user who assigned the menu item.
     */
    public function assignedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_by');
    }

    /**
     * Scope to get active assignments.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get assignments by kitchen.
     */
    public function scopeByKitchen($query, int $kitchenId)
    {
        return $query->where('kitchen_id', $kitchenId);
    }

    /**
     * Scope to get assignments by menu item.
     */
    public function scopeByMenuItem($query, int $menuItemId)
    {
        return $query->where('menu_item_id', $menuItemId);
    }

    /**
     * Scope to order by priority level.
     */
    public function scopeByPriority($query)
    {
        return $query->orderBy('priority_level', 'desc');
    }
}
