<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Order;
use App\Models\MenuItem;
use Modules\Kitchen\Models\Kitchen;
use Modules\Kitchen\Models\KitchenMenuItem;

echo "=== Kitchen Assignment Check ===\n";

// Get the order's branch
$order = Order::where('status', 'confirmed')
    ->whereHas('items')
    ->with(['items.menuItem', 'branch'])
    ->first();

if (!$order) {
    echo "No confirmed orders with items found\n";
    exit;
}

$branchId = $order->branch_id;
echo "Branch ID: {$branchId}\n";
echo "Branch Name: {$order->branch->name}\n";

// Check kitchens for this branch
$kitchens = Kitchen::where('branch_id', $branchId)->get();
echo "Kitchens in branch: {$kitchens->count()}\n";
foreach ($kitchens as $kitchen) {
    echo "  - {$kitchen->name} (Code: {$kitchen->code})\n";
}

// Check menu items in this branch
$menuItems = MenuItem::whereHas('category', function($q) use ($branchId) {
    $q->where('branch_id', $branchId);
})->get();
echo "\nMenu items in branch: {$menuItems->count()}\n";

// Check kitchen menu item assignments for this branch
$assignments = KitchenMenuItem::whereHas('kitchen', function($q) use ($branchId) {
    $q->where('branch_id', $branchId);
})->with(['kitchen', 'menuItem'])->get();

echo "\nKitchen menu item assignments for this branch: {$assignments->count()}\n";
foreach ($assignments as $assignment) {
    echo "  - {$assignment->menuItem->name} -> {$assignment->kitchen->name}\n";
}

// Check specific menu items from the order
echo "\n=== Order Items Kitchen Assignments ===\n";
foreach ($order->items as $item) {
    echo "Item: {$item->menuItem->name}\n";
    $itemAssignments = KitchenMenuItem::where('menu_item_id', $item->menu_item_id)
        ->whereHas('kitchen', function($q) use ($branchId) {
            $q->where('branch_id', $branchId);
        })
        ->with('kitchen')
        ->get();
    
    echo "  Assignments: {$itemAssignments->count()}\n";
    foreach ($itemAssignments as $assignment) {
        echo "    - {$assignment->kitchen->name}\n";
    }
}