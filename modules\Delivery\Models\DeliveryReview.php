<?php

namespace Modules\Delivery\Models;

use App\Models\Customer;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DeliveryReview extends Model
{
    use HasFactory;

    protected $fillable = [
        'delivery_assignment_id',
        'customer_id',
        'rating',
        'comment',
        'review_categories',
        'is_anonymous',
        'is_verified',
        'verified_at',
    ];

    protected $casts = [
        'review_categories' => 'array',
        'is_anonymous' => 'boolean',
        'is_verified' => 'boolean',
        'verified_at' => 'datetime',
    ];

    /**
     * Get the delivery assignment this review belongs to.
     */
    public function deliveryAssignment(): BelongsTo
    {
        return $this->belongsTo(DeliveryAssignment::class);
    }

    /**
     * Get the customer who wrote this review.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the delivery personnel through the assignment.
     */
    public function deliveryPersonnel()
    {
        return $this->hasOneThrough(
            DeliveryPersonnel::class,
            DeliveryAssignment::class,
            'id',
            'id',
            'delivery_assignment_id',
            'delivery_personnel_id'
        );
    }

    /**
     * Verify the review.
     */
    public function verify(): void
    {
        $this->update([
            'is_verified' => true,
            'verified_at' => now(),
        ]);

        // Update delivery personnel rating
        $this->updatePersonnelRating();
    }

    /**
     * Update delivery personnel rating based on reviews.
     */
    public function updatePersonnelRating(): void
    {
        $personnel = $this->deliveryAssignment->deliveryPersonnel;
        
        if (!$personnel) {
            return;
        }

        $averageRating = DeliveryReview::whereHas('deliveryAssignment', function ($query) use ($personnel) {
            $query->where('delivery_personnel_id', $personnel->id);
        })
        ->where('is_verified', true)
        ->avg('rating');

        $personnel->update(['rating' => round($averageRating, 2)]);
    }

    /**
     * Get review categories with their ratings.
     */
    public function getCategoryRatings(): array
    {
        $categories = $this->review_categories ?? [];
        $defaultCategories = [
            'speed' => null,
            'politeness' => null,
            'food_condition' => null,
            'communication' => null,
            'professionalism' => null,
        ];

        return array_merge($defaultCategories, $categories);
    }

    /**
     * Set review categories with validation.
     */
    public function setCategoryRatings(array $categories): void
    {
        $validCategories = [];
        $allowedCategories = ['speed', 'politeness', 'food_condition', 'communication', 'professionalism'];

        foreach ($categories as $category => $rating) {
            if (in_array($category, $allowedCategories) && is_numeric($rating) && $rating >= 1 && $rating <= 5) {
                $validCategories[$category] = (int) $rating;
            }
        }

        $this->update(['review_categories' => $validCategories]);
    }

    /**
     * Check if review is positive (4+ stars).
     */
    public function isPositive(): bool
    {
        return $this->rating >= 4;
    }

    /**
     * Check if review is negative (2 or less stars).
     */
    public function isNegative(): bool
    {
        return $this->rating <= 2;
    }

    /**
     * Get formatted review text for display.
     */
    public function getDisplayComment(): string
    {
        if ($this->is_anonymous) {
            return $this->comment ? 'Anonymous: ' . $this->comment : 'Anonymous review';
        }

        $customerName = $this->customer ? $this->customer->first_name : 'Customer';
        return $this->comment ? $customerName . ': ' . $this->comment : $customerName . ' left a review';
    }

    /**
     * Scope for verified reviews.
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope for reviews by rating.
     */
    public function scopeByRating($query, int $rating)
    {
        return $query->where('rating', $rating);
    }

    /**
     * Scope for positive reviews.
     */
    public function scopePositive($query)
    {
        return $query->where('rating', '>=', 4);
    }

    /**
     * Scope for negative reviews.
     */
    public function scopeNegative($query)
    {
        return $query->where('rating', '<=', 2);
    }

    /**
     * Scope for reviews with comments.
     */
    public function scopeWithComments($query)
    {
        return $query->whereNotNull('comment')->where('comment', '!=', '');
    }
}