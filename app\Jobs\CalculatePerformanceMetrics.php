class CalculatePerformanceMetrics implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    
    public function __construct(
        public int $userId,
        public string $startDate,
        public string $endDate
    ) {}
    
    public function handle(PerformanceService $service): void
    {
        $metrics = $service->calculateMetrics($this->userId, $this->startDate, $this->endDate);
        
        Cache::put("perf_metrics_{$this->userId}_{$this->startDate}_{$this->endDate}", $metrics, 3600);
        
        broadcast(new PerformanceMetricsCalculated($this->userId, $metrics));
    }
}