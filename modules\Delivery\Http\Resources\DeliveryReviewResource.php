<?php

namespace Modules\Delivery\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class DeliveryReviewResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'delivery_assignment_id' => $this->delivery_assignment_id,
            'customer_id' => $this->customer_id,
            'rating' => $this->rating,
            'comment' => $this->comment,
            'review_categories' => $this->review_categories,
            'is_anonymous' => $this->is_anonymous,
            'is_verified' => $this->is_verified,
            'verified_at' => $this->verified_at?->format('Y-m-d H:i:s'),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
            
            // Include relationships when loaded
            'customer' => $this->when(!$this->is_anonymous && $this->relationLoaded('customer'), function () {
                return [
                    'id' => $this->customer->id,
                    'name' => $this->customer->name,
                    'email' => $this->customer->email,
                ];
            }),
            
            'delivery_assignment' => $this->when($this->relationLoaded('deliveryAssignment'), function () {
                return [
                    'id' => $this->deliveryAssignment->id,
                    'order_id' => $this->deliveryAssignment->order_id,
                    'status' => $this->deliveryAssignment->status,
                    'delivered_at' => $this->deliveryAssignment->delivered_at?->format('Y-m-d H:i:s'),
                ];
            }),
            
            'delivery_personnel' => $this->when($this->relationLoaded('deliveryPersonnel'), function () {
                return [
                    'id' => $this->deliveryPersonnel->id,
                    'name' => $this->deliveryPersonnel->user->name ?? null,
                    'rating' => $this->deliveryPersonnel->rating,
                    'total_deliveries' => $this->deliveryPersonnel->total_deliveries,
                ];
            }),
        ];
    }
}