<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Area;

class AreaSeeder extends Seeder
{
    public function run()
    {
        $branch = \App\Models\Branch::first();

        if (!$branch) {
            $this->command->info('No branch found. Skipping AreaSeeder.');
            return;
        }

        Area::create([
            'branch_id' => $branch->id,
            'name' => 'Main Hall',
            // Add other required fields here
        ]);
    }
}