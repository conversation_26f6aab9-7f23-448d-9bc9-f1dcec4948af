<?php

namespace App\Helpers;

use App\Models\Customer;
use App\Models\LoyaltyTransaction;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class CustomerHelper
{
    /**
     * Generate a unique customer code
     */
    public static function generateCustomerCode(): string
    {
        $prefix = 'CUST';
        $timestamp = now()->format('ymd');
        
        // Get the last customer created today
        $lastCustomer = Customer::whereDate('created_at', today())
            ->orderBy('id', 'desc')
            ->first();
        
        $sequence = 1;
        if ($lastCustomer) {
            // Extract sequence from last customer code
            $lastCode = $lastCustomer->customer_code ?? '';
            if (preg_match('/CUST\d{6}(\d{4})/', $lastCode, $matches)) {
                $sequence = intval($matches[1]) + 1;
            }
        }
        
        return $prefix . $timestamp . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Format customer full name
     */
    public static function formatFullName(Customer $customer): string
    {
        return trim($customer->first_name . ' ' . $customer->last_name);
    }

    /**
     * Get customer age from date of birth
     */
    public static function getAge(Customer $customer): ?int
    {
        return $customer->date_of_birth ? $customer->date_of_birth->age : null;
    }

    /**
     * Check if customer is a VIP based on loyalty points
     */
    public static function isVipCustomer(Customer $customer, int $vipThreshold = 1000): bool
    {
        return $customer->loyalty_points >= $vipThreshold;
    }

    /**
     * Get customer tier based on loyalty points
     */
    public static function getCustomerTier(Customer $customer): string
    {
        $points = $customer->loyalty_points;
        
        if ($points >= 5000) {
            return 'Platinum';
        } elseif ($points >= 2000) {
            return 'Gold';
        } elseif ($points >= 500) {
            return 'Silver';
        } else {
            return 'Bronze';
        }
    }

    /**
     * Get customer tier color for UI
     */
    public static function getTierColor(string $tier): string
    {
        return match ($tier) {
            'Platinum' => '#E5E7EB',
            'Gold' => '#FCD34D',
            'Silver' => '#9CA3AF',
            'Bronze' => '#D97706',
            default => '#6B7280'
        };
    }

    /**
     * Calculate customer lifetime value
     */
    public static function calculateLifetimeValue(Customer $customer): float
    {
        return $customer->orders()->sum('total_amount') ?? 0;
    }

    /**
     * Get customer visit frequency (visits per month)
     */
    public static function getVisitFrequency(Customer $customer): float
    {
        $firstOrder = $customer->orders()->oldest()->first();
        if (!$firstOrder) {
            return 0;
        }

        $monthsSinceFirst = $firstOrder->created_at->diffInMonths(now()) ?: 1;
        $totalOrders = $customer->orders()->count();

        return round($totalOrders / $monthsSinceFirst, 2);
    }

    /**
     * Get customer's average order value
     */
    public static function getAverageOrderValue(Customer $customer): float
    {
        $totalAmount = $customer->orders()->sum('total_amount');
        $orderCount = $customer->orders()->count();

        return $orderCount > 0 ? round($totalAmount / $orderCount, 2) : 0;
    }

    /**
     * Get customer's last order date
     */
    public static function getLastOrderDate(Customer $customer): ?Carbon
    {
        $lastOrder = $customer->orders()->latest()->first();
        return $lastOrder ? $lastOrder->created_at : null;
    }

    /**
     * Check if customer is at risk of churning
     */
    public static function isAtRiskOfChurning(Customer $customer, int $daysSinceLastOrder = 90): bool
    {
        $lastOrderDate = self::getLastOrderDate($customer);
        
        if (!$lastOrderDate) {
            return true; // No orders = high risk
        }

        return $lastOrderDate->diffInDays(now()) > $daysSinceLastOrder;
    }

    /**
     * Get customer's preferred contact method
     */
    public static function getPreferredContactMethod(Customer $customer): string
    {
        $preferences = $customer->preferences ?? [];
        
        return $preferences['contact_method'] ?? 'email';
    }

    /**
     * Format customer phone number
     */
    public static function formatPhoneNumber(string $phone): string
    {
        // Remove all non-numeric characters
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        // Format based on length (assuming 10-digit numbers)
        if (strlen($phone) === 10) {
            return sprintf('(%s) %s-%s', 
                substr($phone, 0, 3),
                substr($phone, 3, 3),
                substr($phone, 6, 4)
            );
        }
        
        return $phone;
    }

    /**
     * Get customer statistics for dashboard
     */
    public static function getCustomerStatistics(): array
    {
        $tenantId = auth()->user()->tenant_id;
        $branchId = session('branch_id');

        $query = Customer::where('tenant_id', $tenantId);
        
        if ($branchId) {
            $query->where('branch_id', $branchId);
        }

        $totalCustomers = $query->count();
        $activeCustomers = $query->where('is_active', true)->count();
        $newCustomersThisMonth = $query->whereMonth('created_at', now()->month)
                                      ->whereYear('created_at', now()->year)
                                      ->count();
        
        $totalLoyaltyPoints = $query->sum('loyalty_points');
        $averageLoyaltyPoints = $totalCustomers > 0 ? round($totalLoyaltyPoints / $totalCustomers, 2) : 0;

        return [
            'total_customers' => $totalCustomers,
            'active_customers' => $activeCustomers,
            'inactive_customers' => $totalCustomers - $activeCustomers,
            'new_customers_this_month' => $newCustomersThisMonth,
            'total_loyalty_points' => $totalLoyaltyPoints,
            'average_loyalty_points' => $averageLoyaltyPoints,
            'vip_customers' => $query->where('loyalty_points', '>=', 1000)->count(),
        ];
    }

    /**
     * Get top customers by loyalty points
     */
    public static function getTopLoyaltyCustomers(int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        $tenantId = auth()->user()->tenant_id;
        $branchId = session('branch_id');

        $query = Customer::where('tenant_id', $tenantId)
                        ->where('is_active', true)
                        ->orderBy('loyalty_points', 'desc');
        
        if ($branchId) {
            $query->where('branch_id', $branchId);
        }

        return $query->limit($limit)->get();
    }

    /**
     * Search customers by various criteria
     */
    public static function searchCustomers(string $query, int $limit = 20): \Illuminate\Database\Eloquent\Collection
    {
        $tenantId = auth()->user()->tenant_id;
        $branchId = session('branch_id');

        $customerQuery = Customer::where('tenant_id', $tenantId)
                               ->where('is_active', true)
                               ->where(function ($q) use ($query) {
                                   $q->where('first_name', 'like', "%{$query}%")
                                     ->orWhere('last_name', 'like', "%{$query}%")
                                     ->orWhere('email', 'like', "%{$query}%")
                                     ->orWhere('phone', 'like', "%{$query}%")
                                     ->orWhereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ["%{$query}%"]);
                               });
        
        if ($branchId) {
            $customerQuery->where('branch_id', $branchId);
        }

        return $customerQuery->limit($limit)->get();
    }

    /**
     * Validate loyalty points transaction
     */
    public static function validateLoyaltyTransaction(Customer $customer, float $points, string $type): array
    {
        $errors = [];

        if ($points <= 0) {
            $errors[] = 'Points must be greater than 0';
        }

        if ($type === 'redeemed' && $customer->loyalty_points < $points) {
            $errors[] = 'Insufficient loyalty points. Available: ' . $customer->loyalty_points;
        }

        if ($points > 10000) {
            $errors[] = 'Points cannot exceed 10,000 in a single transaction';
        }

        return $errors;
    }

    /**
     * Calculate points expiry date
     */
    public static function calculatePointsExpiry(int $monthsToExpire = 12): Carbon
    {
        return now()->addMonths($monthsToExpire);
    }

    /**
     * Get customer's expiring points
     */
    public static function getExpiringPoints(Customer $customer, int $daysAhead = 30): float
    {
        return LoyaltyTransaction::where('customer_id', $customer->id)
                                ->where('type', 'earned')
                                ->where('expires_at', '<=', now()->addDays($daysAhead))
                                ->where('expires_at', '>', now())
                                ->sum('points');
    }
}