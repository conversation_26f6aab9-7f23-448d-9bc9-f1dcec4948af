@extends('layouts.master')

@push('inventory-styles')
<style>
.supplier-card {
    border-radius: 10px;
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
    margin-bottom: 20px;
}

.supplier-card:hover {
    transform: translateY(-2px);
}

.supplier-logo {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 50%;
    border: 2px solid #e9ecef;
}

.supplier-rating {
    color: #ffc107;
}

.contact-info {
    font-size: 0.875rem;
    color: #6c757d;
}

.supplier-status-badge {
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 12px;
}

.status-active {
    background: #d4edda;
    color: #155724;
}

.status-inactive {
    background: #f8d7da;
    color: #721c24;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.filter-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}
</style>
@endpush

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">المخزون</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ إدارة الموردين</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-success" onclick="exportSuppliers()">
                <i class="mdi mdi-download"></i> تصدير
            </button>
        </div>
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-info" onclick="importSuppliers()">
                <i class="mdi mdi-upload"></i> استيراد
            </button>
        </div>
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-primary" onclick="addSupplier()">
                <i class="mdi mdi-plus"></i> إضافة مورد جديد
            </button>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<!-- Supplier Statistics -->
<div class="row row-sm">
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
        <div class="card supplier-card">
            <div class="card-body">
                <div class="d-flex">
                    <div class="stats-icon bg-primary">
                        <i class="mdi mdi-truck"></i>
                    </div>
                    <div class="mr-auto">
                        <div class="text-right">
                            <h2 class="mb-1 tx-20 font-weight-bold">{{ $analytics['total_suppliers'] ?? 0 }}</h2>
                            <p class="text-muted mb-0 tx-11">إجمالي الموردين</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
        <div class="card supplier-card">
            <div class="card-body">
                <div class="d-flex">
                    <div class="stats-icon bg-success">
                        <i class="mdi mdi-check-circle"></i>
                    </div>
                    <div class="mr-auto">
                        <div class="text-right">
                            <h2 class="mb-1 tx-20 font-weight-bold text-success">{{ $analytics['active_suppliers'] ?? 0 }}</h2>
                            <p class="text-muted mb-0 tx-11">موردين نشطين</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
        <div class="card supplier-card">
            <div class="card-body">
                <div class="d-flex">
                    <div class="stats-icon bg-warning">
                        <i class="mdi mdi-clock"></i>
                    </div>
                    <div class="mr-auto">
                        <div class="text-right">
                            <h2 class="mb-1 tx-20 font-weight-bold text-warning">{{ $analytics['pending_suppliers'] ?? 0 }}</h2>
                            <p class="text-muted mb-0 tx-11">في الانتظار</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
        <div class="card supplier-card">
            <div class="card-body">
                <div class="d-flex">
                    <div class="stats-icon bg-info">
                        <i class="mdi mdi-package-variant"></i>
                    </div>
                    <div class="mr-auto">
                        <div class="text-right">
                            <h2 class="mb-1 tx-20 font-weight-bold text-info">{{ $analytics['total_products'] ?? 0 }}</h2>
                            <p class="text-muted mb-0 tx-11">المنتجات المورّدة</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row">
    <div class="col-xl-12">
        <div class="filter-card">
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label>البحث</label>
                        <input type="text" id="search-input" class="form-control" placeholder="البحث بالاسم أو الرقم...">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>الحالة</label>
                        <select id="status-filter" class="form-control select2">
                            <option value="">جميع الحالات</option>
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                            <option value="pending">في الانتظار</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>المدينة</label>
                        <select id="city-filter" class="form-control select2">
                            <option value="">جميع المدن</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <div class="d-flex">
                            <button type="button" class="btn btn-primary mr-2" onclick="applySupplierFilters()">
                                <i class="mdi mdi-filter"></i> تطبيق
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="clearSupplierFilters()">
                                <i class="mdi mdi-filter-remove"></i> مسح
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Suppliers Table -->
<div class="row">
    <div class="col-xl-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">قائمة الموردين</h4>
                    <div class="card-options">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="refreshSuppliersTable()">
                                <i class="mdi mdi-refresh"></i> تحديث
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-info" onclick="toggleCardView()">
                                <i class="mdi mdi-view-grid"></i> عرض البطاقات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Table View -->
                <div id="table-view">
                    <div class="table-responsive">
                        <table id="suppliers-table" class="table table-striped table-bordered text-md-nowrap">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>الشعار</th>
                                    <th>اسم المورد</th>
                                    <th>رقم المورد</th>
                                    <th>جهة الاتصال</th>
                                    <th>الهاتف</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>المدينة</th>
                                    <th>التقييم</th>
                                    <th>الحالة</th>
                                    <th>آخر طلب</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Card View -->
                <div id="card-view" style="display: none;">
                    <div id="suppliers-cards" class="row">
                        <!-- Cards will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modals -->
@include('inventory::suppliers.modals')

@endsection

@push('inventory-scripts')
<script>
let suppliersTable;
let cardView = false;

$(document).ready(function() {
    initializeSuppliersTable();
    loadCitiesForFilter();
});

function initializeSuppliersTable() {
    suppliersTable = $('#suppliers-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("inventory.api.suppliers.datatable") }}',
            data: function(d) {
                d.search = $('#search-input').val();
                d.status = $('#status-filter').val();
                d.city = $('#city-filter').val();
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { 
                data: 'logo', 
                name: 'logo', 
                orderable: false, 
                searchable: false,
                render: function(data, type, row) {
                    if (data) {
                        return `<img src="${data}" class="supplier-logo" alt="شعار المورد">`;
                    }
                    return `<div class="supplier-logo bg-light d-flex align-items-center justify-content-center">
                        <i class="mdi mdi-truck text-muted"></i>
                    </div>`;
                }
            },
            { data: 'name', name: 'name' },
            { data: 'supplier_code', name: 'supplier_code' },
            { data: 'contact_person', name: 'contact_person' },
            { data: 'phone', name: 'phone' },
            { data: 'email', name: 'email' },
            { data: 'city', name: 'city' },
            { 
                data: 'rating', 
                name: 'rating',
                render: function(data) {
                    if (!data) return '-';
                    let stars = '';
                    for (let i = 1; i <= 5; i++) {
                        stars += `<i class="mdi mdi-star${i <= data ? '' : '-outline'} supplier-rating"></i>`;
                    }
                    return stars;
                }
            },
            { 
                data: 'status', 
                name: 'status',
                render: function(data) {
                    return getSupplierStatusBadge(data);
                }
            },
            { 
                data: 'last_order_date', 
                name: 'last_order_date',
                render: function(data) {
                    return data ? InventoryModule.formatDate(data) : '-';
                }
            },
            { 
                data: 'actions', 
                name: 'actions', 
                orderable: false, 
                searchable: false,
                render: function(data, type, row) {
                    return `
                        <div class="table-actions">
                            <button type="button" class="btn btn-sm btn-info btn-action" onclick="viewSupplier(${row.id})" title="عرض">
                                <i class="mdi mdi-eye"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-primary btn-action" onclick="editSupplier(${row.id})" title="تعديل">
                                <i class="mdi mdi-pencil"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-success btn-action" onclick="viewProducts(${row.id})" title="المنتجات">
                                <i class="mdi mdi-package-variant"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-warning btn-action" onclick="viewOrders(${row.id})" title="الطلبات">
                                <i class="mdi mdi-cart"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-danger btn-action" onclick="deleteSupplier(${row.id})" title="حذف">
                                <i class="mdi mdi-delete"></i>
                            </button>
                        </div>
                    `;
                }
            }
        ],
        order: [[2, 'asc']],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
        },
        responsive: true,
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'excel',
                text: 'تصدير Excel',
                className: 'btn btn-success btn-sm'
            },
            {
                extend: 'pdf',
                text: 'تصدير PDF',
                className: 'btn btn-danger btn-sm'
            }
        ]
    });
}

function loadCitiesForFilter() {
    $.get('{{ route("inventory.api.suppliers.cities") }}')
        .done(function(cities) {
            const options = cities.map(city => 
                `<option value="${city}">${city}</option>`
            ).join('');
            
            $('#city-filter').html('<option value="">جميع المدن</option>' + options);
        })
        .fail(function() {
            console.error('Failed to load cities for filter');
        });
}

function getSupplierStatusBadge(status) {
    const badges = {
        'active': '<span class="supplier-status-badge status-active">نشط</span>',
        'inactive': '<span class="supplier-status-badge status-inactive">غير نشط</span>',
        'pending': '<span class="supplier-status-badge status-pending">في الانتظار</span>'
    };
    
    return badges[status] || status;
}

function applySupplierFilters() {
    if (cardView) {
        loadSuppliersCards();
    } else {
        suppliersTable.ajax.reload();
    }
}

function clearSupplierFilters() {
    $('#search-input').val('');
    $('#status-filter').val('').trigger('change');
    $('#city-filter').val('').trigger('change');
    
    if (cardView) {
        loadSuppliersCards();
    } else {
        suppliersTable.ajax.reload();
    }
}

function refreshSuppliersTable() {
    if (cardView) {
        loadSuppliersCards();
    } else {
        suppliersTable.ajax.reload();
    }
}

function toggleCardView() {
    cardView = !cardView;
    
    if (cardView) {
        $('#table-view').hide();
        $('#card-view').show();
        loadSuppliersCards();
    } else {
        $('#card-view').hide();
        $('#table-view').show();
    }
}

function loadSuppliersCards() {
    const filters = {
        search: $('#search-input').val(),
        status: $('#status-filter').val(),
        city: $('#city-filter').val()
    };
    
    $.get('{{ route("inventory.api.suppliers.cards") }}', filters)
        .done(function(suppliers) {
            renderSuppliersCards(suppliers);
        })
        .fail(function() {
            InventoryModule.showError('حدث خطأ أثناء جلب بيانات الموردين');
        });
}

function renderSuppliersCards(suppliers) {
    let cardsHtml = '';
    
    suppliers.forEach(function(supplier) {
        const logo = supplier.logo ? 
            `<img src="${supplier.logo}" class="supplier-logo" alt="شعار المورد">` :
            `<div class="supplier-logo bg-light d-flex align-items-center justify-content-center">
                <i class="mdi mdi-truck text-muted"></i>
            </div>`;
            
        const rating = supplier.rating ? 
            Array.from({length: 5}, (_, i) => 
                `<i class="mdi mdi-star${i < supplier.rating ? '' : '-outline'} supplier-rating"></i>`
            ).join('') : '-';
        
        cardsHtml += `
            <div class="col-xl-4 col-lg-6 col-md-6 col-sm-12">
                <div class="card supplier-card">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            ${logo}
                            <div class="mr-3">
                                <h5 class="mb-1">${supplier.name}</h5>
                                <small class="text-muted">${supplier.supplier_code}</small>
                            </div>
                            <div class="mr-auto">
                                ${getSupplierStatusBadge(supplier.status)}
                            </div>
                        </div>
                        
                        <div class="contact-info mb-3">
                            <div class="mb-1">
                                <i class="mdi mdi-account mr-2"></i>${supplier.contact_person || '-'}
                            </div>
                            <div class="mb-1">
                                <i class="mdi mdi-phone mr-2"></i>${supplier.phone || '-'}
                            </div>
                            <div class="mb-1">
                                <i class="mdi mdi-email mr-2"></i>${supplier.email || '-'}
                            </div>
                            <div class="mb-1">
                                <i class="mdi mdi-map-marker mr-2"></i>${supplier.city || '-'}
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <small class="text-muted">التقييم:</small>
                                <div>${rating}</div>
                            </div>
                            <div class="text-right">
                                <small class="text-muted">آخر طلب:</small>
                                <div>${supplier.last_order_date ? InventoryModule.formatDate(supplier.last_order_date) : '-'}</div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-sm btn-info" onclick="viewSupplier(${supplier.id})">
                                <i class="mdi mdi-eye"></i> عرض
                            </button>
                            <button type="button" class="btn btn-sm btn-primary" onclick="editSupplier(${supplier.id})">
                                <i class="mdi mdi-pencil"></i> تعديل
                            </button>
                            <button type="button" class="btn btn-sm btn-success" onclick="viewProducts(${supplier.id})">
                                <i class="mdi mdi-package-variant"></i> المنتجات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    $('#suppliers-cards').html(cardsHtml || '<div class="col-12 text-center text-muted py-4">لا توجد موردين</div>');
}

function addSupplier() {
    $('#addSupplierModal').modal('show');
}

function viewSupplier(id) {
    $.get(`{{ route('inventory.api.suppliers.show', ':id') }}`.replace(':id', id))
        .done(function(data) {
            populateViewSupplierModal(data);
            $('#viewSupplierModal').modal('show');
        })
        .fail(function() {
            InventoryModule.showError('حدث خطأ أثناء جلب بيانات المورد');
        });
}

function editSupplier(id) {
    $.get(`{{ route('inventory.api.suppliers.show', ':id') }}`.replace(':id', id))
        .done(function(data) {
            populateEditSupplierModal(data);
            $('#editSupplierModal').modal('show');
        })
        .fail(function() {
            InventoryModule.showError('حدث خطأ أثناء جلب بيانات المورد');
        });
}

function viewProducts(id) {
    window.location.href = `{{ route('inventory.suppliers.products', ':id') }}`.replace(':id', id);
}

function viewOrders(id) {
    window.location.href = `{{ route('inventory.purchase-orders.supplier', ':id') }}`.replace(':id', id);
}

function deleteSupplier(id) {
    InventoryModule.confirm('هل أنت متأكد من حذف هذا المورد؟', function() {
        $.ajax({
            url: `{{ route('inventory.api.suppliers.destroy', ':id') }}`.replace(':id', id),
            type: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function() {
                InventoryModule.showSuccess('تم حذف المورد بنجاح');
                refreshSuppliersTable();
            },
            error: function(xhr) {
                const message = xhr.responseJSON?.message || 'حدث خطأ أثناء حذف المورد';
                InventoryModule.showError(message);
            }
        });
    });
}

function exportSuppliers() {
    window.location.href = '{{ route("inventory.api.suppliers.export") }}?format=excel';
}

function importSuppliers() {
    $('#importSuppliersModal').modal('show');
}
</script>
@endpush
