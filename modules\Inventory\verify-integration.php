<?php

/**
 * Inventory Module Integration Verification Script
 * 
 * This script performs basic checks to verify the inventory module
 * is properly integrated with the existing system.
 * 
 * Run this script from the Laravel root directory:
 * php modules/Inventory/verify-integration.php
 */

// Include Laravel bootstrap
require_once __DIR__ . '/../../bootstrap/app.php';

class InventoryIntegrationVerifier
{
    private $app;
    private $errors = [];
    private $warnings = [];
    private $success = [];

    public function __construct()
    {
        $this->app = app();
    }

    public function verify()
    {
        echo "🔍 Verifying Inventory Module Integration...\n\n";

        $this->checkRoutes();
        $this->checkViews();
        $this->checkControllers();
        $this->checkModels();
        $this->checkMigrations();
        $this->checkAssets();
        $this->checkConfiguration();

        $this->displayResults();
    }

    private function checkRoutes()
    {
        echo "📍 Checking Routes...\n";

        $routes = [
            'inventory.dashboard' => 'inventory',
            'inventory.items.index' => 'inventory/items',
            'inventory.stock.index' => 'inventory/stock',
            'inventory.suppliers.index' => 'inventory/suppliers',
            'inventory.purchase-orders.index' => 'inventory/purchase-orders',
            'inventory.analytics.dashboard' => 'inventory/analytics',
        ];

        foreach ($routes as $name => $uri) {
            try {
                $route = route($name);
                $this->success[] = "✅ Route '{$name}' is registered";
            } catch (Exception $e) {
                $this->errors[] = "❌ Route '{$name}' is not registered";
            }
        }

        // Check API routes
        $apiRoutes = [
            'inventory.api.items.index',
            'inventory.api.stock.index',
            'inventory.api.suppliers.index',
            'inventory.api.purchase-orders.index',
        ];

        foreach ($apiRoutes as $name) {
            try {
                $route = route($name);
                $this->success[] = "✅ API Route '{$name}' is registered";
            } catch (Exception $e) {
                $this->errors[] = "❌ API Route '{$name}' is not registered";
            }
        }
    }

    private function checkViews()
    {
        echo "👁️  Checking Views...\n";

        $views = [
            'inventory::layouts.inventory',
            'inventory::items.index',
            'inventory::items.modals',
            'inventory::stock.index',
            'inventory::stock.modals',
            'inventory::stock.movements',
            'inventory::suppliers.index',
            'inventory::suppliers.modals',
            'inventory::purchase-orders.index',
            'inventory::purchase-orders.modals',
            'inventory::analytics.dashboard',
        ];

        foreach ($views as $view) {
            if (view()->exists($view)) {
                $this->success[] = "✅ View '{$view}' exists";
            } else {
                $this->errors[] = "❌ View '{$view}' does not exist";
            }
        }
    }

    private function checkControllers()
    {
        echo "🎮 Checking Controllers...\n";

        $controllers = [
            'Modules\\Inventory\\Http\\Controllers\\InventoryWebController',
            'Modules\\Inventory\\Http\\Controllers\\Api\\ItemsController',
            'Modules\\Inventory\\Http\\Controllers\\Api\\StockController',
            'Modules\\Inventory\\Http\\Controllers\\Api\\SuppliersController',
            'Modules\\Inventory\\Http\\Controllers\\Api\\PurchaseOrdersController',
            'Modules\\Inventory\\Http\\Controllers\\Api\\AnalyticsController',
        ];

        foreach ($controllers as $controller) {
            if (class_exists($controller)) {
                $this->success[] = "✅ Controller '{$controller}' exists";
            } else {
                $this->errors[] = "❌ Controller '{$controller}' does not exist";
            }
        }
    }

    private function checkModels()
    {
        echo "📊 Checking Models...\n";

        $models = [
            'Modules\\Inventory\\Models\\InventoryItem',
            'Modules\\Inventory\\Models\\Stock',
            'Modules\\Inventory\\Models\\StockMovement',
            'Modules\\Inventory\\Models\\Supplier',
            'Modules\\Inventory\\Models\\PurchaseOrder',
            'Modules\\Inventory\\Models\\PurchaseOrderItem',
        ];

        foreach ($models as $model) {
            if (class_exists($model)) {
                $this->success[] = "✅ Model '{$model}' exists";
                
                // Check if model has proper table connection
                try {
                    $instance = new $model;
                    $table = $instance->getTable();
                    $this->success[] = "✅ Model '{$model}' connected to table '{$table}'";
                } catch (Exception $e) {
                    $this->warnings[] = "⚠️  Model '{$model}' may have table connection issues";
                }
            } else {
                $this->errors[] = "❌ Model '{$model}' does not exist";
            }
        }
    }

    private function checkMigrations()
    {
        echo "🗄️  Checking Database Tables...\n";

        $tables = [
            'inventory_items',
            'inventory_stock',
            'inventory_stock_movements',
            'inventory_suppliers',
            'inventory_purchase_orders',
            'inventory_purchase_order_items',
        ];

        foreach ($tables as $table) {
            try {
                if (Schema::hasTable($table)) {
                    $this->success[] = "✅ Table '{$table}' exists";
                } else {
                    $this->errors[] = "❌ Table '{$table}' does not exist";
                }
            } catch (Exception $e) {
                $this->errors[] = "❌ Error checking table '{$table}': " . $e->getMessage();
            }
        }
    }

    private function checkAssets()
    {
        echo "🎨 Checking Assets...\n";

        $assetPaths = [
            'modules/Inventory/resources/views',
            'modules/Inventory/resources/assets',
        ];

        foreach ($assetPaths as $path) {
            $fullPath = base_path($path);
            if (is_dir($fullPath)) {
                $this->success[] = "✅ Asset directory '{$path}' exists";
            } else {
                $this->warnings[] = "⚠️  Asset directory '{$path}' does not exist";
            }
        }
    }

    private function checkConfiguration()
    {
        echo "⚙️  Checking Configuration...\n";

        // Check if service provider is registered
        $providers = config('app.providers', []);
        $inventoryProvider = 'Modules\\Inventory\\Providers\\InventoryServiceProvider';
        
        if (in_array($inventoryProvider, $providers)) {
            $this->success[] = "✅ InventoryServiceProvider is registered";
        } else {
            $this->warnings[] = "⚠️  InventoryServiceProvider may not be registered in config/app.php";
        }

        // Check middleware
        try {
            $middleware = app('router')->getMiddleware();
            if (isset($middleware['auth'])) {
                $this->success[] = "✅ Auth middleware is available";
            } else {
                $this->warnings[] = "⚠️  Auth middleware may not be properly configured";
            }
        } catch (Exception $e) {
            $this->warnings[] = "⚠️  Could not verify middleware configuration";
        }
    }

    private function displayResults()
    {
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "📋 VERIFICATION RESULTS\n";
        echo str_repeat("=", 60) . "\n\n";

        if (!empty($this->success)) {
            echo "✅ SUCCESS (" . count($this->success) . " items):\n";
            foreach ($this->success as $item) {
                echo "   {$item}\n";
            }
            echo "\n";
        }

        if (!empty($this->warnings)) {
            echo "⚠️  WARNINGS (" . count($this->warnings) . " items):\n";
            foreach ($this->warnings as $item) {
                echo "   {$item}\n";
            }
            echo "\n";
        }

        if (!empty($this->errors)) {
            echo "❌ ERRORS (" . count($this->errors) . " items):\n";
            foreach ($this->errors as $item) {
                echo "   {$item}\n";
            }
            echo "\n";
        }

        // Summary
        $total = count($this->success) + count($this->warnings) + count($this->errors);
        $successRate = $total > 0 ? round((count($this->success) / $total) * 100, 1) : 0;

        echo str_repeat("-", 60) . "\n";
        echo "📊 SUMMARY:\n";
        echo "   Total Checks: {$total}\n";
        echo "   Success: " . count($this->success) . "\n";
        echo "   Warnings: " . count($this->warnings) . "\n";
        echo "   Errors: " . count($this->errors) . "\n";
        echo "   Success Rate: {$successRate}%\n\n";

        if (empty($this->errors)) {
            echo "🎉 Integration verification completed successfully!\n";
            echo "   The Inventory Module appears to be properly integrated.\n\n";
            echo "📝 Next Steps:\n";
            echo "   1. Run the testing checklist (TESTING_CHECKLIST.md)\n";
            echo "   2. Test the user interface manually\n";
            echo "   3. Verify database operations\n";
            echo "   4. Check browser console for JavaScript errors\n";
        } else {
            echo "🚨 Integration verification found issues!\n";
            echo "   Please address the errors above before proceeding.\n\n";
            echo "💡 Common Solutions:\n";
            echo "   - Run: php artisan migrate\n";
            echo "   - Run: php artisan route:clear\n";
            echo "   - Run: php artisan view:clear\n";
            echo "   - Run: php artisan config:clear\n";
            echo "   - Check file permissions\n";
            echo "   - Verify module structure\n";
        }

        echo str_repeat("=", 60) . "\n";
    }
}

// Run the verification
try {
    $verifier = new InventoryIntegrationVerifier();
    $verifier->verify();
} catch (Exception $e) {
    echo "❌ Fatal Error: " . $e->getMessage() . "\n";
    echo "   Please check your Laravel installation and try again.\n";
}
