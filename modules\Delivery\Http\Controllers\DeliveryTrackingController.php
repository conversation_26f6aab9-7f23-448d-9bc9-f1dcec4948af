<?php

namespace Modules\Delivery\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\Delivery\Services\DeliveryTrackingService;
use Modules\Delivery\Http\Requests\UpdateTrackingLocationRequest;
use Modules\Delivery\Http\Resources\DeliveryTrackingResource;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class DeliveryTrackingController extends Controller
{
    public function __construct(
        private DeliveryTrackingService $trackingService
    ) {}

    /**
     * Get tenant ID from authenticated user
     */
    private function getTenantId(): int
    {
        return Auth::user()->tenant_id;
    }

    /**
     * Update delivery tracking location
     */
    public function updateLocation(UpdateTrackingLocationRequest $request): JsonResponse
    {
        try {
            $tracking = $this->trackingService->recordLocationUpdate(
                $request->assignment_id,
                $request->latitude,
                $request->longitude,
                $request->notes
            );

            return response()->json([
                'success' => true,
                'message' => 'Location updated successfully',
                'data' => new DeliveryTrackingResource($tracking),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update location',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get tracking history for assignment
     */
    public function trackingHistory(Request $request, int $assignmentId): JsonResponse
    {
        try {
            $perPage = $request->get('per_page', 50);
            $history = $this->trackingService->getTrackingHistory($assignmentId, $perPage);

            return response()->json([
                'success' => true,
                'data' => DeliveryTrackingResource::collection($history),
                'meta' => [
                    'current_page' => $history->currentPage(),
                    'last_page' => $history->lastPage(),
                    'per_page' => $history->perPage(),
                    'total' => $history->total(),
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve tracking history',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get latest location for assignment
     */
    public function latestLocation(int $assignmentId): JsonResponse
    {
        try {
            $location = $this->trackingService->getLatestLocation($assignmentId);

            if (!$location) {
                return response()->json([
                    'success' => false,
                    'message' => 'No tracking data found for this assignment',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => new DeliveryTrackingResource($location),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve latest location',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get real-time tracking data for assignment
     */
    public function realTimeTracking(int $assignmentId): JsonResponse
    {
        try {
            $trackingData = $this->trackingService->getRealTimeTrackingData($assignmentId);

            return response()->json([
                'success' => true,
                'data' => $trackingData,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve real-time tracking data',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get customer tracking view
     */
    public function customerTracking(Request $request): JsonResponse
    {
        $request->validate([
            'order_id' => 'required|exists:orders,id',
            'tracking_code' => 'required|string',
        ]);

        try {
            $trackingData = $this->trackingService->getCustomerTrackingData(
                $request->order_id,
                $request->tracking_code
            );

            if (!$trackingData) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid tracking code or order not found',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $trackingData,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve customer tracking data',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get estimated arrival time
     */
    public function estimatedArrival(int $assignmentId): JsonResponse
    {
        try {
            $eta = $this->trackingService->calculateEstimatedArrival($assignmentId);

            return response()->json([
                'success' => true,
                'data' => [
                    'estimated_arrival_time' => $eta['estimated_arrival_time'],
                    'estimated_minutes' => $eta['estimated_minutes'],
                    'distance_remaining' => $eta['distance_remaining'],
                    'current_location' => $eta['current_location'],
                    'destination' => $eta['destination'],
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to calculate estimated arrival time',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get distance calculation
     */
    public function calculateDistance(Request $request): JsonResponse
    {
        $request->validate([
            'from_latitude' => 'required|numeric|between:-90,90',
            'from_longitude' => 'required|numeric|between:-180,180',
            'to_latitude' => 'required|numeric|between:-90,90',
            'to_longitude' => 'required|numeric|between:-180,180',
        ]);

        try {
            $distance = $this->trackingService->calculateDistance(
                $request->from_latitude,
                $request->from_longitude,
                $request->to_latitude,
                $request->to_longitude
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'distance_km' => $distance,
                    'distance_miles' => round($distance * 0.621371, 2),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to calculate distance',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get tracking statistics for personnel
     */
    public function personnelStatistics(Request $request, int $personnelId): JsonResponse
    {
        $request->validate([
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
        ]);

        try {
            $statistics = $this->trackingService->getPersonnelTrackingStatistics(
                $personnelId,
                $request->date_from,
                $request->date_to
            );

            return response()->json([
                'success' => true,
                'data' => $statistics,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve tracking statistics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get active deliveries with tracking
     */
    public function activeDeliveries(): JsonResponse
    {
        try {
            $activeDeliveries = $this->trackingService->getActiveDeliveriesWithTracking();

            return response()->json([
                'success' => true,
                'data' => $activeDeliveries,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve active deliveries',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get delivery route optimization
     */
    public function optimizeRoute(Request $request): JsonResponse
    {
        $request->validate([
            'personnel_id' => 'required|exists:delivery_personnel,id',
            'assignment_ids' => 'required|array|min:1',
            'assignment_ids.*' => 'exists:delivery_assignments,id',
        ]);

        try {
            $optimizedRoute = $this->trackingService->optimizeDeliveryRoute(
                $request->personnel_id,
                $request->assignment_ids
            );

            return response()->json([
                'success' => true,
                'data' => $optimizedRoute,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to optimize delivery route',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Handle location webhook from external services
     */
    public function handleLocationWebhook(Request $request): JsonResponse
    {
        try {
            // This would typically validate webhook signature and process location data
            $locationData = $request->all();
            
            // Process the webhook data
            $result = $this->trackingService->processLocationWebhook($locationData);

            return response()->json([
                'success' => true,
                'message' => 'Location webhook processed successfully',
                'data' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to process location webhook',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}