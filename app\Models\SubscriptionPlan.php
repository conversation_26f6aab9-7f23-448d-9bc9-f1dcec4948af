<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SubscriptionPlan extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'price',
        'billing_cycle',
        'trial_days',
        'max_branches',
        'max_users',
        'max_products',
        'max_orders_per_month',
        'features',
        'limitations',
        'is_active',
        'is_popular',
        'sort_order',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'price' => 'decimal:2',
            'features' => 'array',
            'limitations' => 'array',
            'is_active' => 'boolean',
            'is_popular' => 'boolean',
        ];
    }

    // Relationships
    public function tenantSubscriptions()
    {
        return $this->hasMany(TenantSubscription::class, 'plan_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopePopular($query)
    {
        return $query->where('is_popular', true);
    }

    public function scopeByBillingCycle($query, $cycle)
    {
        return $query->where('billing_cycle', $cycle);
    }

    // Methods
    public function hasFeature($feature)
    {
        return in_array($feature, $this->features ?? []);
    }

    public function hasLimitation($limitation)
    {
        return array_key_exists($limitation, $this->limitations ?? []);
    }

    public function getLimitValue($limitation)
    {
        return $this->limitations[$limitation] ?? null;
    }

    public function getMonthlyPrice()
    {
        switch ($this->billing_cycle) {
            case 'monthly':
                return $this->price;
            case 'yearly':
                return $this->price / 12;
            case 'quarterly':
                return $this->price / 3;
            default:
                return $this->price;
        }
    }

    public function getYearlyPrice()
    {
        switch ($this->billing_cycle) {
            case 'monthly':
                return $this->price * 12;
            case 'yearly':
                return $this->price;
            case 'quarterly':
                return $this->price * 4;
            default:
                return $this->price * 12;
        }
    }
}