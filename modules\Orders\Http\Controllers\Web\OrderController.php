<?php

namespace Modules\Orders\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Modules\Orders\Http\Requests\StoreOrderRequest;
use Modules\Orders\Http\Requests\UpdateOrderRequest;
use Modules\Orders\Services\OrderService;
use Modules\Kitchen\Services\KitchenService;

class OrderController extends Controller
{
    protected $orderService;
    protected $kitchenService;

    public function __construct(OrderService $orderService, KitchenService $kitchenService)
    {
        $this->orderService = $orderService;
        $this->kitchenService = $kitchenService;
    }

    /**
     * Display orders management view (Web).
     */
    public function index()
    {
        return view('orders::index');
    }

    /**
     * Display orders management view (Web) - Alternative method name.
     */
    public function indexView()
    {
        return view('orders::index');
    }

    /**
     * Show the form for creating a new order (Web).
     */
    public function create()
    {
        $user = auth()->user();

        // Get customers for the current branch
        $customers = \App\Models\Customer::where('branch_id', $user->branch_id)->get();

        // Get tables for the current branch
        $tables = \App\Models\Table::where('branch_id', $user->branch_id)->get();

        return view('orders::create', compact('customers', 'tables'));
    }

    /**
     * Store a newly created order (Web form submission).
     */
    public function store(StoreOrderRequest $request)
    {
        try {
            $user = auth()->user();
            
            // Check if user is assigned to a branch
            if (!$user->branch_id) {
                return redirect()->back()->with('error', 'User is not assigned to any branch');
            }

            // Automatically set branch_id and tenant_id from authenticated user
            $data = $request->validated();
            $data['branch_id'] = $user->branch_id;
            $data['tenant_id'] = $user->tenant_id;

            $order = $this->orderService->createOrder($data);

            return redirect()->route('orders.show', $order->id)->with('success', 'Order created successfully');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to create order: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified order (Web).
     */
    public function show(string $id)
    {
        try {
            $user = auth()->user();
            $order = $this->orderService->getOrderById($id, $user->branch_id);
            return view('orders::show', compact('order'));
        } catch (\Exception $e) {
            return redirect()->route('orders.index')->with('error', 'Order not found');
        }
    }

    /**
     * Display the specified order (Web) - Alternative method name.
     */
    public function showView(string $id)
    {
        try {
            $user = auth()->user();
            $order = $this->orderService->getOrderById($id, $user->branch_id);
            return view('orders::show', compact('order'));
        } catch (\Exception $e) {
            return redirect()->route('orders.index')->with('error', 'Order not found');
        }
    }

    /**
     * Show the form for editing the specified order (Web).
     */
    public function edit(string $id)
    {
        try {
            $user = auth()->user();
            $order = $this->orderService->getOrderById($id, $user->branch_id);

            // Get customers for the current branch
            $customers = \App\Models\Customer::where('branch_id', $user->branch_id)->get();

            // Get tables for the current branch
            $tables = \App\Models\Table::where('branch_id', $user->branch_id)->get();

            return view('orders::edit', compact('order', 'customers', 'tables'));
        } catch (\Exception $e) {
            return redirect()->route('orders.index')->with('error', 'Order not found');
        }
    }

    /**
     * Update the specified order (Web form submission).
     */
    public function update(UpdateOrderRequest $request, string $id)
    {
        try {
            $user = auth()->user();
            $order = $this->orderService->updateOrder($id, $request->validated(), $user->branch_id);

            return redirect()->route('orders.show', $order->id)->with('success', 'Order updated successfully');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to update order: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified order (Web).
     */
    public function destroy(string $id)
    {
        try {
            $user = auth()->user();
            $this->orderService->deleteOrder($id, $user->branch_id);

            return redirect()->route('orders.index')->with('success', 'Order deleted successfully');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to delete order: ' . $e->getMessage());
        }
    }

    /**
     * Get orders data for DataTable (AJAX for Web)
     */
public function getOrdersData(Request $request)
{
    $user = auth()->user();
    if (!$user->branch_id) {
        return response()->json([
            'success' => false,
            'message' => 'User is not assigned to any branch'
        ], 400);
    }

    $query = \App\Models\Order::with(['customer', 'table', 'branch'])
        ->where('branch_id', $user->branch_id);

    // Apply filters
    if ($request->filled('status')) {
        $query->where('status', $request->status);
    }

    if ($request->filled('order_type')) {
        $query->where('order_type', $request->order_type);
    }

    if ($request->filled('date')) {
        $query->whereDate('created_at', $request->date);
    }

    return \Yajra\DataTables\Facades\DataTables::of($query)
        ->addIndexColumn()
        ->addColumn('customer_name', function ($order) {
            return $order->customer ? $order->customer->name : 'عميل مجهول';
        })
        ->addColumn('table_name', function ($order) {
            return $order->table ? $order->table->name : 'غير محدد';
        })
        ->addColumn('status_badge', function ($order) {
            $statusClasses = [
                'pending' => 'bg-yellow-100 text-yellow-800',
                'confirmed' => 'bg-blue-100 text-blue-800',
                'preparing' => 'bg-indigo-100 text-indigo-800',
                'ready' => 'bg-green-100 text-green-800',
                'served' => 'bg-gray-200 text-gray-800',
                'completed' => 'bg-green-100 text-green-800',
                'cancelled' => 'bg-red-100 text-red-800'
            ];
            $statusTexts = [
                'pending' => 'معلق',
                'confirmed' => 'مؤكد',
                'preparing' => 'قيد التحضير',
                'ready' => 'جاهز',
                'served' => 'تم التقديم',
                'completed' => 'مكتمل',
                'cancelled' => 'ملغي'
            ];
            $class = $statusClasses[$order->status] ?? 'bg-gray-100 text-gray-800';
            $text = $statusTexts[$order->status] ?? $order->status;
            return '<span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ' . $class . '">' . $text . '</span>';
        })
        ->addColumn('type_badge', function ($order) {
            $typeClasses = [
                'dine_in' => 'bg-green-100 text-green-800',
                'takeaway' => 'bg-yellow-100 text-yellow-800',
                'delivery' => 'bg-blue-100 text-blue-800',
                'online' => 'bg-indigo-100 text-indigo-800'
            ];
            $typeTexts = [
                'dine_in' => 'تناول في المطعم',
                'takeaway' => 'طلب خارجي',
                'delivery' => 'توصيل',
                'online' => 'طلب أونلاين'
            ];
            $class = $typeClasses[$order->order_type] ?? 'bg-gray-100 text-gray-800';
            $text = $typeTexts[$order->order_type] ?? $order->order_type;
            return '<span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ' . $class . '">' . $text . '</span>';
        })
        ->addColumn('formatted_total', function ($order) {
            return '$' . number_format($order->total_amount, 2);
        })
        ->addColumn('formatted_date', function ($order) {
            return $order->created_at->format('Y-m-d H:i');
        })
        ->addColumn('action', function ($order) {
            $actions = '<div class="flex gap-1 justify-center">';
            $actions .= '<a href="' . route('orders.show', $order->id) . '" class="text-blue-600 hover:text-blue-800 text-sm p-1" title="عرض"><i class="mdi mdi-eye"></i></a>';
            $actions .= '<a href="' . route('orders.edit', $order->id) . '" class="text-yellow-600 hover:text-yellow-800 text-sm p-1" title="تعديل"><i class="mdi mdi-pencil"></i></a>';
            $actions .= '<button class="text-red-600 hover:text-red-800 text-sm p-1 delete-order" data-id="' . $order->id . '" title="حذف"><i class="mdi mdi-delete"></i></button>';
            if ($order->status !== 'cancelled') {
                $actions .= '<a href="' . route('pos.orders.kot', $order->id) . '" class="text-green-600 hover:text-green-800 text-sm p-1" title="طباعة KOT"><i class="mdi mdi-printer"></i></a>';
            }
            $actions .= '</div>';
            return $actions;
        })
        ->rawColumns(['status_badge', 'type_badge', 'action'])
        ->make(true);
}


    /**
     * Get orders statistics for web view (AJAX)
     */
    public function getStatistics(Request $request)
    {
        try {
            $user = auth()->user();

            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $today = now()->toDateString();
            $branchId = $user->branch_id;

            $statistics = [
                'total_today' => \App\Models\Order::where('branch_id', $branchId)
                    ->whereDate('created_at', $today)
                    ->count(),
                'pending' => \App\Models\Order::where('branch_id', $branchId)
                    ->where('status', 'pending')
                    ->count(),
                'completed' => \App\Models\Order::where('branch_id', $branchId)
                    ->where('status', 'completed')
                    ->count(),
                'sales_today' => \App\Models\Order::where('branch_id', $branchId)
                    ->whereDate('created_at', $today)
                    ->where('status', '!=', 'cancelled')
                    ->sum('total_amount')
            ];

            return response()->json([
                'success' => true,
                'statistics' => $statistics
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve statistics: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get orders for cards view (AJAX for Web)
     */
    public function getCardsData(Request $request)
    {
        try {
            $user = auth()->user();

            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $query = \App\Models\Order::with(['customer', 'table', 'branch'])
                ->where('branch_id', $user->branch_id);

            // Apply filters
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            if ($request->filled('order_type')) {
                $query->where('order_type', $request->order_type);
            }

            if ($request->filled('date')) {
                $query->whereDate('created_at', $request->date);
            }

            // Order by latest first
            $query->orderBy('created_at', 'desc');

            // Paginate
            $perPage = $request->get('per_page', 12);
            $orders = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $orders->items(),
                'current_page' => $orders->currentPage(),
                'last_page' => $orders->lastPage(),
                'per_page' => $orders->perPage(),
                'total' => $orders->total()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve orders: ' . $e->getMessage()
            ], 500);
        }
    }
}