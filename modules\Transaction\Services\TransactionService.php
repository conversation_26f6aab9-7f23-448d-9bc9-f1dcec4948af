<?php

namespace Modules\Transaction\Services;

use App\Models\Order;
use Modules\Transaction\Models\Transaction;
use Modules\Transaction\Models\Payment;
use Modules\Transaction\Models\PaymentMethod;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class TransactionService
{
    /**
     * Get all transactions with pagination and filters
     */
    public function getAllTransactions(array $filters = []): LengthAwarePaginator
    {
        $query = Transaction::with(['order', 'payments.paymentMethod', 'createdBy']);

        // Apply filters
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['order_id'])) {
            $query->where('order_id', $filters['order_id']);
        }

        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        if (isset($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('transaction_number', 'like', '%' . $filters['search'] . '%')
                  ->orWhereHas('order', function ($orderQuery) use ($filters) {
                      $orderQuery->where('order_number', 'like', '%' . $filters['search'] . '%');
                  });
            });
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortBy, $sortDirection);

        // Pagination
        $perPage = $filters['per_page'] ?? 15;
        
        return $query->paginate($perPage);
    }

    /**
     * Get transaction by ID with relationships.
     */
    public function getTransactionById(int $id): ?Transaction
    {
        return Transaction::with([
            'order.customer',
            'order.table',
            'payments.paymentMethod',
            'payments.processedBy',
            'createdBy',
            'updatedBy'
        ])->find($id);
    }

    /**
     * Create a new transaction from order.
     */
    public function createTransactionFromOrder(Order $order): Transaction
    {
        DB::beginTransaction();
        try {
            $transaction = Transaction::create([
                'order_id' => $order->id,
                'transaction_number' => $this->generateTransactionNumber(),
                'total_amount' => $order->total_amount,
                'paid_amount' => 0,
                'due_amount' => $order->total_amount,
                'status' => 'due',
                'tax_amount' => $order->tax_amount ?? 0,
                'discount_amount' => $order->discount_amount ?? 0,
                'service_charge' => $order->service_charge ?? 0,
                'created_by' => Auth::id(),
            ]);

            DB::commit();
            return $transaction->load(['order', 'payments']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Create a standalone transaction without an order.
     */
    public function createStandaloneTransaction(array $data): Transaction
    {
        DB::beginTransaction();
        try {
            $transactionData = [
                'order_id' => null,
                'transaction_number' => $this->generateTransactionNumber(),
                'total_amount' => $data['total_amount'],
                'paid_amount' => $data['paid_amount'] ?? 0,
                'due_amount' => $data['total_amount'] - ($data['paid_amount'] ?? 0),
                'status' => $data['status'] ?? 'due',
                'tax_amount' => $data['tax_amount'] ?? 0,
                'discount_amount' => $data['discount_amount'] ?? 0,
                'service_charge' => $data['service_charge'] ?? 0,
                'notes' => $data['notes'] ?? null,
                'created_by' => Auth::id(),
            ];

            // Determine status based on payment amounts
            if ($transactionData['paid_amount'] <= 0) {
                $transactionData['status'] = 'due';
            } elseif ($transactionData['paid_amount'] >= $transactionData['total_amount']) {
                $transactionData['status'] = $transactionData['paid_amount'] > $transactionData['total_amount'] ? 'overpaid' : 'paid';
                $transactionData['due_amount'] = max(0, $transactionData['due_amount']);
            } else {
                $transactionData['status'] = 'partially_paid';
            }

            $transaction = Transaction::create($transactionData);

            DB::commit();
            return $transaction->load(['payments']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Update transaction status based on payments.
     */
    public function updateTransactionStatus(Transaction $transaction): Transaction
    {
        DB::beginTransaction();
        try {
            $completedPayments = $transaction->completedPayments;
            $totalPaid = $completedPayments->sum('amount');
            
            $transaction->paid_amount = $totalPaid;
            $transaction->due_amount = $transaction->total_amount - $totalPaid;

            // Determine status
            if ($totalPaid <= 0) {
                $transaction->status = 'due';
            } elseif ($totalPaid >= $transaction->total_amount) {
                $transaction->status = $totalPaid > $transaction->total_amount ? 'overpaid' : 'paid';
                $transaction->due_amount = max(0, $transaction->due_amount);
            } else {
                $transaction->status = 'partially_paid';
            }

            $transaction->updated_by = Auth::id();
            $transaction->save();

            DB::commit();
            return $transaction->fresh();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Calculate due amount for transaction.
     */
    public function calculateDueAmount(Transaction $transaction): float
    {
        $totalPaid = $transaction->completedPayments->sum('amount');
        return max(0, $transaction->total_amount - $totalPaid);
    }

    /**
     * Get transaction statistics.
     */
    public function getTransactionStatistics(array $filters = []): array
    {
        $query = Transaction::query();

        // Apply date filters
        if (isset($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        $stats = [
            'total_transactions' => $query->count(),
            'total_amount' => $query->sum('total_amount'),
            'total_paid' => $query->sum('paid_amount'),
            'total_due' => $query->sum('due_amount'),
            'by_status' => $query->groupBy('status')
                ->selectRaw('status, count(*) as count, sum(total_amount) as amount')
                ->get()
                ->keyBy('status'),
        ];

        return $stats;
    }

    /**
     * Generate unique transaction number.
     */
    public function generateTransactionNumber(): string
    {
        $prefix = 'TXN';
        $timestamp = Carbon::now()->format('YmdHis');
        $random = str_pad(mt_rand(1, 999), 3, '0', STR_PAD_LEFT);
        
        return $prefix . $timestamp . $random;
    }

    /**
     * Void a transaction.
     */
    public function voidTransaction(Transaction $transaction, string $reason = null): Transaction
    {
        DB::beginTransaction();
        try {
            // Cancel all pending payments
            $transaction->payments()->where('status', 'pending')->update([
                'status' => 'cancelled',
                'notes' => 'Transaction voided: ' . $reason,
            ]);

            $transaction->update([
                'status' => 'cancelled',
                'notes' => $transaction->notes . "\nVoided: " . $reason,
                'updated_by' => Auth::id(),
            ]);

            DB::commit();
            return $transaction->fresh();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get transactions due for payment.
     */
    public function getDueTransactions(array $filters = []): Collection
    {
        $query = Transaction::with(['order', 'payments'])
            ->where('status', 'due')
            ->orWhere('status', 'partially_paid');

        if (isset($filters['limit'])) {
            $query->limit($filters['limit']);
        }

        return $query->orderBy('created_at', 'asc')->get();
    }
}
