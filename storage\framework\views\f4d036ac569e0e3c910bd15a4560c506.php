<?php $__env->startSection('title', 'تفاصيل المستأجر'); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h1 class="text-2xl font-bold text-gray-900 flex items-center gap-3">
                    <i class="fas fa-user text-blue-600"></i>
                    تفاصيل المستأجر: <?php echo e($tenant->name); ?>

                </h1>
                <div class="flex gap-2">
                    <a href="<?php echo e(route('tenants.edit', $tenant->id)); ?>" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                        <i class="fas fa-edit text-sm"></i>
                        تعديل
                    </a>
                    <a href="<?php echo e(route('tenants.index')); ?>" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                        <i class="fas fa-arrow-left text-sm"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="fas fa-info-circle text-blue-600"></i>
                        المعلومات الأساسية
                    </h2>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">اسم المستأجر</label>
                            <p class="text-gray-900 font-semibold"><?php echo e($tenant->name); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">الكود</label>
                            <p class="text-gray-900"><?php echo e($tenant->code ?? 'غير محدد'); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">نوع النشاط</label>
                            <p class="text-gray-900">
                                <?php switch($tenant->business_type):
                                    case ('restaurant'): ?>
                                        مطعم
                                        <?php break; ?>
                                    <?php case ('cafe'): ?>
                                        مقهى
                                        <?php break; ?>
                                    <?php case ('bakery'): ?>
                                        مخبز
                                        <?php break; ?>
                                    <?php case ('fast_food'): ?>
                                        وجبات سريعة
                                        <?php break; ?>
                                    <?php case ('catering'): ?>
                                        تموين
                                        <?php break; ?>
                                    <?php default: ?>
                                        <?php echo e($tenant->business_type); ?>

                                <?php endswitch; ?>
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">الدولة</label>
                            <p class="text-gray-900"><?php echo e($tenant->country->name ?? 'غير محدد'); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="fas fa-address-book text-blue-600"></i>
                        معلومات الاتصال
                    </h2>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">اسم جهة الاتصال</label>
                            <p class="text-gray-900"><?php echo e($tenant->primary_contact_name); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">البريد الإلكتروني</label>
                            <p class="text-gray-900">
                                <a href="mailto:<?php echo e($tenant->contact_email); ?>" class="text-blue-600 hover:text-blue-800">
                                    <?php echo e($tenant->contact_email); ?>

                                </a>
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">رقم الهاتف</label>
                            <p class="text-gray-900">
                                <a href="tel:<?php echo e($tenant->contact_phone); ?>" class="text-blue-600 hover:text-blue-800">
                                    <?php echo e($tenant->contact_phone); ?>

                                </a>
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">الموقع الإلكتروني</label>
                            <p class="text-gray-900">
                                <?php if($tenant->website_url): ?>
                                    <a href="<?php echo e($tenant->website_url); ?>" target="_blank" class="text-blue-600 hover:text-blue-800">
                                        <?php echo e($tenant->website_url); ?>

                                        <i class="fas fa-external-link-alt text-xs ml-1"></i>
                                    </a>
                                <?php else: ?>
                                    غير محدد
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>
                    <div class="mt-6">
                        <label class="block text-sm font-medium text-gray-600 mb-1">عنوان النشاط</label>
                        <p class="text-gray-900"><?php echo e($tenant->business_address); ?></p>
                    </div>
                </div>
            </div>

            <!-- Business Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="fas fa-building text-blue-600"></i>
                        معلومات النشاط
                    </h2>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">الرقم الضريبي</label>
                            <p class="text-gray-900"><?php echo e($tenant->tax_number ?? 'غير محدد'); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">رخصة النشاط</label>
                            <p class="text-gray-900"><?php echo e($tenant->business_license ?? 'غير محدد'); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Settings -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="fas fa-cog text-blue-600"></i>
                        إعدادات النظام
                    </h2>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">المنطقة الزمنية</label>
                            <p class="text-gray-900"><?php echo e($tenant->timezone); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">العملة</label>
                            <p class="text-gray-900"><?php echo e($tenant->currency_code); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">اللغة</label>
                            <p class="text-gray-900">
                                <?php echo e($tenant->language_code == 'ar' ? 'العربية' : 'English'); ?>

                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Status Card -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="fas fa-chart-line text-blue-600"></i>
                        الحالة والإحصائيات
                    </h2>
                </div>
                <div class="p-6 space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-600 mb-1">الحالة</label>
                        <div class="flex items-center gap-2">
                            <?php if($tenant->status == 'active'): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i>
                                    نشط
                                </span>
                            <?php elseif($tenant->status == 'inactive'): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <i class="fas fa-times-circle mr-1"></i>
                                    غير نشط
                                </span>
                            <?php elseif($tenant->status == 'suspended'): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    <i class="fas fa-pause-circle mr-1"></i>
                                    معلق
                                </span>
                            <?php elseif($tenant->status == 'trial'): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <i class="fas fa-clock mr-1"></i>
                                    تجريبي
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-600 mb-1">عدد الفروع</label>
                        <p class="text-2xl font-bold text-blue-600"><?php echo e($tenant->branches_count ?? 0); ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-600 mb-1">عدد المستخدمين</label>
                        <p class="text-2xl font-bold text-green-600"><?php echo e($tenant->users_count ?? 0); ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-600 mb-1">تاريخ الإنشاء</label>
                        <p class="text-gray-900"><?php echo e($tenant->created_at->format('Y-m-d')); ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-600 mb-1">آخر تحديث</label>
                        <p class="text-gray-900"><?php echo e($tenant->updated_at->format('Y-m-d H:i')); ?></p>
                    </div>
                </div>
            </div>

            <!-- Actions Card -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="fas fa-tools text-blue-600"></i>
                        الإجراءات
                    </h2>
                </div>
                <div class="p-6 space-y-3">
                    <?php if($tenant->status == 'active'): ?>
                        <button onclick="changeStatus(<?php echo e($tenant->id); ?>, 'deactivate')" 
                                class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                            <i class="fas fa-times-circle text-sm"></i>
                            إلغاء التفعيل
                        </button>
                        <button onclick="changeStatus(<?php echo e($tenant->id); ?>, 'suspend')" 
                                class="w-full bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                            <i class="fas fa-pause-circle text-sm"></i>
                            تعليق
                        </button>
                    <?php elseif($tenant->status == 'inactive'): ?>
                        <button onclick="changeStatus(<?php echo e($tenant->id); ?>, 'activate')" 
                                class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                            <i class="fas fa-check-circle text-sm"></i>
                            تفعيل
                        </button>
                    <?php elseif($tenant->status == 'suspended'): ?>
                        <button onclick="changeStatus(<?php echo e($tenant->id); ?>, 'activate')" 
                                class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                            <i class="fas fa-play-circle text-sm"></i>
                            إعادة التفعيل
                        </button>
                    <?php endif; ?>
                    
                    <a href="<?php echo e(route('tenants.edit', $tenant->id)); ?>" 
                       class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                        <i class="fas fa-edit text-sm"></i>
                        تعديل المستأجر
                    </a>
                    
                    <button onclick="deleteTenant(<?php echo e($tenant->id); ?>)" 
                            class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                        <i class="fas fa-trash text-sm"></i>
                        حذف المستأجر
                    </button>
                </div>
            </div>

            <!-- Quick Links -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="fas fa-link text-blue-600"></i>
                        روابط سريعة
                    </h2>
                </div>
                <div class="p-6 space-y-3">
                    <a href="<?php echo e(route('branches.index', ['tenant_id' => $tenant->id])); ?>" 
                       class="w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                        <i class="fas fa-store text-sm"></i>
                        إدارة الفروع
                    </a>
                    
                    <a href="<?php echo e(route('subscriptions.index', ['tenant_id' => $tenant->id])); ?>" 
                       class="w-full bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                        <i class="fas fa-credit-card text-sm"></i>
                        إدارة الاشتراكات
                    </a>
                    
                    <a href="<?php echo e(route('users.index', ['tenant_id' => $tenant->id])); ?>" 
                       class="w-full bg-teal-600 hover:bg-teal-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                        <i class="fas fa-users text-sm"></i>
                        إدارة المستخدمين
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function changeStatus(tenantId, action) {
    const actions = {
        'activate': 'تفعيل',
        'deactivate': 'إلغاء تفعيل',
        'suspend': 'تعليق'
    };
    
    if (confirm(`هل أنت متأكد من ${actions[action]} هذا المستأجر؟`)) {
        fetch(`/admin/tenants/${tenantId}/${action}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`تم ${actions[action]} المستأجر بنجاح`);
                location.reload();
            } else {
                alert(data.message || `حدث خطأ أثناء ${actions[action]} المستأجر`);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert(`حدث خطأ أثناء ${actions[action]} المستأجر`);
        });
    }
}

function deleteTenant(tenantId) {
    if (confirm('هل أنت متأكد من حذف هذا المستأجر؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        fetch(`/admin/tenants/${tenantId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم حذف المستأجر بنجاح');
                window.location.href = '<?php echo e(route("tenants.index")); ?>';
            } else {
                alert(data.message || 'حدث خطأ أثناء حذف المستأجر');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء حذف المستأجر');
        });
    }
}
</script>

<?php if(session('success')): ?>
    <script>
        alert('<?php echo e(session('success')); ?>');
    </script>
<?php endif; ?>

<?php if(session('error')): ?>
    <script>
        alert('<?php echo e(session('error')); ?>');
    </script>
<?php endif; ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Tenant\Providers/../resources/views/tenants/show.blade.php ENDPATH**/ ?>