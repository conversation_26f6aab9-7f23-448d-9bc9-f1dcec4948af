<?php

namespace Modules\Reservation\Contracts;

interface ReservationServiceInterface
{
    /**
     * Get all reservations with optional filters.
     */
    public function getAllReservations(array $filters = [], int $perPage = 15);

    /**
     * Create a new reservation.
     */
    public function createReservation(array $data);

    /**
     * Get reservation by ID.
     */
    public function getReservationById(int $id);

    /**
     * Update reservation.
     */
    public function updateReservation(int $id, array $data);

    /**
     * Cancel reservation.
     */
    public function cancelReservation(int $id, string $reason = null);

    /**
     * Confirm reservation.
     */
    public function confirmReservation(int $id);

    /**
     * Mark reservation as seated.
     */
    public function seatReservation(int $id, int $tableId = null);

    /**
     * Complete reservation.
     */
    public function completeReservation(int $id);

    /**
     * Mark reservation as no-show.
     */
    public function markAsNoShow(int $id);

    /**
     * Check if table is available for given time.
     */
    public function checkAvailability(int $branchId, string $datetime, int $duration = 120, int $partySize = 1);

    /**
     * Get reservation statistics.
     */
    public function getReservationStats(int $branchId = null, string $dateFrom = null, string $dateTo = null);

    /**
     * Generate reservation number.
     */
    public function generateReservationNumber(int $branchId);
}