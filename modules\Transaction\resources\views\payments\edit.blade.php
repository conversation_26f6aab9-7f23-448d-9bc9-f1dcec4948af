@extends('layouts.master')

@section('title', 'Edit Payment')

@section('styles')
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
    .select2-container--default .select2-selection--single {
        height: 38px;
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        padding: 0.5rem 0.75rem;
    }
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 22px;
        color: #374151;
    }
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 36px;
    }
    .select2-dropdown {
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
    }
    .select2-container--default .select2-results__option--highlighted[aria-selected] {
        background-color: #3b82f6;
    }
</style>
@endsection

@section('content')
<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-edit mr-3"></i>
                Edit Payment
            </h1>
            <p class="text-sm text-gray-600 mt-1">Update payment information</p>
        </div>
        <div class="mt-4 sm:mt-0">
            <a href="{{ route('payments.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Payments
            </a>
        </div>
    </div>

    <!-- Edit Payment Form -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Payment Information</h3>
        </div>
        <form id="edit-payment-form" action="{{ route('payments.update', $payment->id) }}" method="POST">
            @csrf
            @method('PUT')
            <div class="p-6 space-y-6">
                <!-- Current Payment Info -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">Current Payment Details</h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                            <span class="text-gray-600">Payment Number:</span>
                            <span class="ml-2 font-medium">{{ $payment->payment_number }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">Current Amount:</span>
                            <span class="ml-2 font-medium">${{ number_format($payment->amount, 2) }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">Status:</span>
                            <span class="ml-2 font-medium">{{ ucfirst($payment->status) }}</span>
                        </div>
                    </div>
                </div>

                <!-- Transaction Selection (Read-only) -->
                <div>
                    <label for="transaction_id" class="block text-sm font-medium text-gray-700 mb-2">Transaction</label>
                    <div class="relative">
                        <input type="hidden" name="transaction_id" value="{{ $payment->transaction_id }}">
                        <div class="w-full border border-gray-300 rounded-md px-3 py-2 bg-gray-50 text-gray-600">
                            @if($payment->transaction)
                                {{ $payment->transaction->transaction_number }} 
                                @if($payment->transaction->order)
                                    (Order: {{ $payment->transaction->order->order_number }})
                                @endif
                                - ${{ number_format($payment->transaction->total_amount, 2) }}
                            @else
                                No transaction associated
                            @endif
                        </div>
                    </div>
                    <p class="mt-1 text-xs text-gray-500">Transaction cannot be changed after payment creation</p>
                </div>

                <!-- Payment Method -->
                <div>
                    <label for="payment_method_id" class="block text-sm font-medium text-gray-700 mb-2">Payment Method <span class="text-red-500">*</span></label>
                    <select id="payment_method_id" name="payment_method_id" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" required>
                        <option value="">Select Payment Method</option>
                        @foreach($paymentMethods as $method)
                            <option value="{{ $method->id }}" {{ $payment->payment_method_id == $method->id ? 'selected' : '' }}>
                                {{ $method->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('payment_method_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Amount -->
                <div>
                    <label for="amount" class="block text-sm font-medium text-gray-700 mb-2">Amount <span class="text-red-500">*</span></label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span class="text-gray-500 sm:text-sm">$</span>
                        </div>
                        <input type="number" id="amount" name="amount" step="0.01" min="0" value="{{ old('amount', $payment->amount) }}" class="w-full border border-gray-300 rounded-md pl-7 pr-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="0.00" required>
                    </div>
                    @if($payment->transaction)
                        <p class="mt-1 text-xs text-gray-500">
                            Transaction total: ${{ number_format($payment->transaction->total_amount, 2) }} | 
                            Due amount: ${{ number_format($payment->transaction->due_amount, 2) }}
                        </p>
                    @endif
                    @error('amount')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Reference Number -->
                <div>
                    <label for="reference_number" class="block text-sm font-medium text-gray-700 mb-2">Reference Number</label>
                    <input type="text" id="reference_number" name="reference_number" value="{{ old('reference_number', $payment->reference_number) }}" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Enter reference number (optional)">
                    <p class="mt-1 text-xs text-gray-500">Transaction ID, check number, or other reference</p>
                    @error('reference_number')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Change Amount -->
                <div>
                    <label for="change_amount" class="block text-sm font-medium text-gray-700 mb-2">Change Amount</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span class="text-gray-500 sm:text-sm">$</span>
                        </div>
                        <input type="number" id="change_amount" name="change_amount" step="0.01" min="0" value="{{ old('change_amount', $payment->change_amount) }}" class="w-full border border-gray-300 rounded-md pl-7 pr-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="0.00">
                    </div>
                    <p class="mt-1 text-xs text-gray-500">Amount returned to customer (if applicable)</p>
                    @error('change_amount')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Status -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status <span class="text-red-500">*</span></label>
                    <select id="status" name="status" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" required>
                        <option value="pending" {{ $payment->status == 'pending' ? 'selected' : '' }}>Pending</option>
                        <option value="completed" {{ $payment->status == 'completed' ? 'selected' : '' }}>Completed</option>
                        <option value="failed" {{ $payment->status == 'failed' ? 'selected' : '' }}>Failed</option>
                        <option value="cancelled" {{ $payment->status == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                        <option value="refunded" {{ $payment->status == 'refunded' ? 'selected' : '' }}>Refunded</option>
                    </select>
                    @error('status')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Payment Date -->
                <div>
                    <label for="payment_date" class="block text-sm font-medium text-gray-700 mb-2">Payment Date</label>
                    <input type="datetime-local" id="payment_date" name="payment_date" value="{{ old('payment_date', $payment->payment_date ? $payment->payment_date->format('Y-m-d\TH:i') : '') }}" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    @error('payment_date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Notes -->
                <div>
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                    <textarea id="notes" name="notes" rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Enter any additional notes...">{{ old('notes', $payment->notes) }}</textarea>
                    @error('notes')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Payment Details (JSON) -->
                <div>
                    <label for="payment_details" class="block text-sm font-medium text-gray-700 mb-2">Payment Details (JSON)</label>
                    <textarea id="payment_details" name="payment_details" rows="4" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono" placeholder='{"key": "value"}'>{{ old('payment_details', $payment->payment_details ? json_encode($payment->payment_details, JSON_PRETTY_PRINT) : '') }}</textarea>
                    <p class="mt-1 text-xs text-gray-500">Additional payment information in JSON format (optional)</p>
                    @error('payment_details')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Form Actions -->
            <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
                <a href="{{ route('payments.show', $payment->id) }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Cancel
                </a>
                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="fas fa-save mr-2"></i>
                    Update Payment
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
$(document).ready(function() {
    // Initialize Select2
    $('#payment_method_id').select2({
        placeholder: 'Select Payment Method',
        allowClear: true
    });

    // Form validation
    $('#edit-payment-form').on('submit', function(e) {
        var isValid = true;
        var errors = [];

        // Validate amount
        var amount = parseFloat($('#amount').val());
        if (!amount || amount <= 0) {
            errors.push('Amount must be greater than 0');
            isValid = false;
        }

        // Validate payment method
        if (!$('#payment_method_id').val()) {
            errors.push('Payment method is required');
            isValid = false;
        }

        // Validate status
        if (!$('#status').val()) {
            errors.push('Status is required');
            isValid = false;
        }

        // Validate JSON format for payment details
        var paymentDetails = $('#payment_details').val().trim();
        if (paymentDetails) {
            try {
                JSON.parse(paymentDetails);
            } catch (e) {
                errors.push('Payment details must be valid JSON format');
                isValid = false;
            }
        }

        if (!isValid) {
            e.preventDefault();
            Swal.fire({
                title: 'Validation Error',
                html: errors.join('<br>'),
                icon: 'error'
            });
            return false;
        }

        // Show loading state
        $(this).find('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Updating...');
    });

    // Auto-format JSON in payment details
    $('#payment_details').on('blur', function() {
        var value = $(this).val().trim();
        if (value) {
            try {
                var parsed = JSON.parse(value);
                $(this).val(JSON.stringify(parsed, null, 2));
                $(this).removeClass('border-red-300').addClass('border-gray-300');
            } catch (e) {
                $(this).removeClass('border-gray-300').addClass('border-red-300');
            }
        }
    });

    // Validate change amount doesn't exceed payment amount
    $('#change_amount').on('input', function() {
        var changeAmount = parseFloat($(this).val()) || 0;
        var paymentAmount = parseFloat($('#amount').val()) || 0;
        
        if (changeAmount > paymentAmount) {
            $(this).val(paymentAmount.toFixed(2));
        }
    });

    // Update change amount when payment amount changes
    $('#amount').on('input', function() {
        var paymentAmount = parseFloat($(this).val()) || 0;
        var changeAmount = parseFloat($('#change_amount').val()) || 0;
        
        if (changeAmount > paymentAmount) {
            $('#change_amount').val(paymentAmount.toFixed(2));
        }
    });

    // Set default payment date to now if empty
    if (!$('#payment_date').val()) {
        var now = new Date();
        var year = now.getFullYear();
        var month = String(now.getMonth() + 1).padStart(2, '0');
        var day = String(now.getDate()).padStart(2, '0');
        var hours = String(now.getHours()).padStart(2, '0');
        var minutes = String(now.getMinutes()).padStart(2, '0');
        
        $('#payment_date').val(`${year}-${month}-${day}T${hours}:${minutes}`);
    }
});
</script>
@endpush