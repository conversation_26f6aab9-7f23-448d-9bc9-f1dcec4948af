<?php

namespace Modules\HR\Services;

use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * Staff Service - Handles staff management operations
 * This service manages staff CRUD operations, profile management,
 * and staff-related business logic.
 */
class StaffService
{
    /**
     * Get all staff members with filters and pagination
     */
    public function getStaff(int $tenantId, array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = User::where('tenant_id', $tenantId)
            ->where('role', 'staff')
            ->with(['branch', 'position', 'department']);

        // Apply filters
        $this->applyFilters($query, $filters);

        return $query->orderBy('name')->paginate($perPage);
    }

    /**
     * Get staff member by ID
     */
    public function getStaffById(int $staffId, int $tenantId): ?User
    {
        return User::where('id', $staffId)
            ->where('tenant_id', $tenantId)
            ->where('role', 'staff')
            ->with(['branch', 'position', 'department', 'emergencyContacts'])
            ->first();
    }

    /**
     * Create new staff member
     */
    public function createStaff(int $tenantId, array $data): User
    {
        DB::beginTransaction();
        try {
            // Prepare staff data
            $staffData = $this->prepareStaffData($data, $tenantId);
            
            // Create staff member
            $staff = User::create($staffData);

            // Create emergency contacts if provided
            if (!empty($data['emergency_contacts'])) {
                $this->createEmergencyContacts($staff->id, $data['emergency_contacts']);
            }

            // Assign default permissions/roles if needed
            $this->assignDefaultPermissions($staff);

            DB::commit();
            return $staff->load(['branch', 'position', 'department']);
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Update staff member
     */
    public function updateStaff(int $staffId, int $tenantId, array $data): User
    {
        DB::beginTransaction();
        try {
            $staff = $this->getStaffById($staffId, $tenantId);
            
            if (!$staff) {
                throw new \Exception('Staff member not found');
            }

            // Prepare update data
            $updateData = $this->prepareStaffData($data, $tenantId, false);
            
            // Update staff member
            $staff->update($updateData);

            // Update emergency contacts if provided
            if (isset($data['emergency_contacts'])) {
                $this->updateEmergencyContacts($staff->id, $data['emergency_contacts']);
            }

            DB::commit();
            return $staff->fresh(['branch', 'position', 'department']);
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Delete staff member (soft delete)
     */
    public function deleteStaff(int $staffId, int $tenantId): bool
    {
        $staff = $this->getStaffById($staffId, $tenantId);
        
        if (!$staff) {
            throw new \Exception('Staff member not found');
        }

        // Check if staff has active assignments
        if ($this->hasActiveAssignments($staffId)) {
            throw new \Exception('Cannot delete staff member with active assignments');
        }

        return $staff->delete();
    }

    /**
     * Activate staff member
     */
    public function activateStaff(int $staffId, int $tenantId): bool
    {
        $staff = $this->getStaffById($staffId, $tenantId);
        
        if (!$staff) {
            throw new \Exception('Staff member not found');
        }

        return $staff->update(['status' => 'active']);
    }

    /**
     * Deactivate staff member
     */
    public function deactivateStaff(int $staffId, int $tenantId, ?string $reason = null): bool
    {
        $staff = $this->getStaffById($staffId, $tenantId);
        
        if (!$staff) {
            throw new \Exception('Staff member not found');
        }

        return $staff->update([
            'status' => 'inactive',
            'deactivation_reason' => $reason,
            'deactivated_at' => now()
        ]);
    }

    /**
     * Get staff by branch
     */
    public function getStaffByBranch(int $branchId, int $tenantId): Collection
    {
        return User::where('tenant_id', $tenantId)
            ->where('branch_id', $branchId)
            ->where('role', 'staff')
            ->where('status', 'active')
            ->with(['position', 'department'])
            ->orderBy('name')
            ->get();
    }

    /**
     * Get staff by department
     */
    public function getStaffByDepartment(int $departmentId, int $tenantId): Collection
    {
        return User::where('tenant_id', $tenantId)
            ->where('department_id', $departmentId)
            ->where('role', 'staff')
            ->where('status', 'active')
            ->with(['branch', 'position'])
            ->orderBy('name')
            ->get();
    }

    /**
     * Get staff by position
     */
    public function getStaffByPosition(int $positionId, int $tenantId): Collection
    {
        return User::where('tenant_id', $tenantId)
            ->where('position_id', $positionId)
            ->where('role', 'staff')
            ->where('status', 'active')
            ->with(['branch', 'department'])
            ->orderBy('name')
            ->get();
    }

    /**
     * Update staff profile
     */
    public function updateProfile(int $staffId, int $tenantId, array $data): User
    {
        $staff = $this->getStaffById($staffId, $tenantId);
        
        if (!$staff) {
            throw new \Exception('Staff member not found');
        }

        // Only allow certain fields to be updated in profile
        $allowedFields = [
            'phone', 'address', 'emergency_contact_name', 
            'emergency_contact_phone', 'emergency_contact_relationship'
        ];

        $updateData = array_intersect_key($data, array_flip($allowedFields));
        
        $staff->update($updateData);
        
        return $staff->fresh();
    }

    /**
     * Change staff password
     */
    public function changePassword(int $staffId, int $tenantId, string $newPassword): bool
    {
        $staff = $this->getStaffById($staffId, $tenantId);
        
        if (!$staff) {
            throw new \Exception('Staff member not found');
        }

        return $staff->update([
            'password' => Hash::make($newPassword),
            'password_changed_at' => now()
        ]);
    }

    /**
     * Get staff statistics
     */
    public function getStaffStats(int $tenantId, ?int $branchId = null): array
    {
        $query = User::where('tenant_id', $tenantId)->where('role', 'staff');
        
        if ($branchId) {
            $query->where('branch_id', $branchId);
        }

        $totalStaff = $query->count();
        $activeStaff = $query->where('status', 'active')->count();
        $inactiveStaff = $query->where('status', 'inactive')->count();
        $newHires = $query->where('created_at', '>=', now()->subDays(30))->count();

        // Staff by department
        $byDepartment = $query->with('department')
            ->get()
            ->groupBy('department.name')
            ->map(function ($group) {
                return $group->count();
            });

        // Staff by position
        $byPosition = $query->with('position')
            ->get()
            ->groupBy('position.name')
            ->map(function ($group) {
                return $group->count();
            });

        return [
            'total_staff' => $totalStaff,
            'active_staff' => $activeStaff,
            'inactive_staff' => $inactiveStaff,
            'new_hires_30_days' => $newHires,
            'by_department' => $byDepartment,
            'by_position' => $byPosition,
            'activity_rate' => $totalStaff > 0 ? round(($activeStaff / $totalStaff) * 100, 2) : 0
        ];
    }

    /**
     * Apply filters to query
     */
    private function applyFilters($query, array $filters): void
    {
        if (!empty($filters['branch_id'])) {
            $query->where('branch_id', $filters['branch_id']);
        }

        if (!empty($filters['department_id'])) {
            $query->where('department_id', $filters['department_id']);
        }

        if (!empty($filters['position_id'])) {
            $query->where('position_id', $filters['position_id']);
        }

        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('employee_id', 'like', "%{$search}%");
            });
        }

        if (!empty($filters['hire_date_from'])) {
            $query->where('hire_date', '>=', $filters['hire_date_from']);
        }

        if (!empty($filters['hire_date_to'])) {
            $query->where('hire_date', '<=', $filters['hire_date_to']);
        }
    }

    /**
     * Prepare staff data for creation/update
     */
    private function prepareStaffData(array $data, int $tenantId, bool $isCreation = true): array
    {
        $staffData = [
            'tenant_id' => $tenantId,
            'name' => $data['name'],
            'email' => $data['email'],
            'phone' => $data['phone'] ?? null,
            'role' => 'staff',
            'branch_id' => $data['branch_id'] ?? null,
            'department_id' => $data['department_id'] ?? null,
            'position_id' => $data['position_id'] ?? null,
            'employee_id' => $data['employee_id'] ?? null,
            'hire_date' => $data['hire_date'] ?? now(),
            'base_salary' => $data['base_salary'] ?? 0,
            'hourly_rate' => $data['hourly_rate'] ?? 0,
            'address' => $data['address'] ?? null,
            'date_of_birth' => $data['date_of_birth'] ?? null,
            'gender' => $data['gender'] ?? null,
            'status' => $data['status'] ?? 'active',
        ];

        if ($isCreation) {
            $staffData['password'] = Hash::make($data['password'] ?? 'password123');
            $staffData['email_verified_at'] = now();
        }

        return $staffData;
    }

    /**
     * Create emergency contacts
     */
    private function createEmergencyContacts(int $staffId, array $contacts): void
    {
        foreach ($contacts as $contact) {
            EmergencyContact::create([
                'user_id' => $staffId,
                'name' => $contact['name'],
                'phone' => $contact['phone'],
                'relationship' => $contact['relationship'],
                'is_primary' => $contact['is_primary'] ?? false
            ]);
        }
    }

    /**
     * Update emergency contacts
     */
    private function updateEmergencyContacts(int $staffId, array $contacts): void
    {
        // Delete existing contacts
        EmergencyContact::where('user_id', $staffId)->delete();
        
        // Create new contacts
        $this->createEmergencyContacts($staffId, $contacts);
    }

    /**
     * Assign default permissions to staff
     */
    private function assignDefaultPermissions(User $staff): void
    {
        // Implement default permission assignment logic
        // This could involve assigning roles or specific permissions
        // based on the staff's position or department
    }

    /**
     * Check if staff has active assignments
     */
    private function hasActiveAssignments(int $staffId): bool
    {
        // Check for active shift assignments, projects, etc.
        // This would depend on your specific business logic
        return false; // Placeholder implementation
    }
}