/**
 * POS Storage Module - Handles offline data storage using IndexedDB
 * Provides persistent storage for orders, menu items, customers, and other POS data
 */

class POSStorage {
    constructor() {
        this.dbName = 'POSDatabase';
        this.dbVersion = 1;
        this.db = null;
        
        this.stores = {
            menuItems: 'menuItems',
            customers: 'customers',
            tables: 'tables',
            pendingOrders: 'pendingOrders',
            syncQueue: 'syncQueue',
            settings: 'settings'
        };
    }

    /**
     * Initialize IndexedDB
     */
    async init() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.dbVersion);
            
            request.onerror = () => {
                console.error('Failed to open IndexedDB:', request.error);
                reject(request.error);
            };
            
            request.onsuccess = () => {
                this.db = request.result;
                console.log('IndexedDB initialized successfully');
                resolve();
            };
            
            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                this.createObjectStores(db);
            };
        });
    }

    /**
     * Create object stores
     */
    createObjectStores(db) {
        // Menu Items store with enhanced indexing
        if (!db.objectStoreNames.contains(this.stores.menuItems)) {
            const menuStore = db.createObjectStore(this.stores.menuItems, { keyPath: 'id' });
            menuStore.createIndex('category_id', 'category_id', { unique: false });
            menuStore.createIndex('is_active', 'is_active', { unique: false });
            menuStore.createIndex('name', 'name', { unique: false });
            menuStore.createIndex('base_price', 'base_price', { unique: false });
            menuStore.createIndex('category_name', 'category.name', { unique: false });
            menuStore.createIndex('search_text', '_searchText', { unique: false });
            menuStore.createIndex('popularity', '_popularity', { unique: false });
        }

        // Customers store
        if (!db.objectStoreNames.contains(this.stores.customers)) {
            const customerStore = db.createObjectStore(this.stores.customers, { keyPath: 'id' });
            customerStore.createIndex('email', 'email', { unique: false });
            customerStore.createIndex('phone', 'phone', { unique: false });
        }

        // Tables store
        if (!db.objectStoreNames.contains(this.stores.tables)) {
            const tableStore = db.createObjectStore(this.stores.tables, { keyPath: 'id' });
            tableStore.createIndex('status', 'status', { unique: false });
        }

        // Pending Orders store
        if (!db.objectStoreNames.contains(this.stores.pendingOrders)) {
            const orderStore = db.createObjectStore(this.stores.pendingOrders, { keyPath: 'id' });
            orderStore.createIndex('status', 'status', { unique: false });
            orderStore.createIndex('created_at', 'created_at', { unique: false });
        }

        // Sync Queue store
        if (!db.objectStoreNames.contains(this.stores.syncQueue)) {
            const syncStore = db.createObjectStore(this.stores.syncQueue, { keyPath: 'id', autoIncrement: true });
            syncStore.createIndex('type', 'type', { unique: false });
            syncStore.createIndex('priority', 'priority', { unique: false });
            syncStore.createIndex('created_at', 'created_at', { unique: false });
        }

        // Settings store
        if (!db.objectStoreNames.contains(this.stores.settings)) {
            db.createObjectStore(this.stores.settings, { keyPath: 'key' });
        }
    }

    /**
     * Generic method to save data to a store
     */
    async saveToStore(storeName, data) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            
            const request = Array.isArray(data) 
                ? this.bulkSave(store, data)
                : store.put(data);
            
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * Bulk save multiple records
     */
    bulkSave(store, dataArray) {
        const promises = dataArray.map(item => {
            return new Promise((resolve, reject) => {
                const request = store.put(item);
                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
            });
        });
        
        return Promise.all(promises);
    }

    /**
     * Generic method to get data from a store
     */
    async getFromStore(storeName, key = null) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            
            const request = key ? store.get(key) : store.getAll();
            
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * Generic method to delete data from a store
     */
    async deleteFromStore(storeName, key) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            
            const request = store.delete(key);
            
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * Clear all data from a store
     */
    async clearStore(storeName) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            
            const request = store.clear();
            
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // Menu Items methods
    async saveMenuItems(menuItems) {
        await this.clearStore(this.stores.menuItems);
        return await this.saveToStore(this.stores.menuItems, menuItems);
    }

    async getMenuItems() {
        return await this.getFromStore(this.stores.menuItems);
    }

    async getMenuItem(id) {
        return await this.getFromStore(this.stores.menuItems, id);
    }

    /**
     * Search menu items with advanced filtering
     */
    async searchMenuItems(searchTerm, category = null, sortBy = 'name', limit = 50) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.stores.menuItems], 'readonly');
            const store = transaction.objectStore(this.stores.menuItems);
            const results = [];

            let cursor;
            if (searchTerm) {
                // Use search text index for better performance
                const index = store.index('search_text');
                cursor = index.openCursor();
            } else {
                cursor = store.openCursor();
            }

            cursor.onsuccess = (event) => {
                const cursor = event.target.result;
                if (cursor) {
                    const item = cursor.value;

                    // Apply filters
                    let matches = true;

                    // Search term filter
                    if (searchTerm && !item._searchText?.includes(searchTerm.toLowerCase())) {
                        matches = false;
                    }

                    // Category filter
                    if (category && category !== 'all' &&
                        (item.category?.name || 'Uncategorized') !== category) {
                        matches = false;
                    }

                    // Active filter
                    if (!item.is_active) {
                        matches = false;
                    }

                    if (matches) {
                        results.push(item);
                    }

                    cursor.continue();
                } else {
                    // Sort results
                    this.sortMenuItems(results, sortBy);

                    // Apply limit
                    const limitedResults = limit ? results.slice(0, limit) : results;
                    resolve(limitedResults);
                }
            };

            cursor.onerror = () => reject(cursor.error);
        });
    }

    /**
     * Get menu items by category
     */
    async getMenuItemsByCategory(categoryName) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.stores.menuItems], 'readonly');
            const store = transaction.objectStore(this.stores.menuItems);
            const index = store.index('category_name');
            const request = index.getAll(categoryName);

            request.onsuccess = () => {
                const items = request.result.filter(item => item.is_active);
                resolve(items);
            };
            request.onerror = () => reject(request.error);
        });
    }

    /**
     * Get popular menu items
     */
    async getPopularMenuItems(limit = 20) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.stores.menuItems], 'readonly');
            const store = transaction.objectStore(this.stores.menuItems);
            const index = store.index('popularity');
            const request = index.openCursor(null, 'prev'); // Descending order
            const results = [];

            request.onsuccess = (event) => {
                const cursor = event.target.result;
                if (cursor && results.length < limit) {
                    const item = cursor.value;
                    if (item.is_active) {
                        results.push(item);
                    }
                    cursor.continue();
                } else {
                    resolve(results);
                }
            };

            request.onerror = () => reject(request.error);
        });
    }

    /**
     * Sort menu items by specified criteria
     */
    sortMenuItems(items, sortBy) {
        switch (sortBy) {
            case 'name':
                items.sort((a, b) => a.name.localeCompare(b.name));
                break;
            case 'price':
                items.sort((a, b) => parseFloat(a.base_price) - parseFloat(b.base_price));
                break;
            case 'category':
                items.sort((a, b) => {
                    const catA = a.category?.name || 'Uncategorized';
                    const catB = b.category?.name || 'Uncategorized';
                    return catA.localeCompare(catB);
                });
                break;
            case 'popular':
                items.sort((a, b) => (b._popularity || 0) - (a._popularity || 0));
                break;
        }
    }

    // Customers methods
    async saveCustomers(customers) {
        await this.clearStore(this.stores.customers);
        return await this.saveToStore(this.stores.customers, customers);
    }

    async getCustomers() {
        return await this.getFromStore(this.stores.customers);
    }

    async getCustomer(id) {
        return await this.getFromStore(this.stores.customers, id);
    }

    // Tables methods
    async saveTables(tables) {
        await this.clearStore(this.stores.tables);
        return await this.saveToStore(this.stores.tables, tables);
    }

    async getTables() {
        return await this.getFromStore(this.stores.tables);
    }

    async getTable(id) {
        return await this.getFromStore(this.stores.tables, id);
    }

    // Pending Orders methods
    async savePendingOrder(order) {
        return await this.saveToStore(this.stores.pendingOrders, order);
    }

    async getPendingOrders() {
        return await this.getFromStore(this.stores.pendingOrders);
    }

    async getPendingOrder(id) {
        return await this.getFromStore(this.stores.pendingOrders, id);
    }

    async deletePendingOrder(id) {
        return await this.deleteFromStore(this.stores.pendingOrders, id);
    }

    // Sync Queue methods
    async addToSyncQueue(item) {
        const syncItem = {
            ...item,
            created_at: new Date().toISOString(),
            attempts: 0,
            priority: item.priority || 1
        };
        return await this.saveToStore(this.stores.syncQueue, syncItem);
    }

    async getSyncQueue() {
        return await this.getFromStore(this.stores.syncQueue);
    }

    async removeSyncQueueItem(id) {
        return await this.deleteFromStore(this.stores.syncQueue, id);
    }

    async clearSyncQueue() {
        return await this.clearStore(this.stores.syncQueue);
    }

    // Settings methods
    async saveSetting(key, value) {
        return await this.saveToStore(this.stores.settings, { key, value });
    }

    async getSetting(key) {
        const result = await this.getFromStore(this.stores.settings, key);
        return result ? result.value : null;
    }

    async deleteSetting(key) {
        return await this.deleteFromStore(this.stores.settings, key);
    }

    /**
     * Get storage usage statistics
     */
    async getStorageStats() {
        const stats = {};
        
        for (const [name, storeName] of Object.entries(this.stores)) {
            const data = await this.getFromStore(storeName);
            stats[name] = Array.isArray(data) ? data.length : (data ? 1 : 0);
        }
        
        return stats;
    }

    /**
     * Export all data for backup
     */
    async exportData() {
        const exportData = {};
        
        for (const [name, storeName] of Object.entries(this.stores)) {
            exportData[name] = await this.getFromStore(storeName);
        }
        
        return exportData;
    }

    /**
     * Import data from backup
     */
    async importData(data) {
        for (const [name, storeName] of Object.entries(this.stores)) {
            if (data[name]) {
                await this.clearStore(storeName);
                if (Array.isArray(data[name])) {
                    await this.saveToStore(storeName, data[name]);
                } else if (data[name]) {
                    await this.saveToStore(storeName, data[name]);
                }
            }
        }
    }

    /**
     * Close database connection
     */
    close() {
        if (this.db) {
            this.db.close();
            this.db = null;
        }
    }
}
