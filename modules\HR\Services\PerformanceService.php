<?php

namespace Modules\HR\Services;

use App\Models\User;
use App\Models\LeaveRequest;
use App\Models\StaffPenalty;
use Illuminate\Database\Eloquent\Collection;

class PerformanceService
{
    private AttendanceService $attendanceService;
    private PayrollService $payrollService;
    private PenaltyService $penaltyService;

    public function __construct(
        AttendanceService $attendanceService,
        PayrollService $payrollService,
        PenaltyService $penaltyService
    ) {
        $this->attendanceService = $attendanceService;
        $this->payrollService = $payrollService;
        $this->penaltyService = $penaltyService;
    }

    /**
     * Get staff performance metrics
     */
    public function getStaffPerformanceMetrics(int $userId, string $startDate, string $endDate): array
    {
        $user = User::findOrFail($userId);
        
        $attendanceStats = $this->attendanceService->getAttendanceStats(
            $user->tenant_id,
            $startDate,
            $endDate
        );

        $workingHours = $this->payrollService->getStaffWorkingHours(
            $user->tenant_id,
            $userId,
            $startDate,
            $endDate
        );

        // Optimize penalty and leave request queries
        $penaltyStats = StaffPenalty::where('user_id', $userId)
            ->whereBetween('applied_date', [$startDate, $endDate])
            ->where('status', 'active')
            ->selectRaw('COUNT(*) as penalty_count, SUM(amount) as total_penalties')
            ->first();

        $leaveRequestCount = LeaveRequest::where('user_id', $userId)
            ->whereBetween('start_date', [$startDate, $endDate])
            ->count();

        $performanceScore = $this->calculatePerformanceScore($userId, $startDate, $endDate);

        return [
            'user_id' => $userId,
            'period' => ['start' => $startDate, 'end' => $endDate],
            'attendance_rate' => $attendanceStats['attendance_rate'],
            'total_working_hours' => $workingHours['total_hours'],
            'overtime_hours' => $workingHours['overtime_hours'],
            'late_minutes' => $workingHours['total_late_minutes'],
            'total_penalties' => $penaltyStats->total_penalties ?? 0,
            'penalty_count' => $penaltyStats->penalty_count ?? 0,
            'leave_requests' => $leaveRequestCount,
            'performance_score' => $performanceScore,
            'performance_grade' => $this->getPerformanceGrade($performanceScore),
            'recommendations' => $this->generateRecommendations($performanceScore, $attendanceStats, $penaltyStats),
        ];
    }

    /**
     * Calculate performance score
     */
    public function calculatePerformanceScore(int $userId, string $startDate, string $endDate): float
    {
        $user = User::findOrFail($userId);
        
        // Get attendance rate
        $attendanceStats = $this->attendanceService->getAttendanceStats(
            $user->tenant_id,
            $startDate,
            $endDate
        );
        $attendanceRate = $attendanceStats['attendance_rate'];

        // Get working hours
        $workingHours = $this->payrollService->getStaffWorkingHours(
            $user->tenant_id,
            $userId,
            $startDate,
            $endDate
        );

        // Get penalties
        $penalties = $this->penaltyService->getStaffPenalties($userId, $startDate, $endDate);
        $penaltyCount = $penalties->count();

        // Performance score calculation (0-100)
        $weights = config('hr.performance_weights', [
            'attendance' => 0.4,
            'hours' => 0.3,
            'penalties' => 0.3,
        ]);

        $expectedHours = config('hr.expected_monthly_hours', 160);
        
        $attendanceScore = $attendanceRate * $weights['attendance'];
        $hoursScore = min(100, ($workingHours['total_hours'] / $expectedHours) * 100) * $weights['hours'];
        $penaltyScore = max(0, 100 - ($penaltyCount * 10)) * $weights['penalties'];

        $totalScore = $attendanceScore + $hoursScore + $penaltyScore;

        return round(min(100, max(0, $totalScore)), 2);
    }

    /**
     * Get performance grade based on score
     */
    public function getPerformanceGrade(float $score): string
    {
        $grades = config('hr.performance_grades', [
            90 => 'A+',
            85 => 'A',
            80 => 'B+',
            75 => 'B',
            70 => 'C+',
            65 => 'C',
            60 => 'D',
        ]);

        foreach ($grades as $threshold => $grade) {
            if ($score >= $threshold) {
                return $grade;
            }
        }

        return 'F';
    }

    /**
     * Generate performance recommendations
     */
    public function generateRecommendations(float $score, array $attendanceStats, Collection $penalties): array
    {
        $recommendations = [];

        // Attendance recommendations
        if ($attendanceStats['attendance_rate'] < 90) {
            $recommendations[] = [
                'type' => 'attendance',
                'priority' => 'high',
                'message' => 'Improve attendance rate. Current rate is below 90%.',
                'action' => 'Focus on consistent daily attendance',
            ];
        }

        // Penalty recommendations
        if ($penalties->count() > 3) {
            $recommendations[] = [
                'type' => 'penalties',
                'priority' => 'high',
                'message' => 'Reduce penalty occurrences. Multiple penalties detected.',
                'action' => 'Review and improve punctuality and conduct',
            ];
        }

        // Late arrival recommendations
        $lateCount = $penalties->where('type', 'late')->count();
        if ($lateCount > 2) {
            $recommendations[] = [
                'type' => 'punctuality',
                'priority' => 'medium',
                'message' => 'Improve punctuality to avoid late penalties.',
                'action' => 'Plan to arrive 15 minutes early',
            ];
        }

        // Overall performance
        if ($score >= 90) {
            $recommendations[] = [
                'type' => 'recognition',
                'priority' => 'low',
                'message' => 'Excellent performance! Keep up the good work.',
                'action' => 'Consider for performance bonus or recognition',
            ];
        } elseif ($score < 70) {
            $recommendations[] = [
                'type' => 'improvement',
                'priority' => 'high',
                'message' => 'Performance needs significant improvement.',
                'action' => 'Schedule performance review meeting',
            ];
        }

        return $recommendations;
    }

    /**
     * Get team performance summary
     */
    public function getTeamPerformanceSummary(int $tenantId, string $startDate, string $endDate, ?int $branchId = null): array
    {
        $query = User::where('tenant_id', $tenantId);
        
        if ($branchId) {
            $query->where('branch_id', $branchId);
        }

        $users = $query->get();
        $performanceData = [];
        $totalScore = 0;
        $count = 0;

        foreach ($users as $user) {
            $metrics = $this->getStaffPerformanceMetrics($user->id, $startDate, $endDate);
            $performanceData[] = [
                'user_id' => $user->id,
                'name' => $user->name,
                'score' => $metrics['performance_score'],
                'grade' => $metrics['performance_grade'],
                'attendance_rate' => $metrics['attendance_rate'],
            ];
            
            $totalScore += $metrics['performance_score'];
            $count++;
        }

        // Sort by performance score
        usort($performanceData, fn($a, $b) => $b['score'] <=> $a['score']);

        return [
            'period' => ['start' => $startDate, 'end' => $endDate],
            'team_average' => $count > 0 ? round($totalScore / $count, 2) : 0,
            'total_staff' => $count,
            'top_performers' => array_slice($performanceData, 0, 5),
            'needs_attention' => array_filter($performanceData, fn($p) => $p['score'] < 70),
            'performance_distribution' => $this->getPerformanceDistribution($performanceData),
        ];
    }

    /**
     * Get performance distribution
     */
    private function getPerformanceDistribution(array $performanceData): array
    {
        $distribution = [
            'A+' => 0, 'A' => 0, 'B+' => 0, 'B' => 0,
            'C+' => 0, 'C' => 0, 'D' => 0, 'F' => 0,
        ];

        foreach ($performanceData as $data) {
            $distribution[$data['grade']]++;
        }

        return $distribution;
    }

    /**
     * Get team performance metrics
     */
    public function getTeamPerformanceMetrics(array $userIds, string $startDate, string $endDate): array
    {
        // Batch process instead of individual queries
        $users = User::whereIn('id', $userIds)
            ->with(['attendanceRecords' => function($q) use ($startDate, $endDate) {
                $q->whereBetween('date', [$startDate, $endDate]);
            }])
            ->get();
        
        return $users->map(function($user) {
            return $this->calculateMetricsFromRelations($user);
        })->toArray();
    }
}
