<?php

namespace Modules\Transaction\Services;

use Modules\Transaction\Models\Transaction;
use Modules\Transaction\Models\Payment;
use Modules\Transaction\Models\PaymentMethod;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class PaymentService
{
    protected TransactionService $transactionService;

    public function __construct(TransactionService $transactionService)
    {
        $this->transactionService = $transactionService;
    }

    /**
     * Get all payments with pagination and filters
     */
    public function getAllPayments(array $filters = []): LengthAwarePaginator
    {
        $query = Payment::with(['transaction.order', 'paymentMethod', 'processedBy']);

        // Apply filters
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['transaction_id'])) {
            $query->where('transaction_id', $filters['transaction_id']);
        }

        if (isset($filters['payment_method_id'])) {
            $query->where('payment_method_id', $filters['payment_method_id']);
        }

        if (isset($filters['date_from'])) {
            $query->whereDate('payment_date', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('payment_date', '<=', $filters['date_to']);
        }

        if (isset($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('payment_number', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('reference_number', 'like', '%' . $filters['search'] . '%');
            });
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'payment_date';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortBy, $sortDirection);

        // Pagination
        $perPage = $filters['per_page'] ?? 15;
        
        return $query->paginate($perPage);
    }

    /**
     * Get payment by ID with relationships.
     */
    public function getPaymentById(int $id): ?Payment
    {
        return Payment::with([
            'transaction.order',
            'paymentMethod',
            'processedBy'
        ])->find($id);
    }

    /**
     * Process a payment for a transaction.
     */
    public function processPayment(Transaction $transaction, array $paymentData): Payment
    {
        DB::beginTransaction();
        try {
            // Validate transaction can accept payments
            if (!$transaction->canAcceptPayments()) {
                throw new \Exception("Transaction cannot accept payments in '{$transaction->status}' status");
            }

            // Validate payment amount
            $paymentAmount = (float) $paymentData['amount'];
            if ($paymentAmount <= 0) {
                throw new \Exception('Payment amount must be greater than zero');
            }

            // Get payment method
            $paymentMethod = PaymentMethod::findOrFail($paymentData['payment_method_id']);
            if (!$paymentMethod->is_active) {
                throw new \Exception('Payment method is not active');
            }

            // Calculate change for cash payments
            $changeAmount = 0;
            if ($paymentMethod->isCash() && isset($paymentData['cash_received'])) {
                $cashReceived = (float) $paymentData['cash_received'];
                if ($cashReceived < $paymentAmount) {
                    throw new \Exception('Cash received is less than payment amount');
                }
                $changeAmount = $cashReceived - $paymentAmount;
            }

            // Create payment
            $payment = Payment::create([
                'transaction_id' => $transaction->id,
                'payment_method_id' => $paymentMethod->id,
                'payment_number' => $this->generatePaymentNumber(),
                'amount' => $paymentAmount,
                'status' => 'completed', // For now, all payments are immediately completed
                'payment_date' => $paymentData['payment_date'] ?? now(),
                'reference_number' => $paymentData['reference_number'] ?? null,
                'payment_details' => $paymentData['payment_details'] ?? null,
                'notes' => $paymentData['notes'] ?? null,
                'change_amount' => $changeAmount,
                'processed_by' => Auth::id(),
            ]);

            // Update transaction status
            $this->transactionService->updateTransactionStatus($transaction);

            DB::commit();
            return $payment->load(['transaction', 'paymentMethod']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Cancel a payment.
     */
    public function cancelPayment(Payment $payment, string $reason = null): Payment
    {
        DB::beginTransaction();
        try {
            if (!$payment->canBeCancelled()) {
                throw new \Exception("Payment cannot be cancelled in '{$payment->status}' status");
            }

            $payment->update([
                'status' => 'cancelled',
                'notes' => $payment->notes . "\nCancelled: " . $reason,
            ]);

            // Update transaction status
            $this->transactionService->updateTransactionStatus($payment->transaction);

            DB::commit();
            return $payment->fresh();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Refund a payment.
     */
    public function refundPayment(Payment $payment, float $refundAmount = null, string $reason = null): Payment
    {
        DB::beginTransaction();
        try {
            if (!$payment->canBeRefunded()) {
                throw new \Exception("Payment cannot be refunded in '{$payment->status}' status");
            }

            $refundAmount = $refundAmount ?? $payment->amount;
            if ($refundAmount > $payment->amount) {
                throw new \Exception('Refund amount cannot exceed payment amount');
            }

            // Create refund payment record
            $refundPayment = Payment::create([
                'transaction_id' => $payment->transaction_id,
                'payment_method_id' => $payment->payment_method_id,
                'payment_number' => $this->generatePaymentNumber(),
                'amount' => -$refundAmount, // Negative amount for refund
                'status' => 'completed',
                'payment_date' => now(),
                'reference_number' => 'REFUND-' . $payment->payment_number,
                'notes' => 'Refund for payment #' . $payment->payment_number . ': ' . $reason,
                'processed_by' => Auth::id(),
            ]);

            // Update original payment status
            $payment->update([
                'status' => 'refunded',
                'notes' => $payment->notes . "\nRefunded: " . $reason,
            ]);

            // Update transaction status
            $this->transactionService->updateTransactionStatus($payment->transaction);

            DB::commit();
            return $refundPayment->load(['transaction', 'paymentMethod']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get all active payment methods.
     */
    public function getPaymentMethods(): Collection
    {
        return PaymentMethod::where('is_active', true)
            ->orderBy('sort_order', 'asc')
            ->orderBy('name', 'asc')
            ->get();
    }

    /**
     * Get payment statistics.
     */
    public function getPaymentStatistics(array $filters = []): array
    {
        $query = Payment::where('status', 'completed');

        // Apply date filters
        if (isset($filters['date_from'])) {
            $query->whereDate('payment_date', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('payment_date', '<=', $filters['date_to']);
        }

        $stats = [
            'total_payments' => $query->count(),
            'total_amount' => $query->sum('amount'),
            'by_payment_method' => $query->with('paymentMethod')
                ->groupBy('payment_method_id')
                ->selectRaw('payment_method_id, count(*) as count, sum(amount) as amount')
                ->get()
                ->map(function ($item) {
                    return [
                        'payment_method' => $item->paymentMethod->name ?? 'Unknown',
                        'count' => $item->count,
                        'amount' => $item->amount,
                    ];
                }),
        ];

        return $stats;
    }

    /**
     * Generate unique payment number.
     */
    public function generatePaymentNumber(): string
    {
        $prefix = 'PAY';
        $timestamp = Carbon::now()->format('YmdHis');
        $random = str_pad(mt_rand(1, 999), 3, '0', STR_PAD_LEFT);
        
        return $prefix . $timestamp . $random;
    }

    /**
     * Get active payment methods.
     */
    public function getActivePaymentMethods(): Collection
    {
        return PaymentMethod::active()->ordered()->get();
    }

    /**
     * Validate payment data.
     */
    public function validatePaymentData(array $data): array
    {
        $errors = [];

        if (!isset($data['amount']) || $data['amount'] <= 0) {
            $errors[] = 'Payment amount is required and must be greater than zero';
        }

        if (!isset($data['payment_method_id'])) {
            $errors[] = 'Payment method is required';
        } else {
            $paymentMethod = PaymentMethod::find($data['payment_method_id']);
            if (!$paymentMethod || !$paymentMethod->is_active) {
                $errors[] = 'Invalid or inactive payment method';
            }
        }

        return $errors;
    }
}
