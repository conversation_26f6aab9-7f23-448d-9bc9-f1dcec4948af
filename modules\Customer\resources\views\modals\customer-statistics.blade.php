<!-- Customer Statistics Modal -->
<div class="modal fade" id="customerStatsModal" tabindex="-1" aria-labelledby="customerStatsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="customerStatsModalLabel">
                    <i class="fas fa-chart-bar me-2"></i>Customer Statistics & Analytics
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Date Range Filter -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <label for="stats_date_from" class="form-label">From Date</label>
                        <input type="date" class="form-control" id="stats_date_from">
                    </div>
                    <div class="col-md-3">
                        <label for="stats_date_to" class="form-label">To Date</label>
                        <input type="date" class="form-control" id="stats_date_to">
                    </div>
                    <div class="col-md-3">
                        <label for="stats_branch" class="form-label">Branch</label>
                        <select class="form-select" id="stats_branch">
                            <option value="">All Branches</option>
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="button" class="btn btn-primary w-100" onclick="loadCustomerStatistics()">
                            <i class="fas fa-search me-2"></i>Load Statistics
                        </button>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0" id="total_customers">0</h4>
                                        <small>Total Customers</small>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0" id="active_customers">0</h4>
                                        <small>Active Customers</small>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-user-check fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0" id="new_customers">0</h4>
                                        <small>New Customers</small>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-user-plus fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0" id="vip_customers">0</h4>
                                        <small>VIP Customers</small>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-crown fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Row -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">Customer Registration Trend</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="registrationChart" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">Customer Demographics</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="demographicsChart" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Loyalty Statistics -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">Loyalty Program Statistics</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-6">
                                        <div class="text-center">
                                            <h4 class="text-warning" id="total_loyalty_points">0</h4>
                                            <small class="text-muted">Total Points Issued</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="text-center">
                                            <h4 class="text-success" id="redeemed_points">0</h4>
                                            <small class="text-muted">Points Redeemed</small>
                                        </div>
                                    </div>
                                </div>
                                <hr>
                                <canvas id="loyaltyChart" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">Customer Value Distribution</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="valueChart" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Top Customers Tables -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">Top Customers by Spending</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Customer</th>
                                                <th>Total Spent</th>
                                                <th>Orders</th>
                                            </tr>
                                        </thead>
                                        <tbody id="topSpendersTable">
                                            <tr>
                                                <td colspan="3" class="text-center text-muted">Loading...</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">Top Loyalty Customers</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Customer</th>
                                                <th>Points</th>
                                                <th>Tier</th>
                                            </tr>
                                        </thead>
                                        <tbody id="topLoyaltyTable">
                                            <tr>
                                                <td colspan="3" class="text-center text-muted">Loading...</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Close
                </button>
                <button type="button" class="btn btn-primary" onclick="exportStatistics()">
                    <i class="fas fa-download me-2"></i>Export Report
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let registrationChart, demographicsChart, loyaltyChart, valueChart;

// Initialize charts
function initializeCharts() {
    // Registration Trend Chart
    const registrationCtx = document.getElementById('registrationChart').getContext('2d');
    registrationChart = new Chart(registrationCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'New Registrations',
                data: [],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Demographics Chart
    const demographicsCtx = document.getElementById('demographicsChart').getContext('2d');
    demographicsChart = new Chart(demographicsCtx, {
        type: 'doughnut',
        data: {
            labels: ['Male', 'Female', 'Other'],
            datasets: [{
                data: [0, 0, 0],
                backgroundColor: [
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(255, 205, 86, 0.8)'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });

    // Loyalty Chart
    const loyaltyCtx = document.getElementById('loyaltyChart').getContext('2d');
    loyaltyChart = new Chart(loyaltyCtx, {
        type: 'bar',
        data: {
            labels: ['Bronze', 'Silver', 'Gold', 'Platinum'],
            datasets: [{
                label: 'Customers',
                data: [0, 0, 0, 0],
                backgroundColor: [
                    'rgba(205, 127, 50, 0.8)',
                    'rgba(192, 192, 192, 0.8)',
                    'rgba(255, 215, 0, 0.8)',
                    'rgba(229, 228, 226, 0.8)'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Value Distribution Chart
    const valueCtx = document.getElementById('valueChart').getContext('2d');
    valueChart = new Chart(valueCtx, {
        type: 'pie',
        data: {
            labels: ['$0-$100', '$100-$500', '$500-$1000', '$1000+'],
            datasets: [{
                data: [0, 0, 0, 0],
                backgroundColor: [
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 205, 86, 0.8)',
                    'rgba(75, 192, 192, 0.8)'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

// Load customer statistics
function loadCustomerStatistics() {
    const dateFrom = $('#stats_date_from').val();
    const dateTo = $('#stats_date_to').val();
    const branch = $('#stats_branch').val();
    
    const params = new URLSearchParams();
    if (dateFrom) params.append('date_from', dateFrom);
    if (dateTo) params.append('date_to', dateTo);
    if (branch) params.append('branch_id', branch);
    
    // Show loading state
    showStatsLoading();
    
    $.get(`/customers/statistics?${params.toString()}`, function(data) {
        updateStatisticsCards(data.summary);
        updateCharts(data);
        updateTopCustomersTables(data.top_customers);
    }).fail(function() {
        toastr.error('Failed to load customer statistics.');
    });
}

// Show loading state
function showStatsLoading() {
    $('#total_customers, #active_customers, #new_customers, #vip_customers').text('...');
    $('#total_loyalty_points, #redeemed_points').text('...');
    $('#topSpendersTable, #topLoyaltyTable').html('<tr><td colspan="3" class="text-center text-muted">Loading...</td></tr>');
}

// Update statistics cards
function updateStatisticsCards(summary) {
    $('#total_customers').text(summary.total_customers.toLocaleString());
    $('#active_customers').text(summary.active_customers.toLocaleString());
    $('#new_customers').text(summary.new_customers.toLocaleString());
    $('#vip_customers').text(summary.vip_customers.toLocaleString());
    $('#total_loyalty_points').text(summary.total_loyalty_points.toLocaleString());
    $('#redeemed_points').text(summary.redeemed_points.toLocaleString());
}

// Update charts with new data
function updateCharts(data) {
    // Update registration chart
    registrationChart.data.labels = data.registration_trend.labels;
    registrationChart.data.datasets[0].data = data.registration_trend.data;
    registrationChart.update();
    
    // Update demographics chart
    demographicsChart.data.datasets[0].data = [
        data.demographics.male,
        data.demographics.female,
        data.demographics.other
    ];
    demographicsChart.update();
    
    // Update loyalty chart
    loyaltyChart.data.datasets[0].data = [
        data.loyalty_tiers.bronze,
        data.loyalty_tiers.silver,
        data.loyalty_tiers.gold,
        data.loyalty_tiers.platinum
    ];
    loyaltyChart.update();
    
    // Update value chart
    valueChart.data.datasets[0].data = [
        data.value_distribution.low,
        data.value_distribution.medium,
        data.value_distribution.high,
        data.value_distribution.premium
    ];
    valueChart.update();
}

// Update top customers tables
function updateTopCustomersTables(topCustomers) {
    // Top spenders
    const spendersTable = $('#topSpendersTable');
    spendersTable.empty();
    
    if (topCustomers.spenders.length === 0) {
        spendersTable.append('<tr><td colspan="3" class="text-center text-muted">No data available</td></tr>');
    } else {
        topCustomers.spenders.forEach(function(customer) {
            spendersTable.append(`
                <tr>
                    <td>
                        <div class="d-flex align-items-center">
                            <div class="avatar-sm me-2">
                                <div class="avatar-title bg-primary rounded-circle">
                                    ${customer.first_name.charAt(0)}${customer.last_name.charAt(0)}
                                </div>
                            </div>
                            <div>
                                <div class="fw-medium">${customer.first_name} ${customer.last_name}</div>
                                <small class="text-muted">${customer.email}</small>
                            </div>
                        </div>
                    </td>
                    <td>
                        <span class="fw-bold text-success">$${customer.total_spent.toFixed(2)}</span>
                    </td>
                    <td>
                        <span class="badge bg-info">${customer.total_orders}</span>
                    </td>
                </tr>
            `);
        });
    }
    
    // Top loyalty customers
    const loyaltyTable = $('#topLoyaltyTable');
    loyaltyTable.empty();
    
    if (topCustomers.loyalty.length === 0) {
        loyaltyTable.append('<tr><td colspan="3" class="text-center text-muted">No data available</td></tr>');
    } else {
        topCustomers.loyalty.forEach(function(customer) {
            const tierClass = getTierClass(customer.loyalty_tier);
            loyaltyTable.append(`
                <tr>
                    <td>
                        <div class="d-flex align-items-center">
                            <div class="avatar-sm me-2">
                                <div class="avatar-title bg-warning rounded-circle">
                                    ${customer.first_name.charAt(0)}${customer.last_name.charAt(0)}
                                </div>
                            </div>
                            <div>
                                <div class="fw-medium">${customer.first_name} ${customer.last_name}</div>
                                <small class="text-muted">${customer.email}</small>
                            </div>
                        </div>
                    </td>
                    <td>
                        <span class="fw-bold text-warning">${customer.loyalty_points}</span>
                    </td>
                    <td>
                        <span class="badge ${tierClass}">${customer.loyalty_tier}</span>
                    </td>
                </tr>
            `);
        });
    }
}

// Get tier badge class
function getTierClass(tier) {
    switch(tier.toLowerCase()) {
        case 'bronze': return 'bg-secondary';
        case 'silver': return 'bg-light text-dark';
        case 'gold': return 'bg-warning';
        case 'platinum': return 'bg-dark';
        default: return 'bg-primary';
    }
}

// Export statistics
function exportStatistics() {
    const dateFrom = $('#stats_date_from').val();
    const dateTo = $('#stats_date_to').val();
    const branch = $('#stats_branch').val();
    
    const params = new URLSearchParams();
    if (dateFrom) params.append('date_from', dateFrom);
    if (dateTo) params.append('date_to', dateTo);
    if (branch) params.append('branch_id', branch);
    params.append('export', 'true');
    
    window.open(`/customers/statistics?${params.toString()}`, '_blank');
}

// Load branches for filter
function loadBranches() {
    $.get('/branches/list', function(branches) {
        const select = $('#stats_branch');
        select.empty().append('<option value="">All Branches</option>');
        
        branches.forEach(function(branch) {
            select.append(`<option value="${branch.id}">${branch.name}</option>`);
        });
    }).fail(function() {
        console.log('Failed to load branches or no branches available.');
    });
}

// Set default date range (last 30 days)
function setDefaultDateRange() {
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
    
    $('#stats_date_to').val(today.toISOString().split('T')[0]);
    $('#stats_date_from').val(thirtyDaysAgo.toISOString().split('T')[0]);
}

$(document).ready(function() {
    // Initialize when modal is shown
    $('#customerStatsModal').on('shown.bs.modal', function() {
        if (!registrationChart) {
            initializeCharts();
        }
        
        setDefaultDateRange();
        loadBranches();
        loadCustomerStatistics();
    });
    
    // Destroy charts when modal is hidden to prevent memory leaks
    $('#customerStatsModal').on('hidden.bs.modal', function() {
        if (registrationChart) {
            registrationChart.destroy();
            registrationChart = null;
        }
        if (demographicsChart) {
            demographicsChart.destroy();
            demographicsChart = null;
        }
        if (loyaltyChart) {
            loyaltyChart.destroy();
            loyaltyChart = null;
        }
        if (valueChart) {
            valueChart.destroy();
            valueChart = null;
        }
    });
});
</script>