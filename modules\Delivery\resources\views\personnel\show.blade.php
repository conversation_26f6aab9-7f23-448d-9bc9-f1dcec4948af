@extends('layouts.master')

@section('css')
<link href="{{ URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css') }}" rel="stylesheet" />
<link href="{{ URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css') }}" rel="stylesheet">
<link href="{{ URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css') }}" rel="stylesheet" />
<link href="{{ URL::asset('assets/plugins/datatable/css/jquery.dataTables.min.css') }}" rel="stylesheet">
<link href="{{ URL::asset('assets/plugins/datatable/css/responsive.dataTables.min.css') }}" rel="stylesheet">
<link href="{{ URL::asset('assets/plugins/select2/css/select2.min.css') }}" rel="stylesheet">
@endsection

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">إدارة موظفي التوصيل</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ عرض تفاصيل الموظف</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <a href="{{ route('delivery.personnel.index') }}" class="btn btn-secondary">
                <i class="fa fa-arrow-left"></i> العودة للقائمة
            </a>
        </div>
        <div class="pr-1 mb-3 mb-xl-0">
            <a href="{{ route('delivery.personnel.edit', $personnel->id) }}" class="btn btn-primary">
                <i class="fa fa-edit"></i> تعديل
            </a>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')

<!-- row -->
<div class="row">
    <div class="col-lg-12 col-md-12">
        <div class="card">
            <div class="card-body">
                <div class="main-content-label mg-b-5">
                    تفاصيل موظف التوصيل: {{ $personnel->user->name }}
                </div>
                <p class="mg-b-20">عرض جميع تفاصيل ومعلومات موظف التوصيل</p>

                <div class="row">
                    <!-- Personal Information -->
                    <div class="col-md-12">
                        <h6 class="mg-b-10">المعلومات الشخصية</h6>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <tbody>
                                    <tr>
                                        <td class="font-weight-bold" width="200">الاسم الكامل</td>
                                        <td>{{ $personnel->user->name }}</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">رقم الهاتف</td>
                                        <td>{{ $personnel->user->phone ?? 'غير محدد' }}</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">البريد الإلكتروني</td>
                                        <td>{{ $personnel->user->email ?? 'غير محدد' }}</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">رقم الهوية الوطنية</td>
                                        <td>{{ $personnel->national_id ?? 'غير محدد' }}</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">تاريخ الميلاد</td>
                                        <td>{{ $personnel->date_of_birth ? \Carbon\Carbon::parse($personnel->date_of_birth)->format('Y-m-d') : 'غير محدد' }}</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">الفرع</td>
                                        <td>{{ $personnel->branch->name ?? 'غير محدد' }}</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">الحالة</td>
                                        <td>
                                            @if($personnel->status == 'active')
                                                <span class="badge badge-success">نشط</span>
                                            @elseif($personnel->status == 'inactive')
                                                <span class="badge badge-secondary">غير نشط</span>
                                            @elseif($personnel->status == 'busy')
                                                <span class="badge badge-warning">مشغول</span>
                                            @else
                                                <span class="badge badge-light">{{ $personnel->status }}</span>
                                            @endif
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Vehicle Information -->
                    <div class="col-md-12 mt-4">
                        <h6 class="mg-b-10">معلومات المركبة</h6>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <tbody>
                                    <tr>
                                        <td class="font-weight-bold" width="200">نوع المركبة</td>
                                        <td>
                                            @switch($personnel->vehicle_type)
                                                @case('motorcycle')
                                                    دراجة نارية
                                                    @break
                                                @case('car')
                                                    سيارة
                                                    @break
                                                @case('bicycle')
                                                    دراجة هوائية
                                                    @break
                                                @case('scooter')
                                                    سكوتر
                                                    @break
                                                @default
                                                    {{ $personnel->vehicle_type ?? 'غير محدد' }}
                                            @endswitch
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">موديل المركبة</td>
                                        <td>{{ $personnel->vehicle_model ?? 'غير محدد' }}</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">رقم اللوحة</td>
                                        <td>{{ $personnel->vehicle_plate_number ?? 'غير محدد' }}</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">لون المركبة</td>
                                        <td>{{ $personnel->vehicle_color ?? 'غير محدد' }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- License Information -->
                    <div class="col-md-12 mt-4">
                        <h6 class="mg-b-10">معلومات الرخصة</h6>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <tbody>
                                    <tr>
                                        <td class="font-weight-bold" width="200">رقم رخصة القيادة</td>
                                        <td>{{ $personnel->driving_license_number ?? 'غير محدد' }}</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">تاريخ انتهاء الرخصة</td>
                                        <td>
                                            @if($personnel->license_expiry_date)
                                                {{ \Carbon\Carbon::parse($personnel->license_expiry_date)->format('Y-m-d') }}
                                                @if(\Carbon\Carbon::parse($personnel->license_expiry_date)->isPast())
                                                    <span class="badge badge-danger ml-2">منتهية الصلاحية</span>
                                                @elseif(\Carbon\Carbon::parse($personnel->license_expiry_date)->diffInDays() <= 30)
                                                    <span class="badge badge-warning ml-2">تنتهي قريباً</span>
                                                @endif
                                            @else
                                                غير محدد
                                            @endif
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Emergency Contact -->
                    <div class="col-md-12 mt-4">
                        <h6 class="mg-b-10">جهة الاتصال في حالات الطوارئ</h6>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <tbody>
                                    <tr>
                                        <td class="font-weight-bold" width="200">اسم جهة الاتصال</td>
                                        <td>{{ $personnel->emergency_contact_name ?? 'غير محدد' }}</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">رقم هاتف جهة الاتصال</td>
                                        <td>{{ $personnel->emergency_contact_phone ?? 'غير محدد' }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Settings -->
                    <div class="col-md-12 mt-4">
                        <h6 class="mg-b-10">الإعدادات</h6>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <tbody>
                                    <tr>
                                        <td class="font-weight-bold" width="200">الحد الأقصى للطلبات المتزامنة</td>
                                        <td>{{ $personnel->max_concurrent_orders ?? 3 }}</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">تاريخ التسجيل</td>
                                        <td>{{ $personnel->created_at->format('Y-m-d H:i') }}</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">آخر تحديث</td>
                                        <td>{{ $personnel->updated_at->format('Y-m-d H:i') }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Notes -->
                    @if($personnel->notes)
                    <div class="col-md-12 mt-4">
                        <h6 class="mg-b-10">ملاحظات</h6>
                        <div class="alert alert-info">
                            {{ $personnel->notes }}
                        </div>
                    </div>
                    @endif

                    <!-- Recent Delivery Assignments -->
                    @if($personnel->deliveryAssignments && $personnel->deliveryAssignments->count() > 0)
                    <div class="col-md-12 mt-4">
                        <h6 class="mg-b-10">آخر مهام التوصيل</h6>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>رقم الطلب</th>
                                        <th>التاريخ</th>
                                        <th>الحالة</th>
                                        <th>العنوان</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($personnel->deliveryAssignments as $assignment)
                                    <tr>
                                        <td>#{{ $assignment->order->id ?? 'N/A' }}</td>
                                        <td>{{ $assignment->created_at->format('Y-m-d H:i') }}</td>
                                        <td>
                                            @switch($assignment->status)
                                                @case('assigned')
                                                    <span class="badge badge-info">مُعيّن</span>
                                                    @break
                                                @case('picked_up')
                                                    <span class="badge badge-warning">تم الاستلام</span>
                                                    @break
                                                @case('delivered')
                                                    <span class="badge badge-success">تم التوصيل</span>
                                                    @break
                                                @case('cancelled')
                                                    <span class="badge badge-danger">ملغي</span>
                                                    @break
                                                @default
                                                    <span class="badge badge-light">{{ $assignment->status }}</span>
                                            @endswitch
                                        </td>
                                        <td>{{ $assignment->delivery_address ?? 'غير محدد' }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
<!-- /row -->

@endsection

@section('js')
<script src="{{ URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/dataTables.dataTables.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/responsive.dataTables.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/jquery.dataTables.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/dataTables.buttons.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/buttons.bootstrap4.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/jszip.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/pdfmake.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/vfs_fonts.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/buttons.html5.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/buttons.print.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/buttons.colVis.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js') }}"></script>
<script src="{{ URL::asset('assets/plugins/datatable/js/responsive.bootstrap4.min.js') }}"></script>
@endsection