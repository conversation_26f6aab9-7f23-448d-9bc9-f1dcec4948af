<?php

namespace Modules\Transaction\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PaymentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'payment_number' => $this->payment_number,
            'amount' => $this->amount,
            'formatted_amount' => $this->getFormattedAmount(),
            'status' => $this->status,
            'status_label' => $this->getStatusLabel(),
            'payment_date' => $this->payment_date,
            'payment_date_formatted' => $this->payment_date->format('Y-m-d H:i:s'),
            'reference_number' => $this->reference_number,
            'payment_details' => $this->payment_details,
            'notes' => $this->notes,
            'change_amount' => $this->change_amount,
            'is_completed' => $this->isCompleted(),
            'is_pending' => $this->isPending(),
            'is_failed' => $this->isFailed(),
            'can_be_cancelled' => $this->canBeCancelled(),
            'can_be_refunded' => $this->canBeRefunded(),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,

            // Relationships
            'transaction' => $this->whenLoaded('transaction', function () {
                return [
                    'id' => $this->transaction->id,
                    'transaction_number' => $this->transaction->transaction_number,
                    'total_amount' => $this->transaction->total_amount,
                    'status' => $this->transaction->status,
                    'order' => $this->when($this->transaction->relationLoaded('order'), [
                        'id' => $this->transaction->order->id,
                        'order_number' => $this->transaction->order->order_number,
                        'order_type' => $this->transaction->order->order_type,
                    ]),
                ];
            }),

            'payment_method' => $this->whenLoaded('paymentMethod', function () {
                return [
                    'id' => $this->paymentMethod->id,
                    'name' => $this->paymentMethod->name,
                    'code' => $this->paymentMethod->code,
                    'description' => $this->paymentMethod->description,
                    'is_cash' => $this->paymentMethod->isCash(),
                    'is_card' => $this->paymentMethod->isCard(),
                    'is_digital' => $this->paymentMethod->isDigital(),
                ];
            }),

            'processed_by' => $this->whenLoaded('processedBy', function () {
                return [
                    'id' => $this->processedBy->id,
                    'name' => $this->processedBy->name,
                ];
            }),

            // Additional computed fields
            'payment_method_name' => $this->getPaymentMethodName(),
            'display_details' => $this->getDisplayDetails(),
        ];
    }

    /**
     * Get status label for display.
     */
    protected function getStatusLabel(): string
    {
        $labels = [
            'pending' => 'Pending',
            'completed' => 'Completed',
            'failed' => 'Failed',
            'cancelled' => 'Cancelled',
            'refunded' => 'Refunded',
        ];

        return $labels[$this->status] ?? ucfirst($this->status);
    }

    /**
     * Get display details based on payment method and details.
     */
    protected function getDisplayDetails(): array
    {
        $details = [];

        if ($this->relationLoaded('paymentMethod') && $this->paymentMethod) {
            if ($this->paymentMethod->isCash()) {
                $details['type'] = 'Cash Payment';
                if ($this->change_amount > 0) {
                    $details['change_given'] = number_format($this->change_amount, 2);
                }
            } elseif ($this->paymentMethod->isCard()) {
                $details['type'] = 'Card Payment';
                if ($this->payment_details && isset($this->payment_details['card_last_four'])) {
                    $details['card_ending'] = '****' . $this->payment_details['card_last_four'];
                }
                if ($this->payment_details && isset($this->payment_details['card_type'])) {
                    $details['card_type'] = $this->payment_details['card_type'];
                }
                if ($this->payment_details && isset($this->payment_details['authorization_code'])) {
                    $details['auth_code'] = $this->payment_details['authorization_code'];
                }
            } elseif ($this->paymentMethod->isDigital()) {
                $details['type'] = 'Digital Payment';
            }
        }

        if ($this->reference_number) {
            $details['reference'] = $this->reference_number;
        }

        return $details;
    }
}
