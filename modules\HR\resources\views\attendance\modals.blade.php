<!-- Check In/Out Modal -->
<div id="checkInOutModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <!-- Modal Header -->
        <div class="bg-gradient-to-r from-green-500 to-teal-600 -m-5 mb-4 px-6 py-4 rounded-t-md">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-white flex items-center">
                    <i class="fas fa-user-clock ml-2"></i>
                    تسجيل حضور/انصراف
                </h3>
                <button onclick="closeModal('checkInOutModal')" class="text-white hover:text-gray-200 transition-colors duration-200">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Modal Body -->
        <form id="checkInOutForm" class="space-y-6">
            @csrf
            
            <!-- Staff Selection -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-md font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-user ml-2 text-green-600"></i>
                    اختيار الموظف
                </h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Staff Member -->
                    <div class="md:col-span-2">
                        <label for="staff_id" class="block text-sm font-medium text-gray-700 mb-2">الموظف *</label>
                        <select id="staff_id" name="staff_id" required 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                            <option value="">اختر الموظف</option>
                            @foreach($staff ?? [] as $member)
                                <option value="{{ $member->id }}">{{ $member->name }} - {{ $member->role }}</option>
                            @endforeach
                        </select>
                        <div class="text-red-500 text-sm mt-1 hidden" id="staff_id_error"></div>
                    </div>

                    <!-- Action Type -->
                    <div>
                        <label for="action_type" class="block text-sm font-medium text-gray-700 mb-2">نوع العملية *</label>
                        <select id="action_type" name="action_type" required 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                            <option value="">اختر العملية</option>
                            <option value="check_in">تسجيل حضور</option>
                            <option value="check_out">تسجيل انصراف</option>
                        </select>
                        <div class="text-red-500 text-sm mt-1 hidden" id="action_type_error"></div>
                    </div>

                    <!-- Time -->
                    <div>
                        <label for="time" class="block text-sm font-medium text-gray-700 mb-2">الوقت</label>
                        <input type="time" id="time" name="time" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="time_error"></div>
                        <p class="text-xs text-gray-500 mt-1">اتركه فارغاً لاستخدام الوقت الحالي</p>
                    </div>
                </div>

                <!-- Notes -->
                <div class="mt-4">
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">ملاحظات</label>
                    <textarea id="notes" name="notes" rows="2" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                              placeholder="أي ملاحظات إضافية..."></textarea>
                    <div class="text-red-500 text-sm mt-1 hidden" id="notes_error"></div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="flex items-center justify-end space-x-2 space-x-reverse pt-4 border-t border-gray-200">
                <button type="button" onclick="closeModal('checkInOutModal')" 
                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors duration-200">
                    إلغاء
                </button>
                <button type="submit" 
                        class="px-4 py-2 bg-gradient-to-r from-green-500 to-teal-600 text-white rounded-md hover:from-green-600 hover:to-teal-700 transition-all duration-200 flex items-center">
                    <i class="fas fa-save ml-2"></i>
                    تسجيل
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Bulk Attendance Modal -->
<div id="bulkAttendanceModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-2/3 shadow-lg rounded-md bg-white">
        <!-- Modal Header -->
        <div class="bg-gradient-to-r from-blue-500 to-blue-600 -m-5 mb-4 px-6 py-4 rounded-t-md">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-white flex items-center">
                    <i class="fas fa-users-cog ml-2"></i>
                    تسجيل حضور جماعي
                </h3>
                <button onclick="closeModal('bulkAttendanceModal')" class="text-white hover:text-gray-200 transition-colors duration-200">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Modal Body -->
        <form id="bulkAttendanceForm" class="space-y-6">
            @csrf
            
            <!-- Date and Branch Selection -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-md font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-calendar ml-2 text-blue-600"></i>
                    إعدادات التسجيل
                </h4>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Date -->
                    <div>
                        <label for="bulk_date" class="block text-sm font-medium text-gray-700 mb-2">التاريخ *</label>
                        <input type="date" id="bulk_date" name="date" required value="{{ date('Y-m-d') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="bulk_date_error"></div>
                    </div>

                    <!-- Branch -->
                    <div>
                        <label for="bulk_branch" class="block text-sm font-medium text-gray-700 mb-2">الفرع</label>
                        <select id="bulk_branch" name="branch_id" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            <option value="">جميع الفروع</option>
                            @foreach($branches ?? [] as $branch)
                                <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                            @endforeach
                        </select>
                        <div class="text-red-500 text-sm mt-1 hidden" id="bulk_branch_error"></div>
                    </div>

                    <!-- Action Type -->
                    <div>
                        <label for="bulk_action" class="block text-sm font-medium text-gray-700 mb-2">نوع العملية *</label>
                        <select id="bulk_action" name="action_type" required 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            <option value="">اختر العملية</option>
                            <option value="mark_present">تسجيل حضور</option>
                            <option value="mark_absent">تسجيل غياب</option>
                        </select>
                        <div class="text-red-500 text-sm mt-1 hidden" id="bulk_action_error"></div>
                    </div>
                </div>
            </div>

            <!-- Staff Selection -->
            <div class="bg-gray-50 rounded-lg p-4">
                <div class="flex items-center justify-between mb-4">
                    <h4 class="text-md font-semibold text-gray-800 flex items-center">
                        <i class="fas fa-users ml-2 text-blue-600"></i>
                        اختيار الموظفين
                    </h4>
                    <div class="flex space-x-2 space-x-reverse">
                        <button type="button" onclick="selectAllStaff()" class="text-sm bg-blue-100 text-blue-800 px-3 py-1 rounded hover:bg-blue-200">
                            تحديد الكل
                        </button>
                        <button type="button" onclick="deselectAllStaff()" class="text-sm bg-gray-100 text-gray-800 px-3 py-1 rounded hover:bg-gray-200">
                            إلغاء التحديد
                        </button>
                    </div>
                </div>
                
                <!-- Staff List -->
                <div class="max-h-64 overflow-y-auto border border-gray-200 rounded-md">
                    <div id="staff-list" class="p-4 space-y-2">
                        <!-- Staff checkboxes will be loaded here -->
                        <div class="text-center text-gray-500 py-4">
                            <i class="fas fa-spinner fa-spin"></i>
                            جاري تحميل قائمة الموظفين...
                        </div>
                    </div>
                </div>
                <div class="text-red-500 text-sm mt-1 hidden" id="staff_ids_error"></div>
            </div>

            <!-- Modal Footer -->
            <div class="flex items-center justify-end space-x-2 space-x-reverse pt-4 border-t border-gray-200">
                <button type="button" onclick="closeModal('bulkAttendanceModal')" 
                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors duration-200">
                    إلغاء
                </button>
                <button type="submit" 
                        class="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-md hover:from-blue-600 hover:to-blue-700 transition-all duration-200 flex items-center">
                    <i class="fas fa-save ml-2"></i>
                    تسجيل الحضور
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Edit Attendance Modal -->
<div id="editAttendanceModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <!-- Modal Header -->
        <div class="bg-gradient-to-r from-amber-500 to-orange-600 -m-5 mb-4 px-6 py-4 rounded-t-md">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-white flex items-center">
                    <i class="fas fa-edit ml-2"></i>
                    تعديل سجل الحضور
                </h3>
                <button onclick="closeModal('editAttendanceModal')" class="text-white hover:text-gray-200 transition-colors duration-200">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Modal Body -->
        <form id="editAttendanceForm" class="space-y-6">
            @csrf
            @method('PUT')
            <input type="hidden" id="edit_attendance_id" name="attendance_id">
            
            <!-- Attendance Information -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-md font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-clock ml-2 text-amber-600"></i>
                    معلومات الحضور
                </h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Check In Time -->
                    <div>
                        <label for="edit_check_in_time" class="block text-sm font-medium text-gray-700 mb-2">وقت الحضور</label>
                        <input type="time" id="edit_check_in_time" name="check_in_time" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="edit_check_in_time_error"></div>
                    </div>

                    <!-- Check Out Time -->
                    <div>
                        <label for="edit_check_out_time" class="block text-sm font-medium text-gray-700 mb-2">وقت الانصراف</label>
                        <input type="time" id="edit_check_out_time" name="check_out_time" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="edit_check_out_time_error"></div>
                    </div>

                    <!-- Status -->
                    <div>
                        <label for="edit_status" class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                        <select id="edit_status" name="status" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500">
                            <option value="present">حاضر</option>
                            <option value="absent">غائب</option>
                            <option value="late">متأخر</option>
                            <option value="on_leave">في إجازة</option>
                        </select>
                        <div class="text-red-500 text-sm mt-1 hidden" id="edit_status_error"></div>
                    </div>

                    <!-- Late Minutes -->
                    <div>
                        <label for="edit_late_minutes" class="block text-sm font-medium text-gray-700 mb-2">دقائق التأخير</label>
                        <input type="number" id="edit_late_minutes" name="late_minutes" min="0" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="edit_late_minutes_error"></div>
                    </div>
                </div>

                <!-- Notes -->
                <div class="mt-4">
                    <label for="edit_notes" class="block text-sm font-medium text-gray-700 mb-2">ملاحظات</label>
                    <textarea id="edit_notes" name="notes" rows="2" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500"
                              placeholder="أي ملاحظات إضافية..."></textarea>
                    <div class="text-red-500 text-sm mt-1 hidden" id="edit_notes_error"></div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="flex items-center justify-end space-x-2 space-x-reverse pt-4 border-t border-gray-200">
                <button type="button" onclick="closeModal('editAttendanceModal')" 
                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors duration-200">
                    إلغاء
                </button>
                <button type="submit" 
                        class="px-4 py-2 bg-gradient-to-r from-amber-500 to-orange-600 text-white rounded-md hover:from-amber-600 hover:to-orange-700 transition-all duration-200 flex items-center">
                    <i class="fas fa-save ml-2"></i>
                    حفظ التعديلات
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Attendance Details Modal -->
<div id="attendanceDetailsModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <!-- Modal Header -->
        <div class="bg-gradient-to-r from-purple-500 to-purple-600 -m-5 mb-4 px-6 py-4 rounded-t-md">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-white flex items-center">
                    <i class="fas fa-info-circle ml-2"></i>
                    تفاصيل الحضور
                </h3>
                <button onclick="closeModal('attendanceDetailsModal')" class="text-white hover:text-gray-200 transition-colors duration-200">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Modal Body -->
        <div id="attendance-details-content" class="space-y-6">
            <!-- Content will be loaded dynamically -->
            <div class="text-center py-8">
                <i class="fas fa-spinner fa-spin text-gray-400 text-2xl"></i>
                <p class="text-gray-500 mt-2">جاري تحميل التفاصيل...</p>
            </div>
        </div>

        <!-- Modal Footer -->
        <div class="flex items-center justify-end space-x-2 space-x-reverse pt-4 border-t border-gray-200">
            <button type="button" onclick="closeModal('attendanceDetailsModal')" 
                    class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors duration-200">
                إغلاق
            </button>
        </div>
    </div>
</div>

<script>
// Form submission handlers
$(document).ready(function() {
    // Set current time when modal opens
    $('#checkInOutModal').on('show', function() {
        const now = new Date();
        const timeString = now.toTimeString().slice(0, 5);
        $('#time').val(timeString);
    });

    // Load staff list when bulk modal opens
    $('#bulkAttendanceModal').on('show', function() {
        loadStaffList();
    });

    // Check In/Out Form
    $('#checkInOutForm').on('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        
        $.ajax({
            url: '{{ route("hr.attendance.check-in-out") }}',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    closeModal('checkInOutModal');
                    $('#checkInOutForm')[0].reset();
                    $('#attendance-table').DataTable().ajax.reload();
                    showToast('تم تسجيل الحضور/الانصراف بنجاح', 'success');
                }
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    const errors = xhr.responseJSON.errors;
                    Object.keys(errors).forEach(key => {
                        $(`#${key}_error`).text(errors[key][0]).removeClass('hidden');
                    });
                } else {
                    showToast('حدث خطأ أثناء التسجيل', 'error');
                }
            }
        });
    });

    // Bulk Attendance Form
    $('#bulkAttendanceForm').on('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const selectedStaff = [];
        $('input[name="staff_ids[]"]:checked').each(function() {
            selectedStaff.push($(this).val());
        });
        
        if (selectedStaff.length === 0) {
            showToast('يرجى اختيار موظف واحد على الأقل', 'error');
            return;
        }
        
        selectedStaff.forEach(id => {
            formData.append('staff_ids[]', id);
        });
        
        $.ajax({
            url: '{{ route("hr.attendance.bulk") }}',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    closeModal('bulkAttendanceModal');
                    $('#bulkAttendanceForm')[0].reset();
                    $('#attendance-table').DataTable().ajax.reload();
                    showToast(`تم تسجيل حضور ${selectedStaff.length} موظف بنجاح`, 'success');
                }
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    const errors = xhr.responseJSON.errors;
                    Object.keys(errors).forEach(key => {
                        $(`#${key}_error`).text(errors[key][0]).removeClass('hidden');
                    });
                } else {
                    showToast('حدث خطأ أثناء التسجيل الجماعي', 'error');
                }
            }
        });
    });

    // Edit Attendance Form
    $('#editAttendanceForm').on('submit', function(e) {
        e.preventDefault();
        
        const attendanceId = $('#edit_attendance_id').val();
        const formData = new FormData(this);
        
        $.ajax({
            url: `{{ route("hr.attendance.update", ":id") }}`.replace(':id', attendanceId),
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    closeModal('editAttendanceModal');
                    $('#attendance-table').DataTable().ajax.reload();
                    showToast('تم تحديث سجل الحضور بنجاح', 'success');
                }
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    const errors = xhr.responseJSON.errors;
                    Object.keys(errors).forEach(key => {
                        $(`#edit_${key}_error`).text(errors[key][0]).removeClass('hidden');
                    });
                } else {
                    showToast('حدث خطأ أثناء تحديث سجل الحضور', 'error');
                }
            }
        });
    });
});

// Load staff list for bulk attendance
function loadStaffList() {
    const branchId = $('#bulk_branch').val();
    
    $.ajax({
        url: '{{ route("hr.staff.list") }}',
        method: 'GET',
        data: { branch_id: branchId },
        success: function(response) {
            let html = '';
            if (response.data && response.data.length > 0) {
                response.data.forEach(staff => {
                    html += `
                        <div class="flex items-center">
                            <input type="checkbox" id="staff_${staff.id}" name="staff_ids[]" value="${staff.id}" 
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="staff_${staff.id}" class="mr-2 block text-sm text-gray-900 cursor-pointer flex-1">
                                <div class="flex items-center">
                                    <img src="${staff.avatar || '/images/default-avatar.png'}" alt="${staff.name}" 
                                         class="h-8 w-8 rounded-full object-cover ml-2">
                                    <div>
                                        <div class="font-medium">${staff.name}</div>
                                        <div class="text-xs text-gray-500">${staff.role} - ${staff.branch_name}</div>
                                    </div>
                                </div>
                            </label>
                        </div>
                    `;
                });
            } else {
                html = '<div class="text-center text-gray-500 py-4">لا توجد موظفين متاحين</div>';
            }
            $('#staff-list').html(html);
        },
        error: function() {
            $('#staff-list').html('<div class="text-center text-red-500 py-4">حدث خطأ أثناء تحميل قائمة الموظفين</div>');
        }
    });
}

// Select/Deselect all staff
function selectAllStaff() {
    $('input[name="staff_ids[]"]').prop('checked', true);
}

function deselectAllStaff() {
    $('input[name="staff_ids[]"]').prop('checked', false);
}

// Clear form errors when modal is closed
function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
    // Clear all error messages
    document.querySelectorAll('.text-red-500').forEach(el => {
        el.classList.add('hidden');
        el.textContent = '';
    });
}
</script>
