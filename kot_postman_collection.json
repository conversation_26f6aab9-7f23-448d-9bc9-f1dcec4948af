{
  "info": {
    "_postman_id": "your_collection_id",
    "name": "Kot Module Collection",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "item": [
    {
      "name": "Kitchen Management",
      "item": [
        {
          "name": "Get all kitchens",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "Accept",
                "value": "application/json"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/kitchens",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "kitchens"
              ]
            }
          },
          "response": []
        },
        {
          "name": "Create kitchen",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Accept",
                "value": "application/json"
              },
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"branch_id\": \"{{branch_id}}\",\n  \"name\": \"Main Kitchen\",\n  \"code\": \"MK001\",\n  \"description\": \"Main kitchen for hot food preparation\",\n  \"station_type\": \"hot\",\n  \"max_concurrent_orders\": 10,\n  \"average_prep_time_minutes\": 15,\n  \"is_active\": true,\n  \"display_order\": 1,\n  \"manager_id\": \"{{manager_user_id}}\",\n  \"equipment_list\": [\"Oven\", \"Stove\", \"Grill\"],\n  \"operating_hours\": {\n    \"monday\": {\"start\": \"09:00\", \"end\": \"22:00\"},\n    \"tuesday\": {\"start\": \"09:00\", \"end\": \"22:00\"},\n    \"wednesday\": {\"start\": \"09:00\", \"end\": \"22:00\"},\n    \"thursday\": {\"start\": \"09:00\", \"end\": \"22:00\"},\n    \"friday\": {\"start\": \"09:00\", \"end\": \"23:00\"},\n    \"saturday\": {\"start\": \"10:00\", \"end\": \"23:00\"},\n    \"sunday\": {\"start\": \"10:00\", \"end\": \"22:00\"}\n  }\n}"
            },
            "url": {
              "raw": "{{baseUrl}}/kitchens",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "kitchens"
              ]
            }
          },
          "response": []
        },
        {
          "name": "Get kitchen details",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "Accept",
                "value": "application/json"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/kitchens/:kitchen",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "kitchens",
                ":kitchen"
              ]
            }
          },
          "response": []
        },
        {
          "name": "Update kitchen",
          "request": {
            "method": "PUT",
            "header": [
              {
                "key": "Accept",
                "value": "application/json"
              },
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"name\": \"Updated Kitchen Name\",\n  \"description\": \"Updated description for the main kitchen.\",\n  \"station_type\": \"main\",\n  \"max_concurrent_orders\": 12,\n  \"average_prep_time_minutes\": 10,\n  \"is_active\": true,\n  \"display_order\": 1,\n  \"manager_id\": \"{{manager_user_id}}\",\n  \"equipment_list\": [\"Oven\", \"Stove\", \"Grill\", \"Blender\"],\n  \"operating_hours\": {\n    \"monday\": {\"start\": \"08:00\", \"end\": \"23:00\"},\n    \"tuesday\": {\"start\": \"08:00\", \"end\": \"23:00\"}\n  }\n}"
            }
            "url": {
              "raw": "{{baseUrl}}/kitchens/:kitchen",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "kitchens",
                ":kitchen"
              ]
            }
          },
          "response": []
        },
        {
          "name": "Delete kitchen",
          "request": {
            "method": "DELETE",
            "header": [
              {
                "key": "Accept",
                "value": "application/json"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/kitchens/:kitchen",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "kitchens",
                ":kitchen"
              ]
            }
          },
          "response": []
        },
        {
          "name": "Toggle kitchen active status",
          "request": {
            "method": "PATCH",
            "header": [
              {
                "key": "Accept",
                "value": "application/json"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/kitchens/:kitchen/toggle-status",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "kitchens",
                ":kitchen",
                "toggle-status"
              ]
            }
          },
          "response": []
        },
        {
          "name": "Get kitchen menu items",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "Accept",
                "value": "application/json"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/kitchens/:kitchen/menu-items",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "kitchens",
                ":kitchen",
                "menu-items"
              ]
            }
          },
          "response": []
        },
        {
          "name": "Assign menu item to kitchen",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Accept",
                "value": "application/json"
              },
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"menu_item_id\": \"{{menu_item_id}}\",\n  \"prep_time_minutes\": 15,\n  \"priority_level\": 5,\n  \"is_active\": true,\n  \"special_instructions\": \"Prepare with extra care.\"\n}"}]}}}
            },
            "url": {
              "raw": "{{baseUrl}}/kitchens/:kitchen/menu-items",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "kitchens",
                ":kitchen",
                "menu-items"
              ]
            }
          },
          "response": []
        },
        {
          "name": "Remove menu item from kitchen",
          "request": {
            "method": "DELETE",
            "header": [
              {
                "key": "Accept",
                "value": "application/json"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/kitchens/:kitchen/menu-items/:menuItemId",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "kitchens",
                ":kitchen",
                "menu-items",
                ":menuItemId"
              ]
            }
          },
          "response": []
        },
        {
          "name": "Get kitchen dashboard data",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "Accept",
                "value": "application/json"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/kitchens/:kitchen/dashboard",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "kitchens",
                ":kitchen",
                "dashboard"
              ]
            }
          },
          "response": []
        },
        {
          "name": "Get active KOTs for kitchen",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "Accept",
                "value": "application/json"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/kitchens/:kitchen/active-kots",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "kitchens",
                ":kitchen",
                "active-kots"
              ]
            }
          },
          "response": []
        }
      ]
    },
    {
      "name": "KOT (Kitchen Order Ticket) Management",
      "item": [
        {
          "name": "Get KOTs with filters",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "Accept",
                "value": "application/json"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/kots",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "kots"
              ]
            }
          },
          "response": []
        },
        {
          "name": "Create KOT from order",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Accept",
                "value": "application/json"
              },
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"order_id\": \"{{order_id}}\",\n  \"priority\": \"normal\",\n  \"special_instructions\": \"Prepare all items quickly.\"\n}"}]}}}
            },
            "url": {
              "raw": "{{baseUrl}}/kots/create-from-order",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "kots",
                "create-from-order"
              ]
            }
          },
          "response": []
        },
        {
          "name": "Get KOT statistics",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "Accept",
                "value": "application/json"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/kots/statistics",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "kots",
                "statistics"
              ]
            }
          },
          "response": []
        },
        {
          "name": "Get KOT details",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "Accept",
                "value": "application/json"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/kots/:kotOrder",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "kots",
                ":kotOrder"
              ]
            }
          },
          "response": []
        },
        {
          "name": "Update KOT status",
          "request": {
            "method": "PATCH",
            "header": [
              {
                "key": "Accept",
                "value": "application/json"
              },
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"status\": \"preparing\",\n  \"notes\": \"Starting preparation for all items.\"\n}"}]}}}
            },
            "url": {
              "raw": "{{baseUrl}}/kots/:kotOrder/status",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "kots",
                ":kotOrder",
                "status"
              ]
            }
          },
          "response": []
        },
        {
          "name": "Start preparing KOT",
          "request": {
            "method": "PATCH",
            "header": [
              {
                "key": "Accept",
                "value": "application/json"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/kots/:kotOrder/start-preparing",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "kots",
                ":kotOrder",
                "start-preparing"
              ]
            }
          },
          "response": []
        },
        {
          "name": "Mark KOT as ready",
          "request": {
            "method": "PATCH",
            "header": [
              {
                "key": "Accept",
                "value": "application/json"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/kots/:kotOrder/mark-ready",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "kots",
                ":kotOrder",
                "mark-ready"
              ]
            }
          },
          "response": []
        },
        {
          "name": "Complete KOT",
          "request": {
            "method": "PATCH",
            "header": [
              {
                "key": "Accept",
                "value": "application/json"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/kots/:kotOrder/complete",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "kots",
                ":kotOrder",
                "complete"
              ]
            }
          },
          "response": []
        },
        {
          "name": "Cancel KOT",
          "request": {
            "method": "PATCH",
            "header": [
              {
                "key": "Accept",
                "value": "application/json"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/kots/:kotOrder/cancel",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "kots",
                ":kotOrder",
                "cancel"
              ]
            }
          },
          "response": []
        },
        {
          "name": "Update KOT item status",
          "request": {
            "method": "PATCH",
            "header": [
              {
                "key": "Accept",
                "value": "application/json"
              },
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"status\": \"completed\"\n}"}]}}}
            },
            "url": {
              "raw": "{{baseUrl}}/items/:kotOrderItem/status",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "items",
                ":kotOrderItem",
                "status"
              ]
            }
          },
          "response": []
        }
      ]
    }
  ],
  "variable": [
    {
      "key": "baseUrl",
      "value": "http://localhost:8000/api/kitchen",
      "type": "string"
    }
  ]
}
