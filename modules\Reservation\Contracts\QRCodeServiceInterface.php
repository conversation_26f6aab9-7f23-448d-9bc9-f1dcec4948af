<?php

namespace Modules\Reservation\Contracts;

interface QRCodeServiceInterface
{
    /**
     * Generate QR code data for table (frontend will generate the actual QR image).
     */
    public function generateTableQR(int $tableId, array $options = []): array;

    /**
     * Generate QR code content.
     */
    public function generateQRContent(\App\Models\Table $table, string $type = 'table'): array;

    /**
     * Validate QR code.
     */
    public function validateQRCode(string $qrCode): array;

    /**
     * Generate QR code data for menu access.
     */
    public function generateMenuQR(int $tableId, array $options = []): array;

    /**
     * Generate QR code data for direct ordering.
     */
    public function generateOrderQR(int $tableId, array $options = []): array;

    /**
     * Batch generate QR codes for multiple tables.
     */
    public function batchGenerate(array $tableIds, array $options = []): array;
}