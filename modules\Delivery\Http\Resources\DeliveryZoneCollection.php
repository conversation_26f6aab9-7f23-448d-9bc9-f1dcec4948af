<?php

namespace Modules\Delivery\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class DeliveryZoneCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
            'meta' => [
                'total' => $this->total(),
                'count' => $this->count(),
                'per_page' => $this->perPage(),
                'current_page' => $this->currentPage(),
                'total_pages' => $this->lastPage(),
                'has_more_pages' => $this->hasMorePages(),
            ],
            'links' => [
                'first' => $this->url(1),
                'last' => $this->url($this->lastPage()),
                'prev' => $this->previousPageUrl(),
                'next' => $this->nextPageUrl(),
            ],
            'summary' => [
                'total_zones' => $this->collection->count(),
                'active_zones' => $this->collection->where('is_active', true)->count(),
                'inactive_zones' => $this->collection->where('is_active', false)->count(),
                'high_priority_zones' => $this->collection->whereIn('priority', [3, 4, 5])->count(),
                'medium_priority_zones' => $this->collection->where('priority', 2)->count(),
                'low_priority_zones' => $this->collection->where('priority', 1)->count(),
            ],
        ];
    }
}