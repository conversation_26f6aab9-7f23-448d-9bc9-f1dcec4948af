<?php

namespace Modules\Delivery\Providers;

use Illuminate\Support\ServiceProvider;
use Modules\Delivery\Services\DeliveryService;
use Modules\Delivery\Services\DeliveryPersonnelService;
use Modules\Delivery\Services\DeliveryZoneService;
use Modules\Delivery\Services\DeliveryTrackingService;
use Modules\Delivery\Services\DeliveryAnalyticsService;

class DeliveryServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register services as singletons
        $this->app->singleton(DeliveryService::class);
        $this->app->singleton(DeliveryPersonnelService::class);
        $this->app->singleton(DeliveryZoneService::class);
        $this->app->singleton(DeliveryTrackingService::class);
        $this->app->singleton(DeliveryAnalyticsService::class);
        
        // Register config
        $this->mergeConfigFrom(__DIR__ . '/../config/delivery.php', 'delivery');
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Load routes with proper middleware and prefixes
        $this->loadRoutes();
        
        // Load migrations
        $this->loadMigrationsFrom(__DIR__ . '/../database/migrations');
        
        // Load views
        $this->loadViewsFrom(__DIR__ . '/../resources/views', 'delivery');
        
        // Register middleware (commented out as middleware doesn't exist)
        // $router = $this->app['router'];
        // $router->aliasMiddleware('delivery.access', \Modules\Delivery\Http\Middleware\DeliveryAccessMiddleware::class);
        
        // Publish config
        $this->publishes([
            __DIR__ . '/../config/delivery.php' => config_path('delivery.php'),
        ], 'delivery-config');
    }

    /**
     * Load module routes with proper middleware and prefixes
     */
    protected function loadRoutes(): void
    {
        // Load API routes with api middleware and prefix
        if (file_exists(__DIR__ . '/../routes/api.php')) {
            \Illuminate\Support\Facades\Route::group([
                'middleware' => 'api',
                'prefix' => 'api',
                'namespace' => 'Modules\\Delivery\\Http\\Controllers',
            ], function () {
                require __DIR__ . '/../routes/api.php';
            });
        }

        // Load Web routes with web middleware
        if (file_exists(__DIR__ . '/../routes/web.php')) {
            \Illuminate\Support\Facades\Route::group([
                'middleware' => 'web',
                'namespace' => 'Modules\\Delivery\\Http\\Controllers',
            ], function () {
                require __DIR__ . '/../routes/web.php';
            });
        }
    }
}