<?php

namespace Modules\Reservation\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class TableCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
            'meta' => [
                'total' => $this->count(),
                'total_capacity' => $this->collection->sum('seating_capacity'),
                'status_summary' => $this->getStatusSummary(),
                'area_summary' => $this->getAreaSummary(),
            ],
        ];
    }

    /**
     * Get status summary for the table collection.
     */
    private function getStatusSummary(): array
    {
        $statusCounts = $this->collection->groupBy('status')->map->count();
        
        return [
            'available' => $statusCounts->get('available', 0),
            'occupied' => $statusCounts->get('occupied', 0),
            'reserved' => $statusCounts->get('reserved', 0),
            'cleaning' => $statusCounts->get('cleaning', 0),
            'out_of_order' => $statusCounts->get('out_of_order', 0),
        ];
    }

    /**
     * Get area summary for the table collection.
     */
    private function getAreaSummary(): array
    {
        return $this->collection
            ->groupBy('area_id')
            ->map(function ($tables, $areaId) {
                $firstTable = $tables->first();
                return [
                    'area_id' => $areaId,
                    'area_name' => $firstTable->area->name ?? 'No Area',
                    'table_count' => $tables->count(),
                    'total_capacity' => $tables->sum('seating_capacity'),
                ];
            })
            ->values()
            ->toArray();
    }
}