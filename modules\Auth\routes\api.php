<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON>les\Auth\Http\Controllers\AuthController;
use Modules\Auth\Http\Controllers\RoleController;
use Modules\Auth\Http\Controllers\PermissionController;

/*
|--------------------------------------------------------------------------
| Auth API Routes
|--------------------------------------------------------------------------
|
| Here are the authentication API routes for the Auth module.
| These routes are loaded by the AuthServiceProvider within a group which
| contains the "api" middleware group.
| All authentication is now token-based using Laravel Sanctum.
|
*/

// Public routes (no authentication required)
Route::prefix('auth')->group(function () {
    // Authentication routes
    Route::post('register', [AuthController::class, 'register']);
    Route::post('login', [AuthController::class, 'login']);
    
    // Password reset routes
    Route::post('forgot-password', [AuthController::class, 'forgotPassword']);
    Route::post('reset-password', [AuthController::class, 'resetPassword']);
});

// Protected routes (token-based authentication)
Route::middleware(['auth:sanctum'])->prefix('auth')->group(function () {
    Route::post('logout', [AuthController::class, 'logout']);
    Route::post('logout-all', [AuthController::class, 'logoutAll']);
    Route::post('refresh-token', [AuthController::class, 'refreshToken']);
    Route::get('profile', [AuthController::class, 'profile']);
    Route::put('profile', [AuthController::class, 'updateProfile']);
    Route::post('change-password', [AuthController::class, 'changePassword']);
    Route::post('verify-email', [AuthController::class, 'verifyEmail']);
    Route::post('resend-verification', [AuthController::class, 'resendEmailVerification']);
});

// Admin routes (require authentication)
Route::middleware(['auth:sanctum'])->prefix('auth/admin')->name('api.auth.')->group(function () {
// Route::middleware(['auth:sanctum', 'role:admin'])->prefix('auth/admin')->group(function () {
    // Role management
    Route::apiResource('roles', RoleController::class);
    Route::post('roles/{role}/permissions', [RoleController::class, 'assignPermissions'])->name('roles.assign-permissions');
    Route::delete('roles/{role}/permissions', [RoleController::class, 'revokePermissions'])->name('roles.revoke-permissions');
    
    // Permission management
    Route::apiResource('permissions', PermissionController::class);
    Route::get('permissions/grouped', [PermissionController::class, 'grouped'])->name('permissions.grouped');
    Route::post('permissions/bulk', [PermissionController::class, 'bulkStore'])->name('permissions.bulk');
    
    // User management
    Route::get('users', [AuthController::class, 'getUsers'])->name('users.index');
    Route::get('users/branch', [AuthController::class, 'getUsersByBranch'])->name('users.branch');
    Route::post('users/{user}/roles', [AuthController::class, 'assignUserRoles'])->name('users.assign-roles');
    Route::delete('users/{user}/roles', [AuthController::class, 'revokeUserRoles'])->name('users.revoke-roles');
    Route::post('users/{user}/permissions', [AuthController::class, 'assignUserPermissions'])->name('users.assign-permissions');
    Route::delete('users/{user}/permissions', [AuthController::class, 'revokeUserPermissions'])->name('users.revoke-permissions');
});

