<?php

namespace App\Models\Settings;

use App\Models\Tenant;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SecuritySetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'tenant_id',
        'session_timeout',
        'max_login_attempts',
        'lockout_duration',
        'require_password_change',
        'password_expiry_days',
        'two_factor_auth',
        'audit_logging',
        'ip_whitelist',
    ];

    protected $casts = [
        'require_password_change' => 'boolean',
        'two_factor_auth' => 'boolean',
        'audit_logging' => 'boolean',
        'ip_whitelist' => 'array',
    ];

    // Relationships
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    // Scopes
    public function scopeForTenant($query, $tenantId)
    {
        return $query->where('tenant_id', $tenantId);
    }

    // Methods
    public function isIpAllowed($ip): bool
    {
        if (empty($this->ip_whitelist)) {
            return true; // No restrictions
        }
        
        return in_array($ip, $this->ip_whitelist);
    }

    public function addIpToWhitelist($ip): void
    {
        $whitelist = $this->ip_whitelist ?? [];
        
        if (!in_array($ip, $whitelist)) {
            $whitelist[] = $ip;
            $this->ip_whitelist = $whitelist;
            $this->save();
        }
    }

    public function removeIpFromWhitelist($ip): void
    {
        $whitelist = $this->ip_whitelist ?? [];
        
        $key = array_search($ip, $whitelist);
        if ($key !== false) {
            unset($whitelist[$key]);
            $this->ip_whitelist = array_values($whitelist);
            $this->save();
        }
    }

    // Static methods
    public static function getDefaults($tenantId): array
    {
        return [
            'tenant_id' => $tenantId,
            'session_timeout' => 30, // minutes
            'max_login_attempts' => 5,
            'lockout_duration' => 15, // minutes
            'require_password_change' => false,
            'password_expiry_days' => null,
            'two_factor_auth' => false,
            'audit_logging' => true,
            'ip_whitelist' => null,
        ];
    }

    public static function getPasswordRules(): array
    {
        return [
            'min_length' => 8,
            'require_uppercase' => true,
            'require_lowercase' => true,
            'require_numbers' => true,
            'require_symbols' => false,
            'prevent_common_passwords' => true,
            'prevent_personal_info' => true,
        ];
    }
}
