@extends('layouts.master')

@section('title', 'KOT Orders Management')

@push('styles')
<meta name="csrf-token" content="{{ csrf_token() }}">
<!-- DataTables CSS for Tailwind -->
<link href="https://cdn.datatables.net/1.13.7/css/dataTables.tailwindcss.min.css" rel="stylesheet">
<link href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.tailwindcss.min.css" rel="stylesheet">
<link href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.tailwindcss.min.css" rel="stylesheet">
<!-- SweetAlert2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
<style>
.kot-card {
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease-in-out;
    background: #ffffff;
    overflow: hidden;
}

.kot-card:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transform: translateY(-0.125rem);
}

.kot-card-header {
    padding: 1rem;
    border-bottom: 1px solid #f3f4f6;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.kot-card-body {
    padding: 1rem;
}

.kot-items-list {
    max-height: 200px;
    overflow-y: auto;
}

.kot-item {
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    background: #f9fafb;
    transition: all 0.2s ease-in-out;
}

.kot-item:hover {
    background: #f3f4f6;
    border-color: #d1d5db;
}

.kot-item.status-pending {
    border-left: 3px solid #eab308;
}

.kot-item.status-preparing {
    border-left: 3px solid #3b82f6;
}

.kot-item.status-ready {
    border-left: 3px solid #10b981;
}

.kot-item.status-completed {
    border-left: 3px solid #6b7280;
}

.order-number-display {
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    background: #f3f4f6;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    border: 1px solid #d1d5db;
}

.items-summary {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-top: 0.5rem;
}

.item-count-badge {
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.item-count-pending {
    background: #fef3c7;
    color: #92400e;
}

.item-count-preparing {
    background: #dbeafe;
    color: #1e40af;
}

.item-count-ready {
    background: #d1fae5;
    color: #065f46;
}

.item-count-completed {
    background: #f3f4f6;
    color: #374151;
}

.kot-priority-urgent {
    border-left: 4px solid #ef4444;
}

.kot-priority-high {
    border-left: 4px solid #eab308;
}

.kot-priority-normal {
    border-left: 4px solid #3b82f6;
}

.kot-priority-low {
    border-left: 4px solid #6b7280;
}

.time-indicator {
    font-weight: 600;
}

.time-overdue {
    color: #ef4444;
    animation: blink 1s infinite;
}

.time-warning {
    color: #eab308;
}

.time-normal {
    color: #10b981;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.5; }
}

.filter-section {
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.status-filter-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.status-filter-btn {
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.25rem;
    background-color: #ffffff;
    color: #374151;
    cursor: pointer;
    transition: all 0.3s ease-in-out;
}

.status-filter-btn.active {
    background-color: #3b82f6;
    color: #ffffff;
    border-color: #3b82f6;
}

.status-filter-btn:hover {
    background-color: #f3f4f6;
}

.status-filter-btn.active:hover {
    background-color: #2563eb;
}
</style>
@endpush

@section('page-header')
<!-- breadcrumb -->
<div class="flex justify-between items-center">
    <div>
        <div class="flex items-center">
            <h4 class="text-xl font-semibold text-gray-900 mb-0">Kitchen</h4>
            <span class="text-gray-500 text-sm ml-2">/ KOT Orders</span>
        </div>
    </div>
    <div class="flex items-center">
        <div class="mr-2">
            <a href="{{ route('kitchen-display.index') }}" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors">
                <i class="fas fa-tv mr-2"></i> Kitchen Display
            </a>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<div class="container-fluid">
    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-xl shadow-lg overflow-hidden">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h6 class="text-white text-sm font-medium mb-2">Pending KOTs</h6>
                        <h2 class="text-white text-3xl font-bold" id="pending-kots">0</h2>
                    </div>
                    <div class="text-white opacity-80">
                        <i class="fas fa-clock text-3xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-br from-blue-400 to-blue-600 rounded-xl shadow-lg overflow-hidden">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h6 class="text-white text-sm font-medium mb-2">Preparing</h6>
                        <h2 class="text-white text-3xl font-bold" id="preparing-kots">0</h2>
                    </div>
                    <div class="text-white opacity-80">
                        <i class="fas fa-fire text-3xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-br from-green-400 to-green-600 rounded-xl shadow-lg overflow-hidden">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h6 class="text-white text-sm font-medium mb-2">Ready</h6>
                        <h2 class="text-white text-3xl font-bold" id="ready-kots">0</h2>
                    </div>
                    <div class="text-white opacity-80">
                        <i class="fas fa-check-circle text-3xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-br from-purple-400 to-purple-600 rounded-xl shadow-lg overflow-hidden">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h6 class="text-white text-sm font-medium mb-2">Completed Today</h6>
                        <h2 class="text-white text-3xl font-bold" id="completed-kots">0</h2>
                    </div>
                    <div class="text-white opacity-80">
                        <i class="fas fa-trophy text-3xl"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-section">
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            @if($branches->isNotEmpty())
            <div>
                <label for="branch-filter" class="block text-sm font-medium text-gray-700 mb-2">Branch</label>
                <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="branch-filter">
                    <option value="">All Branches</option>
                    @foreach($branches as $branch)
                        <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                    @endforeach
                </select>
            </div>
            @endif
            
            <div>
                <label for="kitchen-filter" class="block text-sm font-medium text-gray-700 mb-2">Kitchen</label>
                <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="kitchen-filter">
                    <option value="">All Kitchens</option>
                    @foreach($kitchens as $kitchen)
                        <option value="{{ $kitchen->id }}">{{ $kitchen->name }}</option>
                    @endforeach
                </select>
            </div>

            <div>
                <label for="status-filter" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="status-filter">
                    <option value="">All Statuses</option>
                    @foreach($statuses as $status)
                        <option value="{{ $status }}">{{ ucfirst($status) }}</option>
                    @endforeach
                </select>
            </div>

            <div>
                <label for="priority-filter" class="block text-sm font-medium text-gray-700 mb-2">Priority</label>
                <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="priority-filter">
                    <option value="">All Priorities</option>
                    @foreach($priorities as $priority)
                        <option value="{{ $priority }}">{{ ucfirst($priority) }}</option>
                    @endforeach
                </select>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">&nbsp;</label>
                <div class="flex space-x-2">
                    <button type="button" class="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors" id="apply-filters">
                        <i class="fas fa-filter mr-2"></i> Apply
                    </button>
                    <button type="button" class="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-lg transition-colors" id="clear-filters">
                        <i class="fas fa-times mr-2"></i> Clear
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- View Toggle and KOT Orders Table -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-900">KOT Orders</h3>
            <div class="flex space-x-2">
                <button type="button" class="px-3 py-2 text-sm font-medium text-white bg-blue-600 border border-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors view-toggle active" data-view="card">
                    <i class="fas fa-th-large mr-2"></i>Cards
                </button>
                <button type="button" class="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors view-toggle" data-view="table">
                    <i class="fas fa-table mr-2"></i>Table
                </button>
            </div>
        </div>
        
        <!-- Card View -->
        <div class="p-6" id="card-view">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-6" id="kot-orders-cards">
                <!-- KOT order cards will be loaded here -->
            </div>
        </div>
        
        <!-- Table View -->
        <div class="p-6 hidden" id="table-view">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200" id="kot-orders-table" data-page-length='25'>
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">KOT Number</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kitchen</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order #</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assigned To</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Elapsed Time</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remaining Time</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Update Status Modal -->
<div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden" id="updateStatusModal">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-md shadow-lg rounded-lg bg-white">
        <div class="flex items-center justify-between pb-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Update KOT Status</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600 transition-colors" id="closeStatusModal">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <form id="updateStatusForm" class="mt-6">
            <input type="hidden" id="kot_order_id" name="kot_order_id">
            <div>
                <div class="mb-4">
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                        Status <span class="text-red-500">*</span>
                    </label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="status" name="status" required>
                        <option value="">Select Status</option>
                        <option value="preparing">Preparing</option>
                        <option value="ready">Ready</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>

                <div class="mb-4 hidden" id="assigned-to-group">
                    <label for="assigned_to" class="block text-sm font-medium text-gray-700 mb-2">Assign To</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="assigned_to" name="assigned_to">
                        <option value="">Select User</option>
                        <!-- Users will be loaded dynamically -->
                    </select>
                </div>

                <div class="mb-4 hidden" id="reason-group">
                    <label for="reason" class="block text-sm font-medium text-gray-700 mb-2">
                        Cancellation Reason <span class="text-red-500">*</span>
                    </label>
                    <textarea class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="reason" name="reason" rows="3"></textarea>
                </div>

                <div class="mb-4">
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                    <textarea class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="notes" name="notes" rows="3"></textarea>
                </div>
            </div>

            <div class="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200">
                <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors" id="cancelStatusModal">
                    Cancel
                </button>
                <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                    Update Status
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<!-- DataTables JS for Tailwind -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.tailwindcss.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.tailwindcss.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.colVis.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.tailwindcss.min.js"></script>
<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
$(document).ready(function() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    let table;

    // Modal functionality
    function showModal(modalId) {
        $('#' + modalId).removeClass('hidden');
    }

    function hideModal(modalId) {
        $('#' + modalId).addClass('hidden');
    }

    // Initialize DataTable
    function initializeDataTable() {
        table = $('#kot-orders-table').DataTable({
            processing: true,
            serverSide: true,
            responsive: true,
            ajax: {
                url: '{{ route("kot-orders.index") }}',
                type: 'GET',
                data: function(d) {
                    d.kitchen_id = $('#kitchen-filter').val();
                    d.status = $('#status-filter').val();
                    d.priority = $('#priority-filter').val();
                    @if($branches->isNotEmpty())
                    d.branch_id = $('#branch-filter').val();
                    @endif
                }
            },
            columns: [
                { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
                { data: 'kot_number', name: 'kot_number' },
                { data: 'kitchen_name', name: 'kitchen.name' },
                { data: 'order_number', name: 'order.order_number' },
                { data: 'status_badge', name: 'status' },
                { data: 'priority_badge', name: 'priority' },
                { data: 'assigned_to_name', name: 'assignedTo.name' },
                { data: 'elapsed_time', name: 'elapsed_time' },
                { data: 'remaining_time', name: 'remaining_time' },
                { data: 'created_at', name: 'created_at' },
                { data: 'actions', name: 'actions', orderable: false, searchable: false }
            ],
            dom: 'Bfrtip',
            buttons: [
                'copy', 'csv', 'excel', 'pdf', 'print'
            ],
            order: [[9, 'desc']], // Order by created_at descending
            language: {
                processing: '<div class="flex items-center justify-center"><i class="fas fa-spinner fa-spin mr-2"></i>Loading...</div>'
            }
        });
    }

    // Load statistics
    function loadStatistics() {
        $.ajax({
            url: '{{ route("kot-orders.statistics") }}',
            method: 'GET',
            data: {
                kitchen_id: $('#kitchen-filter').val(),
                status: $('#status-filter').val(),
                priority: $('#priority-filter').val(),
                @if($branches->isNotEmpty())
                branch_id: $('#branch-filter').val(),
                @endif
            },
            success: function(response) {
                if (response.success && response.statistics) {
                    $('#pending-kots').text(response.statistics.pending || 0);
                    $('#preparing-kots').text(response.statistics.preparing || 0);
                    $('#ready-kots').text(response.statistics.ready || 0);
                    $('#completed-kots').text(response.statistics.completed_today || 0);
                }
            },
            error: function(xhr) {
                console.error('Error loading statistics:', xhr);
            }
        });
    }

    // Load KOT order cards
     function loadKotOrderCards() {
         $.ajax({
             url: '{{ route("kot-orders.index") }}',
            method: 'GET',
            data: {
                kitchen_id: $('#kitchen-filter').val(),
                status: $('#status-filter').val(),
                priority: $('#priority-filter').val(),
                @if($branches->isNotEmpty())
                branch_id: $('#branch-filter').val(),
                @endif
                view: 'cards'
            },
            success: function(response) {
                if (response.data) {
                    displayKotOrderCards(response.data);
                }
            },
            error: function(xhr) {
                console.error('Error loading KOT order cards:', xhr);
            }
        });
    }

    // Display KOT order cards
    function displayKotOrderCards(kotOrders) {
        const cardsContainer = $('#kot-orders-cards');
        cardsContainer.empty();

        if (kotOrders.length === 0) {
            cardsContainer.html(`
                <div class="col-span-full text-center py-12">
                    <div class="text-gray-500">
                        <i class="fas fa-clipboard-list text-4xl mb-4"></i>
                        <p class="text-lg">No KOT orders found</p>
                        <p class="text-sm">Try adjusting your filters</p>
                    </div>
                </div>
            `);
            return;
        }

        kotOrders.forEach(kotOrder => {
            const cardHtml = generateKotOrderCard(kotOrder);
            cardsContainer.append(cardHtml);
        });
    }

    // Initialize card view as default and load statistics
    loadKotOrderCards();
    loadStatistics();
    // Initialize table for when user switches to table view
    initializeDataTable();

    // Close modal handlers
    $('#closeStatusModal, #cancelStatusModal').on('click', function() {
        hideModal('updateStatusModal');
    });

    // Close modal when clicking outside
    $('#updateStatusModal').on('click', function(e) {
        if (e.target === this) {
            hideModal('updateStatusModal');
        }
    });

    // Filter change handlers
    $('#kitchen-filter, #status-filter, #priority-filter, #branch-filter').on('change', function() {
        if ($('#card-view').hasClass('hidden')) {
            table.draw();
        } else {
            loadKotOrderCards();
        }
        loadStatistics();
    });

    // Apply filters
    $('#apply-filters').on('click', function() {
        if ($('#card-view').hasClass('hidden')) {
            table.draw();
        } else {
            loadKotOrderCards();
        }
        loadStatistics();
    });

    // Clear filters
    $('#clear-filters').on('click', function() {
        $('#kitchen-filter').val('');
        $('#status-filter').val('');
        $('#priority-filter').val('');
        @if($branches->isNotEmpty())
        $('#branch-filter').val('');
        @endif
        if ($('#card-view').hasClass('hidden')) {
            table.draw();
        } else {
            loadKotOrderCards();
        }
        loadStatistics();
    });

    // Handle status update
    $(document).on('click', '.update-status', function() {
        const kotOrderId = $(this).data('kot-id');
        const currentStatus = $(this).data('current-status');

        $('#kot_order_id').val(kotOrderId);
        $('#status').val(currentStatus);

        showModal('updateStatusModal');
    });

    // Handle status form submission
    $('#updateStatusForm').on('submit', function(e) {
        e.preventDefault();

        const submitButton = $(this).find('button[type="submit"]');
        const originalText = submitButton.text();
        submitButton.prop('disabled', true).text('Updating...');

        const kotOrderId = $('#kot_order_id').val();
        const formData = new FormData(this);

        $.ajax({
            url: `{{ url('kot-orders') }}/${kotOrderId}/status`,
            method: 'PATCH',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    Swal.fire({
                        title: 'Success!',
                        text: 'KOT status updated successfully',
                        icon: 'success',
                        confirmButtonColor: '#3b82f6'
                    });

                    // Reset form and close modal
                    $('#updateStatusForm')[0].reset();
                    hideModal('updateStatusModal');

                    // Refresh table and statistics
                    table.draw();
                    loadStatistics();
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: response.message || 'Failed to update KOT status',
                        icon: 'error',
                        confirmButtonColor: '#3b82f6'
                    });
                }
            },
            error: function(xhr) {
                console.error('Error updating KOT status:', xhr);
                let errorMessage = "Failed to update KOT status";

                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    errorMessage = errors.join(', ');
                }

                Swal.fire({
                    title: 'Error!',
                    text: errorMessage,
                    icon: 'error',
                    confirmButtonColor: '#3b82f6'
                });
            },
            complete: function() {
                // Restore button state
                submitButton.prop('disabled', false).text(originalText);
            }
        });
    });

    // View toggle functionality
    $('.view-toggle').on('click', function() {
        const view = $(this).data('view');

        // Update button states
        $('.view-toggle').removeClass('active bg-blue-600 text-white').addClass('bg-white text-gray-700 border-gray-300');
        $(this).removeClass('bg-white text-gray-700 border-gray-300').addClass('active bg-blue-600 text-white border-blue-600');

        if (view === 'table') {
            $('#table-view').removeClass('hidden');
            $('#card-view').addClass('hidden');
            table.draw(); // Refresh table data
        } else {
            $('#table-view').addClass('hidden');
            $('#card-view').removeClass('hidden');
            loadKotOrderCards();
        }
    });

    // Load KOT order cards
    function loadKotOrderCards() {
        $.ajax({
            url: '{{ route("kot-orders.index") }}',
            method: 'GET',
            data: {
                view: 'cards',
                kitchen_id: $('#kitchen-filter').val(),
                status: $('#status-filter').val(),
                priority: $('#priority-filter').val(),
                @if($branches->isNotEmpty())
                branch_id: $('#branch-filter').val(),
                @endif
            },
            success: function(response) {
                if (response.success && response.data) {
                    displayKotOrderCards(response.data);
                }
            },
            error: function(xhr) {
                console.error('Error loading KOT order cards:', xhr);
            }
        });
    }

    // Generate KOT order card HTML
    function generateKotOrderCard(kotOrder) {
        const statusColors = {
            'pending': 'bg-yellow-100 text-yellow-800 border-yellow-200',
            'preparing': 'bg-blue-100 text-blue-800 border-blue-200',
            'ready': 'bg-green-100 text-green-800 border-green-200',
            'completed': 'bg-purple-100 text-purple-800 border-purple-200',
            'cancelled': 'bg-red-100 text-red-800 border-red-200'
        };

        const priorityColors = {
            'low': 'bg-gray-100 text-gray-800',
            'normal': 'bg-blue-100 text-blue-800',
            'high': 'bg-yellow-100 text-yellow-800',
            'urgent': 'bg-red-100 text-red-800'
        };

        const statusColor = statusColors[kotOrder.status] || 'bg-gray-100 text-gray-800';
        const priorityColor = priorityColors[kotOrder.priority] || 'bg-gray-100 text-gray-800';
        
        // Generate items list if available
        let itemsHtml = '';
        if (kotOrder.items && kotOrder.items.length > 0) {
            itemsHtml = '<div class="kot-items-list mt-3">';
            kotOrder.items.forEach(item => {
                const itemStatusClass = `status-${item.status}`;
                const statusColors = {
                    'pending': 'bg-yellow-50 border-yellow-200',
                    'preparing': 'bg-blue-50 border-blue-200',
                    'ready': 'bg-green-50 border-green-200',
                    'completed': 'bg-purple-50 border-purple-200',
                    'cancelled': 'bg-red-50 border-red-200'
                };
                const itemStatusColor = statusColors[item.status] || 'bg-gray-50 border-gray-200';
                
                itemsHtml += `
                    <div class="kot-item ${itemStatusClass} ${itemStatusColor} mb-2 p-3 rounded-lg border" data-item-id="${item.id}">
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <div class="font-medium text-sm text-gray-900">${item.menu_item_name}</div>
                                <div class="text-xs text-gray-500 mt-1">Qty: ${item.quantity}</div>
                                ${item.special_instructions ? `<div class="text-xs text-orange-600 mt-1"><i class="fas fa-info-circle mr-1"></i>${item.special_instructions}</div>` : ''}
                                ${item.modifications && item.modifications.length > 0 ? `<div class="text-xs text-blue-600 mt-1"><i class="fas fa-edit mr-1"></i>Modified</div>` : ''}
                            </div>
                            <div class="ml-3">
                                <select class="text-xs border border-gray-300 rounded px-2 py-1 item-status-select focus:outline-none focus:ring-1 focus:ring-blue-500" data-item-id="${item.id}">
                                    <option value="pending" ${item.status === 'pending' ? 'selected' : ''}>Pending</option>
                                    <option value="preparing" ${item.status === 'preparing' ? 'selected' : ''}>Preparing</option>
                                    <option value="ready" ${item.status === 'ready' ? 'selected' : ''}>Ready</option>
                                    <option value="completed" ${item.status === 'completed' ? 'selected' : ''}>Completed</option>
                                    <option value="cancelled" ${item.status === 'cancelled' ? 'selected' : ''}>Cancelled</option>
                                </select>
                            </div>
                        </div>
                    </div>
                `;
            });
            itemsHtml += '</div>';
        }

        // Generate items summary badges
        let itemsSummary = '';
        if (kotOrder.items_summary) {
            if (kotOrder.items_summary.pending > 0) {
                itemsSummary += `<span class="item-count-badge item-count-pending">${kotOrder.items_summary.pending} Pending</span>`;
            }
            if (kotOrder.items_summary.preparing > 0) {
                itemsSummary += `<span class="item-count-badge item-count-preparing">${kotOrder.items_summary.preparing} Preparing</span>`;
            }
            if (kotOrder.items_summary.ready > 0) {
                itemsSummary += `<span class="item-count-badge item-count-ready">${kotOrder.items_summary.ready} Ready</span>`;
            }
            if (kotOrder.items_summary.completed > 0) {
                itemsSummary += `<span class="item-count-badge item-count-completed">${kotOrder.items_summary.completed} Completed</span>`;
            }
        }

        return `
            <div class="kot-card kot-priority-${kotOrder.priority}">
                <div class="kot-card-header">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="text-lg font-semibold text-gray-900">#${kotOrder.kot_number}</h3>
                        <div class="flex space-x-2">
                            <span class="px-2 py-1 text-xs font-semibold rounded-full ${statusColor}">${kotOrder.status.toUpperCase()}</span>
                            <span class="px-2 py-1 text-xs font-semibold rounded-full ${priorityColor}">${kotOrder.priority.toUpperCase()}</span>
                        </div>
                    </div>
                    <div class="order-number-display">
                        Order: ${kotOrder.order_number}
                    </div>
                    <div class="items-summary">
                        ${itemsSummary}
                    </div>
                </div>

                <div class="kot-card-body">
                    <div class="space-y-2 mb-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">Kitchen:</span>
                            <span class="font-medium">${kotOrder.kitchen_name}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">Assigned To:</span>
                            <span class="font-medium">${kotOrder.assigned_to_name || 'Unassigned'}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">Elapsed Time:</span>
                            <span class="font-medium">${kotOrder.elapsed_time}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">Remaining Time:</span>
                            <span class="font-medium">${kotOrder.remaining_time || 'N/A'}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">Created:</span>
                            <span class="font-medium">${kotOrder.created_at}</span>
                        </div>
                    </div>
                    
                    ${itemsHtml}

                    <div class="flex space-x-2 mt-4">
                        <button type="button" class="flex-1 px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors update-status"
                                data-kot-id="${kotOrder.id}" data-current-status="${kotOrder.status}">
                            <i class="fas fa-edit mr-1"></i> Update Status
                        </button>
                        <button type="button" class="flex-1 px-3 py-2 text-sm font-medium text-green-600 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors mark-all-ready"
                                data-kot-id="${kotOrder.id}">
                            <i class="fas fa-check mr-1"></i> All Ready
                        </button>
                        <button type="button" class="flex-1 px-3 py-2 text-sm font-medium text-purple-600 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors mark-all-completed"
                                data-kot-id="${kotOrder.id}">
                            <i class="fas fa-check-double mr-1"></i> All Done
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // Status change handler for showing/hiding fields
    $('#status').on('change', function() {
        const status = $(this).val();

        if (status === 'preparing') {
            $('#assigned-to-group').removeClass('hidden');
            $('#reason-group').addClass('hidden');
        } else if (status === 'cancelled') {
            $('#assigned-to-group').addClass('hidden');
            $('#reason-group').removeClass('hidden');
        } else {
            $('#assigned-to-group').addClass('hidden');
            $('#reason-group').addClass('hidden');
        }
    });

    // Handle item status updates
     $(document).on('change', '.item-status-select', function() {
         const itemId = $(this).data('item-id');
         const newStatus = $(this).val();
         
         $.ajax({
             url: `{{ url('kot-orders/items') }}/${itemId}/status`,
             method: 'PATCH',
             data: {
                 _token: '{{ csrf_token() }}',
                 status: newStatus
             },
            success: function(response) {
                if (response.success) {
                    Swal.fire({
                        title: 'Success!',
                        text: 'Item status updated successfully',
                        icon: 'success',
                        confirmButtonColor: '#3b82f6',
                        timer: 1500
                    });
                    loadKotOrderCards();
                    loadStatistics();
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: response.message || 'Failed to update item status',
                        icon: 'error',
                        confirmButtonColor: '#3b82f6'
                    });
                }
            },
            error: function(xhr) {
                console.error('Error updating item status:', xhr);
                Swal.fire({
                    title: 'Error!',
                    text: 'Failed to update item status',
                    icon: 'error',
                    confirmButtonColor: '#3b82f6'
                });
            }
        });
    });
    
    // Handle mark all items as ready
     $(document).on('click', '.mark-all-ready', function() {
         const kotId = $(this).data('kot-id');
         
         $.ajax({
             url: `{{ url('kot-orders') }}/${kotId}/mark-all-ready`,
             method: 'PATCH',
             data: {
                 _token: '{{ csrf_token() }}'
             },
            success: function(response) {
                if (response.success) {
                    Swal.fire({
                        title: 'Success!',
                        text: 'All items marked as ready',
                        icon: 'success',
                        confirmButtonColor: '#3b82f6',
                        timer: 1500
                    });
                    loadKotOrderCards();
                    loadStatistics();
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: response.message || 'Failed to mark items as ready',
                        icon: 'error',
                        confirmButtonColor: '#3b82f6'
                    });
                }
            },
            error: function(xhr) {
                console.error('Error marking items as ready:', xhr);
                Swal.fire({
                    title: 'Error!',
                    text: 'Failed to mark items as ready',
                    icon: 'error',
                    confirmButtonColor: '#3b82f6'
                });
            }
        });
    });
    
    // Handle mark all items as completed
    $(document).on('click', '.mark-all-completed', function() {
        const kotId = $(this).data('kot-id');
        
        $.ajax({
            url: `{{ url('kot-orders') }}/${kotId}/mark-all-completed`,
            method: 'PATCH',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    Swal.fire({
                        title: 'Success!',
                        text: 'All items marked as completed',
                        icon: 'success',
                        confirmButtonColor: '#3b82f6',
                        timer: 1500
                    });
                    loadKotOrderCards();
                    loadStatistics();
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: response.message || 'Failed to mark items as completed',
                        icon: 'error',
                        confirmButtonColor: '#3b82f6'
                    });
                }
            },
            error: function(xhr) {
                console.error('Error marking items as completed:', xhr);
                Swal.fire({
                    title: 'Error!',
                    text: 'Failed to mark items as completed',
                    icon: 'error',
                    confirmButtonColor: '#3b82f6'
                });
            }
        });
    });
    
    // Auto-refresh every 30 seconds
    setInterval(function() {
        if ($('#card-view').hasClass('hidden')) {
            table.draw(false); // false to maintain current page
        } else {
            loadKotOrderCards();
        }
        loadStatistics(); // Refresh statistics
    }, 30000);
});
</script>
@endpush
