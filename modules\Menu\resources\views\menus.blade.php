@extends('layouts.master')

@section('title', 'إدارة القوائم')

@push('styles')
<meta name="csrf-token" content="{{ csrf_token() }}">
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
    /* Custom DataTables Tailwind Styling */
    .dataTables_wrapper {
        font-family: inherit;
    }
    
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_processing,
    .dataTables_wrapper .dataTables_paginate {
        color: #374151;
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }
    .dataTables_processing{
        margin: auto
    }
    .dataTables_wrapper .dataTables_length select,
    .dataTables_wrapper .dataTables_filter input {
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
    
    .dataTables_wrapper .dataTables_filter input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.5rem 0.75rem !important;
        margin: 0 0.125rem !important;
        border-radius: 0.375rem !important;
        border: 1px solid #d1d5db !important;
        background: white !important;
        color: #374151 !important;
        text-decoration: none !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background: #f3f4f6 !important;
        border-color: #9ca3af !important;
        color: #374151 !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background: #3b82f6 !important;
        border-color: #3b82f6 !important;
        color: white !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
        background: #f9fafb !important;
        color: #9ca3af !important;
        border-color: #e5e7eb !important;
    }
    
    table.dataTable {
        border-collapse: separate !important;
        border-spacing: 0 !important;
    }
    
    table.dataTable thead th {
        background-color: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
        font-weight: 600;
        color: #374151;
        padding: 0.75rem;
    }
    
    table.dataTable tbody td {
        padding: 0.75rem;
        border-bottom: 1px solid #f3f4f6;
    }
    
    table.dataTable tbody tr:hover {
        background-color: #f9fafb;
    }
    
    .select2-container--default .select2-selection--single {
        height: 42px;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        padding: 0.5rem;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 26px;
        padding-left: 0;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 40px;
    }
    
    .modal-backdrop {
        background-color: rgba(0, 0, 0, 0.5);
    }
    
    .animate-fadeIn {
        animation: fadeIn 0.3s ease-in-out;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: scale(0.95); }
        to { opacity: 1; transform: scale(1); }
    }
    
    .animate-slideDown {
        animation: slideDown 0.3s ease-in-out;
    }
    
    @keyframes slideDown {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>
@endpush

@section('content')
<div class="container-fluid" dir="rtl">
    <!-- Page Header -->
    <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 mb-2">إدارة القوائم</h1>
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="inline-flex items-center space-x-1 md:space-x-3">
                        <li class="inline-flex items-center">
                            <a href="{{ route('dashboard') }}" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                                <i class="fas fa-home ml-2"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i class="fas fa-chevron-left text-gray-400 mx-2"></i>
                                <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">القوائم</span>
                            </div>
                        </li>
                    </ol>
                </nav>
            </div>
            <div class="mt-4 md:mt-0">
                <button type="button" id="addMenuBtn" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg shadow-sm transition-colors duration-200">
                    <i class="fas fa-plus ml-2"></i>
                    إضافة قائمة جديدة
                </button>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
                <label for="filter_status" class="block text-sm font-medium text-gray-700 mb-2">حالة القائمة</label>
                <select id="filter_status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">جميع الحالات</option>
                    <option value="1">نشط</option>
                    <option value="0">غير نشط</option>
                </select>
            </div>
            <div>
                <label for="filter_type" class="block text-sm font-medium text-gray-700 mb-2">نوع القائمة</label>
                <select id="filter_type" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">جميع الأنواع</option>
                    <option value="breakfast">إفطار</option>
                    <option value="lunch">غداء</option>
                    <option value="dinner">عشاء</option>
                    <option value="drinks">مشروبات</option>
                    <option value="desserts">حلويات</option>
                </select>
            </div>
            <div>
                <label for="filter_default" class="block text-sm font-medium text-gray-700 mb-2">القائمة الافتراضية</label>
                <select id="filter_default" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">الكل</option>
                    <option value="1">افتراضي</option>
                    <option value="0">عادي</option>
                </select>
            </div>
            <div class="flex items-end">
                <button type="button" id="resetFilters" class="w-full px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                    <i class="fas fa-undo ml-2"></i>
                    إعادة تعيين
                </button>
            </div>
        </div>
    </div>

    <!-- DataTable Section -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
        <div class="p-6">
            <div class="overflow-x-auto">
                <table id="menusTable" class="w-full text-sm text-right text-gray-500">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3">#</th>
                            <th scope="col" class="px-6 py-3">اسم القائمة</th>
                          
                            <th scope="col" class="px-6 py-3">نوع القائمة</th>
                            
                            <th scope="col" class="px-6 py-3">الحالة</th>
                            <th scope="col" class="px-6 py-3">افتراضي</th>
                           
                            <th scope="col" class="px-6 py-3">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Menu Modal -->
<div id="menuModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white animate-fadeIn">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between p-4 border-b">
                <h3 class="text-lg font-semibold text-gray-900" id="modalTitle">إضافة قائمة جديدة</h3>
                <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center" id="closeModal">
                    <i class="fas fa-times w-5 h-5"></i>
                </button>
            </div>
            
            <!-- Modal Body -->
            <div class="p-6">
                <form id="menuForm" class="space-y-6">
                    <input type="hidden" id="menuId" name="id">
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Menu Name -->
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">اسم القائمة *</label>
                            <input type="text" id="name" name="name" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="أدخل اسم القائمة">
                            <div class="text-red-500 text-sm mt-1 hidden" id="name_error"></div>
                        </div>
                        
                        <!-- Menu Code -->
                        <div>
                            <label for="code" class="block text-sm font-medium text-gray-700 mb-2">كود القائمة *</label>
                            <input type="text" id="code" name="code" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="أدخل كود القائمة">
                            <div class="text-red-500 text-sm mt-1 hidden" id="code_error"></div>
                        </div>
                    </div>
                    
                    <!-- Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                        <textarea id="description" name="description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="أدخل وصف القائمة"></textarea>
                        <div class="text-red-500 text-sm mt-1 hidden" id="description_error"></div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Menu Type -->
                        <div>
                            <label for="menu_type" class="block text-sm font-medium text-gray-700 mb-2">نوع القائمة</label>
                            <select id="menu_type" name="menu_type" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">اختر نوع القائمة</option>
                                <option value="breakfast">إفطار</option>
                                <option value="lunch">غداء</option>
                                <option value="dinner">عشاء</option>
                                <option value="drinks">مشروبات</option>
                                <option value="desserts">حلويات</option>
                            </select>
                            <div class="text-red-500 text-sm mt-1 hidden" id="menu_type_error"></div>
                        </div>
                        
                        <!-- Sort Order -->
                        <div>
                            <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-2">ترتيب العرض</label>
                            <input type="number" id="sort_order" name="sort_order" min="0" value="0" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <div class="text-red-500 text-sm mt-1 hidden" id="sort_order_error"></div>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Start Time -->
                        <div>
                            <label for="start_time" class="block text-sm font-medium text-gray-700 mb-2">وقت البداية</label>
                            <input type="time" id="start_time" name="start_time" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <div class="text-red-500 text-sm mt-1 hidden" id="start_time_error"></div>
                        </div>
                        
                        <!-- End Time -->
                        <div>
                            <label for="end_time" class="block text-sm font-medium text-gray-700 mb-2">وقت النهاية</label>
                            <input type="time" id="end_time" name="end_time" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <div class="text-red-500 text-sm mt-1 hidden" id="end_time_error"></div>
                        </div>
                    </div>
                    
                    <!-- Available Days -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الأيام المتاحة</label>
                        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-2">
                            <label class="flex items-center">
                                <input type="checkbox" name="available_days[]" value="0" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="mr-2 text-sm">الأحد</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="available_days[]" value="1" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="mr-2 text-sm">الاثنين</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="available_days[]" value="2" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="mr-2 text-sm">الثلاثاء</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="available_days[]" value="3" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="mr-2 text-sm">الأربعاء</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="available_days[]" value="4" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="mr-2 text-sm">الخميس</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="available_days[]" value="5" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="mr-2 text-sm">الجمعة</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="available_days[]" value="6" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="mr-2 text-sm">السبت</span>
                            </label>
                        </div>
                        <div class="text-red-500 text-sm mt-1 hidden" id="available_days_error"></div>
                    </div>
                    
                    <!-- Status Checkboxes -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="flex items-center">
                            <input type="checkbox" id="is_active" name="is_active" value="1" checked class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <label for="is_active" class="mr-2 text-sm font-medium text-gray-700">قائمة نشطة</label>
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" id="is_default" name="is_default" value="1" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <label for="is_default" class="mr-2 text-sm font-medium text-gray-700">قائمة افتراضية</label>
                        </div>
                    </div>
                </form>
            </div>
            
            <!-- Modal Footer -->
            <div class="flex items-center justify-end p-6 border-t border-gray-200 space-x-2">
                <button type="button" id="cancelBtn" class="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                    إلغاء
                </button>
                <button type="button" id="saveBtn" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                    <i class="fas fa-save ml-2"></i>
                    حفظ
                </button>
            </div>
        </div>
    </div>
</div>

<!-- View Menu Modal -->
<div id="viewMenuModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white animate-fadeIn">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between p-4 border-b">
                <h3 class="text-lg font-semibold text-gray-900">تفاصيل القائمة</h3>
                <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center" id="closeViewModal">
                    <i class="fas fa-times w-5 h-5"></i>
                </button>
            </div>
            
            <!-- Modal Body -->
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">اسم القائمة</label>
                        <p class="text-gray-900" id="view_name"></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">كود القائمة</label>
                        <p class="text-gray-900" id="view_code"></p>
                    </div>
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">الوصف</label>
                        <p class="text-gray-900" id="view_description"></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">نوع القائمة</label>
                        <p class="text-gray-900" id="view_menu_type"></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">ترتيب العرض</label>
                        <p class="text-gray-900" id="view_sort_order"></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">وقت البداية</label>
                        <p class="text-gray-900" id="view_start_time"></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">وقت النهاية</label>
                        <p class="text-gray-900" id="view_end_time"></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">الحالة</label>
                        <p class="text-gray-900" id="view_is_active"></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">افتراضي</label>
                        <p class="text-gray-900" id="view_is_default"></p>
                    </div>
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">الأيام المتاحة</label>
                        <p class="text-gray-900" id="view_available_days"></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">تاريخ الإنشاء</label>
                        <p class="text-gray-900" id="view_created_at"></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">آخر تحديث</label>
                        <p class="text-gray-900" id="view_updated_at"></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
$(document).ready(function() {
    // CSRF Token Setup
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Initialize DataTable
    let table = $('#menusTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: "{{ route('menus.index') }}",
            data: function(d) {
                d.status = $('#filter_status').val();
                d.type = $('#filter_type').val();
                d.is_default = $('#filter_default').val();
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'name', name: 'name' },
     
            { data: 'menu_type', name: 'menu_type' },
       
            { data: 'is_active', name: 'is_active', orderable: false },
            { data: 'is_default', name: 'is_default', orderable: false },
        
            { data: 'action', name: 'action', orderable: false, searchable: false }
        ],
        language: {
            "sProcessing": "جارٍ التحميل...",
            "sLengthMenu": "أظهر _MENU_ ",
            "sZeroRecords": "لم يعثر على أية سجلات",
            "sInfo": "إظهار _START_ إلى _END_ من أصل _TOTAL_ مدخل",
            "sInfoEmpty": "يعرض 0 إلى 0 من أصل 0 سجل",
            "sInfoFiltered": "(منتقاة من مجموع _MAX_ مُدخل)",
            "sInfoPostFix": "",
            "sSearch": "بحث",
            "sUrl": "",
            "oPaginate": {
                "sFirst": "الأول",
                "sPrevious": "السابق",
                "sNext": "التالي",
                "sLast": "الأخير"
            }
        },
        responsive: true,
        dom: 'lfrtip',
        pageLength: 10,
        lengthMenu: [[10, 20, 30, 40, 50], [10, 20, 30, 40, 50]],
        order: [[0, 'desc']],
        autoWidth: false,
        columnDefs: [
            { responsivePriority: 1, targets: 0 },
            { responsivePriority: 2, targets: -1 }
        ]
    });

    // Filter functionality
    $('#filter_status, #filter_type, #filter_default').change(function() {
        table.draw();
    });

    // Reset filters
    $('#resetFilters').click(function() {
        $('#filter_status, #filter_type, #filter_default').val('');
        table.draw();
    });

    // Modal functions
    function openModal() {
        $('#menuModal').removeClass('hidden');
        $('body').addClass('overflow-hidden');
    }

    function closeModal() {
        $('#menuModal').addClass('hidden');
        $('body').removeClass('overflow-hidden');
        resetForm();
    }

    function openViewModal() {
        $('#viewMenuModal').removeClass('hidden');
        $('body').addClass('overflow-hidden');
    }

    function closeViewModal() {
        $('#viewMenuModal').addClass('hidden');
        $('body').removeClass('overflow-hidden');
    }

    function resetForm() {
        $('#menuForm')[0].reset();
        $('#menuId').val('');
        $('#modalTitle').text('إضافة قائمة جديدة');
        $('.text-red-500').addClass('hidden');
        $('input[name="available_days[]"]').prop('checked', false);
        $('#is_active').prop('checked', true);
        $('#is_default').prop('checked', false);
    }

    function showErrors(errors) {
        $('.text-red-500').addClass('hidden');
        $.each(errors, function(field, messages) {
            $('#' + field + '_error').text(messages[0]).removeClass('hidden');
        });
    }

    // Event handlers
    $('#addMenuBtn').click(function() {
        resetForm();
        openModal();
    });

    $('#closeModal, #cancelBtn').click(closeModal);
    $('#closeViewModal').click(closeViewModal);

    // Close modal when clicking outside
    $('#menuModal, #viewMenuModal').click(function(e) {
        if (e.target === this) {
            closeModal();
            closeViewModal();
        }
    });

    // Save menu
    $('#saveBtn').click(function() {
        let formData = new FormData($('#menuForm')[0]);
        let menuId = $('#menuId').val();
        let url = menuId ? `/menu/menus/${menuId}` : "{{ route('menus.store') }}";
        let method = menuId ? 'PUT' : 'POST';

        // Handle available days
        let availableDays = [];
        $('input[name="available_days[]"]:checked').each(function() {
            availableDays.push($(this).val());
        });
        
        // Remove existing available_days entries
        formData.delete('available_days[]');
        
        // Add available days to form data
        availableDays.forEach(function(day) {
            formData.append('available_days[]', day);
        });

        // Handle checkboxes
        formData.set('is_active', $('#is_active').is(':checked') ? '1' : '0');
        formData.set('is_default', $('#is_default').is(':checked') ? '1' : '0');

        if (method === 'PUT') {
            formData.append('_method', 'PUT');
        }

        $.ajax({
            url: url,
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'نجح!',
                        text: response.message,
                        timer: 2000,
                        showConfirmButton: false
                    });
                    closeModal();
                    table.draw();
                }
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    showErrors(xhr.responseJSON.errors);
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ!',
                        text: xhr.responseJSON?.message || 'حدث خطأ غير متوقع'
                    });
                }
            }
        });
    });

    // Show menu details
    $(document).on('click', '.show-menu', function() {
        let id = $(this).data('id');
        
        $.ajax({
            url: `/menu/menus/${id}/show`,
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    let menu = response.data;
                    $('#view_name').text(menu.name || '-');
                    $('#view_code').text(menu.code || '-');
                    $('#view_description').text(menu.description || '-');
                    $('#view_menu_type').text(menu.menu_type || '-');
                    $('#view_sort_order').text(menu.sort_order || '0');
                    $('#view_start_time').text(menu.start_time || '-');
                    $('#view_end_time').text(menu.end_time || '-');
                    $('#view_is_active').html(menu.is_active ? '<span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">نشط</span>' : '<span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded">غير نشط</span>');
                    $('#view_is_default').html(menu.is_default ? '<span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">افتراضي</span>' : '<span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">عادي</span>');
                    
                    // Available days
                    let days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
                    let availableDays = menu.available_days ? menu.available_days.map(day => days[day]).join(', ') : 'غير محدد';
                    $('#view_available_days').text(availableDays);
                    
                    $('#view_created_at').text(menu.created_at || '-');
                    $('#view_updated_at').text(menu.updated_at || '-');
                    
                    openViewModal();
                }
            },
            error: function(xhr) {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ!',
                    text: xhr.responseJSON?.message || 'فشل في استرجاع بيانات القائمة'
                });
            }
        });
    });

    // Edit menu
    $(document).on('click', '.edit-menu', function() {
        let id = $(this).data('id');
        
        $.ajax({
            url: `/menu/menus/${id}/edit`,
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    let menu = response.data;
                    $('#menuId').val(menu.id);
                    $('#name').val(menu.name);
                    $('#code').val(menu.code);
                    $('#description').val(menu.description);
                    $('#menu_type').val(menu.menu_type);
                    $('#sort_order').val(menu.sort_order || 0);
                    $('#start_time').val(menu.start_time);
                    $('#end_time').val(menu.end_time);
                    $('#is_active').prop('checked', menu.is_active);
                    $('#is_default').prop('checked', menu.is_default);
                    
                    // Set available days
                    $('input[name="available_days[]"]').prop('checked', false);
                    if (menu.available_days) {
                        menu.available_days.forEach(function(day) {
                            $(`input[name="available_days[]"][value="${day}"]`).prop('checked', true);
                        });
                    }
                    
                    $('#modalTitle').text('تعديل القائمة');
                    openModal();
                }
            },
            error: function(xhr) {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ!',
                    text: xhr.responseJSON?.message || 'فشل في استرجاع بيانات القائمة للتعديل'
                });
            }
        });
    });

    // Delete menu
    $(document).on('click', '.delete-menu', function() {
        let id = $(this).data('id');
        
        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: 'لن تتمكن من التراجع عن هذا الإجراء!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'نعم، احذف!',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: `/menu/menus/${id}`,
                    method: 'DELETE',
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'تم الحذف!',
                                text: response.message,
                                timer: 2000,
                                showConfirmButton: false
                            });
                            table.draw();
                        }
                    },
                    error: function(xhr) {
                        Swal.fire({
                            icon: 'error',
                            title: 'خطأ!',
                            text: xhr.responseJSON?.message || 'فشل في حذف القائمة'
                        });
                    }
                });
            }
        });
    });
});
</script>
@endpush