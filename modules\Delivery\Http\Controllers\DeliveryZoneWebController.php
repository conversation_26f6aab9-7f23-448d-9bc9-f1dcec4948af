<?php

namespace Modules\Delivery\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\Delivery\Models\DeliveryZone;
use Modules\Delivery\Services\DeliveryZoneService;
use App\Models\Branch;
use Illuminate\Http\Request;
use Ya<PERSON>ra\DataTables\Facades\DataTables;

class DeliveryZoneWebController extends Controller
{
    protected $zoneService;

    public function __construct(DeliveryZoneService $zoneService)
    {
        $this->zoneService = $zoneService;
    }

    /**
     * Display a listing of delivery zones.
     */
    public function index()
    {
        $branches = Branch::where('status', 'active')->get();
        
        return view('delivery::zones.index', compact('branches'));
    }

    /**
     * Show the form for creating a new delivery zone.
     */
    public function create()
    {
        $branches = Branch::where('status', 'active')->get();
        
        return view('delivery::zones.create', compact('branches'));
    }

    /**
     * Store a newly created delivery zone.
     */
    public function store(Request $request)
    {
        $request->validate([
            'branch_id' => 'required|exists:branches,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'delivery_fee' => 'required|numeric|min:0',
            'minimum_order_amount' => 'required|numeric|min:0',
            'estimated_delivery_time_minutes' => 'required|integer|min:1',
            'priority' => 'required|integer|min:1',
            'coordinates' => 'required|array|min:3',
            'coordinates.*.latitude' => 'required|numeric|between:-90,90',
            'coordinates.*.longitude' => 'required|numeric|between:-180,180',
        ]);

        try {
            $this->zoneService->createZone($request->all());
            
            return redirect()->route('delivery.zones.index')
                ->with('success', 'تم إنشاء منطقة التوصيل بنجاح');
        } catch (\Exception $e) {
            return back()->withInput()
                ->with('error', 'حدث خطأ أثناء إنشاء منطقة التوصيل: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified delivery zone.
     */
    public function show(DeliveryZone $zone)
    {
        $zone->load(['branch']);
        
        return view('delivery::zones.show', compact('zone'));
    }

    /**
     * Show the form for editing the specified delivery zone.
     */
    public function edit(DeliveryZone $zone)
    {
        $branches = Branch::where('status', 'active')->get();
        
        return view('delivery::zones.edit', compact('zone', 'branches'));
    }

    /**
     * Update the specified delivery zone.
     */
    public function update(Request $request, DeliveryZone $zone)
    {
        $request->validate([
            'branch_id' => 'required|exists:branches,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'delivery_fee' => 'required|numeric|min:0',
            'minimum_order_amount' => 'required|numeric|min:0',
            'estimated_delivery_time_minutes' => 'required|integer|min:1',
            'priority' => 'required|integer|min:1',
            'coordinates' => 'required|array|min:3',
            'coordinates.*.latitude' => 'required|numeric|between:-90,90',
            'coordinates.*.longitude' => 'required|numeric|between:-180,180',
        ]);

        try {
            $this->zoneService->updateZone($zone->id, $request->all());
            
            return redirect()->route('delivery.zones.show', $zone)
                ->with('success', 'تم تحديث منطقة التوصيل بنجاح');
        } catch (\Exception $e) {
            return back()->withInput()
                ->with('error', 'حدث خطأ أثناء تحديث منطقة التوصيل: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified delivery zone.
     */
    public function destroy(DeliveryZone $zone)
    {
        try {
            $this->zoneService->deleteZone($zone->id);
            
            return redirect()->route('delivery.zones.index')
                ->with('success', 'تم حذف منطقة التوصيل بنجاح');
        } catch (\Exception $e) {
            return back()->with('error', 'حدث خطأ أثناء حذف منطقة التوصيل: ' . $e->getMessage());
        }
    }

    /**
     * Get zones data for DataTable.
     */
    public function getZonesData(Request $request)
    {
        $query = DeliveryZone::with(['branch'])
            ->select(['id', 'branch_id', 'name', 'description', 'delivery_fee', 'minimum_order_amount', 
                     'estimated_delivery_time_minutes', 'priority', 'is_active', 'created_at']);

        // Apply filters
        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        if ($request->filled('is_active')) {
            $query->where('is_active', $request->is_active);
        }

        return DataTables::of($query)
            ->addIndexColumn()
            ->addColumn('branch_name', function ($zone) {
                return $zone->branch ? $zone->branch->name : '-';
            })
            ->addColumn('delivery_info', function ($zone) {
                return '<div class="delivery-info">' .
                       '<div><strong>رسوم التوصيل:</strong> ' . number_format($zone->delivery_fee, 2) . ' ر.س</div>' .
                       '<div><strong>الحد الأدنى:</strong> ' . number_format($zone->minimum_order_amount, 2) . ' ر.س</div>' .
                       '<div><strong>وقت التوصيل:</strong> ' . $zone->estimated_delivery_time_minutes . ' دقيقة</div>' .
                       '</div>';
            })
            ->addColumn('status_badge', function ($zone) {
                return $zone->is_active 
                    ? '<span class="badge badge-success">نشط</span>'
                    : '<span class="badge badge-secondary">غير نشط</span>';
            })
            ->addColumn('action', function ($zone) {
                $actions = '<div class="btn-group" role="group">';
                $actions .= '<a href="' . route('delivery.zones.show', $zone) . '" class="btn btn-sm btn-info" title="عرض"><i class="fa fa-eye"></i></a>';
                $actions .= '<a href="' . route('delivery.zones.edit', $zone) . '" class="btn btn-sm btn-primary" title="تعديل"><i class="fa fa-edit"></i></a>';
                
                if ($zone->is_active) {
                    $actions .= '<button type="button" class="btn btn-sm btn-warning" onclick="toggleStatus(' . $zone->id . ', false)" title="إلغاء تفعيل"><i class="fa fa-pause"></i></button>';
                } else {
                    $actions .= '<button type="button" class="btn btn-sm btn-success" onclick="toggleStatus(' . $zone->id . ', true)" title="تفعيل"><i class="fa fa-play"></i></button>';
                }
                
                $actions .= '<button type="button" class="btn btn-sm btn-danger" onclick="deleteZone(' . $zone->id . ')" title="حذف"><i class="fa fa-trash"></i></button>';
                $actions .= '</div>';
                
                return $actions;
            })
            ->rawColumns(['delivery_info', 'status_badge', 'action'])
            ->make(true);
    }

    /**
     * Toggle zone status.
     */
    public function toggleStatus(Request $request, DeliveryZone $zone)
    {
        try {
            $zone->update(['is_active' => $request->is_active]);
            
            return response()->json([
                'success' => true,
                'message' => $request->is_active ? 'تم تفعيل المنطقة بنجاح' : 'تم إلغاء تفعيل المنطقة بنجاح'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تغيير حالة المنطقة'
            ], 500);
        }
    }
}