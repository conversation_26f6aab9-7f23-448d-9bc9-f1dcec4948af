<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Tenant;
use App\Models\User;
use App\Models\Branch;
use Modules\Transaction\Models\Transaction;
use Modules\Transaction\Models\Payment;
use Modules\Transaction\Models\PaymentMethod;
use App\Models\Order;

try {
    // Get the tenant and user
    $tenant = Tenant::where('code', 'DEFAULT_TENANT')->first();
    $user = User::where('email', '<EMAIL>')->first();
    
    if (!$tenant || !$user) {
        echo "Error: Tenant or user not found\n";
        exit(1);
    }
    
    echo "Using Tenant ID: {$tenant->id}, User ID: {$user->id}\n";
    
    // Create or get a branch
    $branch = Branch::firstOrCreate(
        ['tenant_id' => $tenant->id, 'name' => 'Main Branch'],
        [
            'tenant_id' => $tenant->id,
            'name' => 'Main Branch',
            'code' => 'MAIN',
            'address' => '123 Main Street',
            'phone' => '************',
            'email' => '<EMAIL>',
            'is_active' => true,
        ]
    );
    
    echo "Using Branch ID: {$branch->id}\n";
    
    // Get payment methods
    $paymentMethods = PaymentMethod::where('is_active', true)->get();
    echo "Found " . $paymentMethods->count() . " payment methods\n";
    
    // Create sample orders and transactions
    for ($i = 1; $i <= 5; $i++) {
        // Create a sample order
        $order = Order::create([
            'tenant_id' => $tenant->id,
            'branch_id' => $branch->id,
            'order_number' => 'ORD-' . str_pad($i, 6, '0', STR_PAD_LEFT),
            'order_type' => 'dine_in',
            'status' => 'completed',
            'subtotal' => rand(50, 200),
            'tax_amount' => rand(5, 20),
            'total_amount' => rand(55, 220),
            'cashier_id' => $user->id,
            'created_at' => now()->subDays(rand(0, 30)),
        ]);
        
        // Create a transaction for the order
        $transaction = Transaction::create([
            'tenant_id' => $tenant->id,
            'order_id' => $order->id,
            'transaction_number' => 'TXN-' . str_pad($i, 6, '0', STR_PAD_LEFT),
            'transaction_type' => 'sale',
            'total_amount' => $order->total_amount,
            'paid_amount' => $order->total_amount,
            'due_amount' => 0,
            'status' => 'completed',
            'processed_by' => $user->id,
            'created_at' => $order->created_at,
        ]);
        
        // Create a payment for the transaction
        $paymentMethod = $paymentMethods->random();
        $payment = Payment::create([
            'tenant_id' => $tenant->id,
            'transaction_id' => $transaction->id,
            'payment_method_id' => $paymentMethod->id,
            'payment_number' => 'PAY-' . str_pad($i, 6, '0', STR_PAD_LEFT),
            'amount' => $transaction->total_amount,
            'status' => 'completed',
            'payment_date' => $transaction->created_at,
            'reference_number' => 'REF-' . rand(100000, 999999),
            'payment_details' => "Payment via {$paymentMethod->name}",
            'processed_by' => $user->id,
            'created_at' => $transaction->created_at,
        ]);
        
        echo "Created Order: {$order->order_number}, Transaction: {$transaction->transaction_number}, Payment: {$payment->payment_number}\n";
    }
    
    echo "\nSample data created successfully!\n";
    echo "Total payments created: " . Payment::where('tenant_id', $tenant->id)->count() . "\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}