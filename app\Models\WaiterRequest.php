<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WaiterRequest extends Model
{
    use HasFactory;

    protected $fillable = [
        'tenant_id',
        'table_id',
        'branch_id',
        'status',
        'waiter_id',
        'notes',
        'request_type',
        'response_time',
    ];

    // Status options
    const STATUSES = [
        'pending' => 'Pending',
        'completed' => 'Completed',
        'cancelled' => 'Cancelled',
    ];

    // Request type options
    const REQUEST_TYPES = [
        'service' => 'Service',
        'bill' => 'Bill',
        'assistance' => 'Assistance',
        'complaint' => 'Complaint',
    ];

    protected $casts = [
        'response_time' => 'datetime',
    ];

    /**
     * Get the branch that owns the waiter request.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the table that owns the waiter request.
     */
    public function table(): BelongsTo
    {
        return $this->belongsTo(Table::class);
    }

    /**
     * Get the waiter assigned to this request.
     */
    public function waiter(): BelongsTo
    {
        return $this->belongsTo(User::class, 'waiter_id');
    }

    /**
     * Scope a query to only include pending requests.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include completed requests.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope a query to filter by waiter.
     */
    public function scopeByWaiter($query, $waiterId)
    {
        return $query->where('waiter_id', $waiterId);
    }

    /**
     * Scope a query to filter by branch.
     */
    public function scopeByBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * Mark the request as completed.
     */
    public function complete()
    {
        $this->update(['status' => 'completed']);
    }

    /**
     * Cancel the request.
     */
    public function cancel()
    {
        $this->update(['status' => 'cancelled']);
    }

    /**
     * Get the human-readable status.
     */
    public function getStatusNameAttribute()
    {
        return self::STATUSES[$this->status] ?? $this->status;
    }

    /**
     * Check if the request is pending.
     */
    public function isPending()
    {
        return $this->status === 'pending';
    }

    /**
     * Check if the request is completed.
     */
    public function isCompleted()
    {
        return $this->status === 'completed';
    }

    /**
     * Check if the request is cancelled.
     */
    public function isCancelled()
    {
        return $this->status === 'cancelled';
    }
}