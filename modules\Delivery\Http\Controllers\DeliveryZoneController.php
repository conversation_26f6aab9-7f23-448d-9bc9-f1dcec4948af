<?php

namespace Modules\Delivery\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\Delivery\Services\DeliveryZoneService;
use Modules\Delivery\Http\Requests\CreateDeliveryZoneRequest;
use Modules\Delivery\Http\Requests\UpdateDeliveryZoneRequest;
use Modules\Delivery\Http\Resources\DeliveryZoneResource;
use Modules\Delivery\Http\Resources\DeliveryZoneCollection;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class DeliveryZoneController extends Controller
{
    public function __construct(
        private DeliveryZoneService $zoneService
    ) {}

    /**
     * Get tenant ID from authenticated user
     */
    private function getTenantId(): int
    {
        return Auth::user()->tenant_id;
    }

    /**
     * Get delivery zones with filters
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $filters = $request->only([
                'branch_id', 'is_active', 'name'
            ]);
            
            $perPage = $request->get('per_page', 15);
            
            $zones = $this->zoneService->getZones($filters, $perPage);

            return response()->json([
                'success' => true,
                'data' => new DeliveryZoneCollection($zones),
                'meta' => [
                    'current_page' => $zones->currentPage(),
                    'last_page' => $zones->lastPage(),
                    'per_page' => $zones->perPage(),
                    'total' => $zones->total(),
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve delivery zones',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Create new delivery zone
     */
    public function store(CreateDeliveryZoneRequest $request): JsonResponse
    {
        try {
            $zone = $this->zoneService->createZone(
                $request->validated()
            );

            return response()->json([
                'success' => true,
                'message' => 'Delivery zone created successfully',
                'data' => new DeliveryZoneResource($zone),
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create delivery zone',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get specific delivery zone
     */
    public function show(int $id): JsonResponse
    {
        try {
            $zone = $this->zoneService->getZoneById($id);
            
            if (!$zone) {
                return response()->json([
                    'success' => false,
                    'message' => 'Delivery zone not found',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => new DeliveryZoneResource($zone),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve delivery zone',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update delivery zone
     */
    public function update(UpdateDeliveryZoneRequest $request, int $id): JsonResponse
    {
        try {
            $zone = $this->zoneService->updateZone(
                $id,
                $request->validated()
            );

            return response()->json([
                'success' => true,
                'message' => 'Delivery zone updated successfully',
                'data' => new DeliveryZoneResource($zone),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update delivery zone',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete delivery zone
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $this->zoneService->deleteZone($id);

            return response()->json([
                'success' => true,
                'message' => 'Delivery zone deleted successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete delivery zone',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Find zone by coordinates
     */
    public function findByCoordinates(Request $request): JsonResponse
    {
        $request->validate([
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
        ]);

        try {
            $zone = $this->zoneService->findZoneByCoordinates(
                $request->latitude,
                $request->longitude
            );

            if (!$zone) {
                return response()->json([
                    'success' => false,
                    'message' => 'No delivery zone found for these coordinates',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => new DeliveryZoneResource($zone),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to find delivery zone',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Check delivery availability for coordinates or address
     */
    public function checkAvailability(Request $request): JsonResponse
    {
        $request->validate([
            'latitude' => 'required_without:address|numeric|between:-90,90',
            'longitude' => 'required_without:address|numeric|between:-180,180',
            'address' => 'required_without_all:latitude,longitude|string|max:255',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:100',
            'branch_id' => 'nullable|exists:branches,id',
        ]);

        try {
            // Check by coordinates if provided
            if ($request->has('latitude') && $request->has('longitude')) {
                $availability = $this->zoneService->checkDeliveryAvailability(
                    $request->latitude,
                    $request->longitude,
                    $request->branch_id
                );
            }
            // Check by address if provided
            elseif ($request->has('address')) {
                $availability = $this->zoneService->checkDeliveryAvailabilityByAddress(
                    $request->address,
                    $request->city,
                    $request->state,
                    $request->postal_code,
                    $request->country,
                    $request->branch_id
                );
            }

            $responseData = [
                'success' => true,
                'data' => [
                    'is_available' => $availability['available']
                ]
            ];
            
            if ($availability['available']) {
                $responseData['data']['zone'] = new DeliveryZoneResource($availability['zone']);
                $responseData['data']['delivery_fee'] = $availability['delivery_fee'];
                $responseData['data']['minimum_order_amount'] = $availability['minimum_order_amount'];
                $responseData['data']['estimated_delivery_time'] = $availability['estimated_delivery_time'];
            }
            
            return response()->json($responseData);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to check delivery availability',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Calculate delivery fee for coordinates
     */
    public function calculateFee(Request $request): JsonResponse
    {
        $request->validate([
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'branch_id' => 'nullable|exists:branches,id',
        ]);

        try {
            $feeData = $this->zoneService->calculateDeliveryFee(
                $request->latitude,
                $request->longitude,
                $request->branch_id
            );

            return response()->json([
                'success' => true,
                'data' => $feeData,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to calculate delivery fee',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Toggle zone status
     */
    public function toggleStatus(int $id): JsonResponse
    {
        try {
            $zone = $this->zoneService->toggleZoneStatus($id);

            return response()->json([
                'success' => true,
                'message' => 'Zone status updated successfully',
                'data' => new DeliveryZoneResource($zone),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to toggle zone status',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get zone statistics
     */
    public function statistics(Request $request, int $id): JsonResponse
    {
        $request->validate([
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
        ]);

        try {
            $statistics = $this->zoneService->getZoneStatistics(
                $id,
                $request->date_from,
                $request->date_to
            );

            return response()->json([
                'success' => true,
                'data' => $statistics,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve zone statistics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get all zones for branch
     */
    public function byBranch(Request $request, int $branchId): JsonResponse
    {
        try {
            $zones = $this->zoneService->getZonesByBranch($branchId);

            return response()->json([
                'success' => true,
                'data' => DeliveryZoneResource::collection($zones),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve zones for branch',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}