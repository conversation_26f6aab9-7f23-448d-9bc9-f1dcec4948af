<?php

namespace Modules\Menu\Http\Controllers\Web;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Modules\Menu\Http\Requests\StoreAddonRequest;
use Modules\Menu\Http\Requests\UpdateAddonRequest;
use Modules\Menu\Services\AddonService;
use App\Helpers\BranchHelper;
use App\Helpers\ResponseHelper;
use App\Models\MenuItemAddon as Addon;
use Yajra\DataTables\Facades\DataTables;

class AddonController extends Controller
{
    protected $addonService;

    public function __construct(AddonService $addonService)
    {
        $this->addonService = $addonService;
    }

    /**
     * Display a listing of addons for DataTable
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $branchId = BranchHelper::getCurrentBranchId();
            $addons = Addon::with(['menuItem.menu'])
                ->forBranch($branchId)
                ->select('menu_item_addons.*');

            // Apply filters
            if ($request->filled('menu_item_id')) {
                $addons->where('menu_item_id', $request->menu_item_id);
            }

            if ($request->filled('status')) {
                $addons->where('is_active', $request->status);
            }

            if ($request->filled('is_required')) {
                $addons->where('is_required', $request->is_required);
            }

            return DataTables::of($addons)
                ->addIndexColumn()
                ->addColumn('menu_item', function ($addon) {
                    return $addon->menuItem ? $addon->menuItem->name : 'غير محدد';
                })
                ->editColumn('addon_group_name', function ($addon) {
                    return $addon->addon_group_name ?: '-';
                })
                ->editColumn('price', function ($addon) {
                    return number_format((float)$addon->price, 2) . ' ريال';
                })
                ->addColumn('is_required', function ($addon) {
                    return $addon->is_required ?
                        '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">مطلوب</span>' :
                        '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">اختياري</span>';
                })
                ->addColumn('is_active', function ($addon) {
                    return $addon->is_active ?
                        '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">نشط</span>' :
                        '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">غير نشط</span>';
                })
                ->editColumn('created_at', function ($addon) {
                    return $addon->created_at->format('Y-m-d H:i');
                })
                ->addColumn('action', function ($addon) {
                    return '
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <button type="button" class="inline-flex items-center px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded transition-colors duration-200 show-addon" data-id="' . $addon->id . '" title="عرض">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                            </button>
                            <button type="button" class="inline-flex items-center px-2 py-1 bg-yellow-600 hover:bg-yellow-700 text-white text-xs font-medium rounded transition-colors duration-200 edit-addon" data-id="' . $addon->id . '" title="تعديل">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                            </button>
                            <button type="button" class="inline-flex items-center px-2 py-1 bg-red-600 hover:bg-red-700 text-white text-xs font-medium rounded transition-colors duration-200 delete-addon" data-id="' . $addon->id . '" title="حذف">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    ';
                })
                ->rawColumns(['is_required', 'is_active', 'action'])
                ->make(true);
        }

        return view('menu::addons');
    }

    /**
     * Get menu items list for filter dropdown
     */
    public function getMenuItemsList(): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $menuItems = \App\Models\MenuItem::whereHas('menu', function ($q) use ($branchId) {
                    $q->where('branch_id', $branchId);
                })
                ->where('is_active', true)
                ->select('id', 'name')
                ->orderBy('name')
                ->get();
            
            return ResponseHelper::success($menuItems, 'Menu items retrieved successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve menu items: ' . $e->getMessage());
        }
    }

    /**
     * Store a newly created addon
     */
    public function store(StoreAddonRequest $request): JsonResponse
    {
        try {
            $data = BranchHelper::addBranchAndTenantIds($request->validated());
            $addon = $this->addonService->createAddonForWeb($data);
            
            return ResponseHelper::success($addon, 'Addon created successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to create addon: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified addon
     */
    public function show(int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $addon = $this->addonService->getAddonByIdForBranch($id, $branchId);
            
            if (!$addon) {
                return ResponseHelper::notFound('Addon not found');
            }
            
            return ResponseHelper::success($addon, 'Addon retrieved successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve addon: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for editing the specified addon
     */
    public function edit(int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $addon = $this->addonService->getAddonByIdForBranch($id, $branchId);
            
            if (!$addon) {
                return ResponseHelper::notFound('Addon not found');
            }
            
            return ResponseHelper::success($addon, 'Addon retrieved for editing');
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve addon: ' . $e->getMessage());
        }
    }

    /**
     * Update the specified addon
     */
    public function update(UpdateAddonRequest $request, int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $addon = $this->addonService->updateAddonForWeb($id, $request->validated(), $branchId);
            
            if (!$addon) {
                return ResponseHelper::notFound('Addon not found');
            }
            
            return ResponseHelper::success($addon, 'Addon updated successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to update addon: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified addon
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $deleted = $this->addonService->deleteAddonForBranch($id, $branchId);
            
            if (!$deleted) {
                return ResponseHelper::notFound('Addon not found');
            }
            
            return ResponseHelper::success(null, 'Addon deleted successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to delete addon: ' . $e->getMessage());
        }
    }
}