<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام نقاط البيع للمطاعم | أدر مطعمك بذكاء</title>
    
    <!-- External Libraries -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/feather-icons"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>

    <style>
        :root {
            --primary-color: #16c79a;
            --primary-dark: #12a17d;
            --secondary-color: #f8f9fa;
            --background-color: #0f172a;
            --surface-color: #1e293b;
            --text-color: #e2e8f0;
            --text-muted-color: #94a3b8;
            --border-radius: 12px;
            --font-family: 'Tajawal', sans-serif;
            --gradient-primary: linear-gradient(135deg, #16c79a 0%, #11998e 100%);
            --gradient-surface: linear-gradient(135deg, #1e293b 0%, #334155 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: var(--font-family);
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.7;
            overflow-x: hidden;
        }

        h1, h2, h3, h4 {
            font-weight: 700;
            line-height: 1.3;
        }

        h1 { font-size: 3.5rem; }
        h2 { font-size: 2.5rem; }
        h3 { font-size: 1.5rem; }

        p {
            color: var(--text-muted-color);
            font-weight: 400;
        }

        .section-title {
            text-align: center;
            margin-bottom: 1rem;
            color: var(--secondary-color);
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: var(--gradient-primary);
            border-radius: 2px;
        }

        .section-subtitle {
            text-align: center;
            max-width: 600px;
            margin: 0 auto 4rem auto;
        }

        .cta-button {
            display: inline-block;
            padding: 14px 32px;
            border-radius: var(--border-radius);
            font-weight: 700;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            text-decoration: none;
        }

        .cta-button.primary {
            background: var(--gradient-primary);
            color: #fff;
        }

        .cta-button.primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .cta-button.primary:hover::before {
            left: 100%;
        }

        .cta-button.primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(22, 199, 154, 0.3);
        }

        .cta-button.secondary {
            background-color: transparent;
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
        }

        .cta-button.secondary:hover {
            background-color: var(--primary-color);
            color: #fff;
            transform: translateY(-2px);
        }

        /* Navbar */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem 5%;
            position: fixed;
            width: 100%;
            top: 0;
            left: 0;
            z-index: 1000;
            background: rgba(15, 23, 42, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .navbar.scrolled {
            background: rgba(15, 23, 42, 0.98);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 800;
            color: #fff;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2.5rem;
        }

        .nav-links a {
            color: var(--secondary-color);
            font-weight: 500;
            position: relative;
            padding-bottom: 5px;
            text-decoration: none;
        }

        .nav-links a::after {
            content: '';
            position: absolute;
            bottom: 0;
            right: 0;
            width: 0;
            height: 2px;
            background: var(--gradient-primary);
            transition: width 0.3s ease;
        }

        .nav-links a:hover::after {
            width: 100%;
        }

        /* Enhanced Hero Section */
        .hero-section {
            position: relative;
            height: 100vh;
            display: grid;
            grid-template-columns: 1fr 1fr;
            align-items: center;
            padding: 0 5%;
            background: radial-gradient(ellipse at center, #1e293b 0%, var(--background-color) 70%);
            overflow: hidden;
        }

        .hero-content {
            z-index: 2;
            max-width: 600px;
        }

        .hero-content h1 {
            margin-bottom: 1.5rem;
            color: #fff;
            background: linear-gradient(135deg, #fff 0%, var(--primary-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-content p {
            font-size: 1.2rem;
            margin-bottom: 3rem;
            color: var(--text-color);
        }

        .hero-buttons {
            display: flex;
            gap: 1.5rem;
            align-items: center;
        }

        .hero-visual {
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        #hero-canvas {
            width: 100%;
            height: 600px;
            max-width: 500px;
        }

        .floating-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .floating-icon {
            position: absolute;
            width: 40px;
            height: 40px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            animation: float 6s ease-in-out infinite;
        }

        .floating-icon:nth-child(1) { top: 20%; right: 10%; animation-delay: 0s; }
        .floating-icon:nth-child(2) { top: 60%; right: 20%; animation-delay: 2s; }
        .floating-icon:nth-child(3) { top: 40%; left: 10%; animation-delay: 4s; }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        /* Stats Section */
        .stats-section {
            padding: 80px 5%;
            background: var(--surface-color);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 3rem;
            text-align: center;
        }

        .stat-item {
            padding: 2rem;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: block;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-color);
            font-weight: 500;
        }

        /* Interactive Features Section */
        .interactive-features {
            padding: 100px 5%;
            background-color: var(--background-color);
        }

        .features-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: start;
            margin-top: 4rem;
        }

        .features-list .feature-item {
            margin-bottom: 8rem;
            padding: 2rem;
            border-radius: var(--border-radius);
            transition: all 0.4s ease;
            cursor: default;
            border: 1px solid transparent;
        }

        .features-list .feature-item.active {
            background: var(--gradient-surface);
            border-color: rgba(22, 199, 154, 0.3);
            transform: translateX(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .features-list .feature-item h3 {
            color: var(--secondary-color);
            margin-bottom: 1rem;
        }

        .features-visual {
            position: sticky;
            top: 150px;
            height: calc(100vh - 200px);
        }

        .tablet-frame {
            width: 100%;
            max-width: 500px;
            aspect-ratio: 4 / 3;
            background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
            border-radius: 25px;
            padding: 20px;
            box-shadow: 
                0 25px 50px -12px rgba(0, 0, 0, 0.5),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: center;
            align-items: center;
            margin: auto;
            position: relative;
        }

        .tablet-frame::before {
            content: '';
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 4px;
            background: #333;
            border-radius: 2px;
        }

        #feature-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 15px;
            transition: all 0.4s ease-in-out;
        }

        /* Pricing Section */
        .pricing-section {
            padding: 100px 5%;
            background: linear-gradient(135deg, var(--background-color) 0%, var(--surface-color) 100%);
            position: relative;
        }

        .pricing-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="%23ffffff" opacity="0.03"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 2rem;
            margin-top: 4rem;
        }

        .pricing-card {
            background: rgba(30, 41, 59, 0.7);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 3rem 2rem;
            text-align: center;
            position: relative;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .pricing-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .pricing-card:hover::before {
            transform: scaleX(1);
        }

        .pricing-card:hover {
            transform: translateY(-10px);
            border-color: rgba(22, 199, 154, 0.3);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        }

        .pricing-card.popular {
            border-color: var(--primary-color);
            background: rgba(22, 199, 154, 0.1);
            transform: scale(1.05);
        }

        .pricing-card.popular::before {
            transform: scaleX(1);
        }

        .popular-badge {
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--gradient-primary);
            color: white;
            padding: 5px 20px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .plan-name {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--secondary-color);
            margin-bottom: 1rem;
        }

        .plan-price {
            font-size: 3rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        .plan-period {
            color: var(--text-muted-color);
            margin-bottom: 2rem;
        }

        .plan-features {
            list-style: none;
            margin-bottom: 2.5rem;
        }

        .plan-features li {
            padding: 0.7rem 0;
            color: var(--text-color);
            position: relative;
            padding-right: 30px;
        }

        .plan-features li::before {
            content: '✓';
            position: absolute;
            right: 0;
            color: var(--primary-color);
            font-weight: bold;
        }

        /* Showcase Section */
        .showcase-section {
            padding: 100px 5%;
            background-color: var(--background-color);
            position: relative;
            overflow: hidden;
        }

        .showcase-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .showcase-card {
            background: rgba(30, 41, 59, 0.5);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: 2.5rem 2rem;
            border-radius: var(--border-radius);
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .showcase-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(22, 199, 154, 0.1), transparent);
            transition: left 0.5s;
        }

        .showcase-card:hover::before {
            left: 100%;
        }

        .showcase-card:hover {
            transform: translateY(-10px);
            border-color: rgba(22, 199, 154, 0.5);
        }

        .showcase-card i {
            color: var(--primary-color);
            width: 48px;
            height: 48px;
            margin-bottom: 1.5rem;
        }

        .showcase-card h4 {
            margin-bottom: 1rem;
            color: var(--secondary-color);
        }

        /* Final CTA Section */
        .cta-final-section {
            padding: 100px 5%;
            text-align: center;
            background: var(--gradient-surface);
            position: relative;
        }

        .cta-content {
            max-width: 600px;
            margin: auto;
        }

        .cta-content h2 {
            margin-bottom: 1rem;
            color: var(--secondary-color);
        }

        .cta-form {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
            background: rgba(255, 255, 255, 0.05);
            padding: 10px;
            border-radius: var(--border-radius);
            backdrop-filter: blur(10px);
        }

        .cta-form input {
            flex-grow: 1;
            padding: 14px;
            border-radius: var(--border-radius);
            border: 1px solid rgba(255, 255, 255, 0.1);
            background-color: rgba(255, 255, 255, 0.05);
            color: var(--text-color);
            font-family: var(--font-family);
            font-size: 1rem;
        }

        .cta-form input:focus {
            outline: none;
            border-color: var(--primary-color);
            background-color: rgba(255, 255, 255, 0.1);
        }

        /* Footer */
        .footer {
            background-color: #0c1221;
            padding: 4rem 5% 2rem;
        }

        .footer-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .footer h4 {
            color: var(--secondary-color);
            margin-bottom: 1rem;
        }

        .footer-links ul {
            list-style: none;
        }

        .footer-links li {
            margin-bottom: 0.5rem;
        }

        .footer-links a {
            color: var(--text-muted-color);
            text-decoration: none;
        }

        .footer-links a:hover {
            color: var(--primary-color);
        }

        .footer-contact p {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid var(--surface-color);
            color: var(--text-muted-color);
            font-size: 0.9rem;
        }

        /* Animations */
        .reveal {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.8s ease, transform 0.8s ease;
        }

        .reveal.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .animate-on-load {
            animation: fadeInSlideUp 1s ease-out forwards;
            opacity: 0;
        }
        .animate-on-load:nth-child(2) { animation-delay: 0.2s; }
        .animate-on-load:nth-child(3) { animation-delay: 0.4s; }

        @keyframes fadeInSlideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive Design */
        @media (max-width: 992px) {
            .hero-section {
                grid-template-columns: 1fr;
                text-align: center;
                gap: 2rem;
            }
            
            .features-container {
                grid-template-columns: 1fr;
            }
            
            .features-visual {
                display: none;
            }
            
            .features-list .feature-item {
                margin-bottom: 2rem;
                background: var(--gradient-surface);
            }
            
            .pricing-grid {
                grid-template-columns: 1fr;
            }
            
            .pricing-card.popular {
                transform: none;
            }
        }

        @media (max-width: 768px) {
            h1 { font-size: 2.5rem; }
            h2 { font-size: 2rem; }

            .nav-links { 
                display: none; 
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .cta-form {
                flex-direction: column;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .floating-icon {
                display: none;
            }
        }
    </style>
</head>
<body>

    <!-- Enhanced Header & Hero Section -->
    <header class="hero-section">
        <nav class="navbar">
            <div class="logo">نظامك POS</div>
            <ul class="nav-links">
                <li><a href="#features">المزايا</a></li>
                <li><a href="#pricing">الأسعار</a></li>
                <li><a href="#showcase">النظام بالصور</a></li>
                <li><a href="#contact">تواصل معنا</a></li>
            </ul>
            <a href="#contact" class="cta-button secondary">اطلب عرض تجريبي</a>
        </nav>
        
        <div class="hero-content">
            <h1 class="animate-on-load">نظام المستقبل لإدارة مطعمك</h1>
            <p class="animate-on-load">أدر عملياتك بسهولة، زد أرباحك بدقة، وقدم لعملائك تجربة لا تُنسى. نظام نقاط البيع المتكامل الذي ينمو مع طموحاتك.</p>
            <div class="hero-buttons animate-on-load">
                <a href="#contact" class="cta-button primary">اطلب عرضًا تجريبيًا مجانيًا</a>
                <a href="#features" class="cta-button secondary">اكتشف المزايا</a>
            </div>
        </div>
        
        <div class="hero-visual">
            <canvas id="hero-canvas"></canvas>
            <div class="floating-elements">
                <div class="floating-icon">
                    <i data-feather="shopping-cart"></i>
                </div>
                <div class="floating-icon">
                    <i data-feather="credit-card"></i>
                </div>
                <div class="floating-icon">
                    <i data-feather="users"></i>
                </div>
            </div>
        </div>
    </header>

    <main>
        <!-- Stats Section -->
        <section class="stats-section">
            <div class="stats-grid">
                <div class="stat-item reveal">
                    <span class="stat-number" data-target="5000">0</span>
                    <span class="stat-label">مطعم يثق بنا</span>
                </div>
                <div class="stat-item reveal">
                    <span class="stat-number" data-target="2.5">0</span>
                    <span class="stat-label">مليون طلب شهريًا</span>
                </div>
                <div class="stat-item reveal">
                    <span class="stat-number" data-target="99.9">0</span>
                    <span class="stat-label">% وقت التشغيل</span>
                </div>
                <div class="stat-item reveal">
                    <span class="stat-number" data-target="24">0</span>
                    <span class="stat-label">ساعة دعم فني</span>
                </div>
            </div>
        </section>

        <!-- Interactive Features Section -->
        <section id="features" class="interactive-features">
            <h2 class="section-title reveal">مصمم لكل تفاصيل مطعمك</h2>
            <div class="features-container">
                <div class="features-list">
                    <div class="feature-item" data-image="https://i.ibb.co/L5S2m4r/pos-screen-arabic.png">
                        <h3>إدارة القوائم والوصفات</h3>
                        <p>أنشئ قوائم طعام متعددة، وحدد الأصناف والإضافات، واربط كل طبق بوصفته في المخزون لحساب التكلفة وتتبع الاستهلاك بدقة.</p>
                    </div>
                    <div class="feature-item" data-image="https://i.ibb.co/3s6t9rP/table-management-arabic.png">
                        <h3>إدارة الطلبات والطاولات</h3>
                        <p>واجهة نقاط بيع سريعة وخريطة طاولات مرئية تظهر حالتها (متاحة، مشغولة، محجوزة) لتنظيم فائق وتجربة عملاء أفضل.</p>
                    </div>
                    <div class="feature-item" data-image="https://i.ibb.co/yqg0Y8b/inventory-dashboard-arabic.png">
                        <h3>تحكم ذكي في المخزون</h3>
                        <p>تتبع فوري لكميات المخزون مع كل عملية بيع، وإدارة الموردين وطلبات الشراء، مع سجلات مفصلة لجميع حركات المخزون.</p>
                    </div>
                    <div class="feature-item" data-image="https://i.ibb.co/L95q4Jb/sales-report-arabic.png">
                        <h3>تقارير وتحليلات متقدمة</h3>
                        <p>احصل على تقارير مبيعات مفصلة ورسوم بيانية توضح أداء مطعمك، الأصناف الأكثر مبيعًا، والإيرادات لاتخاذ قرارات استراتيجية.</p>
                    </div>
                </div>
                <div class="features-visual">
                    <div class="tablet-frame">
                        <img id="feature-image" src="https://i.ibb.co/L5S2m4r/pos-screen-arabic.png" alt="شاشة نظام نقاط البيع">
                    </div>
                </div>
            </div>
        </section>

        <!-- Pricing Section -->
        <section id="pricing" class="pricing-section">
            <h2 class="section-title reveal">خطط مرنة تناسب احتياجاتك</h2>
            <p class="section-subtitle reveal">اختر الخطة التي تناسب حجم مطعمك، مع إمكانية الترقية في أي وقت</p>
            
            <div class="pricing-grid">
                <div class="pricing-card reveal">
                    <div class="plan-name">البداية</div>
                    <div class="plan-price">299</div>
                    <div class="plan-period">ريال / شهريًا</div>
                    <ul class="plan-features">
                        <li>نقطة بيع واحدة</li>
                        <li>إدارة القوائم الأساسية</li>
                        <li>تقارير أساسية</li>
                        <li>دعم فني عبر الإيميل</li>
                        <li>تخزين سحابي 5 جيجا</li>
                    </ul>
                    <a href="#contact" class="cta-button secondary">ابدأ الآن</a>
                </div>
                
                <div class="pricing-card popular reveal">
                    <div class="popular-badge">الأكثر شعبية</div>
                    <div class="plan-name">المتقدم</div>
                    <div class="plan-price">599</div>
                    <div class="plan-period">ريال / شهريًا</div>
                    <ul class="plan-features">
                        <li>3 نقاط بيع</li>
                        <li>إدارة كاملة للمخزون</li>
                        <li>إدارة الطاولات والحجوزات</li>
                        <li>تقارير متقدمة ورسوم بيانية</li>
                        <li>برنامج ولاء العملاء</li>
                        <li>دعم فني عبر الهاتف</li>
                        <li>تخزين سحابي 25 جيجا</li>
                        <li>تطبيق موبايل للإدارة</li>
                    </ul>
                    <a href="#contact" class="cta-button primary">ابدأ الآن</a>
                </div>
                
                <div class="pricing-card reveal">
                    <div class="plan-name">المؤسسات</div>
                    <div class="plan-price">999</div>
                    <div class="plan-period">ريال / شهريًا</div>
                    <ul class="plan-features">
                        <li>نقاط بيع غير محدودة</li>
                        <li>إدارة فروع متعددة</li>
                        <li>إدارة الموظفين والرواتب</li>
                        <li>تحليلات ذكية بالذكاء الاصطناعي</li>
                        <li>تكامل مع أنظمة المحاسبة</li>
                        <li>دعم فني مخصص 24/7</li>
                        <li>تخزين سحابي غير محدود</li>
                        <li>تدريب مجاني للفريق</li>
                    </ul>
                    <a href="#contact" class="cta-button secondary">تواصل معنا</a>
                </div>
            </div>
        </section>

        <!-- Showcase Section -->
        <section id="showcase" class="showcase-section">
             <h2 class="section-title reveal">منصة متكاملة للنمو والتوسع</h2>
             <p class="section-subtitle reveal">أدوات قوية لمساعدتك على إدارة أكثر من مجرد طلبات.</p>
             <div class="showcase-grid">
                 <div class="showcase-card reveal">
                     <i data-feather="users"></i>
                     <h4>إدارة الموظفين والصلاحيات</h4>
                     <p>تحكم كامل في أدوار الموظفين وصلاحياتهم، مع نظام متكامل لتسجيل الحضور والانصراف وجدولة الورديات وحساب الرواتب.</p>
                 </div>
                 <div class="showcase-card reveal">
                    <i data-feather="git-branch"></i>
                    <h4>إدارة فروع متعددة</h4>
                    <p>أدر جميع فروعك من لوحة تحكم مركزية. وحد الأسعار أو خصصها لكل فرع، وقارن الأداء بسهولة لاتخاذ قرارات مدروسة.</p>
                </div>
                <div class="showcase-card reveal">
                    <i data-feather="gift"></i>
                    <h4>برنامج ولاء العملاء (CRM)</h4>
                    <p>ابنِ علاقة قوية مع عملائك. أنشئ برنامج نقاط لمكافأة العملاء الأوفياء وتشجيعهم على العودة مجددًا وزيادة المبيعات.</p>
                </div>
                <div class="showcase-card reveal">
                    <i data-feather="smartphone"></i>
                    <h4>تطبيق موبايل متكامل</h4>
                    <p>تابع مطعمك من أي مكان مع تطبيق الموبايل. اطلع على المبيعات والتقارير واستقبل إشعارات فورية عن العمليات المهمة.</p>
                </div>
                <div class="showcase-card reveal">
                    <i data-feather="shield-check"></i>
                    <h4>أمان وحماية البيانات</h4>
                    <p>بياناتك في أمان تام مع تشفير متقدم ونسخ احتياطية تلقائية. نلتزم بأعلى معايير الأمان العالمية لحماية معلوماتك.</p>
                </div>
                <div class="showcase-card reveal">
                    <i data-feather="zap"></i>
                    <h4>أداء فائق السرعة</h4>
                    <p>نظام سحابي بأداء عالي يضمن سرعة في المعاملات وعدم انقطاع الخدمة. خوادم متطورة موزعة جغرافيًا لأفضل أداء.</p>
                </div>
             </div>
        </section>

        <!-- Final CTA Section -->
        <section id="contact" class="cta-final-section">
            <div class="cta-content reveal">
                <h2>هل أنت مستعد لتطوير أداء مطعمك؟</h2>
                <p>دعنا نريك كيف يمكن لنظامنا أن يغير طريقة إدارتك لمطعمك. اطلب عرضًا تجريبيًا مجانيًا اليوم، بدون أي التزامات.</p>
                <form class="cta-form" onsubmit="event.preventDefault(); alert('شكراً لاهتمامك! سنتواصل معك قريباً.');">
                    <input type="text" placeholder="اسمك" required>
                    <input type="email" placeholder="بريدك الإلكتروني" required>
                    <input type="tel" placeholder="رقم الهاتف" required>
                    <button type="submit" class="cta-button primary">اطلب الآن</button>
                </form>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-container">
            <div class="footer-about">
                <h4>عن نظامك POS</h4>
                <p>نظام نقاط بيع سحابي متكامل، مصمم خصيصًا للمطاعم والمقاهي في العالم العربي لتسهيل العمليات وزيادة الأرباح.</p>
            </div>
            <div class="footer-links">
                <h4>روابط سريعة</h4>
                <ul>
                    <li><a href="#features">المزايا</a></li>
                    <li><a href="#pricing">الأسعار</a></li>
                    <li><a href="#showcase">النظام بالصور</a></li>
                    <li><a href="#contact">اطلب عرض تجريبي</a></li>
                </ul>
            </div>
            <div class="footer-links">
                <h4>الدعم</h4>
                <ul>
                    <li><a href="#">مركز المساعدة</a></li>
                    <li><a href="#">الأسئلة الشائعة</a></li>
                    <li><a href="#">فيديوهات تعليمية</a></li>
                    <li><a href="#">تحديثات النظام</a></li>
                </ul>
            </div>
            <div class="footer-contact">
                <h4>تواصل معنا</h4>
                <p><i data-feather="mail"></i> <EMAIL></p>
                <p><i data-feather="phone"></i> +966 12 345 6789</p>
                <p><i data-feather="map-pin"></i> الرياض، المملكة العربية السعودية</p>
            </div>
        </div>
        <div class="footer-bottom">
            <p>جميع الحقوق محفوظة © 2024 | نظامك POS</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', () => {

            // Navbar scroll effect
            const navbar = document.querySelector('.navbar');
            window.addEventListener('scroll', () => {
                if (window.scrollY > 100) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }
            });

            // 3D Tablet Animation with Three.js
            const canvas = document.getElementById('hero-canvas');
            if (canvas && typeof THREE !== 'undefined') {
                const scene = new THREE.Scene();
                const camera = new THREE.PerspectiveCamera(75, canvas.clientWidth / canvas.clientHeight, 0.1, 1000);
                const renderer = new THREE.WebGLRenderer({ canvas: canvas, alpha: true, antialias: true });
                renderer.setSize(canvas.clientWidth, canvas.clientHeight);
                renderer.setPixelRatio(window.devicePixelRatio);

                // Create tablet geometry
                const tabletGeometry = new THREE.BoxGeometry(4, 3, 0.2);
                const tabletMaterial = new THREE.MeshPhongMaterial({ 
                    color: 0x2a2a2a,
                    shininess: 100 
                });
                const tablet = new THREE.Mesh(tabletGeometry, tabletMaterial);

                // Create screen
                const screenGeometry = new THREE.PlaneGeometry(3.6, 2.6);
                const screenMaterial = new THREE.MeshBasicMaterial({ 
                    color: 0x16c79a,
                    transparent: true,
                    opacity: 0.8
                });
                const screen = new THREE.Mesh(screenGeometry, screenMaterial);
                screen.position.z = 0.11;

                // Add glow effect
                const glowGeometry = new THREE.PlaneGeometry(4.2, 3.2);
                const glowMaterial = new THREE.MeshBasicMaterial({
                    color: 0x16c79a,
                    transparent: true,
                    opacity: 0.1
                });
                const glow = new THREE.Mesh(glowGeometry, glowMaterial);
                glow.position.z = -0.1;

                scene.add(tablet);
                scene.add(screen);
                scene.add(glow);

                // Lighting
                const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
                scene.add(ambientLight);

                const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
                directionalLight.position.set(5, 5, 5);
                scene.add(directionalLight);

                const pointLight = new THREE.PointLight(0x16c79a, 0.5, 50);
                pointLight.position.set(0, 0, 10);
                scene.add(pointLight);

                camera.position.z = 8;

                // Animation
                function animate() {
                    requestAnimationFrame(animate);
                    
                    tablet.rotation.y += 0.005;
                    tablet.rotation.x = Math.sin(Date.now() * 0.001) * 0.1;
                    screen.rotation.y += 0.005;
                    screen.rotation.x = Math.sin(Date.now() * 0.001) * 0.1;
                    glow.rotation.y += 0.005;
                    glow.rotation.x = Math.sin(Date.now() * 0.001) * 0.1;
                    
                    // Pulsing glow
                    glow.material.opacity = 0.1 + Math.sin(Date.now() * 0.003) * 0.05;
                    
                    renderer.render(scene, camera);
                }
                animate();

                // Handle resize
                window.addEventListener('resize', () => {
                    const width = canvas.clientWidth;
                    const height = canvas.clientHeight;
                    camera.aspect = width / height;
                    camera.updateProjectionMatrix();
                    renderer.setSize(width, height);
                });
            }

            // Animated counters for stats
            const animateCounter = (element, target, duration = 2000) => {
                const start = 0;
                const increment = target / (duration / 16);
                let current = start;
                
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        element.textContent = target;
                        clearInterval(timer);
                    } else {
                        element.textContent = Math.floor(current);
                    }
                }, 16);
            };

            const statNumbers = document.querySelectorAll('.stat-number');
            const statsObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const target = parseInt(entry.target.getAttribute('data-target'));
                        animateCounter(entry.target, target);
                        statsObserver.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.5 });

            statNumbers.forEach(stat => {
                statsObserver.observe(stat);
            });

            // Scrollytelling for Features Section
            const featureItems = document.querySelectorAll('.feature-item');
            const featureImage = document.getElementById('feature-image');
            
            if (featureItems.length > 0 && featureImage) {
                const observerOptions = {
                    root: null,
                    rootMargin: '-50% 0px -50% 0px',
                    threshold: 0
                };

                const featureObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const newImageSrc = entry.target.getAttribute('data-image');
                            
                            featureImage.style.opacity = '0';
                            setTimeout(() => {
                                featureImage.src = newImageSrc;
                                featureImage.style.opacity = '1';
                            }, 300);

                            featureItems.forEach(item => item.classList.remove('active'));
                            entry.target.classList.add('active');
                        }
                    });
                }, observerOptions);

                featureItems.forEach(item => {
                    featureObserver.observe(item);
                });
            }

            // General scroll reveal animation
            const revealElements = document.querySelectorAll('.reveal');
            if (revealElements.length > 0) {
                const revealObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.classList.add('visible');
                            revealObserver.unobserve(entry.target);
                        }
                    });
                }, { threshold: 0.1 });

                revealElements.forEach(element => {
                    revealObserver.observe(element);
                });
            }

        });

        // Initialize Feather Icons
        if (typeof feather !== 'undefined') {
            feather.replace();
        }
    </script>
</body>
</html>