<!-- Add Supplier Modal -->
<div class="modal fade" id="addSupplierModal" tabindex="-1" role="dialog" aria-labelledby="addSupplierModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addSupplierModalLabel">إضافة مورد جديد</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="addSupplierForm" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="supplier_name">اسم المورد <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="supplier_name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="supplier_code">رقم المورد</label>
                                <input type="text" class="form-control" id="supplier_code" name="supplier_code" placeholder="سيتم إنشاؤه تلقائياً">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="contact_person">جهة الاتصال</label>
                                <input type="text" class="form-control" id="contact_person" name="contact_person">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="phone">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="email">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="website">الموقع الإلكتروني</label>
                                <input type="url" class="form-control" id="website" name="website">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="address">العنوان</label>
                        <textarea class="form-control" id="address" name="address" rows="2"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="city">المدينة</label>
                                <input type="text" class="form-control" id="city" name="city">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="state">المنطقة</label>
                                <input type="text" class="form-control" id="state" name="state">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="postal_code">الرمز البريدي</label>
                                <input type="text" class="form-control" id="postal_code" name="postal_code">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="tax_number">الرقم الضريبي</label>
                                <input type="text" class="form-control" id="tax_number" name="tax_number">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="payment_terms">شروط الدفع</label>
                                <select class="form-control" id="payment_terms" name="payment_terms">
                                    <option value="">اختر شروط الدفع</option>
                                    <option value="cash">نقداً</option>
                                    <option value="net_15">خلال 15 يوم</option>
                                    <option value="net_30">خلال 30 يوم</option>
                                    <option value="net_60">خلال 60 يوم</option>
                                    <option value="net_90">خلال 90 يوم</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="status">الحالة</label>
                                <select class="form-control" id="status" name="status" required>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                    <option value="pending">في الانتظار</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="logo">شعار المورد</label>
                                <input type="file" class="form-control-file" id="logo" name="logo" accept="image/*">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="notes">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="mdi mdi-plus"></i> إضافة المورد
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Supplier Modal -->
<div class="modal fade" id="editSupplierModal" tabindex="-1" role="dialog" aria-labelledby="editSupplierModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editSupplierModalLabel">تعديل بيانات المورد</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="editSupplierForm" enctype="multipart/form-data">
                <input type="hidden" id="edit_supplier_id" name="id">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_supplier_name">اسم المورد <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="edit_supplier_name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_supplier_code">رقم المورد</label>
                                <input type="text" class="form-control" id="edit_supplier_code" name="supplier_code" readonly>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_contact_person">جهة الاتصال</label>
                                <input type="text" class="form-control" id="edit_contact_person" name="contact_person">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_phone">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="edit_phone" name="phone">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_email">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="edit_email" name="email">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_website">الموقع الإلكتروني</label>
                                <input type="url" class="form-control" id="edit_website" name="website">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit_address">العنوان</label>
                        <textarea class="form-control" id="edit_address" name="address" rows="2"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="edit_city">المدينة</label>
                                <input type="text" class="form-control" id="edit_city" name="city">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="edit_state">المنطقة</label>
                                <input type="text" class="form-control" id="edit_state" name="state">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="edit_postal_code">الرمز البريدي</label>
                                <input type="text" class="form-control" id="edit_postal_code" name="postal_code">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_tax_number">الرقم الضريبي</label>
                                <input type="text" class="form-control" id="edit_tax_number" name="tax_number">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_payment_terms">شروط الدفع</label>
                                <select class="form-control" id="edit_payment_terms" name="payment_terms">
                                    <option value="">اختر شروط الدفع</option>
                                    <option value="cash">نقداً</option>
                                    <option value="net_15">خلال 15 يوم</option>
                                    <option value="net_30">خلال 30 يوم</option>
                                    <option value="net_60">خلال 60 يوم</option>
                                    <option value="net_90">خلال 90 يوم</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_status">الحالة</label>
                                <select class="form-control" id="edit_status" name="status" required>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                    <option value="pending">في الانتظار</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_logo">شعار المورد</label>
                                <input type="file" class="form-control-file" id="edit_logo" name="logo" accept="image/*">
                                <div id="current_logo_preview" class="mt-2" style="display: none;">
                                    <img id="current_logo_img" src="" alt="الشعار الحالي" style="max-width: 100px; max-height: 100px;">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit_notes">ملاحظات</label>
                        <textarea class="form-control" id="edit_notes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="mdi mdi-check"></i> حفظ التغييرات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View Supplier Modal -->
<div class="modal fade" id="viewSupplierModal" tabindex="-1" role="dialog" aria-labelledby="viewSupplierModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewSupplierModalLabel">تفاصيل المورد</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <div id="view_supplier_logo" class="mb-3">
                            <!-- Logo will be displayed here -->
                        </div>
                        <div id="view_supplier_rating" class="mb-3">
                            <!-- Rating will be displayed here -->
                        </div>
                    </div>
                    <div class="col-md-9">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">اسم المورد:</label>
                                    <p id="view_supplier_name" class="mb-2"></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">رقم المورد:</label>
                                    <p id="view_supplier_code" class="mb-2"></p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">جهة الاتصال:</label>
                                    <p id="view_contact_person" class="mb-2"></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">رقم الهاتف:</label>
                                    <p id="view_phone" class="mb-2"></p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">البريد الإلكتروني:</label>
                                    <p id="view_email" class="mb-2"></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">الموقع الإلكتروني:</label>
                                    <p id="view_website" class="mb-2"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="font-weight-bold">العنوان:</label>
                    <p id="view_address" class="mb-2"></p>
                </div>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="font-weight-bold">المدينة:</label>
                            <p id="view_city" class="mb-2"></p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="font-weight-bold">المنطقة:</label>
                            <p id="view_state" class="mb-2"></p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="font-weight-bold">الرمز البريدي:</label>
                            <p id="view_postal_code" class="mb-2"></p>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="font-weight-bold">الرقم الضريبي:</label>
                            <p id="view_tax_number" class="mb-2"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="font-weight-bold">شروط الدفع:</label>
                            <p id="view_payment_terms" class="mb-2"></p>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="font-weight-bold">الحالة:</label>
                            <p id="view_status" class="mb-2"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="font-weight-bold">تاريخ التسجيل:</label>
                            <p id="view_created_at" class="mb-2"></p>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="font-weight-bold">ملاحظات:</label>
                    <p id="view_notes" class="mb-2"></p>
                </div>
                
                <!-- Statistics -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h6 class="font-weight-bold mb-3">إحصائيات المورد</h6>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 id="view_total_orders" class="text-primary mb-1">0</h4>
                            <small class="text-muted">إجمالي الطلبات</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 id="view_total_products" class="text-success mb-1">0</h4>
                            <small class="text-muted">المنتجات المورّدة</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 id="view_total_amount" class="text-info mb-1">0</h4>
                            <small class="text-muted">إجمالي المبلغ</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 id="view_last_order" class="text-warning mb-1">-</h4>
                            <small class="text-muted">آخر طلب</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="editSupplierFromView()">
                    <i class="mdi mdi-pencil"></i> تعديل
                </button>
                <button type="button" class="btn btn-success" onclick="viewSupplierProducts()">
                    <i class="mdi mdi-package-variant"></i> المنتجات
                </button>
                <button type="button" class="btn btn-info" onclick="viewSupplierOrders()">
                    <i class="mdi mdi-cart"></i> الطلبات
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Import Suppliers Modal -->
<div class="modal fade" id="importSuppliersModal" tabindex="-1" role="dialog" aria-labelledby="importSuppliersModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importSuppliersModalLabel">استيراد الموردين</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="importSuppliersForm" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="mdi mdi-information"></i>
                        يمكنك استيراد الموردين من ملف Excel. تأكد من أن الملف يحتوي على الأعمدة المطلوبة.
                    </div>
                    
                    <div class="form-group">
                        <label for="suppliers_file">اختر ملف Excel <span class="text-danger">*</span></label>
                        <input type="file" class="form-control-file" id="suppliers_file" name="file" accept=".xlsx,.xls" required>
                        <small class="form-text text-muted">الملفات المدعومة: Excel (.xlsx, .xls)</small>
                    </div>
                    
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="update_existing" name="update_existing">
                            <label class="custom-control-label" for="update_existing">تحديث الموردين الموجودين</label>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <a href="#" class="btn btn-sm btn-outline-info" onclick="downloadSuppliersTemplate()">
                            <i class="mdi mdi-download"></i> تحميل نموذج الملف
                        </a>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-info">
                        <i class="mdi mdi-upload"></i> استيراد الموردين
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Add Supplier Form Handler
$('#addSupplierForm').on('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    $.ajax({
        url: '{{ route("inventory.api.suppliers.store") }}',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            $('#addSupplierModal').modal('hide');
            $('#addSupplierForm')[0].reset();
            InventoryModule.showSuccess('تم إضافة المورد بنجاح');
            refreshSuppliersTable();
        },
        error: function(xhr) {
            const errors = xhr.responseJSON?.errors;
            if (errors) {
                let errorMessage = 'يرجى تصحيح الأخطاء التالية:\n';
                Object.keys(errors).forEach(key => {
                    errorMessage += `- ${errors[key][0]}\n`;
                });
                InventoryModule.showError(errorMessage);
            } else {
                InventoryModule.showError('حدث خطأ أثناء إضافة المورد');
            }
        }
    });
});

// Edit Supplier Form Handler
$('#editSupplierForm').on('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const supplierId = $('#edit_supplier_id').val();

    $.ajax({
        url: `{{ route('inventory.api.suppliers.update', ':id') }}`.replace(':id', supplierId),
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
            'X-HTTP-Method-Override': 'PUT'
        },
        success: function(response) {
            $('#editSupplierModal').modal('hide');
            InventoryModule.showSuccess('تم تحديث بيانات المورد بنجاح');
            refreshSuppliersTable();
        },
        error: function(xhr) {
            const errors = xhr.responseJSON?.errors;
            if (errors) {
                let errorMessage = 'يرجى تصحيح الأخطاء التالية:\n';
                Object.keys(errors).forEach(key => {
                    errorMessage += `- ${errors[key][0]}\n`;
                });
                InventoryModule.showError(errorMessage);
            } else {
                InventoryModule.showError('حدث خطأ أثناء تحديث المورد');
            }
        }
    });
});

// Import Suppliers Form Handler
$('#importSuppliersForm').on('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    InventoryModule.showLoading($('#importSuppliersModal .modal-body'));

    $.ajax({
        url: '{{ route("inventory.api.suppliers.import") }}',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            $('#importSuppliersModal').modal('hide');
            $('#importSuppliersForm')[0].reset();
            InventoryModule.showSuccess(`تم استيراد ${response.imported} مورد بنجاح`);
            refreshSuppliersTable();
        },
        error: function(xhr) {
            const message = xhr.responseJSON?.message || 'حدث خطأ أثناء استيراد الموردين';
            InventoryModule.showError(message);
        },
        complete: function() {
            InventoryModule.hideLoading($('#importSuppliersModal .modal-body'));
        }
    });
});

function populateEditSupplierModal(supplier) {
    $('#edit_supplier_id').val(supplier.id);
    $('#edit_supplier_name').val(supplier.name);
    $('#edit_supplier_code').val(supplier.supplier_code);
    $('#edit_contact_person').val(supplier.contact_person);
    $('#edit_phone').val(supplier.phone);
    $('#edit_email').val(supplier.email);
    $('#edit_website').val(supplier.website);
    $('#edit_address').val(supplier.address);
    $('#edit_city').val(supplier.city);
    $('#edit_state').val(supplier.state);
    $('#edit_postal_code').val(supplier.postal_code);
    $('#edit_tax_number').val(supplier.tax_number);
    $('#edit_payment_terms').val(supplier.payment_terms);
    $('#edit_status').val(supplier.status);
    $('#edit_notes').val(supplier.notes);

    if (supplier.logo) {
        $('#current_logo_preview').show();
        $('#current_logo_img').attr('src', supplier.logo);
    } else {
        $('#current_logo_preview').hide();
    }
}

function populateViewSupplierModal(supplier) {
    $('#view_supplier_name').text(supplier.name || '-');
    $('#view_supplier_code').text(supplier.supplier_code || '-');
    $('#view_contact_person').text(supplier.contact_person || '-');
    $('#view_phone').text(supplier.phone || '-');
    $('#view_email').text(supplier.email || '-');
    $('#view_website').text(supplier.website || '-');
    $('#view_address').text(supplier.address || '-');
    $('#view_city').text(supplier.city || '-');
    $('#view_state').text(supplier.state || '-');
    $('#view_postal_code').text(supplier.postal_code || '-');
    $('#view_tax_number').text(supplier.tax_number || '-');
    $('#view_payment_terms').text(getPaymentTermsText(supplier.payment_terms));
    $('#view_status').html(getSupplierStatusBadge(supplier.status));
    $('#view_created_at').text(supplier.created_at ? InventoryModule.formatDate(supplier.created_at) : '-');
    $('#view_notes').text(supplier.notes || '-');

    // Store supplier ID for other functions
    $('#viewSupplierModal').data('supplier-id', supplier.id);

    // Logo
    if (supplier.logo) {
        $('#view_supplier_logo').html(`<img src="${supplier.logo}" class="supplier-logo" alt="شعار المورد">`);
    } else {
        $('#view_supplier_logo').html(`<div class="supplier-logo bg-light d-flex align-items-center justify-content-center">
            <i class="mdi mdi-truck text-muted"></i>
        </div>`);
    }

    // Rating
    if (supplier.rating) {
        let stars = '';
        for (let i = 1; i <= 5; i++) {
            stars += `<i class="mdi mdi-star${i <= supplier.rating ? '' : '-outline'} supplier-rating"></i>`;
        }
        $('#view_supplier_rating').html(stars);
    } else {
        $('#view_supplier_rating').html('-');
    }

    // Statistics
    $('#view_total_orders').text(supplier.statistics?.total_orders || 0);
    $('#view_total_products').text(supplier.statistics?.total_products || 0);
    $('#view_total_amount').text(InventoryModule.formatCurrency(supplier.statistics?.total_amount || 0));
    $('#view_last_order').text(supplier.statistics?.last_order_date ?
        InventoryModule.formatDate(supplier.statistics.last_order_date) : '-');
}

function getPaymentTermsText(terms) {
    const termsMap = {
        'cash': 'نقداً',
        'net_15': 'خلال 15 يوم',
        'net_30': 'خلال 30 يوم',
        'net_60': 'خلال 60 يوم',
        'net_90': 'خلال 90 يوم'
    };

    return termsMap[terms] || terms || '-';
}

function editSupplierFromView() {
    const supplierId = $('#viewSupplierModal').data('supplier-id');
    $('#viewSupplierModal').modal('hide');
    editSupplier(supplierId);
}

function viewSupplierProducts() {
    const supplierId = $('#viewSupplierModal').data('supplier-id');
    viewProducts(supplierId);
}

function viewSupplierOrders() {
    const supplierId = $('#viewSupplierModal').data('supplier-id');
    viewOrders(supplierId);
}

function downloadSuppliersTemplate() {
    window.location.href = '{{ route("inventory.api.suppliers.template") }}';
}
</script>
