{"info": {"name": "Reservation Module API Collection", "description": "Comprehensive API collection for the Reservation module including reservation management, area management, table management, and QR code functionality.", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://127.0.0.1:8000", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}], "item": [{"name": "Reservations", "item": [{"name": "List Reservations", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/reservation/reservations?branch_id=1&status=confirmed&per_page=15", "host": ["{{base_url}}"], "path": ["api", "reservation", "reservations"], "query": [{"key": "branch_id", "value": "1", "description": "Filter by branch ID"}, {"key": "status", "value": "confirmed", "description": "pending, confirmed, seated, completed, cancelled, no_show"}, {"key": "date_from", "value": "", "description": "Filter from date (YYYY-MM-DD)", "disabled": true}, {"key": "date_to", "value": "", "description": "Filter to date (YYYY-MM-DD)", "disabled": true}, {"key": "customer_phone", "value": "", "description": "Filter by customer phone", "disabled": true}, {"key": "customer_name", "value": "", "description": "Filter by customer name", "disabled": true}, {"key": "table_id", "value": "", "description": "Filter by table ID", "disabled": true}, {"key": "area_id", "value": "", "description": "Filter by area ID", "disabled": true}, {"key": "per_page", "value": "15", "description": "Items per page"}]}}}, {"name": "Create Reservation", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"branch_id\": 1,\n  \"customer_id\": null,\n  \"table_id\": null,\n  \"customer_name\": \"<PERSON>\",\n  \"customer_phone\": \"+1234567890\",\n  \"email\": \"<EMAIL>\",\n  \"party_size\": 4,\n  \"reservation_datetime\": \"2024-12-25 19:00:00\",\n  \"duration_minutes\": 120,\n  \"special_requests\": \"Window seat preferred\",\n  \"notes\": \"Anniversary dinner\",\n  \"area_id\": 1\n}"}, "url": {"raw": "{{base_url}}/api/reservation/reservations", "host": ["{{base_url}}"], "path": ["api", "reservation", "reservations"]}}}, {"name": "Get Reservation", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/reservation/reservations/1", "host": ["{{base_url}}"], "path": ["api", "reservation", "reservations", "1"]}}}, {"name": "Update Reservation", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"customer_name\": \"<PERSON>\",\n  \"customer_phone\": \"+1234567890\",\n  \"email\": \"<EMAIL>\",\n  \"party_size\": 6,\n  \"reservation_datetime\": \"2024-12-25 20:00:00\",\n  \"duration_minutes\": 150,\n  \"special_requests\": \"Quiet area preferred\",\n  \"notes\": \"Updated anniversary dinner\"\n}"}, "url": {"raw": "{{base_url}}/api/reservation/reservations/1", "host": ["{{base_url}}"], "path": ["api", "reservation", "reservations", "1"]}}}, {"name": "Cancel Reservation", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/reservation/reservations/1", "host": ["{{base_url}}"], "path": ["api", "reservation", "reservations", "1"]}}}, {"name": "Confirm Reservation", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/reservation/reservations/1/confirm", "host": ["{{base_url}}"], "path": ["api", "reservation", "reservations", "1", "confirm"]}}}, {"name": "Seat Reservation", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"table_id\": 5\n}"}, "url": {"raw": "{{base_url}}/api/reservation/reservations/1/seat", "host": ["{{base_url}}"], "path": ["api", "reservation", "reservations", "1", "seat"]}}}, {"name": "Complete Reservation", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/reservation/reservations/1/complete", "host": ["{{base_url}}"], "path": ["api", "reservation", "reservations", "1", "complete"]}}}, {"name": "<PERSON> as No-Show", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/reservation/reservations/1/no-show", "host": ["{{base_url}}"], "path": ["api", "reservation", "reservations", "1", "no-show"]}}}, {"name": "Check Availability", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/reservation/reservations/check-availability?branch_id=1&datetime=2024-12-25 19:00:00&party_size=4&duration=120", "host": ["{{base_url}}"], "path": ["api", "reservation", "reservations", "check-availability"], "query": [{"key": "branch_id", "value": "1", "description": "Required: Branch ID"}, {"key": "datetime", "value": "2024-12-25 19:00:00", "description": "Required: Reservation datetime"}, {"key": "party_size", "value": "4", "description": "Required: Number of guests"}, {"key": "duration", "value": "120", "description": "Optional: Duration in minutes (default: 120)"}]}}}, {"name": "Get Statistics", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/reservation/reservations/statistics?branch_id=1&date_from=2024-12-01&date_to=2024-12-31", "host": ["{{base_url}}"], "path": ["api", "reservation", "reservations", "statistics"], "query": [{"key": "branch_id", "value": "1", "description": "Filter by branch ID"}, {"key": "date_from", "value": "2024-12-01", "description": "Start date for statistics"}, {"key": "date_to", "value": "2024-12-31", "description": "End date for statistics"}]}}}]}, {"name": "Areas", "item": [{"name": "List Areas", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/reservation/areas?branch_id=1", "host": ["{{base_url}}"], "path": ["api", "reservation", "areas"], "query": [{"key": "branch_id", "value": "1", "description": "Filter by branch ID"}]}}}, {"name": "Create Area", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"branch_id\": 1,\n  \"name\": \"Main Dining Area\",\n  \"description\": \"Primary dining area with window views\"\n}"}, "url": {"raw": "{{base_url}}/api/reservation/areas", "host": ["{{base_url}}"], "path": ["api", "reservation", "areas"]}}}, {"name": "Get Area", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/reservation/areas/1", "host": ["{{base_url}}"], "path": ["api", "reservation", "areas", "1"]}}}, {"name": "Update Area", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Main Dining Area\",\n  \"description\": \"Updated primary dining area with enhanced window views\"\n}"}, "url": {"raw": "{{base_url}}/api/reservation/areas/1", "host": ["{{base_url}}"], "path": ["api", "reservation", "areas", "1"]}}}, {"name": "Delete Area", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/reservation/areas/1", "host": ["{{base_url}}"], "path": ["api", "reservation", "areas", "1"]}}}, {"name": "Get Area Tables", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/reservation/areas/1/tables", "host": ["{{base_url}}"], "path": ["api", "reservation", "areas", "1", "tables"]}}}, {"name": "Get Area Statistics", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/reservation/areas/1/statistics", "host": ["{{base_url}}"], "path": ["api", "reservation", "areas", "1", "statistics"]}}}]}, {"name": "Tables", "item": [{"name": "List Tables", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/reservation/tables", "host": ["{{base_url}}"], "path": ["api", "reservation", "tables"]}}}, {"name": "Create Table", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"branch_id\": 1,\n  \"area_id\": 1,\n  \"table_number\": \"T001\",\n  \"table_name\": \"Window Table 1\",\n  \"seating_capacity\": 4,\n  \"section\": \"Window Section\",\n  \"status\": \"available\",\n  \"position_coordinates\": {\"x\": 100, \"y\": 150},\n  \"notes\": \"Prime window location\",\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/reservation/tables", "host": ["{{base_url}}"], "path": ["api", "reservation", "tables"]}}}, {"name": "Get Available Tables", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/reservation/tables/available", "host": ["{{base_url}}"], "path": ["api", "reservation", "tables", "available"]}}}, {"name": "Get Table Occupancy", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/reservation/tables/occupancy", "host": ["{{base_url}}"], "path": ["api", "reservation", "tables", "occupancy"]}}}, {"name": "Get Table by QR Code", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/reservation/tables/qr/QR123456", "host": ["{{base_url}}"], "path": ["api", "reservation", "tables", "qr", "QR123456"]}}}, {"name": "Get Table", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/reservation/tables/1", "host": ["{{base_url}}"], "path": ["api", "reservation", "tables", "1"]}}}, {"name": "Update Table", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"table_name\": \"Updated Window Table 1\",\n  \"seating_capacity\": 6,\n  \"section\": \"Premium Window Section\",\n  \"notes\": \"Updated prime window location with enhanced view\",\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/reservation/tables/1", "host": ["{{base_url}}"], "path": ["api", "reservation", "tables", "1"]}}}, {"name": "Delete Table", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/reservation/tables/1", "host": ["{{base_url}}"], "path": ["api", "reservation", "tables", "1"]}}}, {"name": "Update Table Status", "request": {"method": "PATCH", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"occupied\"\n}"}, "url": {"raw": "{{base_url}}/api/reservation/tables/1/status", "host": ["{{base_url}}"], "path": ["api", "reservation", "tables", "1", "status"]}}}, {"name": "Generate Table QR Code", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/reservation/tables/1/generate-qr", "host": ["{{base_url}}"], "path": ["api", "reservation", "tables", "1", "generate-qr"]}}}]}, {"name": "QR Codes", "item": [{"name": "Validate QR Code", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"qr_code\": \"QR123456\"\n}"}, "url": {"raw": "{{base_url}}/api/reservation/qr/validate", "host": ["{{base_url}}"], "path": ["api", "reservation", "qr", "validate"]}}}, {"name": "Batch Generate QR Codes", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"table_ids\": [1, 2, 3, 4, 5],\n  \"qr_type\": \"table\"\n}"}, "url": {"raw": "{{base_url}}/api/reservation/qr/batch-generate", "host": ["{{base_url}}"], "path": ["api", "reservation", "qr", "batch-generate"]}}}, {"name": "Get QR Content for Table", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/reservation/qr/table/1/content", "host": ["{{base_url}}"], "path": ["api", "reservation", "qr", "table", "1", "content"]}}}, {"name": "Generate Table QR Code", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/reservation/qr/table/1/generate", "host": ["{{base_url}}"], "path": ["api", "reservation", "qr", "table", "1", "generate"]}}}, {"name": "Generate Menu QR Code", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/reservation/qr/table/1/menu", "host": ["{{base_url}}"], "path": ["api", "reservation", "qr", "table", "1", "menu"]}}}, {"name": "Generate Order QR Code", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/reservation/qr/table/1/order", "host": ["{{base_url}}"], "path": ["api", "reservation", "qr", "table", "1", "order"]}}}]}, {"name": "Waiter Requests", "item": [{"name": "List Waiter Requests", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/reservation/waiter-requests?status=pending&per_page=15", "host": ["{{base_url}}"], "path": ["api", "reservation", "waiter-requests"], "query": [{"key": "status", "value": "pending", "description": "pending, in_progress, completed, cancelled"}, {"key": "table_id", "value": "", "description": "Filter by table ID", "disabled": true}, {"key": "waiter_id", "value": "", "description": "Filter by waiter ID", "disabled": true}, {"key": "request_type", "value": "", "description": "service, bill, assistance, cleaning", "disabled": true}, {"key": "per_page", "value": "15", "description": "Items per page"}]}}}, {"name": "Create Waiter Request", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"table_id\": 1,\n  \"branch_id\": 1,\n  \"request_type\": \"service\",\n  \"notes\": \"Need assistance with menu selection\"\n}"}, "url": {"raw": "{{base_url}}/api/reservation/waiter-requests", "host": ["{{base_url}}"], "path": ["api", "reservation", "waiter-requests"]}}}, {"name": "Get Waiter Request", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/reservation/waiter-requests/1", "host": ["{{base_url}}"], "path": ["api", "reservation", "waiter-requests", "1"]}}}, {"name": "Update Waiter Request", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"in_progress\",\n  \"waiter_id\": 2,\n  \"notes\": \"Waiter assigned and responding\"\n}"}, "url": {"raw": "{{base_url}}/api/reservation/waiter-requests/1", "host": ["{{base_url}}"], "path": ["api", "reservation", "waiter-requests", "1"]}}}, {"name": "Delete Waiter Request", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/reservation/waiter-requests/1", "host": ["{{base_url}}"], "path": ["api", "reservation", "waiter-requests", "1"]}}}, {"name": "Assign <PERSON><PERSON> to Request", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"waiter_id\": 2\n}"}, "url": {"raw": "{{base_url}}/api/reservation/waiter-requests/1/assign", "host": ["{{base_url}}"], "path": ["api", "reservation", "waiter-requests", "1", "assign"]}}}, {"name": "Complete Waiter Request", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"notes\": \"Request completed successfully\"\n}"}, "url": {"raw": "{{base_url}}/api/reservation/waiter-requests/1/complete", "host": ["{{base_url}}"], "path": ["api", "reservation", "waiter-requests", "1", "complete"]}}}, {"name": "Get Pending Requests for Waiter", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/reservation/waiter-requests/waiter?waiter_id=2", "host": ["{{base_url}}"], "path": ["api", "reservation", "waiter-requests", "waiter"], "query": [{"key": "waiter_id", "value": "2", "description": "Waiter ID (optional, defaults to authenticated user)"}]}}}]}]}