<!-- Add Item Modal -->
<div id="addItemModal" class="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-0 border-0 w-11/12 max-w-4xl shadow-2xl rounded-lg bg-white">
        <!-- Modal Header -->
        <div class="flex justify-between items-center px-6 py-4 bg-gradient-to-r from-purple-600 to-purple-700 text-white rounded-t-lg">
            <div class="flex items-center gap-3">
                <div class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <i class="fas fa-plus text-sm"></i>
                </div>
                <h3 class="text-lg font-semibold">إضافة مادة جديدة</h3>
            </div>
            <button type="button" class="text-white hover:text-gray-200 transition-colors duration-200" onclick="closeModal('addItemModal')">
                <i class="fas fa-times text-lg"></i>
            </button>
        </div>

        <!-- Modal Body -->
        <div class="px-6 py-6">
            <form id="addItemForm">
                @csrf
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Basic Information Section -->
                    <div class="md:col-span-2">
                        <h4 class="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
                            <i class="fas fa-info-circle text-purple-600"></i>
                            المعلومات الأساسية
                        </h4>
                    </div>

                    <div>
                        <label for="add_name" class="block text-sm font-medium text-gray-700 mb-2">
                            اسم المادة <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="name" id="add_name" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500" required>
                    </div>

                    <div>
                        <label for="add_sku" class="block text-sm font-medium text-gray-700 mb-2">الرمز (SKU)</label>
                        <input type="text" name="sku" id="add_sku" placeholder="سيتم إنشاؤه تلقائياً" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                    </div>

                    <div>
                        <label for="add_category" class="block text-sm font-medium text-gray-700 mb-2">
                            الفئة <span class="text-red-500">*</span>
                        </label>
                        <select name="category" id="add_category" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500" required>
                            <option value="">اختر الفئة</option>
                            <option value="ingredients">المكونات</option>
                            <option value="beverages">المشروبات</option>
                            <option value="packaging">التعبئة والتغليف</option>
                            <option value="cleaning">مواد التنظيف</option>
                            <option value="equipment">المعدات</option>
                            <option value="general">عام</option>
                        </select>
                    </div>

                    <div>
                        <label for="add_unit_id" class="block text-sm font-medium text-gray-700 mb-2">
                            وحدة القياس <span class="text-red-500">*</span>
                        </label>
                        <select name="unit_id" id="add_unit_id" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500" required>
                            <option value="">اختر وحدة القياس</option>
                        </select>
                    </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="add_description">الوصف</label>
                        <textarea class="form-control" id="add_description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="add_initial_stock">المخزون الأولي</label>
                                <input type="number" class="form-control" id="add_initial_stock" name="initial_stock" min="0" value="0">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="add_minimum_stock">الحد الأدنى <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="add_minimum_stock" name="minimum_stock" min="0" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="add_maximum_stock">الحد الأقصى</label>
                                <input type="number" class="form-control" id="add_maximum_stock" name="maximum_stock" min="0">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="add_reorder_point">نقطة إعادة الطلب</label>
                                <input type="number" class="form-control" id="add_reorder_point" name="reorder_point" min="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="add_cost_per_unit">التكلفة لكل وحدة</label>
                                <input type="number" class="form-control" id="add_cost_per_unit" name="cost_per_unit" min="0" step="0.01">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="add_is_active" name="is_active" checked>
                            <label class="custom-control-label" for="add_is_active">نشط</label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="mdi mdi-content-save"></i> حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Item Modal -->
<div class="modal fade" id="editItemModal" tabindex="-1" role="dialog" aria-labelledby="editItemModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editItemModalLabel">تعديل المادة</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="editItemForm">
                <input type="hidden" id="edit_item_id" name="item_id">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_name">اسم المادة <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="edit_name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_sku">الرمز (SKU)</label>
                                <input type="text" class="form-control" id="edit_sku" name="sku">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_category">الفئة <span class="text-danger">*</span></label>
                                <select class="form-control select2" id="edit_category" name="category" required>
                                    <option value="">اختر الفئة</option>
                                    <option value="ingredients">المكونات</option>
                                    <option value="beverages">المشروبات</option>
                                    <option value="packaging">التعبئة والتغليف</option>
                                    <option value="cleaning">مواد التنظيف</option>
                                    <option value="equipment">المعدات</option>
                                    <option value="general">عام</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_unit_id">وحدة القياس <span class="text-danger">*</span></label>
                                <select class="form-control select2" id="edit_unit_id" name="unit_id" required>
                                    <option value="">اختر وحدة القياس</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit_description">الوصف</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="edit_minimum_stock">الحد الأدنى <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="edit_minimum_stock" name="minimum_stock" min="0" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="edit_maximum_stock">الحد الأقصى</label>
                                <input type="number" class="form-control" id="edit_maximum_stock" name="maximum_stock" min="0">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="edit_reorder_point">نقطة إعادة الطلب</label>
                                <input type="number" class="form-control" id="edit_reorder_point" name="reorder_point" min="0">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit_cost_per_unit">التكلفة لكل وحدة</label>
                        <input type="number" class="form-control" id="edit_cost_per_unit" name="cost_per_unit" min="0" step="0.01">
                    </div>
                    
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="edit_is_active" name="is_active">
                            <label class="custom-control-label" for="edit_is_active">نشط</label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="mdi mdi-content-save"></i> حفظ التغييرات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View Item Modal -->
<div class="modal fade" id="viewItemModal" tabindex="-1" role="dialog" aria-labelledby="viewItemModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewItemModalLabel">تفاصيل المادة</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-8">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>اسم المادة:</strong></td>
                                <td id="view_name"></td>
                            </tr>
                            <tr>
                                <td><strong>الرمز:</strong></td>
                                <td id="view_sku"></td>
                            </tr>
                            <tr>
                                <td><strong>الفئة:</strong></td>
                                <td id="view_category"></td>
                            </tr>
                            <tr>
                                <td><strong>وحدة القياس:</strong></td>
                                <td id="view_unit"></td>
                            </tr>
                            <tr>
                                <td><strong>الوصف:</strong></td>
                                <td id="view_description"></td>
                            </tr>
                            <tr>
                                <td><strong>المخزون الحالي:</strong></td>
                                <td id="view_current_stock"></td>
                            </tr>
                            <tr>
                                <td><strong>الحد الأدنى:</strong></td>
                                <td id="view_minimum_stock"></td>
                            </tr>
                            <tr>
                                <td><strong>الحد الأقصى:</strong></td>
                                <td id="view_maximum_stock"></td>
                            </tr>
                            <tr>
                                <td><strong>نقطة إعادة الطلب:</strong></td>
                                <td id="view_reorder_point"></td>
                            </tr>
                            <tr>
                                <td><strong>التكلفة لكل وحدة:</strong></td>
                                <td id="view_cost_per_unit"></td>
                            </tr>
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td id="view_status"></td>
                            </tr>
                            <tr>
                                <td><strong>آخر تحديث:</strong></td>
                                <td id="view_last_updated"></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <img id="view_image" src="" alt="صورة المادة" class="img-fluid rounded" style="max-height: 200px; display: none;">
                            <div id="view_no_image" class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 200px;">
                                <i class="mdi mdi-package-variant text-muted" style="font-size: 48px;"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="editItemFromView()">
                    <i class="mdi mdi-pencil"></i> تعديل
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Update Stock Modal -->
<div class="modal fade" id="updateStockModal" tabindex="-1" role="dialog" aria-labelledby="updateStockModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="updateStockModalLabel">تحديث المخزون</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="updateStockForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="stock_type">نوع العملية <span class="text-danger">*</span></label>
                        <select class="form-control" id="stock_type" name="type" required>
                            <option value="">اختر نوع العملية</option>
                            <option value="add">إضافة مخزون</option>
                            <option value="subtract">خصم مخزون</option>
                            <option value="set">تعيين مخزون</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="stock_quantity">الكمية <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="stock_quantity" name="quantity" min="0" required>
                    </div>

                    <div class="form-group">
                        <label for="stock_reason">السبب</label>
                        <textarea class="form-control" id="stock_reason" name="reason" rows="3" placeholder="اختياري - سبب تحديث المخزون"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="mdi mdi-cube-outline"></i> تحديث المخزون
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Import Items Modal -->
<div class="modal fade" id="importItemsModal" tabindex="-1" role="dialog" aria-labelledby="importItemsModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importItemsModalLabel">استيراد المواد</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="importItemsForm" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="mdi mdi-information"></i>
                        يمكنك استيراد المواد من ملف Excel أو CSV. تأكد من أن الملف يحتوي على الأعمدة المطلوبة.
                    </div>

                    <div class="form-group">
                        <label for="import_file">اختر الملف <span class="text-danger">*</span></label>
                        <input type="file" class="form-control-file" id="import_file" name="file" accept=".xlsx,.xls,.csv" required>
                        <small class="form-text text-muted">الملفات المدعومة: Excel (.xlsx, .xls) أو CSV (.csv)</small>
                    </div>

                    <div class="form-group">
                        <label for="import_format">تنسيق الملف <span class="text-danger">*</span></label>
                        <select class="form-control" id="import_format" name="format" required>
                            <option value="">اختر التنسيق</option>
                            <option value="excel">Excel</option>
                            <option value="csv">CSV</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <a href="#" class="btn btn-sm btn-outline-info" onclick="downloadTemplate()">
                            <i class="mdi mdi-download"></i> تحميل نموذج الملف
                        </a>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="mdi mdi-upload"></i> استيراد
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Load units for select dropdowns
    loadUnits();

    // Initialize form handlers
    initializeFormHandlers();
});

function loadUnits() {
    $.get('{{ route("inventory.api.units") }}')
        .done(function(units) {
            const options = units.map(unit =>
                `<option value="${unit.id}">${unit.name} (${unit.symbol})</option>`
            ).join('');

            $('#add_unit_id, #edit_unit_id').html('<option value="">اختر وحدة القياس</option>' + options);
        })
        .fail(function() {
            console.error('Failed to load units');
        });
}

function initializeFormHandlers() {
    // Add Item Form
    $('#addItemForm').on('submit', function(e) {
        e.preventDefault();
        const formData = new FormData(this);

        $.ajax({
            url: '{{ route("inventory.api.items.store") }}',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                $('#addItemModal').modal('hide');
                $('#addItemForm')[0].reset();
                InventoryModule.showSuccess('تم إضافة المادة بنجاح');
                itemsTable.ajax.reload();
            },
            error: function(xhr) {
                const errors = xhr.responseJSON?.errors;
                if (errors) {
                    let errorMessage = 'يرجى تصحيح الأخطاء التالية:\n';
                    Object.values(errors).forEach(error => {
                        errorMessage += '- ' + error[0] + '\n';
                    });
                    InventoryModule.showError(errorMessage);
                } else {
                    InventoryModule.showError('حدث خطأ أثناء إضافة المادة');
                }
            }
        });
    });

    // Edit Item Form
    $('#editItemForm').on('submit', function(e) {
        e.preventDefault();
        const itemId = $('#edit_item_id').val();
        const formData = new FormData(this);
        formData.append('_method', 'PUT');

        $.ajax({
            url: `{{ route("inventory.api.items.update", ":id") }}`.replace(':id', itemId),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                $('#editItemModal').modal('hide');
                InventoryModule.showSuccess('تم تحديث المادة بنجاح');
                itemsTable.ajax.reload();
            },
            error: function(xhr) {
                const errors = xhr.responseJSON?.errors;
                if (errors) {
                    let errorMessage = 'يرجى تصحيح الأخطاء التالية:\n';
                    Object.values(errors).forEach(error => {
                        errorMessage += '- ' + error[0] + '\n';
                    });
                    InventoryModule.showError(errorMessage);
                } else {
                    InventoryModule.showError('حدث خطأ أثناء تحديث المادة');
                }
            }
        });
    });

    // Update Stock Form
    $('#updateStockForm').on('submit', function(e) {
        e.preventDefault();
        const itemId = $('#updateStockModal').data('item-id');
        const formData = $(this).serialize();

        $.ajax({
            url: `{{ route("inventory.api.items.update-stock", ":id") }}`.replace(':id', itemId),
            type: 'POST',
            data: formData,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                $('#updateStockModal').modal('hide');
                $('#updateStockForm')[0].reset();
                InventoryModule.showSuccess('تم تحديث المخزون بنجاح');
                itemsTable.ajax.reload();
            },
            error: function(xhr) {
                const message = xhr.responseJSON?.message || 'حدث خطأ أثناء تحديث المخزون';
                InventoryModule.showError(message);
            }
        });
    });

    // Import Items Form
    $('#importItemsForm').on('submit', function(e) {
        e.preventDefault();
        const formData = new FormData(this);

        $.ajax({
            url: '{{ route("inventory.api.import") }}',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                $('#importItemsModal').modal('hide');
                $('#importItemsForm')[0].reset();
                InventoryModule.showSuccess('تم استيراد المواد بنجاح');
                itemsTable.ajax.reload();
            },
            error: function(xhr) {
                const message = xhr.responseJSON?.message || 'حدث خطأ أثناء استيراد المواد';
                InventoryModule.showError(message);
            }
        });
    });
}

function populateViewModal(item) {
    $('#view_name').text(item.product.name);
    $('#view_sku').text(item.product.sku);
    $('#view_category').text(getCategoryName(item.product.category));
    $('#view_unit').text(item.product.unit ? `${item.product.unit.name} (${item.product.unit.symbol})` : '-');
    $('#view_description').text(item.product.description || '-');
    $('#view_current_stock').text(`${item.current_stock} ${item.product.unit?.symbol || ''}`);
    $('#view_minimum_stock').text(item.minimum_stock);
    $('#view_maximum_stock').text(item.maximum_stock || '-');
    $('#view_reorder_point').text(item.reorder_point);
    $('#view_cost_per_unit').text(InventoryModule.formatCurrency(item.cost_per_unit));
    $('#view_status').html(getStatusBadge(item.current_stock, item.minimum_stock));
    $('#view_last_updated').text(InventoryModule.formatDate(item.last_updated));

    // Handle image
    if (item.image) {
        $('#view_image').attr('src', item.image).show();
        $('#view_no_image').hide();
    } else {
        $('#view_image').hide();
        $('#view_no_image').show();
    }

    // Store item ID for edit button
    $('#viewItemModal').data('item-id', item.id);
}

function populateEditModal(item) {
    $('#edit_item_id').val(item.id);
    $('#edit_name').val(item.product.name);
    $('#edit_sku').val(item.product.sku);
    $('#edit_category').val(item.product.category).trigger('change');
    $('#edit_unit_id').val(item.product.unit_id).trigger('change');
    $('#edit_description').val(item.product.description);
    $('#edit_minimum_stock').val(item.minimum_stock);
    $('#edit_maximum_stock').val(item.maximum_stock);
    $('#edit_reorder_point').val(item.reorder_point);
    $('#edit_cost_per_unit').val(item.cost_per_unit);
    $('#edit_is_active').prop('checked', item.product.is_active);
}

function editItemFromView() {
    const itemId = $('#viewItemModal').data('item-id');
    $('#viewItemModal').modal('hide');
    editItem(itemId);
}

function getCategoryName(category) {
    const categories = {
        'ingredients': 'المكونات',
        'beverages': 'المشروبات',
        'packaging': 'التعبئة والتغليف',
        'cleaning': 'مواد التنظيف',
        'equipment': 'المعدات',
        'general': 'عام'
    };
    return categories[category] || category;
}

function downloadTemplate() {
    // This would download a template file
    window.location.href = '{{ route("inventory.api.export") }}?format=template';
}
</script>
