<?php

namespace Modules\Menu\Http\Controllers\Api;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Modules\Menu\Http\Requests\StoreMenuItemRequest;
use Modules\Menu\Http\Requests\UpdateMenuItemRequest;
use Modules\Menu\Services\MenuItemService;
use App\Helpers\BranchHelper;
use App\Helpers\ResponseHelper;

class MenuItemController extends Controller
{
    protected $menuItemService;

    public function __construct(MenuItemService $menuItemService)
    {
        $this->menuItemService = $menuItemService;
    }

    /**
     * Display a listing of menu items
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            
            // Convert request parameters to filters array
            $filters = $request->only([
                'category_id', 'menu_id', 'is_active', 'is_featured',
                'min_price', 'max_price', 'search', 'sort_by', 
                'sort_direction', 'per_page'
            ]);
            
            $menuItems = $this->menuItemService->getMenuItemsForBranch($branchId, $filters);
            
            return ResponseHelper::success($menuItems, 'Menu items retrieved successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve menu items: ' . $e->getMessage());
        }
    }

    /**
     * Store a newly created menu item
     */
    public function store(StoreMenuItemRequest $request): JsonResponse
    {
        try {
            $data = BranchHelper::addBranchAndTenantIds($request->validated());
            $menuItem = $this->menuItemService->createMenuItem($data);
            
            return ResponseHelper::success($menuItem, 'Menu item created successfully', 201);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to create menu item: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified menu item
     */
    public function show(int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $menuItem = $this->menuItemService->getMenuItemByIdForBranch($id, $branchId);
            
            if (!$menuItem) {
                return ResponseHelper::notFound('Menu item not found');
            }
            
            return ResponseHelper::success($menuItem, 'Menu item retrieved successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve menu item: ' . $e->getMessage());
        }
    }

    /**
     * Update the specified menu item
     */
    public function update(UpdateMenuItemRequest $request, int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $menuItem = $this->menuItemService->updateMenuItemForBranch($id, $request->validated(), $branchId);
            
            if (!$menuItem) {
                return ResponseHelper::notFound('Menu item not found');
            }
            
            return ResponseHelper::success($menuItem, 'Menu item updated successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to update menu item: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified menu item
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $deleted = $this->menuItemService->deleteMenuItemForBranch($id, $branchId);
            
            if (!$deleted) {
                return ResponseHelper::notFound('Menu item not found');
            }
            
            return ResponseHelper::success(null, 'Menu item deleted successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to delete menu item: ' . $e->getMessage());
        }
    }
}