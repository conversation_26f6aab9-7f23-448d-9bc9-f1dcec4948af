<?php

namespace Modules\Delivery\Services;

use Modules\Delivery\Models\DeliveryPersonnel;
use App\Models\User;
use App\Models\Branch;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class DeliveryPersonnelService
{
    /**
     * Create new delivery personnel
     */
    public function createPersonnel(array $data): DeliveryPersonnel
    {
        DB::beginTransaction();
        
        try {
            // Create user if user_id not provided
            if (!isset($data['user_id'])) {
                $user = User::create([
                    'name' => $data['name'],
                    'email' => $data['email'],
                    'password' => Hash::make($data['password'] ?? 'password123'),
                    'tenant_id' => $data['tenant_id'],
                    'role' => 'delivery_personnel',
                ]);
                $data['user_id'] = $user->id;
            }

            $personnel = DeliveryPersonnel::create([
                'user_id' => $data['user_id'],
                'branch_id' => $data['branch_id'],
                'license_number' => $data['license_number'],
                'license_expiry_date' => $data['license_expiry_date'] ?? null,
                'vehicle_type' => $data['vehicle_type'],
                'vehicle_plate_number' => $data['vehicle_plate_number'],
                'vehicle_model' => $data['vehicle_model'] ?? null,
                'phone_number' => $data['phone_number'],
                'emergency_contact_name' => $data['emergency_contact_name'],
                'emergency_contact_phone' => $data['emergency_contact_phone'],
                'max_concurrent_deliveries' => $data['max_concurrent_deliveries'] ?? config('delivery.max_concurrent_deliveries'),
                'delivery_radius_km' => $data['delivery_radius_km'] ?? $data['delivery_radius'] ?? config('delivery.default_delivery_radius'),
                'working_hours' => $data['working_hours'] ?? [],
                'status' => 'inactive', // Start as inactive until verified
            ]);

            DB::commit();
            
            return $personnel->load(['user', 'branch']);
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Update delivery personnel
     */
    public function updatePersonnel(DeliveryPersonnel $personnel, array $data): DeliveryPersonnel
    {
        $personnel->update($data);
        return $personnel->fresh(['user', 'branch']);
    }

    /**
     * Verify delivery personnel
     */
    public function verifyPersonnel(int $personnelId): DeliveryPersonnel
    {
        $personnel = DeliveryPersonnel::findOrFail($personnelId);
        
        $personnel->update([
            'is_verified' => true,
            'verified_at' => now(),
            'status' => 'active',
        ]);

        return $personnel->fresh(['user', 'branch']);
    }

    /**
     * Update personnel location
     */
    public function updateLocation(int $personnelId, float $latitude, float $longitude): DeliveryPersonnel
    {
        $personnel = DeliveryPersonnel::findOrFail($personnelId);
        
        $personnel->updateLocation($latitude, $longitude);
        
        return $personnel;
    }

    /**
     * Get available personnel for delivery
     */
    public function getAvailablePersonnel(float $lat, float $lng, float $radius = null): Collection
    {
        $radius = $radius ?? config('delivery.default_delivery_radius');
        
        return DeliveryPersonnel::available()
            ->withinRadius($lat, $lng, $radius)
            ->with(['user', 'branch'])
            ->orderByRaw("
                (6371 * acos(cos(radians(?)) * cos(radians(current_latitude)) * 
                cos(radians(current_longitude) - radians(?)) + 
                sin(radians(?)) * sin(radians(current_latitude))))
            ", [$lat, $lng, $lat])
            ->get();
    }

    /**
     * Get personnel with filters
     */
    public function getPersonnel(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = DeliveryPersonnel::with(['user', 'branch']);

        if (isset($filters['branch_id'])) {
            $query->where('branch_id', $filters['branch_id']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['is_verified'])) {
            $query->where('is_verified', $filters['is_verified']);
        }

        if (isset($filters['vehicle_type'])) {
            $query->where('vehicle_type', $filters['vehicle_type']);
        }

        if (isset($filters['search'])) {
            $search = $filters['search'];
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            })->orWhere('phone_number', 'like', "%{$search}%")
              ->orWhere('license_number', 'like', "%{$search}%");
        }

        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    /**
     * Get personnel by ID
     */
    public function getPersonnelById(int $id): ?DeliveryPersonnel
    {
        return DeliveryPersonnel::with(['user', 'branch', 'deliveryAssignments.order'])
            ->find($id);
    }

    /**
     * Update personnel status
     */
    public function updateStatus(int $personnelId, string $status): DeliveryPersonnel
    {
        $personnel = DeliveryPersonnel::findOrFail($personnelId);
        
        if (!in_array($status, config('delivery.personnel_statuses'))) {
            throw new \Exception("Invalid personnel status: {$status}");
        }

        $personnel->update(['status' => $status]);
        
        return $personnel;
    }

    /**
     * Get personnel performance metrics
     */
    public function getPersonnelPerformance(int $personnelId, array $filters = []): array
    {
        $personnel = DeliveryPersonnel::with(['deliveryAssignments' => function ($query) use ($filters) {
            if (isset($filters['date_from'])) {
                $query->whereDate('assigned_at', '>=', $filters['date_from']);
            }
            if (isset($filters['date_to'])) {
                $query->whereDate('assigned_at', '<=', $filters['date_to']);
            }
        }])->findOrFail($personnelId);

        $assignments = $personnel->deliveryAssignments;
        
        $totalDeliveries = $assignments->count();
        $successfulDeliveries = $assignments->where('status', 'delivered')->count();
        $failedDeliveries = $assignments->where('status', 'failed')->count();
        $cancelledDeliveries = $assignments->where('status', 'cancelled')->count();
        
        $successRate = $totalDeliveries > 0 ? ($successfulDeliveries / $totalDeliveries) * 100 : 0;
        
        $avgDeliveryTime = $assignments->where('status', 'delivered')
            ->whereNotNull('actual_duration_minutes')
            ->avg('actual_duration_minutes');
            
        $totalEarnings = $assignments->where('status', 'delivered')
            ->sum('delivery_fee_earned');

        return [
            'personnel' => $personnel,
            'total_deliveries' => $totalDeliveries,
            'successful_deliveries' => $successfulDeliveries,
            'failed_deliveries' => $failedDeliveries,
            'cancelled_deliveries' => $cancelledDeliveries,
            'success_rate' => round($successRate, 2),
            'average_delivery_time' => round($avgDeliveryTime ?? 0, 2),
            'total_earnings' => $totalEarnings,
            'current_rating' => $personnel->rating,
        ];
    }

    /**
     * Delete personnel
     */
    public function deletePersonnel(int $personnelId): bool
    {
        $personnel = DeliveryPersonnel::findOrFail($personnelId);
        
        // Check if personnel has active deliveries
        if ($personnel->activeDeliveries()->count() > 0) {
            throw new \Exception('Cannot delete personnel with active deliveries');
        }

        return $personnel->delete();
    }

    /**
     * Get personnel working hours
     */
    public function getWorkingHours(int $personnelId): array
    {
        $personnel = DeliveryPersonnel::findOrFail($personnelId);
        return $personnel->working_hours ?? [];
    }

    /**
     * Update personnel working hours
     */
    public function updateWorkingHours(int $personnelId, array $workingHours): DeliveryPersonnel
    {
        $personnel = DeliveryPersonnel::findOrFail($personnelId);
        
        $personnel->update(['working_hours' => $workingHours]);
        
        return $personnel;
    }

    /**
     * Check if personnel is currently working
     */
    public function isCurrentlyWorking(int $personnelId): bool
    {
        $personnel = DeliveryPersonnel::findOrFail($personnelId);
        
        if (empty($personnel->working_hours)) {
            return true; // No restrictions
        }

        $currentDay = strtolower(now()->format('l'));
        $currentTime = now()->format('H:i');
        
        foreach ($personnel->working_hours as $schedule) {
            if ($schedule['day'] === $currentDay) {
                return $currentTime >= $schedule['start_time'] && $currentTime <= $schedule['end_time'];
            }
        }
        
        return false;
    }
}