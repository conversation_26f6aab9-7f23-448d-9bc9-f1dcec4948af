@extends('layouts.master')

@section('title', 'إدارة المستأجرين')
@push('styles')
<meta name="csrf-token" content="{{ csrf_token() }}">
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
    /* Custom DataTables Tailwind Styling */
    .dataTables_wrapper {
        font-family: inherit;
    }
    
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_processing,
    .dataTables_wrapper .dataTables_paginate {
        color: #374151;
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }
    .dataTables_processing{
        margin: auto
    }
    .dataTables_wrapper .dataTables_length select,
    .dataTables_wrapper .dataTables_filter input {
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
    
    .dataTables_wrapper .dataTables_filter input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.5rem 0.75rem !important;
        margin: 0 0.125rem !important;
        border-radius: 0.375rem !important;
        border: 1px solid #d1d5db !important;
        background: white !important;
        color: #374151 !important;
        text-decoration: none !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background: #f3f4f6 !important;
        border-color: #9ca3af !important;
        color: #374151 !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background: #3b82f6 !important;
        border-color: #3b82f6 !important;
        color: white !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
        background: #f9fafb !important;
        color: #9ca3af !important;
        border-color: #e5e7eb !important;
    }
    
    table.dataTable {
        border-collapse: separate !important;
        border-spacing: 0 !important;
    }
    
    table.dataTable thead th {
        background-color: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
        font-weight: 600;
        color: #374151;
        padding: 0.75rem;
    }
    
    table.dataTable tbody td {
        padding: 0.75rem;
        border-bottom: 1px solid #f3f4f6;
    }
    
    table.dataTable tbody tr:hover {
        background-color: #f9fafb;
    }
    
    .select2-container--default .select2-selection--single {
        height: 42px;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        padding: 0.5rem;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 26px;
        padding-left: 0;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 40px;
    }
    
    .modal-backdrop {
        background-color: rgba(0, 0, 0, 0.5);
    }
    
    .animate-fadeIn {
        animation: fadeIn 0.3s ease-in-out;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: scale(0.95); }
        to { opacity: 1; transform: scale(1); }
    }
    
    .animate-slideDown {
        animation: slideDown 0.3s ease-in-out;
    }
    
    @keyframes slideDown {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>
@endpush

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h1 class="text-2xl font-bold text-gray-900 flex items-center gap-3">
                    <i class="fas fa-users text-blue-600"></i>
                    إدارة المستأجرين
                </h1>
                <button onclick="openCreateModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                    <i class="fas fa-plus text-sm"></i>
                    إضافة مستأجر جديد
                </button>
            </div>
        </div>
        
        <!-- Filters -->
        <div class="p-6 bg-gray-50 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                <i class="fas fa-filter text-blue-600"></i>
                فلاتر البحث
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label for="statusFilter" class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                    <select id="statusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                        <option value="suspended">معلق</option>
                        <option value="trial">تجريبي</option>
                    </select>
                </div>
                <div>
                    <label for="businessTypeFilter" class="block text-sm font-medium text-gray-700 mb-2">نوع النشاط</label>
                    <select id="businessTypeFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">جميع الأنواع</option>
                        <option value="restaurant">مطعم</option>
                        <option value="cafe">مقهى</option>
                        <option value="bakery">مخبز</option>
                        <option value="fast_food">وجبات سريعة</option>
                        <option value="catering">تموين</option>
                    </select>
                </div>
                <div>
                    <label for="countryFilter" class="block text-sm font-medium text-gray-700 mb-2">الدولة</label>
                    <select id="countryFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">جميع الدول</option>
                        @foreach($countries as $country)
                            <option value="{{ $country->id }}">{{ $country->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="flex items-end gap-2">
                    <button onclick="applyFilters()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                        <i class="fas fa-search text-sm"></i>
                        بحث
                    </button>
                    <button onclick="clearFilters()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                        <i class="fas fa-times text-sm"></i>
                        مسح
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- DataTable -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">قائمة المستأجرين</h2>
        </div>
        <div class="overflow-x-auto">
            <table id="tenantsTable" class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">#</th>
                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">الاسم</th>
                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">نوع النشاط</th>
                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">الدولة</th>
                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">معلومات الاتصال</th>
                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">الحالة</th>
                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">الفروع</th>
                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">المستخدمين</th>
                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <!-- DataTable will populate this -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Create Modal -->
<div id="createModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">إضافة مستأجر جديد</h3>
            <button onclick="closeCreateModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        
        <form id="createForm" method="POST" action="{{ route('tenants.store') }}">
            @csrf
            <div class="space-y-4 max-h-96 overflow-y-auto">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="create_name" class="block text-sm font-medium text-gray-700 mb-2">
                            اسم المستأجر <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="name" id="create_name" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label for="create_code" class="block text-sm font-medium text-gray-700 mb-2">
                            الكود
                        </label>
                        <input type="text" name="code" id="create_code" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="create_business_type" class="block text-sm font-medium text-gray-700 mb-2">
                            نوع النشاط <span class="text-red-500">*</span>
                        </label>
                        <select name="business_type" id="create_business_type" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">اختر نوع النشاط</option>
                            <option value="restaurant">مطعم</option>
                            <option value="cafe">مقهى</option>
                            <option value="bakery">مخبز</option>
                            <option value="fast_food">وجبات سريعة</option>
                            <option value="catering">تموين</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="create_country_id" class="block text-sm font-medium text-gray-700 mb-2">
                            الدولة <span class="text-red-500">*</span>
                        </label>
                        <select name="country_id" id="create_country_id" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">اختر الدولة</option>
                            @foreach($countries as $country)
                                <option value="{{ $country->id }}">{{ $country->name }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="create_primary_contact_name" class="block text-sm font-medium text-gray-700 mb-2">
                            اسم جهة الاتصال <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="primary_contact_name" id="create_primary_contact_name" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label for="create_contact_email" class="block text-sm font-medium text-gray-700 mb-2">
                            البريد الإلكتروني <span class="text-red-500">*</span>
                        </label>
                        <input type="email" name="contact_email" id="create_contact_email" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="create_contact_phone" class="block text-sm font-medium text-gray-700 mb-2">
                            رقم الهاتف <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="contact_phone" id="create_contact_phone" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label for="create_website_url" class="block text-sm font-medium text-gray-700 mb-2">
                            الموقع الإلكتروني
                        </label>
                        <input type="url" name="website_url" id="create_website_url" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
                
                <div>
                    <label for="create_business_address" class="block text-sm font-medium text-gray-700 mb-2">
                        عنوان النشاط <span class="text-red-500">*</span>
                    </label>
                    <textarea name="business_address" id="create_business_address" rows="3" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"></textarea>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="create_tax_number" class="block text-sm font-medium text-gray-700 mb-2">
                            الرقم الضريبي
                        </label>
                        <input type="text" name="tax_number" id="create_tax_number" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label for="create_business_license" class="block text-sm font-medium text-gray-700 mb-2">
                            رخصة النشاط
                        </label>
                        <input type="text" name="business_license" id="create_business_license" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="create_timezone" class="block text-sm font-medium text-gray-700 mb-2">
                            المنطقة الزمنية <span class="text-red-500">*</span>
                        </label>
                        <select name="timezone" id="create_timezone" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="Asia/Riyadh">Asia/Riyadh</option>
                            <option value="Asia/Dubai">Asia/Dubai</option>
                            <option value="Asia/Kuwait">Asia/Kuwait</option>
                            <option value="Asia/Qatar">Asia/Qatar</option>
                            <option value="Asia/Bahrain">Asia/Bahrain</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="create_currency_code" class="block text-sm font-medium text-gray-700 mb-2">
                            العملة <span class="text-red-500">*</span>
                        </label>
                        <select name="currency_code" id="create_currency_code" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="SAR">ريال سعودي (SAR)</option>
                            <option value="AED">درهم إماراتي (AED)</option>
                            <option value="KWD">دينار كويتي (KWD)</option>
                            <option value="QAR">ريال قطري (QAR)</option>
                            <option value="BHD">دينار بحريني (BHD)</option>
                            <option value="USD">دولار أمريكي (USD)</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="create_language_code" class="block text-sm font-medium text-gray-700 mb-2">
                            اللغة <span class="text-red-500">*</span>
                        </label>
                        <select name="language_code" id="create_language_code" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="ar">العربية</option>
                            <option value="en">English</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="flex justify-end gap-3 mt-6 pt-4 border-t border-gray-200">
                <button type="button" onclick="closeCreateModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                    إلغاء
                </button>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    إضافة المستأجر
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Edit Modal -->
<div id="editModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">تعديل المستأجر</h3>
            <button onclick="closeEditModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        
        <form id="editForm" method="POST">
            @csrf
            @method('PUT')
            <div class="space-y-4 max-h-96 overflow-y-auto">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="edit_name" class="block text-sm font-medium text-gray-700 mb-2">
                            اسم المستأجر <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="name" id="edit_name" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label for="edit_code" class="block text-sm font-medium text-gray-700 mb-2">
                            الكود
                        </label>
                        <input type="text" name="code" id="edit_code" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="edit_business_type" class="block text-sm font-medium text-gray-700 mb-2">
                            نوع النشاط <span class="text-red-500">*</span>
                        </label>
                        <select name="business_type" id="edit_business_type" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">اختر نوع النشاط</option>
                            <option value="restaurant">مطعم</option>
                            <option value="cafe">مقهى</option>
                            <option value="bakery">مخبز</option>
                            <option value="fast_food">وجبات سريعة</option>
                            <option value="catering">تموين</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="edit_country_id" class="block text-sm font-medium text-gray-700 mb-2">
                            الدولة <span class="text-red-500">*</span>
                        </label>
                        <select name="country_id" id="edit_country_id" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">اختر الدولة</option>
                            @foreach($countries as $country)
                                <option value="{{ $country->id }}">{{ $country->name }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="edit_primary_contact_name" class="block text-sm font-medium text-gray-700 mb-2">
                            اسم جهة الاتصال <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="primary_contact_name" id="edit_primary_contact_name" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label for="edit_contact_email" class="block text-sm font-medium text-gray-700 mb-2">
                            البريد الإلكتروني <span class="text-red-500">*</span>
                        </label>
                        <input type="email" name="contact_email" id="edit_contact_email" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="edit_contact_phone" class="block text-sm font-medium text-gray-700 mb-2">
                            رقم الهاتف <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="contact_phone" id="edit_contact_phone" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label for="edit_website_url" class="block text-sm font-medium text-gray-700 mb-2">
                            الموقع الإلكتروني
                        </label>
                        <input type="url" name="website_url" id="edit_website_url" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
                
                <div>
                    <label for="edit_business_address" class="block text-sm font-medium text-gray-700 mb-2">
                        عنوان النشاط <span class="text-red-500">*</span>
                    </label>
                    <textarea name="business_address" id="edit_business_address" rows="3" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"></textarea>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="edit_tax_number" class="block text-sm font-medium text-gray-700 mb-2">
                            الرقم الضريبي
                        </label>
                        <input type="text" name="tax_number" id="edit_tax_number" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label for="edit_business_license" class="block text-sm font-medium text-gray-700 mb-2">
                            رخصة النشاط
                        </label>
                        <input type="text" name="business_license" id="edit_business_license" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="edit_timezone" class="block text-sm font-medium text-gray-700 mb-2">
                            المنطقة الزمنية <span class="text-red-500">*</span>
                        </label>
                        <select name="timezone" id="edit_timezone" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="Asia/Riyadh">Asia/Riyadh</option>
                            <option value="Asia/Dubai">Asia/Dubai</option>
                            <option value="Asia/Kuwait">Asia/Kuwait</option>
                            <option value="Asia/Qatar">Asia/Qatar</option>
                            <option value="Asia/Bahrain">Asia/Bahrain</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="edit_currency_code" class="block text-sm font-medium text-gray-700 mb-2">
                            العملة <span class="text-red-500">*</span>
                        </label>
                        <select name="currency_code" id="edit_currency_code" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="SAR">ريال سعودي (SAR)</option>
                            <option value="AED">درهم إماراتي (AED)</option>
                            <option value="KWD">دينار كويتي (KWD)</option>
                            <option value="QAR">ريال قطري (QAR)</option>
                            <option value="BHD">دينار بحريني (BHD)</option>
                            <option value="USD">دولار أمريكي (USD)</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="edit_language_code" class="block text-sm font-medium text-gray-700 mb-2">
                            اللغة <span class="text-red-500">*</span>
                        </label>
                        <select name="language_code" id="edit_language_code" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="ar">العربية</option>
                            <option value="en">English</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="flex justify-end gap-3 mt-6 pt-4 border-t border-gray-200">
                <button type="button" onclick="closeEditModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                    إلغاء
                </button>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    تحديث المستأجر
                </button>
            </div>
        </form>
    </div>
</div>

<!-- View Modal -->
<div id="viewModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">تفاصيل المستأجر</h3>
            <button onclick="closeViewModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        
        <div id="viewContent" class="space-y-4 max-h-96 overflow-y-auto">
            <!-- Content will be loaded here -->
        </div>
        
        <div class="flex justify-end mt-6 pt-4 border-t border-gray-200">
            <button onclick="closeViewModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                إغلاق
            </button>
        </div>
    </div>
</div>
@endsection


@push('scripts')
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.tailwindcss.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable
    window.tenantsTable = $('#tenantsTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("tenants.data") }}',
            data: function(d) {
                d.status = $('#statusFilter').val();
                d.business_type = $('#businessTypeFilter').val();
                d.country_id = $('#countryFilter').val();
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'name', name: 'name' },
       
            { data: 'business_type', name: 'business_type' },
            { data: 'country_name', name: 'country.name' },
            { data: 'contact_info', name: 'contact_info', orderable: false, searchable: false },
            { data: 'status_badge', name: 'status' },
            { data: 'branches_count', name: 'branches_count', orderable: false, searchable: false },
            { data: 'users_count', name: 'users_count', orderable: false, searchable: false },
            { data: 'action', name: 'action', orderable: false, searchable: false }
        ],

        order: [[1, 'asc']],
        pageLength: 25,
        responsive: true,
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json'
        }
    });
});

// Modal Functions
function openCreateModal() {
    document.getElementById('createModal').classList.remove('hidden');
}

function closeCreateModal() {
    document.getElementById('createModal').classList.add('hidden');
    document.getElementById('createForm').reset();
}

function openEditModal(tenantId) {
    // Fetch tenant data and populate form
    fetch(`/admin/tenants/${tenantId}`)
        .then(response => response.json())
        .then(data => {
            // Populate form fields
            document.getElementById('edit_name').value = data.name || '';
            document.getElementById('edit_code').value = data.code || '';
            document.getElementById('edit_business_type').value = data.business_type || '';
            document.getElementById('edit_country_id').value = data.country_id || '';
            document.getElementById('edit_primary_contact_name').value = data.primary_contact_name || '';
            document.getElementById('edit_contact_email').value = data.contact_email || '';
            document.getElementById('edit_contact_phone').value = data.contact_phone || '';
            document.getElementById('edit_website_url').value = data.website_url || '';
            document.getElementById('edit_business_address').value = data.business_address || '';
            document.getElementById('edit_tax_number').value = data.tax_number || '';
            document.getElementById('edit_business_license').value = data.business_license || '';
            document.getElementById('edit_timezone').value = data.timezone || '';
            document.getElementById('edit_currency_code').value = data.currency_code || '';
            document.getElementById('edit_language_code').value = data.language_code || '';
            
            // Set form action
            document.getElementById('editForm').action = `/admin/tenants/${tenantId}`;
            
            // Show modal
            document.getElementById('editModal').classList.remove('hidden');
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'خطأ!',
                text: 'حدث خطأ أثناء تحميل بيانات المستأجر',
                confirmButtonText: 'موافق'
            });
        });
}

function closeEditModal() {
    document.getElementById('editModal').classList.add('hidden');
    document.getElementById('editForm').reset();
}

function openViewModal(tenantId) {
    // Fetch tenant data and display
    fetch(`/admin/tenants/${tenantId}`)
        .then(response => response.json())
        .then(data => {
            const content = `
                <div class="space-y-4">
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-gray-800 mb-3">المعلومات الأساسية</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-600">اسم المستأجر</label>
                                <p class="text-gray-800">${data.name || 'غير محدد'}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-600">الكود</label>
                                <p class="text-gray-800">${data.code || 'غير محدد'}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-600">نوع النشاط</label>
                                <p class="text-gray-800">${data.business_type || 'غير محدد'}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-600">الدولة</label>
                                <p class="text-gray-800">${data.country ? data.country.name : 'غير محدد'}</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-gray-800 mb-3">معلومات الاتصال</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-600">اسم جهة الاتصال</label>
                                <p class="text-gray-800">${data.primary_contact_name || 'غير محدد'}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-600">البريد الإلكتروني</label>
                                <p class="text-gray-800">${data.contact_email || 'غير محدد'}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-600">رقم الهاتف</label>
                                <p class="text-gray-800">${data.contact_phone || 'غير محدد'}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-600">الموقع الإلكتروني</label>
                                <p class="text-gray-800">${data.website_url || 'غير محدد'}</p>
                            </div>
                        </div>
                        <div class="mt-4">
                            <label class="block text-sm font-medium text-gray-600">عنوان النشاط</label>
                            <p class="text-gray-800">${data.business_address || 'غير محدد'}</p>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-gray-800 mb-3">الإعدادات</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-600">المنطقة الزمنية</label>
                                <p class="text-gray-800">${data.timezone || 'غير محدد'}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-600">العملة</label>
                                <p class="text-gray-800">${data.currency_code || 'غير محدد'}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-600">اللغة</label>
                                <p class="text-gray-800">${data.language_code || 'غير محدد'}</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.getElementById('viewContent').innerHTML = content;
            document.getElementById('viewModal').classList.remove('hidden');
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'خطأ!',
                text: 'حدث خطأ أثناء تحميل بيانات المستأجر',
                confirmButtonText: 'موافق'
            });
        });
}

function closeViewModal() {
    document.getElementById('viewModal').classList.add('hidden');
}

function deleteTenant(tenantId) {
    Swal.fire({
        title: 'هل أنت متأكد؟',
        text: 'هل أنت متأكد من حذف هذا المستأجر؟ هذا الإجراء لا يمكن التراجع عنه.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'نعم، احذف!',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`/admin/tenants/${tenantId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'تم الحذف!',
                        text: 'تم حذف المستأجر بنجاح',
                        confirmButtonText: 'موافق'
                    });
                    tenantsTable.ajax.reload();
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ!',
                        text: data.message || 'حدث خطأ أثناء حذف المستأجر',
                        confirmButtonText: 'موافق'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ!',
                    text: 'حدث خطأ أثناء حذف المستأجر',
                    confirmButtonText: 'موافق'
                });
            });
        }
    });
}

function changeStatus(tenantId, action) {
    const actions = {
        'activate': 'تفعيل',
        'deactivate': 'إلغاء تفعيل',
        'suspend': 'تعليق'
    };
    
    Swal.fire({
        title: 'تأكيد العملية',
        text: `هل أنت متأكد من ${actions[action]} هذا المستأجر؟`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'نعم، تأكيد',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`/admin/tenants/${tenantId}/${action}`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'تم بنجاح!',
                        text: `تم ${actions[action]} المستأجر بنجاح`,
                        confirmButtonText: 'موافق'
                    });
                    tenantsTable.ajax.reload();
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ!',
                        text: data.message || `حدث خطأ أثناء ${actions[action]} المستأجر`,
                        confirmButtonText: 'موافق'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ!',
                    text: `حدث خطأ أثناء ${actions[action]} المستأجر`,
                    confirmButtonText: 'موافق'
                });
            });
        }
    });
}

// Filter Functions
function applyFilters() {
    tenantsTable.ajax.reload();
}

function clearFilters() {
    document.getElementById('statusFilter').value = '';
    document.getElementById('businessTypeFilter').value = '';
    document.getElementById('countryFilter').value = '';
    tenantsTable.ajax.reload();
}

// Form submission handlers
document.getElementById('createForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch(this.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire({
                icon: 'success',
                title: 'تم بنجاح!',
                text: 'تم إضافة المستأجر بنجاح',
                confirmButtonText: 'موافق'
            });
            closeCreateModal();
            tenantsTable.ajax.reload();
        } else {
            Swal.fire({
                icon: 'error',
                title: 'خطأ!',
                text: data.message || 'حدث خطأ أثناء إضافة المستأجر',
                confirmButtonText: 'موافق'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'خطأ!',
            text: 'حدث خطأ أثناء إضافة المستأجر',
            confirmButtonText: 'موافق'
        });
    });
});

document.getElementById('editForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch(this.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire({
                icon: 'success',
                title: 'تم بنجاح!',
                text: 'تم تحديث المستأجر بنجاح',
                confirmButtonText: 'موافق'
            });
            closeEditModal();
            tenantsTable.ajax.reload();
        } else {
            Swal.fire({
                icon: 'error',
                title: 'خطأ!',
                text: data.message || 'حدث خطأ أثناء تحديث المستأجر',
                confirmButtonText: 'موافق'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'خطأ!',
            text: 'حدث خطأ أثناء تحديث المستأجر',
            confirmButtonText: 'موافق'
        });
    });
});
</script>
@endpush