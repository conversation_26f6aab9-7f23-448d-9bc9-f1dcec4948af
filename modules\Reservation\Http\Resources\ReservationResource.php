<?php

namespace Modules\Reservation\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ReservationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'branch_id' => $this->branch_id,
            'customer_id' => $this->customer_id,
            'table_id' => $this->table_id,
            'reservation_number' => $this->reservation_number,
            'customer' => [
                'name' => $this->name,
                'phone' => $this->phone,
                'email' => $this->email,
            ],
            'party_size' => $this->party_size,
            'reservation_datetime' => $this->reservation_datetime,
            'duration_minutes' => $this->duration_minutes,
            'special_requests' => $this->special_requests,
            'notes' => $this->notes,
            'created_by' => $this->created_by,
            'seated_at' => $this->seated_at,
            'completed_at' => $this->completed_at,
            'area_id' => $this->area_id,
            'reservation_status_id' => $this->reservation_status_id,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            
            // Relationships
            'branch' => $this->whenLoaded('branch', function () {
                return [
                    'id' => $this->branch->id,
                    'name' => $this->branch->name,
                ];
            }),
            
            'customer_details' => $this->whenLoaded('customer', function () {
                return [
                    'id' => $this->customer->id,
                    'name' => $this->customer->name,
                    'email' => $this->customer->email,
                    'phone' => $this->customer->phone,
                ];
            }),
            
            'table' => $this->whenLoaded('table', function () {
                return [
                    'id' => $this->table->id,
                    'table_number' => $this->table->table_number,
                    'table_name' => $this->table->table_name,
                    'seating_capacity' => $this->table->seating_capacity,
                    'section' => $this->table->section,
                    'status' => $this->table->status,
                ];
            }),
            
            'area' => $this->whenLoaded('area', function () {
                return [
                    'id' => $this->area->id,
                    'name' => $this->area->name,
                    'description' => $this->area->description,
                ];
            }),
            
            'status' => $this->whenLoaded('status', function () {
                return [
                    'id' => $this->status->id,
                    'name' => $this->status->name,
                    'description' => $this->status->description,
                ];
            }),
            
            'created_by_user' => $this->whenLoaded('createdBy', function () {
                return [
                    'id' => $this->createdBy->id,
                    'name' => $this->createdBy->name,
                    'email' => $this->createdBy->email,
                ];
            }),
        ];
    }
}