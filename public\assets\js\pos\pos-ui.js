/**
 * POS UI Module - Handles user interface updates and interactions
 * Manages connection status, notifications, and UI state changes
 */

class POSUI {
    constructor(posCore) {
        this.posCore = posCore;
        this.connectionStatusElement = null;
        this.notificationContainer = null;
        
        this.init();
    }

    /**
     * Initialize UI components
     */
    init() {
        this.setupConnectionStatus();
        this.setupNotifications();
        this.setupOfflineIndicators();
        this.setupSyncIndicators();
    }

    /**
     * Setup connection status indicator
     */
    setupConnectionStatus() {
        this.connectionStatusElement = document.getElementById('connectionStatus');
        
        if (this.connectionStatusElement) {
            this.updateConnectionStatus(navigator.onLine);
        }
    }

    /**
     * Update connection status display
     */
    updateConnectionStatus(isOnline) {
        if (!this.connectionStatusElement) return;

        const icon = this.connectionStatusElement.querySelector('i');
        const text = this.connectionStatusElement.querySelector('span');

        if (isOnline) {
            this.connectionStatusElement.className = 'online inline-flex items-center gap-2 px-4 py-2 text-sm';
            icon.className = 'mdi mdi-wifi';
            text.textContent = 'Online';
        } else {
            this.connectionStatusElement.className = 'offline inline-flex items-center gap-2 px-4 py-2 text-sm';
            icon.className = 'mdi mdi-wifi-off';
            text.textContent = 'Offline';
        }
    }

    /**
     * Setup notification system
     */
    setupNotifications() {
        // Create notification container if it doesn't exist
        this.notificationContainer = document.getElementById('notificationContainer');
        
        if (!this.notificationContainer) {
            this.notificationContainer = document.createElement('div');
            this.notificationContainer.id = 'notificationContainer';
            this.notificationContainer.className = 'fixed top-4 right-4 z-50 space-y-2';
            document.body.appendChild(this.notificationContainer);
        }
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type} max-w-sm bg-white border border-gray-200 rounded-lg shadow-lg p-4 transform transition-all duration-300 translate-x-full`;
        
        const iconClass = this.getNotificationIcon(type);
        const bgColor = this.getNotificationBgColor(type);
        
        notification.innerHTML = `
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i class="${iconClass} text-lg ${bgColor}"></i>
                </div>
                <div class="ml-3 w-0 flex-1">
                    <p class="text-sm font-medium text-gray-900">${message}</p>
                </div>
                <div class="ml-4 flex-shrink-0 flex">
                    <button class="close-notification bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none">
                        <i class="mdi mdi-close text-sm"></i>
                    </button>
                </div>
            </div>
        `;

        this.notificationContainer.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Setup close button
        const closeBtn = notification.querySelector('.close-notification');
        closeBtn.addEventListener('click', () => {
            this.removeNotification(notification);
        });

        // Auto remove after duration
        if (duration > 0) {
            setTimeout(() => {
                this.removeNotification(notification);
            }, duration);
        }

        return notification;
    }

    /**
     * Remove notification
     */
    removeNotification(notification) {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }

    /**
     * Get notification icon based on type
     */
    getNotificationIcon(type) {
        const icons = {
            success: 'mdi mdi-check-circle',
            error: 'mdi mdi-alert-circle',
            warning: 'mdi mdi-alert',
            info: 'mdi mdi-information'
        };
        return icons[type] || icons.info;
    }

    /**
     * Get notification background color based on type
     */
    getNotificationBgColor(type) {
        const colors = {
            success: 'text-green-600',
            error: 'text-red-600',
            warning: 'text-yellow-600',
            info: 'text-blue-600'
        };
        return colors[type] || colors.info;
    }

    /**
     * Show success message
     */
    showSuccess(message, duration = 3000) {
        return this.showNotification(message, 'success', duration);
    }

    /**
     * Show error message
     */
    showError(message, duration = 5000) {
        return this.showNotification(message, 'error', duration);
    }

    /**
     * Show warning message
     */
    showWarning(message, duration = 4000) {
        return this.showNotification(message, 'warning', duration);
    }

    /**
     * Show info message
     */
    showInfo(message, duration = 3000) {
        return this.showNotification(message, 'info', duration);
    }

    /**
     * Setup offline indicators
     */
    setupOfflineIndicators() {
        // Add offline banner
        this.createOfflineBanner();
        
        // Listen for offline events
        window.addEventListener('offline', () => {
            this.showOfflineBanner();
            this.showWarning('You are now offline. Orders will be saved locally and synced when connection is restored.');
        });

        window.addEventListener('online', () => {
            this.hideOfflineBanner();
            this.showSuccess('Connection restored! Syncing data...');
        });
    }

    /**
     * Create offline banner
     */
    createOfflineBanner() {
        const banner = document.createElement('div');
        banner.id = 'offlineBanner';
        banner.className = 'fixed top-0 left-0 right-0 bg-yellow-500 text-white text-center py-2 px-4 z-40 transform -translate-y-full transition-transform duration-300';
        banner.innerHTML = `
            <div class="flex items-center justify-center gap-2">
                <i class="mdi mdi-wifi-off"></i>
                <span>Working offline - Orders will be synced when connection is restored</span>
            </div>
        `;
        document.body.appendChild(banner);
    }

    /**
     * Show offline banner
     */
    showOfflineBanner() {
        const banner = document.getElementById('offlineBanner');
        if (banner) {
            banner.classList.remove('-translate-y-full');
        }
    }

    /**
     * Hide offline banner
     */
    hideOfflineBanner() {
        const banner = document.getElementById('offlineBanner');
        if (banner) {
            banner.classList.add('-translate-y-full');
        }
    }

    /**
     * Setup sync indicators
     */
    setupSyncIndicators() {
        // Listen for sync events
        window.addEventListener('pos-sync-status', (event) => {
            const { type, message } = event.detail;
            
            if (type === 'success') {
                this.showSyncSuccess(message);
            } else {
                this.showSyncError(message);
            }
        });

        // Create sync status indicator
        this.createSyncStatusIndicator();
    }

    /**
     * Create sync status indicator
     */
    createSyncStatusIndicator() {
        const indicator = document.createElement('div');
        indicator.id = 'syncStatusIndicator';
        indicator.className = 'fixed bottom-4 left-4 bg-white border border-gray-200 rounded-lg shadow-lg p-3 z-40 hidden';
        indicator.innerHTML = `
            <div class="flex items-center gap-2">
                <div class="sync-spinner animate-spin">
                    <i class="mdi mdi-sync text-blue-600"></i>
                </div>
                <span class="text-sm text-gray-700">Syncing data...</span>
            </div>
        `;
        document.body.appendChild(indicator);
    }

    /**
     * Show sync in progress
     */
    showSyncInProgress() {
        const indicator = document.getElementById('syncStatusIndicator');
        if (indicator) {
            indicator.classList.remove('hidden');
        }
    }

    /**
     * Hide sync indicator
     */
    hideSyncIndicator() {
        const indicator = document.getElementById('syncStatusIndicator');
        if (indicator) {
            indicator.classList.add('hidden');
        }
    }

    /**
     * Show sync success
     */
    showSyncSuccess(message) {
        this.hideSyncIndicator();
        // Don't show notification for every sync success to avoid spam
        // this.showSuccess(message, 2000);
    }

    /**
     * Show sync error
     */
    showSyncError(message) {
        this.hideSyncIndicator();
        this.showError(message);
    }

    /**
     * Update order display
     */
    updateOrderDisplay() {
        // This method will be called by POS Core to update the order display
        // Implementation depends on the existing order display structure
        const orderContainer = document.getElementById('orderItems');
        if (orderContainer && this.posCore.currentOrder) {
            this.renderOrderItems();
            this.updateOrderTotals();
        }
    }

    /**
     * Render order items
     */
    renderOrderItems() {
        const orderContainer = document.getElementById('orderItems');
        if (!orderContainer) return;

        const items = this.posCore.currentOrder.items;
        
        if (items.length === 0) {
            orderContainer.innerHTML = '<p class="text-gray-500 text-center py-4">No items in order</p>';
            return;
        }

        let itemsHtml = '';
        items.forEach((item, index) => {
            const addonsHtml = item.addons && item.addons.length > 0 
                ? item.addons.map(addon => `<div class="addon-item">+ ${addon.name} (+$${addon.price})</div>`).join('')
                : '';

            itemsHtml += `
                <div class="order-item" data-index="${index}">
                    <div class="flex justify-between items-start mb-2">
                        <div class="flex-1">
                            <h4 class="font-medium">${item.menu_item_name}</h4>
                            ${item.variant_name ? `<small class="text-gray-600">Variant: ${item.variant_name}</small>` : ''}
                            ${item.notes ? `<small class="text-gray-600 block">Notes: ${item.notes}</small>` : ''}
                        </div>
                        <button class="remove-item text-red-600 hover:text-red-800 ml-2" data-index="${index}">
                            <i class="mdi mdi-delete"></i>
                        </button>
                    </div>
                    ${addonsHtml}
                    <div class="flex justify-between items-center mt-2">
                        <div class="flex items-center gap-2">
                            <button class="decrease-qty bg-gray-200 hover:bg-gray-300 w-8 h-8 rounded flex items-center justify-center" data-index="${index}">-</button>
                            <span class="w-8 text-center">${item.quantity}</span>
                            <button class="increase-qty bg-gray-200 hover:bg-gray-300 w-8 h-8 rounded flex items-center justify-center" data-index="${index}">+</button>
                        </div>
                        <div class="font-semibold">$${item.total_price.toFixed(2)}</div>
                    </div>
                </div>
            `;
        });

        orderContainer.innerHTML = itemsHtml;
    }

    /**
     * Update order totals display
     */
    updateOrderTotals() {
        const order = this.posCore.currentOrder;
        
        // Update subtotal
        const subtotalElement = document.getElementById('orderSubtotal');
        if (subtotalElement) {
            subtotalElement.textContent = `$${order.subtotal.toFixed(2)}`;
        }

        // Update tax
        const taxElement = document.getElementById('orderTax');
        if (taxElement) {
            taxElement.textContent = `$${order.tax_amount.toFixed(2)}`;
        }

        // Update discount
        const discountElement = document.getElementById('orderDiscount');
        if (discountElement) {
            discountElement.textContent = `$${order.discount_amount.toFixed(2)}`;
        }

        // Update total
        const totalElement = document.getElementById('orderTotal');
        if (totalElement) {
            totalElement.textContent = `$${order.total_amount.toFixed(2)}`;
        }
    }

    /**
     * Show loading overlay
     */
    showLoading(message = 'Processing...') {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            const messageElement = overlay.querySelector('p');
            if (messageElement) {
                messageElement.textContent = message;
            }
            overlay.style.display = 'flex';
        }
    }

    /**
     * Hide loading overlay
     */
    hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.style.display = 'none';
        }
    }
}
