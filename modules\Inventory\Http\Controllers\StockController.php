<?php

namespace Modules\Inventory\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\BranchInventory;
use App\Models\InventoryMovement;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class StockController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Get stock data for DataTable using Yajra DataTables
     */
    public function datatable(Request $request)
    {
        $user = Auth::user();
        
        if (!$user->branch_id) {
            return response()->json([
                'error' => 'User is not assigned to any branch'
            ], 400);
        }

        $query = BranchInventory::with(['product.unit', 'branch'])
            ->where('branch_id', $user->branch_id);

        // Apply filters
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->whereHas('product', function ($q) use ($searchTerm) {
                $q->where('name', 'like', "%{$searchTerm}%")
                  ->orWhere('code', 'like', "%{$searchTerm}%");
            });
        }

        if ($request->filled('stock_status')) {
            switch ($request->stock_status) {
                case 'low':
                    $query->whereColumn('current_stock', '<=', 'minimum_level');
                    break;
                case 'out':
                    $query->where('current_stock', '<=', 0);
                    break;
                case 'normal':
                    $query->whereColumn('current_stock', '>', 'minimum_level');
                    break;
            }
        }

        return \Yajra\DataTables\Facades\DataTables::of($query)
            ->addIndexColumn()
            ->addColumn('product_name', function ($row) {
                return $row->product->name ?? 'N/A';
            })
            ->addColumn('product_code', function ($row) {
                return $row->product->code ?? 'N/A';
            })
            ->addColumn('unit', function ($row) {
                return $row->product->unit->name ?? 'N/A';
            })
            ->addColumn('stock_status', function ($row) {
                $current = $row->current_stock;
                $minimum = $row->minimum_level;
                
                if ($current <= 0) {
                    $status = 'نفد من المخزون';
                    $class = 'badge-danger';
                } elseif ($current <= $minimum) {
                    $status = 'مخزون منخفض';
                    $class = 'badge-warning';
                } else {
                    $status = 'مخزون طبيعي';
                    $class = 'badge-success';
                }
                
                return "<span class='badge {$class}'>{$status}</span>";
            })
            ->addColumn('stock_levels', function ($row) {
                return "
                    <div class='stock-levels'>
                        <div><strong>الحالي:</strong> {$row->current_stock}</div>
                        <div><strong>الحد الأدنى:</strong> {$row->minimum_level}</div>
                        <div><strong>الحد الأقصى:</strong> {$row->maximum_level}</div>
                        <div><strong>المتاح:</strong> {$row->available_stock}</div>
                    </div>
                ";
            })
            ->addColumn('last_movement', function ($row) {
                return $row->last_movement_at ? $row->last_movement_at->format('Y-m-d H:i') : 'لا يوجد';
            })
            ->addColumn('actions', function ($row) {
                return "
                    <div class='btn-group' role='group'>
                        <button type='button' class='btn btn-sm btn-success' onclick='adjustStock({$row->id})' title='تعديل المخزون'>
                            <i class='fas fa-edit'></i>
                        </button>
                        <button type='button' class='btn btn-sm btn-primary' onclick='viewMovements({$row->id})' title='عرض الحركات'>
                            <i class='fas fa-history'></i>
                        </button>
                        <button type='button' class='btn btn-sm btn-info' onclick='stockCount({$row->id})' title='جرد'>
                            <i class='fas fa-clipboard-check'></i>
                        </button>
                    </div>
                ";
            })
            ->rawColumns(['stock_status', 'stock_levels', 'actions'])
            ->make(true);
    }

    /**
     * Get stock movements data for DataTable
     */
    public function movementsDatatable(Request $request)
    {
        $user = Auth::user();
        
        if (!$user->branch_id) {
            return response()->json([
                'error' => 'User is not assigned to any branch'
            ], 400);
        }

        $query = InventoryMovement::with(['branchInventory.product', 'user'])
            ->whereHas('branchInventory', function ($q) use ($user) {
                $q->where('branch_id', $user->branch_id);
            });

        // Apply filters
        if ($request->filled('item_id')) {
            $query->where('branch_inventory_id', $request->item_id);
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('performed_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('performed_at', '<=', $request->date_to);
        }

        return \Yajra\DataTables\Facades\DataTables::of($query)
            ->addIndexColumn()
            ->addColumn('product_name', function ($row) {
                return $row->branchInventory->product->name ?? 'N/A';
            })
            ->addColumn('type_badge', function ($row) {
                $typeMap = [
                    'in' => ['إدخال', 'badge-success'],
                    'out' => ['إخراج', 'badge-danger'],
                    'adjustment' => ['تعديل', 'badge-warning'],
                    'transfer' => ['نقل', 'badge-info'],
                ];
                
                $type = $typeMap[$row->type] ?? ['غير معروف', 'badge-dark'];
                return "<span class='badge {$type[1]}'>{$type[0]}</span>";
            })
            ->addColumn('quantity_display', function ($row) {
                $sign = $row->type === 'out' ? '-' : '+';
                return "{$sign}{$row->quantity}";
            })
            ->addColumn('total_cost', function ($row) {
                return number_format($row->total_cost, 2) . ' ر.س';
            })
            ->addColumn('performed_at', function ($row) {
                return $row->performed_at ? $row->performed_at->format('Y-m-d H:i') : 'N/A';
            })
            ->addColumn('user_name', function ($row) {
                return $row->user->name ?? 'N/A';
            })
            ->rawColumns(['type_badge'])
            ->make(true);
    }

    /**
     * Update stock for an item
     */
    public function updateStock(Request $request, $id)
    {
        $user = Auth::user();
        
        $request->validate([
            'quantity' => 'required|numeric',
            'type' => 'required|in:in,out,adjustment',
            'reason' => 'required|string|max:255',
            'notes' => 'nullable|string|max:500',
            'unit_cost' => 'nullable|numeric|min:0',
        ]);

        $item = BranchInventory::where('branch_id', $user->branch_id)
            ->findOrFail($id);

        // Update stock based on type
        $oldStock = $item->current_stock;
        
        switch ($request->type) {
            case 'in':
                $item->current_stock += $request->quantity;
                break;
            case 'out':
                $item->current_stock -= $request->quantity;
                break;
            case 'adjustment':
                $item->current_stock = $request->quantity;
                break;
        }

        // Update available stock
        $item->available_stock = $item->current_stock - $item->reserved_stock;
        
        // Update unit cost if provided
        if ($request->filled('unit_cost')) {
            $item->unit_cost = $request->unit_cost;
        }

        $item->last_movement_at = now();
        $item->save();

        // Create movement record
        $item->movements()->create([
            'type' => $request->type,
            'quantity' => $request->quantity,
            'unit_cost' => $request->unit_cost ?? $item->unit_cost,
            'total_cost' => $request->quantity * ($request->unit_cost ?? $item->unit_cost),
            'reason' => $request->reason,
            'notes' => $request->notes,
            'user_id' => $user->id,
            'performed_at' => now(),
            'reference_type' => 'manual_adjustment',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث المخزون بنجاح',
            'data' => $item->fresh(['product.unit', 'branch'])
        ]);
    }

    /**
     * Get stock movements timeline data
     */
    public function movementsTimeline(Request $request)
    {
        $user = Auth::user();
        
        if (!$user->branch_id) {
            return response()->json([
                'error' => 'User is not assigned to any branch'
            ], 400);
        }

        $query = InventoryMovement::with(['branchInventory.product.unit', 'user'])
            ->whereHas('branchInventory', function ($q) use ($user) {
                $q->where('branch_id', $user->branch_id);
            })
            ->orderBy('performed_at', 'desc');

        // Apply filters
        if ($request->filled('item_id')) {
            $query->where('branch_inventory_id', $request->item_id);
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('performed_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('performed_at', '<=', $request->date_to);
        }

        $movements = $query->limit(100)->get();

        return response()->json($movements);
    }

    /**
     * Export stock movements
     */
    public function exportMovements(Request $request)
    {
        $user = Auth::user();
        
        if (!$user->branch_id) {
            return response()->json([
                'error' => 'User is not assigned to any branch'
            ], 400);
        }

        $query = InventoryMovement::with(['branchInventory.product.unit', 'user'])
            ->whereHas('branchInventory', function ($q) use ($user) {
                $q->where('branch_id', $user->branch_id);
            })
            ->orderBy('performed_at', 'desc');

        // Apply filters
        if ($request->filled('item_id')) {
            $query->where('branch_inventory_id', $request->item_id);
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('performed_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('performed_at', '<=', $request->date_to);
        }

        $movements = $query->get();

        // Create CSV content
        $csvContent = "التاريخ,المادة,نوع الحركة,الكمية,التكلفة الإجمالية,السبب,المستخدم\n";
        
        foreach ($movements as $movement) {
            $typeMap = [
                'in' => 'إدخال',
                'out' => 'إخراج',
                'adjustment' => 'تعديل',
                'transfer' => 'نقل',
            ];
            
            $type = $typeMap[$movement->type] ?? $movement->type;
            $sign = $movement->type === 'out' ? '-' : '+';
            
            $csvContent .= sprintf(
                "%s,%s,%s,%s%s,%s,%s,%s\n",
                $movement->performed_at->format('Y-m-d H:i'),
                $movement->branchInventory->product->name ?? 'N/A',
                $type,
                $sign,
                $movement->quantity,
                number_format($movement->total_cost, 2),
                $movement->reason ?? '',
                $movement->user->name ?? 'N/A'
            );
        }

        return response($csvContent)
            ->header('Content-Type', 'text/csv; charset=UTF-8')
            ->header('Content-Disposition', 'attachment; filename="stock_movements_' . date('Y-m-d') . '.csv"');
    }

    /**
     * Generate stock movements report
     */
    public function movementsReport(Request $request)
    {
        $user = Auth::user();
        
        if (!$user->branch_id) {
            return response()->json([
                'error' => 'User is not assigned to any branch'
            ], 400);
        }

        $query = InventoryMovement::with(['branchInventory.product.unit', 'user'])
            ->whereHas('branchInventory', function ($q) use ($user) {
                $q->where('branch_id', $user->branch_id);
            });

        // Apply filters
        if ($request->filled('item_id')) {
            $query->where('branch_inventory_id', $request->item_id);
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('performed_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('performed_at', '<=', $request->date_to);
        }

        $movements = $query->get();

        // Generate PDF report (simplified version)
        $html = view('Inventory::reports.movements', compact('movements'))->render();
        
        // For now, return HTML content
        // In a real implementation, you would use a PDF library like DomPDF
        return response($html)
            ->header('Content-Type', 'text/html; charset=UTF-8');
    }

    /**
     * Export stock data to CSV
     */
    public function export(Request $request)
    {
        $user = Auth::user();
        
        if (!$user->branch_id) {
            return response()->json([
                'error' => 'User is not assigned to any branch'
            ], 400);
        }

        $query = BranchInventory::with(['product.unit', 'branch'])
            ->where('branch_id', $user->branch_id);

        // Apply filters
        if ($request->filled('stock_status')) {
            switch ($request->stock_status) {
                case 'low':
                    $query->whereColumn('current_stock', '<=', 'minimum_level');
                    break;
                case 'out':
                    $query->where('current_stock', '<=', 0);
                    break;
                case 'normal':
                    $query->whereColumn('current_stock', '>', 'minimum_level');
                    break;
            }
        }

        $items = $query->get();

        // Create CSV content
        $csvContent = "المادة,الرمز,المخزون الحالي,الحد الأدنى,الحد الأقصى,المتاح,الوحدة,الحالة,آخر حركة\n";
        
        foreach ($items as $item) {
            $current = $item->current_stock;
            $minimum = $item->minimum_level;
            
            if ($current <= 0) {
                $status = 'نفد من المخزون';
            } elseif ($current <= $minimum) {
                $status = 'مخزون منخفض';
            } else {
                $status = 'مخزون طبيعي';
            }
            
            $csvContent .= sprintf(
                "%s,%s,%s,%s,%s,%s,%s,%s,%s\n",
                $item->product->name ?? 'N/A',
                $item->product->code ?? 'N/A',
                $item->current_stock,
                $item->minimum_level,
                $item->maximum_level,
                $item->available_stock,
                $item->product->unit->name ?? 'N/A',
                $status,
                $item->last_movement_at ? $item->last_movement_at->format('Y-m-d H:i') : 'لا يوجد'
            );
        }

        return response($csvContent)
            ->header('Content-Type', 'text/csv; charset=UTF-8')
            ->header('Content-Disposition', 'attachment; filename="stock_report_' . date('Y-m-d') . '.csv"');
    }

    /**
     * Generate stock report
     */
    public function report(Request $request)
    {
        $user = Auth::user();
        
        if (!$user->branch_id) {
            return response()->json([
                'error' => 'User is not assigned to any branch'
            ], 400);
        }

        $query = BranchInventory::with(['product.unit', 'branch'])
            ->where('branch_id', $user->branch_id);

        // Apply filters
        if ($request->filled('stock_status')) {
            switch ($request->stock_status) {
                case 'low':
                    $query->whereColumn('current_stock', '<=', 'minimum_level');
                    break;
                case 'out':
                    $query->where('current_stock', '<=', 0);
                    break;
                case 'normal':
                    $query->whereColumn('current_stock', '>', 'minimum_level');
                    break;
            }
        }

        $items = $query->get();

        // Calculate summary statistics
        $totalItems = $items->count();
        $lowStockItems = $items->where('current_stock', '<=', $items->pluck('minimum_level'))->count();
        $outOfStockItems = $items->where('current_stock', '<=', 0)->count();
        $totalValue = $items->sum(function ($item) {
            return $item->current_stock * $item->unit_cost;
        });

        $reportData = [
            'summary' => [
                'total_items' => $totalItems,
                'low_stock_items' => $lowStockItems,
                'out_of_stock_items' => $outOfStockItems,
                'total_value' => $totalValue,
                'generated_at' => now()->format('Y-m-d H:i:s'),
                'branch_name' => $user->branch->name ?? 'N/A'
            ],
            'items' => $items
        ];

        // Generate PDF report (simplified version)
        $html = view('Inventory::reports.stock', $reportData)->render();
        
        // For now, return HTML content
        // In a real implementation, you would use a PDF library like DomPDF
        return response($html)
            ->header('Content-Type', 'text/html; charset=UTF-8');
    }
}
