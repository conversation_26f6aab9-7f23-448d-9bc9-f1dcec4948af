{"info": {"name": "Restaurant POS - Customer Mo<PERSON><PERSON>", "_postman_id": "d6e0eae1-customer-module-demo", "description": "Postman collection for Customer Module of Restaurant POS System", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Customer CRUD", "item": [{"name": "Get Customers by Branch", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/customers?branch_id=1", "host": ["{{base_url}}"], "path": ["api", "customers"], "query": [{"key": "branch_id", "value": "1"}]}}}, {"name": "Create Customer", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"branch_id\": 1,\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"+1234567890\",\n  \"date_of_birth\": \"1990-01-01\",\n  \"gender\": \"male\",\n  \"address\": \"123 Main St\",\n  \"city\": \"New York\",\n  \"postal_code\": \"10001\",\n  \"preferences\": [\"vegetarian\"],\n  \"notes\": \"Regular customer\",\n  \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/customers", "host": ["{{base_url}}"], "path": ["api", "customers"]}}}, {"name": "Get Customer by ID", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/customers/1", "host": ["{{base_url}}"], "path": ["api", "customers", "1"]}}}, {"name": "Update Customer", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON>\",\n  \"phone\": \"+1987654321\"\n}"}, "url": {"raw": "{{base_url}}/api/customers/1", "host": ["{{base_url}}"], "path": ["api", "customers", "1"]}}}, {"name": "Delete Customer (Soft Delete)", "request": {"method": "DELETE", "url": {"raw": "{{base_url}}/api/customers/1", "host": ["{{base_url}}"], "path": ["api", "customers", "1"]}}}]}, {"name": "Customer Search", "item": [{"name": "Search by Phone, Email or Name", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/customers/search/customers?branch_id=1&search=john", "host": ["{{base_url}}"], "path": ["api", "customers", "search", "customers"], "query": [{"key": "branch_id", "value": "1"}, {"key": "search", "value": "john"}]}}}]}, {"name": "Customer Status Management", "item": [{"name": "Activate Customer", "request": {"method": "PATCH", "url": {"raw": "{{base_url}}/api/customers/1/activate", "host": ["{{base_url}}"], "path": ["api", "customers", "1", "activate"]}}}, {"name": "Deactivate Customer", "request": {"method": "PATCH", "url": {"raw": "{{base_url}}/api/customers/1/deactivate", "host": ["{{base_url}}"], "path": ["api", "customers", "1", "deactivate"]}}}, {"name": "Update Visit Timestamp", "request": {"method": "PATCH", "url": {"raw": "{{base_url}}/api/customers/1/update-visit", "host": ["{{base_url}}"], "path": ["api", "customers", "1", "update-visit"]}}}]}, {"name": "Loyalty Points", "item": [{"name": "Add Loyalty Points", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"points\": 100.50,\n  \"description\": \"Purchase reward\"\n}"}, "url": {"raw": "{{base_url}}/api/customers/1/loyalty/add", "host": ["{{base_url}}"], "path": ["api", "customers", "1", "loyalty", "add"]}}}, {"name": "Redeem Loyalty Points", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"points\": 50.25,\n  \"description\": \"Discount applied\"\n}"}, "url": {"raw": "{{base_url}}/api/customers/1/loyalty/redeem", "host": ["{{base_url}}"], "path": ["api", "customers", "1", "loyalty", "redeem"]}}}, {"name": "Get Loyalty History", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/customers/1/loyalty/history", "host": ["{{base_url}}"], "path": ["api", "customers", "1", "loyalty", "history"]}}}]}, {"name": "Miscellaneous", "item": [{"name": "Get All Active Customers (by Branch)", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/customers/active/list?branch_id=1", "host": ["{{base_url}}"], "path": ["api", "customers", "active", "list"], "query": [{"key": "branch_id", "value": "1"}]}}}]}], "variable": [{"key": "base_url", "value": "http://localhost"}]}