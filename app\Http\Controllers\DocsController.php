<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Route;

class DocsController extends Controller
{
    /**
     * Display the main documentation homepage
     */
    public function index()
    {
        // Define modules with their information for the documentation dashboard
        $modules = [
            [
                'name' => 'Authentication',
                'description' => 'User authentication and authorization',
                'icon' => 'fas fa-shield-alt',
                'color' => 'blue',
                'endpoints' => 12,
                'status' => 'complete'
            ],
            [
                'name' => 'Menu',
                'description' => 'Menu management and categories',
                'icon' => 'fas fa-utensils',
                'color' => 'purple',
                'endpoints' => 8,
                'status' => 'in-progress'
            ],
            [
                'name' => 'Orders',
                'description' => 'Order processing and management',
                'icon' => 'fas fa-shopping-cart',
                'color' => 'green',
                'endpoints' => 15,
                'status' => 'in-progress'
            ],
            [
                'name' => 'Customers',
                'description' => 'Customer management and profiles',
                'icon' => 'fas fa-users',
                'color' => 'blue',
                'endpoints' => 10,
                'status' => 'planned'
            ],
            [
                'name' => 'Staff',
                'description' => 'Staff management and scheduling',
                'icon' => 'fas fa-user-tie',
                'color' => 'indigo',
                'endpoints' => 12,
                'status' => 'planned'
            ],
            [
                'name' => 'Inventory',
                'description' => 'Stock and inventory management',
                'icon' => 'fas fa-boxes',
                'color' => 'yellow',
                'endpoints' => 14,
                'status' => 'planned'
            ],
            [
                'name' => 'Kitchen',
                'description' => 'Kitchen operations and workflow',
                'icon' => 'fas fa-fire',
                'color' => 'orange',
                'endpoints' => 9,
                'status' => 'planned'
            ],
            [
                'name' => 'Payment',
                'description' => 'Payment processing and transactions',
                'icon' => 'fas fa-credit-card',
                'color' => 'emerald',
                'endpoints' => 11,
                'status' => 'planned'
            ],
            [
                'name' => 'Delivery',
                'description' => 'Delivery management and tracking',
                'icon' => 'fas fa-truck',
                'color' => 'teal',
                'endpoints' => 7,
                'status' => 'planned'
            ],
            [
                'name' => 'Reservation',
                'description' => 'Table reservations and booking',
                'icon' => 'fas fa-calendar-check',
                'color' => 'pink',
                'endpoints' => 8,
                'status' => 'planned'
            ],
            [
                'name' => 'Reports',
                'description' => 'Analytics and reporting',
                'icon' => 'fas fa-chart-bar',
                'color' => 'cyan',
                'endpoints' => 6,
                'status' => 'planned'
            ],
            [
                'name' => 'Notifications',
                'description' => 'Real-time notifications and alerts',
                'icon' => 'fas fa-bell',
                'color' => 'amber',
                'endpoints' => 5,
                'status' => 'planned'
            ]
        ];

        // Additional data for the dashboard
        $stats = [
            'total_modules' => count($modules),
            'total_endpoints' => array_sum(array_column($modules, 'endpoints')),
            'completed_modules' => count(array_filter($modules, fn($m) => $m['status'] === 'complete')),
            'in_progress_modules' => count(array_filter($modules, fn($m) => $m['status'] === 'in-progress'))
        ];

        $recentUpdates = [
            [
                'module' => 'Authentication',
                'action' => 'Documentation completed',
                'date' => now()->format('M d, Y')
            ],
            [
                'module' => 'Menu',
                'action' => 'API endpoints added',
                'date' => now()->subDays(1)->format('M d, Y')
            ],
            [
                'module' => 'Orders',
                'action' => 'Initial setup completed',
                'date' => now()->subDays(2)->format('M d, Y')
            ]
        ];

        return view('docs.index', compact('modules', 'stats', 'recentUpdates'));
    }

    /**
     * Display documentation for a specific module.
     *
     * @param string $module
     * @return \Illuminate\View\View|\Illuminate\Http\Response
     */
    public function showModule(string $module)
    {
        $viewPath = 'docs.modules.' . $module;

        if (view()->exists($viewPath)) {
            return view($viewPath);
        } else {
            abort(404, 'Module documentation not found.');
        }
    }
}