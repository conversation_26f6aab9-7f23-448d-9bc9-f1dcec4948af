<?php

namespace Modules\Transaction\Services;

use Modules\Transaction\Models\PaymentMethod;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class PaymentMethodService
{
    /**
     * Get all payment methods for the current tenant.
     */
    public function getAllPaymentMethods(array $filters = []): Collection
    {
        $user = Auth::user();
        $query = PaymentMethod::query();

        // Filter by tenant (include global methods with null tenant_id)
        if ($user && $user->tenant_id) {
            $query->where(function ($q) use ($user) {
                $q->where('tenant_id', $user->tenant_id)
                  ->orWhereNull('tenant_id');
            });
        } else {
            $query->whereNull('tenant_id');
        }

        // Apply filters
        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        if (isset($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        return $query->ordered()->get();
    }

    /**
     * Get active payment methods only.
     */
    public function getActivePaymentMethods(): Collection
    {
        return $this->getAllPaymentMethods(['is_active' => true]);
    }

    /**
     * Get payment method by ID.
     */
    public function getPaymentMethodById(int $id): ?PaymentMethod
    {
        $user = Auth::user();
        $query = PaymentMethod::query();

        if ($user && $user->tenant_id) {
            $query->where(function ($q) use ($user) {
                $q->where('tenant_id', $user->tenant_id)
                  ->orWhereNull('tenant_id');
            });
        } else {
            $query->whereNull('tenant_id');
        }

        return $query->find($id);
    }

    /**
     * Create a new payment method.
     */
    public function createPaymentMethod(array $data): PaymentMethod
    {
        $user = Auth::user();
        
        DB::beginTransaction();
        try {
            $paymentMethodData = [
                'tenant_id' => $user ? $user->tenant_id : null,
                'name' => $data['name'],
                'code' => $data['code'],
                'description' => $data['description'] ?? null,
                'is_active' => $data['is_active'] ?? true,
                'configuration' => $data['configuration'] ?? null,
                'sort_order' => $data['sort_order'] ?? 0,
            ];

            $paymentMethod = PaymentMethod::create($paymentMethodData);

            DB::commit();
            return $paymentMethod;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Update an existing payment method.
     */
    public function updatePaymentMethod(PaymentMethod $paymentMethod, array $data): PaymentMethod
    {
        DB::beginTransaction();
        try {
            $updateData = [
                'name' => $data['name'] ?? $paymentMethod->name,
                'description' => $data['description'] ?? $paymentMethod->description,
                'is_active' => $data['is_active'] ?? $paymentMethod->is_active,
                'configuration' => $data['configuration'] ?? $paymentMethod->configuration,
                'sort_order' => $data['sort_order'] ?? $paymentMethod->sort_order,
            ];

            // Don't allow updating code for existing payment methods
            if (isset($data['code']) && $paymentMethod->code !== $data['code']) {
                throw new \Exception('Payment method code cannot be changed');
            }

            $paymentMethod->update($updateData);

            DB::commit();
            return $paymentMethod->fresh();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Delete a payment method.
     */
    public function deletePaymentMethod(PaymentMethod $paymentMethod): bool
    {
        DB::beginTransaction();
        try {
            // Check if payment method is being used
            if ($paymentMethod->payments()->exists()) {
                throw new \Exception('Cannot delete payment method that has associated payments');
            }

            $paymentMethod->delete();

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Toggle payment method status.
     */
    public function toggleStatus(PaymentMethod $paymentMethod): PaymentMethod
    {
        DB::beginTransaction();
        try {
            $paymentMethod->update([
                'is_active' => !$paymentMethod->is_active
            ]);

            DB::commit();
            return $paymentMethod->fresh();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Update sort order for payment methods.
     */
    public function updateSortOrder(array $sortData): bool
    {
        DB::beginTransaction();
        try {
            foreach ($sortData as $item) {
                PaymentMethod::where('id', $item['id'])
                    ->update(['sort_order' => $item['sort_order']]);
            }

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get payment method statistics.
     */
    public function getPaymentMethodStatistics(): array
    {
        $user = Auth::user();
        $query = PaymentMethod::query();

        if ($user && $user->tenant_id) {
            $query->where(function ($q) use ($user) {
                $q->where('tenant_id', $user->tenant_id)
                  ->orWhereNull('tenant_id');
            });
        } else {
            $query->whereNull('tenant_id');
        }

        $total = $query->count();
        $active = $query->where('is_active', true)->count();
        $inactive = $total - $active;

        return [
            'total' => $total,
            'active' => $active,
            'inactive' => $inactive,
        ];
    }

    /**
     * Validate payment method data.
     */
    public function validatePaymentMethodData(array $data, ?PaymentMethod $paymentMethod = null): array
    {
        $errors = [];

        // Required fields
        if (empty($data['name'])) {
            $errors[] = 'Payment method name is required';
        }

        if (empty($data['code'])) {
            $errors[] = 'Payment method code is required';
        }

        // Check for duplicate code within tenant
        if (!empty($data['code'])) {
            $user = Auth::user();
            $query = PaymentMethod::where('code', $data['code']);
            
            if ($user && $user->tenant_id) {
                $query->where('tenant_id', $user->tenant_id);
            } else {
                $query->whereNull('tenant_id');
            }

            if ($paymentMethod) {
                $query->where('id', '!=', $paymentMethod->id);
            }

            if ($query->exists()) {
                $errors[] = 'Payment method code already exists';
            }
        }

        // Validate code format
        if (!empty($data['code']) && !preg_match('/^[a-z0-9_]+$/', $data['code'])) {
            $errors[] = 'Payment method code must contain only lowercase letters, numbers, and underscores';
        }

        return $errors;
    }
}