<?php

use Illuminate\Support\Facades\Route;
use Modules\Menu\Http\Controllers\Api\MenuController;
use Modules\Menu\Http\Controllers\Api\CategoryController;
use Modules\Menu\Http\Controllers\Api\MenuItemController;
use Modules\Menu\Http\Controllers\Api\AddonController;
use Mo<PERSON>les\Menu\Http\Controllers\Api\VariantController;
use Modules\Menu\Http\Controllers\Api\BannerController;
use Modules\Menu\Http\Controllers\Api\EventController;
use Modules\Menu\Http\Controllers\Api\OfferController;
use Modules\Menu\Http\Controllers\Api\Public\PublicMenuController;

/*
|--------------------------------------------------------------------------
| Menu Module API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for the Menu module.
| All routes are prefixed with 'api/menu' and require authentication.
|
*/

Route::prefix('api/menu')->middleware(['auth:sanctum'])->name('api.menu.')->group(function () {
    
    // Menu routes
    Route::apiResource('menus', MenuController::class);
    
    // Category routes
    Route::apiResource('categories', CategoryController::class);
    
    // Menu Item routes
    Route::apiResource('menu-items', MenuItemController::class);
    
    // Addon routes
    Route::apiResource('addons', AddonController::class);
    
    // Variant routes
    Route::apiResource('variants', VariantController::class);
    
    // Banner routes
    Route::apiResource('banners', BannerController::class);
    Route::get('banners/display/{location}', [BannerController::class, 'display'])->name('banners.display');
    Route::get('banners/featured', [BannerController::class, 'featured'])->name('banners.featured');
    
    // Event routes
    Route::apiResource('events', EventController::class);
    Route::get('events/featured', [EventController::class, 'featured'])->name('events.featured');
    Route::get('events/upcoming', [EventController::class, 'upcoming'])->name('events.upcoming');
    Route::post('events/{id}/register', [EventController::class, 'register'])->name('events.register');
    
    // Offer routes
    Route::apiResource('offers', OfferController::class);
    Route::get('offers/featured', [OfferController::class, 'featured'])->name('offers.featured');
    Route::get('offers/active', [OfferController::class, 'active'])->name('offers.active');
    Route::post('offers/validate', [OfferController::class, 'validate'])->name('offers.validate');
    Route::post('offers/apply', [OfferController::class, 'apply'])->name('offers.apply');
    
});

// Public routes are now defined in routes/public.php

// Public routes with restaurant (tenant) name parameter
// Route::prefix('menu/restaurant/{tenantName}')->name('api.menu.restaurant.')->group(function () {
    
//     // Restaurant info
//     Route::get('info', [PublicMenuController::class, 'getRestaurantInfo'])->name('info');
    
//     // Menus
//     Route::get('menus', [PublicMenuController::class, 'getMenus'])->name('menus.index');
//     Route::get('menus/{menuId}', [PublicMenuController::class, 'getMenu'])->name('menus.show');
    
//     // Categories (will be implemented)
//     Route::get('categories', [PublicMenuController::class, 'getCategories'])->name('categories.index');
//     // Route::get('categories/{categoryId}', [PublicMenuController::class, 'getCategory'])->name('categories.show');
    
//     // Menu Items (will be implemented)
//     Route::get('menu-items', [PublicMenuController::class, 'getMenuItems'])->name('menu-items.index');
//     // Route::get('menu-items/{itemId}', [PublicMenuController::class, 'getMenuItem'])->name('menu-items.show');
    
//     // Addons (will be implemented)
//     Route::get('addons', [PublicMenuController::class, 'getAddons'])->name('addons.index');
    
//     // Variants (will be implemented)
//     Route::get('variants', [PublicMenuController::class, 'getVariants'])->name('variants.index');
    
// });