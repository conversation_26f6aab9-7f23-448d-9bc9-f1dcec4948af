<?php

namespace Modules\Delivery\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DeliveryAnalyticsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'overview' => $this->when(isset($this->resource['overview']), function () {
                return [
                    'total_deliveries' => $this->resource['overview']['total_deliveries'] ?? 0,
                    'completed_deliveries' => $this->resource['overview']['completed_deliveries'] ?? 0,
                    'pending_deliveries' => $this->resource['overview']['pending_deliveries'] ?? 0,
                    'failed_deliveries' => $this->resource['overview']['failed_deliveries'] ?? 0,
                    'success_rate' => $this->resource['overview']['success_rate'] ?? 0,
                    'total_revenue' => $this->resource['overview']['total_revenue'] ?? 0,
                    'average_delivery_time' => $this->resource['overview']['average_delivery_time'] ?? 0,
                    'active_personnel' => $this->resource['overview']['active_personnel'] ?? 0,
                ];
            }),

            'performance' => $this->when(isset($this->resource['performance']), function () {
                return [
                    'on_time_deliveries' => $this->resource['performance']['on_time_deliveries'] ?? 0,
                    'delayed_deliveries' => $this->resource['performance']['delayed_deliveries'] ?? 0,
                    'on_time_percentage' => $this->resource['performance']['on_time_percentage'] ?? 0,
                    'average_delay_minutes' => $this->resource['performance']['average_delay_minutes'] ?? 0,
                    'fastest_delivery_time' => $this->resource['performance']['fastest_delivery_time'] ?? 0,
                    'slowest_delivery_time' => $this->resource['performance']['slowest_delivery_time'] ?? 0,
                    'customer_satisfaction' => $this->resource['performance']['customer_satisfaction'] ?? 0,
                    'total_distance_covered' => $this->resource['performance']['total_distance_covered'] ?? 0,
                ];
            }),

            'personnel_metrics' => $this->when(isset($this->resource['personnel_metrics']), function () {
                return [
                    'total_personnel' => $this->resource['personnel_metrics']['total_personnel'] ?? 0,
                    'active_personnel' => $this->resource['personnel_metrics']['active_personnel'] ?? 0,
                    'busy_personnel' => $this->resource['personnel_metrics']['busy_personnel'] ?? 0,
                    'offline_personnel' => $this->resource['personnel_metrics']['offline_personnel'] ?? 0,
                    'average_deliveries_per_personnel' => $this->resource['personnel_metrics']['average_deliveries_per_personnel'] ?? 0,
                    'top_performers' => $this->resource['personnel_metrics']['top_performers'] ?? [],
                    'utilization_rate' => $this->resource['personnel_metrics']['utilization_rate'] ?? 0,
                ];
            }),

            'zone_metrics' => $this->when(isset($this->resource['zone_metrics']), function () {
                return [
                    'total_zones' => $this->resource['zone_metrics']['total_zones'] ?? 0,
                    'active_zones' => $this->resource['zone_metrics']['active_zones'] ?? 0,
                    'busiest_zones' => $this->resource['zone_metrics']['busiest_zones'] ?? [],
                    'zone_performance' => $this->resource['zone_metrics']['zone_performance'] ?? [],
                    'coverage_area' => $this->resource['zone_metrics']['coverage_area'] ?? 0,
                ];
            }),

            'trends' => $this->when(isset($this->resource['trends']), function () {
                return [
                    'daily_trends' => $this->formatTrendData($this->resource['trends']['daily_trends'] ?? []),
                    'hourly_trends' => $this->formatTrendData($this->resource['trends']['hourly_trends'] ?? []),
                    'weekly_trends' => $this->formatTrendData($this->resource['trends']['weekly_trends'] ?? []),
                    'peak_hours' => $this->resource['trends']['peak_hours'] ?? [],
                    'growth_rate' => $this->resource['trends']['growth_rate'] ?? 0,
                ];
            }),

            'real_time' => $this->when(isset($this->resource['real_time']), function () {
                return [
                    'active_deliveries' => $this->resource['real_time']['active_deliveries'] ?? 0,
                    'deliveries_in_transit' => $this->resource['real_time']['deliveries_in_transit'] ?? 0,
                    'pending_assignments' => $this->resource['real_time']['pending_assignments'] ?? 0,
                    'available_personnel' => $this->resource['real_time']['available_personnel'] ?? 0,
                    'current_load' => $this->resource['real_time']['current_load'] ?? 0,
                    'estimated_completion_time' => $this->resource['real_time']['estimated_completion_time'] ?? null,
                ];
            }),

            'financial' => $this->when(isset($this->resource['financial']), function () {
                return [
                    'total_delivery_fees' => $this->resource['financial']['total_delivery_fees'] ?? 0,
                    'total_tips' => $this->resource['financial']['total_tips'] ?? 0,
                    'personnel_earnings' => $this->resource['financial']['personnel_earnings'] ?? 0,
                    'average_order_value' => $this->resource['financial']['average_order_value'] ?? 0,
                    'revenue_per_delivery' => $this->resource['financial']['revenue_per_delivery'] ?? 0,
                    'cost_per_delivery' => $this->resource['financial']['cost_per_delivery'] ?? 0,
                    'profit_margin' => $this->resource['financial']['profit_margin'] ?? 0,
                ];
            }),

            'customer_insights' => $this->when(isset($this->resource['customer_insights']), function () {
                return [
                    'total_customers' => $this->resource['customer_insights']['total_customers'] ?? 0,
                    'repeat_customers' => $this->resource['customer_insights']['repeat_customers'] ?? 0,
                    'customer_retention_rate' => $this->resource['customer_insights']['customer_retention_rate'] ?? 0,
                    'average_rating' => $this->resource['customer_insights']['average_rating'] ?? 0,
                    'total_reviews' => $this->resource['customer_insights']['total_reviews'] ?? 0,
                    'satisfaction_distribution' => $this->resource['customer_insights']['satisfaction_distribution'] ?? [],
                    'complaint_rate' => $this->resource['customer_insights']['complaint_rate'] ?? 0,
                ];
            }),

            'operational' => $this->when(isset($this->resource['operational']), function () {
                return [
                    'fleet_utilization' => $this->resource['operational']['fleet_utilization'] ?? 0,
                    'average_delivery_distance' => $this->resource['operational']['average_delivery_distance'] ?? 0,
                    'fuel_efficiency' => $this->resource['operational']['fuel_efficiency'] ?? 0,
                    'maintenance_alerts' => $this->resource['operational']['maintenance_alerts'] ?? 0,
                    'route_optimization_savings' => $this->resource['operational']['route_optimization_savings'] ?? 0,
                    'delivery_density' => $this->resource['operational']['delivery_density'] ?? 0,
                ];
            }),

            'alerts' => $this->when(isset($this->resource['alerts']), function () {
                return [
                    'overdue_deliveries' => $this->resource['alerts']['overdue_deliveries'] ?? [],
                    'personnel_issues' => $this->resource['alerts']['personnel_issues'] ?? [],
                    'zone_issues' => $this->resource['alerts']['zone_issues'] ?? [],
                    'system_alerts' => $this->resource['alerts']['system_alerts'] ?? [],
                    'performance_warnings' => $this->resource['alerts']['performance_warnings'] ?? [],
                ];
            }),

            'metadata' => [
                'generated_at' => now()->format('Y-m-d H:i:s'),
                'period' => $this->resource['period'] ?? 'today',
                'timezone' => config('app.timezone'),
                'currency' => config('app.currency', 'USD'),
                'data_freshness' => $this->getDataFreshness(),
                'report_version' => '1.0',
            ],
        ];
    }

    /**
     * Format trend data for consistent output
     */
    private function formatTrendData(array $trends): array
    {
        return array_map(function ($trend) {
            return [
                'period' => $trend['period'] ?? '',
                'value' => $trend['value'] ?? 0,
                'change' => $trend['change'] ?? 0,
                'change_percentage' => $trend['change_percentage'] ?? 0,
                'formatted_period' => $this->formatPeriod($trend['period'] ?? ''),
            ];
        }, $trends);
    }

    /**
     * Format period for display
     */
    private function formatPeriod(string $period): string
    {
        if (empty($period)) {
            return '';
        }

        // Try to parse as date
        try {
            $date = \Carbon\Carbon::parse($period);
            
            // Check if it's a date or datetime
            if ($date->format('H:i:s') === '00:00:00') {
                return $date->format('M j, Y');
            } else {
                return $date->format('M j, Y H:i');
            }
        } catch (\Exception $e) {
            // If not a valid date, return as is
            return $period;
        }
    }

    /**
     * Get data freshness indicator
     */
    private function getDataFreshness(): array
    {
        $lastUpdate = $this->resource['last_updated'] ?? now();
        $minutesAgo = now()->diffInMinutes($lastUpdate);

        $status = match (true) {
            $minutesAgo <= 5 => 'fresh',
            $minutesAgo <= 15 => 'recent',
            $minutesAgo <= 60 => 'stale',
            default => 'outdated',
        };

        return [
            'status' => $status,
            'last_updated' => $lastUpdate instanceof \Carbon\Carbon 
                ? $lastUpdate->format('Y-m-d H:i:s') 
                : $lastUpdate,
            'minutes_ago' => $minutesAgo,
            'label' => $this->getFreshnessLabel($status, $minutesAgo),
        ];
    }

    /**
     * Get freshness label
     */
    private function getFreshnessLabel(string $status, int $minutesAgo): string
    {
        return match ($status) {
            'fresh' => 'Real-time data',
            'recent' => "Updated {$minutesAgo} minutes ago",
            'stale' => "Updated {$minutesAgo} minutes ago",
            'outdated' => 'Data may be outdated',
            default => 'Unknown',
        };
    }
}