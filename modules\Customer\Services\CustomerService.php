<?php

namespace Modules\Customer\Services;

use App\Models\Customer;
use App\Helpers\BranchHelper;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class CustomerService
{
    /**
     * Get all customers for the current branch
     */
    public function getAllCustomers(): Collection
    {
        $branchId = BranchHelper::getCurrentBranchId();
        
        return Customer::where('branch_id', $branchId)
            ->where('is_active', true)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get paginated customers for DataTables
     */
    public function getPaginatedCustomers(array $filters = []): LengthAwarePaginator
    {
        $branchId = BranchHelper::getCurrentBranchId();
        
        $query = Customer::where('branch_id', $branchId);

        // Apply filters
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        return $query->orderBy('created_at', 'desc')
            ->paginate($filters['per_page'] ?? 15);
    }

    /**
     * Create a new customer
     */
    public function createCustomer(array $data): Customer
    {
        $data['branch_id'] = BranchHelper::getCurrentBranchId();
        $data['tenant_id'] = Auth::user()->tenant_id;
        
        return Customer::create($data);
    }

    /**
     * Update customer
     */
    public function updateCustomer(Customer $customer, array $data): Customer
    {
        $customer->update($data);
        return $customer->fresh();
    }

    /**
     * Delete customer (soft delete)
     */
    public function deleteCustomer(Customer $customer): bool
    {
        return $customer->delete();
    }

    /**
     * Activate customer
     */
    public function activateCustomer(Customer $customer): Customer
    {
        $customer->update(['is_active' => true]);
        return $customer->fresh();
    }

    /**
     * Deactivate customer
     */
    public function deactivateCustomer(Customer $customer): Customer
    {
        $customer->update(['is_active' => false]);
        return $customer->fresh();
    }

    /**
     * Update last visit
     */
    public function updateLastVisit(Customer $customer): Customer
    {
        $customer->update(['last_visit_at' => now()]);
        return $customer->fresh();
    }

    /**
     * Search customers
     */
    public function searchCustomers(string $query): Collection
    {
        $branchId = BranchHelper::getCurrentBranchId();
        
        return Customer::where('branch_id', $branchId)
            ->where('is_active', true)
            ->where(function ($q) use ($query) {
                $q->where('first_name', 'like', "%{$query}%")
                  ->orWhere('last_name', 'like', "%{$query}%")
                  ->orWhere('email', 'like', "%{$query}%")
                  ->orWhere('phone', 'like', "%{$query}%");
            })
            ->limit(10)
            ->get();
    }

    /**
     * Get customer statistics
     */
    public function getStatistics(): array
    {
        $branchId = BranchHelper::getCurrentBranchId();
        
        $totalCustomers = Customer::where('branch_id', $branchId)->count();
        $activeCustomers = Customer::where('branch_id', $branchId)
            ->where('is_active', true)->count();
        $newCustomersThisMonth = Customer::where('branch_id', $branchId)
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->count();
        $loyaltyCustomers = Customer::where('branch_id', $branchId)
            ->where('loyalty_points', '>', 0)->count();

        return [
            'total_customers' => $totalCustomers,
            'active_customers' => $activeCustomers,
            'new_customers_this_month' => $newCustomersThisMonth,
            'loyalty_customers' => $loyaltyCustomers,
        ];
    }

    /**
     * Get active customers by branch
     */
    public function getActiveCustomers(): Collection
    {
        $branchId = BranchHelper::getCurrentBranchId();
        
        return Customer::where('branch_id', $branchId)
            ->where('is_active', true)
            ->orderBy('last_visit_at', 'desc')
            ->get();
    }
}