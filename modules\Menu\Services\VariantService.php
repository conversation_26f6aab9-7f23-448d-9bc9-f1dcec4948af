<?php

namespace Modules\Menu\Services;

use App\Models\MenuItemVariant;
use Illuminate\Pagination\LengthAwarePaginator;

class VariantService
{
    protected $codeGenerator;

    public function __construct(CodeGeneratorService $codeGenerator)
    {
        $this->codeGenerator = $codeGenerator;
    }
    /**
     * Get variants for a specific branch with pagination and filtering.
     */
    public function getVariantsForBranch(int $branchId, array $filters = []): LengthAwarePaginator
    {
        $query = MenuItemVariant::with(['menuItem'])
            ->whereHas('menuItem.menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            });

        // Apply filters
        if (isset($filters['menu_item_id'])) {
            $query->where('menu_item_id', $filters['menu_item_id']);
        }

        if (isset($filters['is_default'])) {
            $query->where('is_default', $filters['is_default']);
        }

        if (isset($filters['min_price_modifier'])) {
            $query->where('price_modifier', '>=', $filters['min_price_modifier']);
        }

        if (isset($filters['max_price_modifier'])) {
            $query->where('price_modifier', '<=', $filters['max_price_modifier']);
        }

        if (isset($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'sort_order';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortBy, $sortDirection);

        // Pagination
        $perPage = $filters['per_page'] ?? 15;
        
        return $query->paginate($perPage);
    }

    /**
     * Get variant by ID with relationships.
     */
    public function getVariantById(int $id): ?MenuItemVariant
    {
        return MenuItemVariant::with(['menuItem'])
            ->find($id);
    }

    /**
     * Get variant by ID for a specific branch.
     */
    public function getVariantByIdForBranch(int $id, int $branchId): ?MenuItemVariant
    {
        return MenuItemVariant::with(['menuItem'])
            ->whereHas('menuItem.menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })
            ->find($id);
    }

    /**
     * Create a new variant.
     */
    public function createVariant(array $data): MenuItemVariant
    {
        // Generate code automatically if not provided
        if (empty($data['code'])) {
            $data['code'] = $this->codeGenerator->generateVariantCode(
                $data['name'], 
                $data['menu_item_id'] ?? null
            );
        }

        return MenuItemVariant::create($data);
    }

    /**
     * Update an existing variant.
     */
    public function updateVariant(int $id, array $data): ?MenuItemVariant
    {
        $variant = MenuItemVariant::find($id);
        
        if (!$variant) {
            return null;
        }

        $variant->update($data);
        
        return $variant->fresh(['menuItem']);
    }

    /**
     * Update an existing variant for a specific branch.
     */
    public function updateVariantForBranch(int $id, array $data, int $branchId): ?MenuItemVariant
    {
        $variant = MenuItemVariant::whereHas('menuItem.menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })
            ->find($id);
        
        if (!$variant) {
            return null;
        }

        $variant->update($data);
        
        return $variant->fresh(['menuItem']);
    }

    /**
     * Delete a variant.
     */
    public function deleteVariant(int $id): bool
    {
        $variant = MenuItemVariant::find($id);
        
        if (!$variant) {
            return false;
        }

        return $variant->delete();
    }

    /**
     * Delete a variant for a specific branch.
     */
    public function deleteVariantForBranch(int $id, int $branchId): bool
    {
        $variant = MenuItemVariant::whereHas('menuItem.menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })
            ->find($id);
        
        if (!$variant) {
            return false;
        }

        return $variant->delete();
    }

    /**
     * Get variants for a menu item.
     */
    public function getVariantsForMenuItem(int $menuItemId): \Illuminate\Database\Eloquent\Collection
    {
        return MenuItemVariant::where('menu_item_id', $menuItemId)
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * Get default variant for a menu item.
     */
    public function getDefaultVariantForMenuItem(int $menuItemId): ?MenuItemVariant
    {
        return MenuItemVariant::where('menu_item_id', $menuItemId)
            ->where('is_default', true)
            ->first();
    }

    /**
     * Set default variant for a menu item (unsets other defaults).
     */
    public function setDefaultVariant(int $variantId): bool
    {
        $variant = MenuItemVariant::find($variantId);
        
        if (!$variant) {
            return false;
        }

        // Unset other defaults for the same menu item
        MenuItemVariant::where('menu_item_id', $variant->menu_item_id)
            ->where('id', '!=', $variantId)
            ->update(['is_default' => false]);

        // Set this variant as default
        $variant->update(['is_default' => true]);

        return true;
    }

    /**
     * Create variant for web interface.
     */
    public function createVariantForWeb(array $data): MenuItemVariant
    {
        // Generate code automatically if not provided
        if (empty($data['code'])) {
            $data['code'] = $this->codeGenerator->generateVariantCode(
                $data['name'], 
                $data['menu_item_id'] ?? null
            );
        }

        $variant = MenuItemVariant::create($data);
        return $variant->load(['menuItem']);
    }

    /**
     * Update variant for web interface.
     */
    public function updateVariantForWeb(int $id, array $data, int $branchId): ?MenuItemVariant
    {
        return $this->updateVariantForBranch($id, $data, $branchId);
    }

    /**
     * Get variants for DataTable.
     */
    public function getVariantsForDataTable(int $branchId, \Illuminate\Http\Request $request): \Illuminate\Http\JsonResponse
    {
        $query = MenuItemVariant::with(['menuItem'])
            ->whereHas('menuItem.menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            });

        // Apply search
        if ($request->has('search') && !empty($request->search['value'])) {
            $search = $request->search['value'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%");
            });
        }

        // Apply ordering
        if ($request->has('order')) {
            $columns = ['id', 'name', 'type', 'price', 'is_active', 'created_at'];
            $orderColumn = $columns[$request->order[0]['column']] ?? 'id';
            $orderDirection = $request->order[0]['dir'] ?? 'asc';
            $query->orderBy($orderColumn, $orderDirection);
        }

        $totalRecords = $query->count();
        
        // Apply pagination
        $start = $request->start ?? 0;
        $length = $request->length ?? 10;
        $variants = $query->skip($start)->take($length)->get();

        // Format data for DataTable
        $data = $variants->map(function ($variant, $index) use ($start) {
            return [
                'DT_RowIndex' => $start + $index + 1,
                'menu_item_name' => $variant->menuItem ? $variant->menuItem->name : '-',
                'variation_group_name' => $variant->variation_group_name ?? '-',
                'name' => $variant->name,
                'code' => $variant->code ?? '-',
                'price' => number_format($variant->price, 2) . ' ريال',
                'cost' => number_format($variant->cost ?? 0, 2) . ' ريال',
                'is_active' => $variant->is_active ? 
                    '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">نشط</span>' : 
                    '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">غير نشط</span>',
                'action' => $this->getActionButtons($variant->id)
            ];
        });

        return response()->json([
            'draw' => intval($request->draw),
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $totalRecords,
            'data' => $data
        ]);
    }

    /**
     * Generate action buttons for DataTable.
     */
    private function getActionButtons(int $id): string
    {
        return '
            <div class="flex space-x-2">
                <button type="button" class="inline-flex items-center px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded transition-colors duration-200 show-variation" data-id="' . $id . '" title="عرض">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                </button>
                <button type="button" class="inline-flex items-center px-2 py-1 bg-green-600 hover:bg-green-700 text-white text-xs font-medium rounded transition-colors duration-200 edit-variation" data-id="' . $id . '" title="تعديل">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                </button>
                <button type="button" class="inline-flex items-center px-2 py-1 bg-red-600 hover:bg-red-700 text-white text-xs font-medium rounded transition-colors duration-200 delete-variation" data-id="' . $id . '" title="حذف">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                </button>
            </div>
        ';
    }
}