<?php

namespace Modules\Inventory\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\InventoryCategory;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class InventoryCategoryController extends Controller
{
    public function index()
    {
        return view('Inventory::categories.index');
    }

    public function datatable(Request $request)
    {
        $categories = InventoryCategory::with(['parent', 'tenant'])
            ->where('tenant_id', auth()->user()->tenant_id);

        return DataTables::of($categories)
            ->addColumn('parent_name', function ($category) {
                return $category->parent ? $category->parent->name : '-';
            })
            ->addColumn('products_count', function ($category) {
                return $category->products()->count();
            })
            ->addColumn('status', function ($category) {
                return $category->is_active 
                    ? '<span class="badge bg-success">نشط</span>' 
                    : '<span class="badge bg-danger">غير نشط</span>';
            })
            ->addColumn('actions', function ($category) {
                return '
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-primary edit-category" 
                                data-id="' . $category->id . '">
                            <i class="fe fe-edit"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-danger delete-category" 
                                data-id="' . $category->id . '">
                            <i class="fe fe-trash"></i>
                        </button>
                    </div>
                ';
            })
            ->rawColumns(['status', 'actions'])
            ->make(true);
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'parent_id' => 'nullable|exists:inventory_categories,id',
            'sort_order' => 'nullable|integer',
        ]);

        $category = InventoryCategory::create([
            'tenant_id' => auth()->user()->tenant_id,
            'name' => $request->name,
            'description' => $request->description,
            'parent_id' => $request->parent_id,
            'sort_order' => $request->sort_order ?? 0,
            'is_active' => $request->has('is_active'),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم إنشاء الفئة بنجاح',
            'data' => $category
        ]);
    }

    public function show($id)
    {
        $category = InventoryCategory::with(['parent', 'children', 'products'])->findOrFail($id);
        return response()->json($category);
    }

    public function update(Request $request, $id)
    {
        $category = InventoryCategory::findOrFail($id);
        
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'parent_id' => 'nullable|exists:inventory_categories,id',
            'sort_order' => 'nullable|integer',
        ]);

        $category->update([
            'name' => $request->name,
            'description' => $request->description,
            'parent_id' => $request->parent_id,
            'sort_order' => $request->sort_order ?? 0,
            'is_active' => $request->has('is_active'),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث الفئة بنجاح',
            'data' => $category
        ]);
    }

    public function destroy($id)
    {
        $category = InventoryCategory::findOrFail($id);
        
        // Check if category has products
        if ($category->products()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن حذف الفئة لأنها تحتوي على منتجات'
            ], 422);
        }

        // Check if category has children
        if ($category->children()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن حذف الفئة لأنها تحتوي على فئات فرعية'
            ], 422);
        }

        $category->delete();

        return response()->json([
            'success' => true,
            'message' => 'تم حذف الفئة بنجاح'
        ]);
    }

    public function getCategories()
    {
        $categories = InventoryCategory::where('tenant_id', auth()->user()->tenant_id)
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get(['id', 'name', 'parent_id']);

        return response()->json($categories);
    }
}