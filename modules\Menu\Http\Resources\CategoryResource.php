<?php

namespace Modules\Menu\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'menu_id' => $this->menu_id,
            'parent_category_id' => $this->parent_category_id,
            'name' => $this->name,
            'icon' => $this->icon,
            'code' => $this->code,
            'description' => $this->description,
            'image_url' => $this->image_url,
            'sort_order' => $this->sort_order,
            'is_active' => $this->is_active,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            
            // Relationships
            'menu' => $this->whenLoaded('menu'),
            'parent_category' => new CategoryResource($this->whenLoaded('parentCategory')),
            'child_categories' => CategoryResource::collection($this->whenLoaded('childCategories')),
            'menu_items' => MenuItemResource::collection($this->whenLoaded('menuItems')),
            
            // Computed attributes
            'menu_items_count' => $this->whenCounted('menuItems'),
            'child_categories_count' => $this->whenCounted('childCategories'),
        ];
    }
}