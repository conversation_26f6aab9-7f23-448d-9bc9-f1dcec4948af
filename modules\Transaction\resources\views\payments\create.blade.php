<!-- Create Payment Modal -->
<div id="create-payment-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-2/3 xl:w-1/2 shadow-lg rounded-md bg-white max-h-screen overflow-y-auto">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h3 class="text-xl font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-plus mr-3 text-blue-600"></i>
                        Create Payment
                    </h3>
                    <p class="text-sm text-gray-600 mt-1">Process a new payment for a transaction</p>
                </div>
                <button type="button" class="close-modal text-gray-400 hover:text-gray-600 text-xl">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        
            <!-- Payment Form -->
            <form id="payment-form" action="{{ route('payments.store') }}" method="POST">
                @csrf
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Transaction Selection -->
                <div class="md:col-span-2">
                    <label for="transaction_id" class="block text-sm font-medium text-gray-700 mb-2">
                        Transaction <span class="text-red-500">*</span>
                    </label>
                    <select id="transaction_id" name="transaction_id" required class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Select a transaction</option>
                        @if($transaction)
                            <option value="{{ $transaction->id }}" selected>
                                {{ $transaction->transaction_number }} - ${{ number_format($transaction->due_amount, 2) }} due
                            </option>
                        @endif
                        @foreach($dueTransactions as $dueTransaction)
                            @if(!$transaction || $dueTransaction->id !== $transaction->id)
                                <option value="{{ $dueTransaction->id }}">
                                    {{ $dueTransaction->transaction_number }} - ${{ number_format($dueTransaction->due_amount, 2) }} due
                                </option>
                            @endif
                        @endforeach
                    </select>
                    @error('transaction_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Payment Method -->
                <div>
                    <label for="payment_method_id" class="block text-sm font-medium text-gray-700 mb-2">
                        Payment Method <span class="text-red-500">*</span>
                    </label>
                    <select id="payment_method_id" name="payment_method_id" required class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Select payment method</option>
                        @foreach($paymentMethods as $method)
                            <option value="{{ $method->id }}" {{ old('payment_method_id') == $method->id ? 'selected' : '' }}>
                                {{ $method->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('payment_method_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Amount Received -->
                <div>
                    <label for="amount_received" class="block text-sm font-medium text-gray-700 mb-2">
                        Amount Received <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span class="text-gray-500 sm:text-sm">$</span>
                        </div>
                        <input type="number" id="amount_received" name="amount_received" step="0.01" min="0" required 
                               value="{{ old('amount_received') }}"
                               class="w-full pl-7 border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                               placeholder="0.00">
                    </div>
                    @error('amount_received')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Payment Amount (calculated) -->
                <div>
                    <label for="amount" class="block text-sm font-medium text-gray-700 mb-2">
                        Payment Amount <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span class="text-gray-500 sm:text-sm">$</span>
                        </div>
                        <input type="number" id="amount" name="amount" step="0.01" min="0" required readonly
                               value="{{ old('amount') }}"
                               class="w-full pl-7 border border-gray-300 rounded-md px-3 py-2 text-sm bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                               placeholder="0.00">
                    </div>
                    @error('amount')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    <p class="mt-1 text-xs text-gray-500">Automatically calculated based on due amount and amount received</p>
                </div>

                <!-- Reference Number -->
                <div>
                    <label for="reference_number" class="block text-sm font-medium text-gray-700 mb-2">
                        Reference Number
                    </label>
                    <input type="text" id="reference_number" name="reference_number" 
                           value="{{ old('reference_number') }}"
                           class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                           placeholder="Enter reference number">
                    @error('reference_number')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Change Amount (calculated) -->
                <div>
                    <label for="change_amount" class="block text-sm font-medium text-gray-700 mb-2">
                        Change Amount
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span class="text-gray-500 sm:text-sm">$</span>
                        </div>
                        <input type="number" id="change_amount" name="change_amount" step="0.01" min="0" readonly
                               value="{{ old('change_amount', '0.00') }}"
                               class="w-full pl-7 border border-gray-300 rounded-md px-3 py-2 text-sm bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                               placeholder="0.00">
                    </div>
                    @error('change_amount')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    <p class="mt-1 text-xs text-gray-500">Automatically calculated when amount received exceeds due amount</p>
                </div>

                <!-- Notes -->
                <div class="md:col-span-2">
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">
                        Notes
                    </label>
                    <textarea id="notes" name="notes" rows="3" 
                              class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                              placeholder="Enter any additional notes...">{{ old('notes') }}</textarea>
                    @error('notes')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Transaction Details (if selected) -->
            <div id="transaction-details" class="mt-6 hidden">
                <div class="border-t border-gray-200 pt-6">
                    <h4 class="text-md font-medium text-gray-900 mb-4">Transaction Details</h4>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div>
                                <span class="font-medium text-gray-700">Transaction #:</span>
                                <span id="detail-transaction-number" class="text-gray-900"></span>
                            </div>
                            <div>
                                <span class="font-medium text-gray-700">Total Amount:</span>
                                <span id="detail-total-amount" class="text-gray-900"></span>
                            </div>
                            <div>
                                <span class="font-medium text-gray-700">Paid Amount:</span>
                                <span id="detail-paid-amount" class="text-gray-900"></span>
                            </div>
                            <div>
                                <span class="font-medium text-gray-700">Due Amount:</span>
                                <span id="detail-due-amount" class="text-gray-900 font-semibold"></span>
                            </div>
                            <div>
                                <span class="font-medium text-gray-700">Status:</span>
                                <span id="detail-status" class="text-gray-900"></span>
                            </div>
                            <div>
                                <span class="font-medium text-gray-700">Order #:</span>
                                <span id="detail-order-number" class="text-gray-900"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Calculation Summary -->
            <div id="payment-summary" class="mt-6 bg-blue-50 p-4 rounded-lg border border-blue-200 hidden">
                <h4 class="text-sm font-medium text-blue-900 mb-3">Payment Summary</h4>
                <div class="grid grid-cols-3 gap-4 text-sm">
                    <div class="text-center">
                        <div class="text-blue-600 font-medium">Amount Received</div>
                        <div id="summary-received" class="text-lg font-bold text-blue-900">$0.00</div>
                    </div>
                    <div class="text-center">
                        <div class="text-green-600 font-medium">Payment Amount</div>
                        <div id="summary-payment" class="text-lg font-bold text-green-900">$0.00</div>
                    </div>
                    <div class="text-center">
                        <div class="text-orange-600 font-medium">Change</div>
                        <div id="summary-change" class="text-lg font-bold text-orange-900">$0.00</div>
                    </div>
                </div>
                <div class="mt-3 pt-3 border-t border-blue-200">
                    <div class="flex justify-between text-sm">
                        <span class="text-blue-600">Remaining Due:</span>
                        <span id="summary-remaining" class="font-medium text-blue-900">$0.00</span>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="mt-6 flex justify-end space-x-3">
                <a href="{{ route('payments.index') }}" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Cancel
                </a>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="fas fa-credit-card mr-2"></i>
                    Process Payment
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
$(document).ready(function() {
    let currentTransaction = null;

    // Initialize Select2
    $('#transaction_id').select2({
        placeholder: 'Select a transaction',
        allowClear: true,
        width: '100%'
    });

    $('#payment_method_id').select2({
        placeholder: 'Select payment method',
        allowClear: true,
        width: '100%'
    });

    // Handle transaction selection
    $('#transaction_id').on('change', function() {
        const transactionId = $(this).val();
        
        if (transactionId) {
            // Fetch transaction details
            $.ajax({
                url: `/transactions/${transactionId}`,
                method: 'GET',
                success: function(response) {
                    if (response.success) {
                        currentTransaction = response.data;
                        
                        // Update transaction details
                        $('#detail-transaction-number').text(currentTransaction.transaction_number || '-');
                        $('#detail-total-amount').text('$' + parseFloat(currentTransaction.total_amount || 0).toFixed(2));
                        $('#detail-paid-amount').text('$' + parseFloat(currentTransaction.paid_amount || 0).toFixed(2));
                        $('#detail-due-amount').text('$' + parseFloat(currentTransaction.due_amount || 0).toFixed(2));
                        $('#detail-status').text(currentTransaction.status || '-');
                        $('#detail-order-number').text(currentTransaction.order?.order_number || '-');
                        
                        // Show transaction details
                        $('#transaction-details').removeClass('hidden');
                        
                        // Reset calculations
                        calculatePayment();
                    }
                },
                error: function() {
                    alert('Error fetching transaction details');
                }
            });
        } else {
            // Hide transaction details and reset form
            currentTransaction = null;
            $('#transaction-details').addClass('hidden');
            $('#payment-summary').addClass('hidden');
            resetForm();
        }
    });

    // Handle amount received input
    $('#amount_received').on('input', function() {
        calculatePayment();
    });

    // Calculate payment amounts
    function calculatePayment() {
        if (!currentTransaction) {
            return;
        }

        const amountReceived = parseFloat($('#amount_received').val()) || 0;
        const dueAmount = parseFloat(currentTransaction.due_amount || 0);
        
        let paymentAmount = 0;
        let changeAmount = 0;
        let remainingDue = dueAmount;

        if (amountReceived > 0) {
            if (amountReceived >= dueAmount) {
                // Full payment or overpayment
                paymentAmount = dueAmount;
                changeAmount = amountReceived - dueAmount;
                remainingDue = 0;
            } else {
                // Partial payment
                paymentAmount = amountReceived;
                changeAmount = 0;
                remainingDue = dueAmount - amountReceived;
            }

            // Update form fields
            $('#amount').val(paymentAmount.toFixed(2));
            $('#change_amount').val(changeAmount.toFixed(2));

            // Update summary
            $('#summary-received').text('$' + amountReceived.toFixed(2));
            $('#summary-payment').text('$' + paymentAmount.toFixed(2));
            $('#summary-change').text('$' + changeAmount.toFixed(2));
            $('#summary-remaining').text('$' + remainingDue.toFixed(2));

            // Show payment summary
            $('#payment-summary').removeClass('hidden');
        } else {
            // Reset when no amount received
            $('#amount').val('');
            $('#change_amount').val('0.00');
            $('#payment-summary').addClass('hidden');
        }
    }

    // Reset form
    function resetForm() {
        $('#amount_received').val('');
        $('#amount').val('');
        $('#change_amount').val('0.00');
        $('#reference_number').val('');
        $('#notes').val('');
    }

    // Form validation
    $('#payment-form').on('submit', function(e) {
        const amountReceived = parseFloat($('#amount_received').val()) || 0;
        const paymentAmount = parseFloat($('#amount').val()) || 0;
        
        if (!currentTransaction) {
            e.preventDefault();
            alert('Please select a transaction');
            return false;
        }
        
        if (amountReceived <= 0) {
            e.preventDefault();
            alert('Amount received must be greater than 0');
            return false;
        }
        
        if (paymentAmount <= 0) {
            e.preventDefault();
            alert('Payment amount must be greater than 0');
            return false;
        }

        if (!$('#payment_method_id').val()) {
            e.preventDefault();
            alert('Please select a payment method');
            return false;
        }
    });

    // Auto-load transaction details if transaction is pre-selected
    @if($transaction)
        $('#transaction_id').trigger('change');
    @endif
});
</script>
@endpush