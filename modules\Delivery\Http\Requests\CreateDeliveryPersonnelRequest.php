<?php

namespace Modules\Delivery\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class CreateDeliveryPersonnelRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // return Auth::check();
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'user_id' => 'required|exists:users,id|unique:delivery_personnel,user_id',
            'branch_id' => 'required|exists:branches,id',
            'vehicle_type' => 'required|in:bike,motorcycle,car,van,truck,bicycle,scooter',
            'vehicle_plate_number' => 'required|string|max:20|unique:delivery_personnel,vehicle_plate_number',
            'license_number' => 'required|string|max:50|unique:delivery_personnel,license_number',
            'license_expiry_date' => 'nullable|date|after:today',
            'phone_number' => 'required|string|max:20',
            'emergency_contact_name' => 'required|string|max:100',
            'emergency_contact_phone' => 'required|string|max:20',
            'max_concurrent_deliveries' => 'nullable|integer|min:1|max:10',
            'delivery_radius_km' => 'nullable|numeric|min:1|max:100',
            'delivery_radius' => 'nullable|numeric|min:1|max:100', // For backward compatibility
            'hourly_rate' => 'nullable|numeric|min:0',
            'commission_rate' => 'nullable|numeric|min:0|max:100',
            'status' => 'nullable|in:active,inactive,on_break,off_duty',
            'working_hours' => 'nullable|array',
            'working_hours.*.day' => 'required_with:working_hours|in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'working_hours.*.start_time' => 'required_with:working_hours|date_format:H:i',
            'working_hours.*.end_time' => 'required_with:working_hours|date_format:H:i|after:working_hours.*.start_time',
            'working_hours.*.is_working' => 'required_with:working_hours|boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'user_id.required' => 'User is required',
            'user_id.exists' => 'Selected user does not exist',
            'user_id.unique' => 'This user is already registered as delivery personnel',
            'branch_id.required' => 'Branch is required',
            'branch_id.exists' => 'Selected branch does not exist',
            'vehicle_type.required' => 'Vehicle type is required',
            'vehicle_type.in' => 'Vehicle type must be one of: bike, motorcycle, car, van, truck, bicycle, scooter',
            'vehicle_plate_number.required' => 'Vehicle plate number is required',
            'vehicle_plate_number.unique' => 'This vehicle plate number is already registered',
            'license_number.required' => 'License number is required',
            'license_number.unique' => 'This license number is already registered',
            'license_expiry_date.date' => 'License expiry date must be a valid date',
            'license_expiry_date.after' => 'License expiry date must be in the future',
            'phone_number.required' => 'Phone number is required',
            'emergency_contact_name.required' => 'Emergency contact name is required',
            'emergency_contact_phone.required' => 'Emergency contact phone is required',
            'max_concurrent_deliveries.min' => 'Maximum concurrent deliveries must be at least 1',
            'max_concurrent_deliveries.max' => 'Maximum concurrent deliveries cannot exceed 10',
            'delivery_radius_km.min' => 'Delivery radius must be at least 1 km',
            'delivery_radius_km.max' => 'Delivery radius cannot exceed 100 km',
            'hourly_rate.min' => 'Hourly rate cannot be negative',
            'commission_rate.min' => 'Commission rate cannot be negative',
            'commission_rate.max' => 'Commission rate cannot exceed 100%',
            'status.in' => 'Status must be one of: active, inactive, on_break, off_duty',
            'working_hours.*.day.in' => 'Day must be a valid weekday',
            'working_hours.*.start_time.date_format' => 'Start time must be in HH:MM format',
            'working_hours.*.end_time.date_format' => 'End time must be in HH:MM format',
            'working_hours.*.end_time.after' => 'End time must be after start time',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default values
        if (!$this->has('max_concurrent_deliveries')) {
            $this->merge(['max_concurrent_deliveries' => 3]);
        }

        if (!$this->has('delivery_radius_km')) {
            $this->merge(['delivery_radius_km' => 10]);
        }

        if (!$this->has('status')) {
            $this->merge(['status' => 'active']);
        }

        // Set default working hours if not provided
        if (!$this->has('working_hours')) {
            $defaultWorkingHours = [];
            $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
            
            foreach ($days as $day) {
                $defaultWorkingHours[] = [
                    'day' => $day,
                    'start_time' => '09:00',
                    'end_time' => '18:00',
                    'is_working' => in_array($day, ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']),
                ];
            }
            
            $this->merge(['working_hours' => $defaultWorkingHours]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Check if user exists and is not already delivery personnel
            if ($this->user_id) {
                $user = \App\Models\User::find($this->user_id);
                if (!$user) {
                    $validator->errors()->add('user_id', 'Selected user does not exist');
                } else {
                    // Check if user belongs to the same tenant
                    $currentUser = Auth::user();
                    if ($currentUser && $user->tenant_id !== $currentUser->tenant_id) {
                        $validator->errors()->add('user_id', 'User does not belong to your organization');
                    }
                }
            }

            // Validate working hours uniqueness
            if ($this->has('working_hours')) {
                $days = collect($this->working_hours)->pluck('day');
                if ($days->count() !== $days->unique()->count()) {
                    $validator->errors()->add('working_hours', 'Duplicate days found in working hours');
                }
            }
        });
    }
}