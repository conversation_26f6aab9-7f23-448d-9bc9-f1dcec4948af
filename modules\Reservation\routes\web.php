<?php

use Illuminate\Support\Facades\Route;
use Modules\Reservation\Http\Controllers\Web\QRTestController;
use Modules\Reservation\Http\Controllers\Web\ReservationController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// QR Code Test Routes (Public - no auth required)
Route::prefix('reservation/qr-test')->name('reservation.qr.')->group(function () {
    Route::get('/', [QRTestController::class, 'index'])->name('test');
    Route::post('/generate', [QRTestController::class, 'generateQR'])->name('generate');
    Route::post('/validate', [QRTestController::class, 'validateQR'])->name('validate');
    Route::post('/custom', [QRTestController::class, 'generateCustomQR'])->name('custom');
    Route::post('/batch', [QRTestController::class, 'batchGenerate'])->name('batch');
    Route::get('/table-info', [QRTestController::class, 'getTableInfo'])->name('table-info');
});

// Authenticated Web Routes
Route::middleware(['auth'])->group(function () {
    
    // Main Reservation Routes
    Route::prefix('reservation')->group(function () {
        // Dashboard
        Route::get('/', [ReservationController::class, 'dashboard'])->name('reservation.dashboard');
        
        // DataTable Views
        Route::get('/reservations', [ReservationController::class, 'reservationsIndex'])->name('reservation.reservations');
        
        // CRUD Routes for Waiter Requests
        Route::get('/waiter-requests', [ReservationController::class, 'waiterRequestsIndex'])->name('reservation.waiter-requests');
    });

    // DataTable AJAX Routes (for frontend DataTables)
    Route::get('/reservations-data', [ReservationController::class, 'reservationsDataTable'])->name('reservations.data');
    Route::get('/areas-data', [ReservationController::class, 'areasDataTable'])->name('areas.data');
    Route::get('/tables-data', [ReservationController::class, 'tablesDataTable'])->name('tables.data');
    Route::get('/waiter-requests-data', [ReservationController::class, 'waiterRequestsDataTable'])->name('waiter-requests.data');
    Route::get('/waiter-requests-cards', [ReservationController::class, 'waiterRequestsCards'])->name('waiter-requests.cards');

    // CRUD Routes for Reservations
    Route::post('/reservations', [ReservationController::class, 'reservationsStore'])->name('reservations.store');
    Route::get('/reservations/{id}', [ReservationController::class, 'reservationsShow'])->name('reservations.show');
    Route::get('/reservations/{id}/edit', [ReservationController::class, 'reservationsEdit'])->name('reservations.edit');
    Route::put('/reservations/{id}', [ReservationController::class, 'reservationsUpdate'])->name('reservations.update');
    Route::delete('/reservations/{id}', [ReservationController::class, 'reservationsDestroy'])->name('reservations.destroy');
    Route::post('/reservations/{id}/confirm', [ReservationController::class, 'reservationsConfirm'])->name('reservations.confirm');

    // CRUD Routes for Areas
    Route::prefix('areas')->group(function () {
        Route::get('/', [ReservationController::class, 'areasIndex'])->name('areas.index');
        Route::post('/', [ReservationController::class, 'areasStore'])->name('areas.store');
        Route::get('/{id}', [ReservationController::class, 'areasShow'])->name('areas.show');
        Route::get('/{id}/edit', [ReservationController::class, 'areasEdit'])->name('areas.edit');
        Route::put('/{id}', [ReservationController::class, 'areasUpdate'])->name('areas.update');
        Route::delete('/{id}', [ReservationController::class, 'areasDestroy'])->name('areas.destroy');
        Route::get('/{id}/tables', [ReservationController::class, 'getAreaTables'])->name('areas.tables');
    });

    // CRUD Routes for Tables
    Route::prefix('tables')->group(function () {
        Route::get('/', [ReservationController::class, 'tablesIndex'])->name('tables.index');
        Route::post('/', [ReservationController::class, 'tablesStore'])->name('tables.store');
        Route::get('/{id}', [ReservationController::class, 'tablesShow'])->name('tables.show');
        Route::get('/{id}/edit', [ReservationController::class, 'tablesEdit'])->name('tables.edit');
        Route::put('/{id}', [ReservationController::class, 'tablesUpdate'])->name('tables.update');
        Route::delete('/{id}', [ReservationController::class, 'tablesDestroy'])->name('tables.destroy');
        
        // DataTable AJAX endpoint
        Route::get('/data/tables', [ReservationController::class, 'tablesDataTable'])->name('reservation.tables.data');
        
        // QR Code Management
        Route::post('/{id}/generate-qr', [ReservationController::class, 'generateTableQR'])->name('tables.generate-qr');
        Route::post('/{id}/regenerate-qr', [ReservationController::class, 'regenerateTableQR'])->name('tables.regenerate-qr');
        Route::post('/{id}/set-manual-qr', [ReservationController::class, 'setManualTableQR'])->name('tables.set-manual-qr');
    });

    // CRUD Routes for Waiter Requests
    Route::prefix('waiter-requests')->group(function () {
        Route::post('/', [ReservationController::class, 'waiterRequestsStore'])->name('waiter-requests.store');
        Route::get('/{id}', [ReservationController::class, 'waiterRequestsShow'])->name('waiter-requests.show');
        Route::get('/{id}/edit', [ReservationController::class, 'waiterRequestsEdit'])->name('waiter-requests.edit');
        Route::put('/{id}', [ReservationController::class, 'waiterRequestsUpdate'])->name('waiter-requests.update');
        Route::delete('/{id}', [ReservationController::class, 'waiterRequestsDestroy'])->name('waiter-requests.destroy');
        Route::patch('/{id}/complete', [ReservationController::class, 'waiterRequestsComplete'])->name('waiter-requests.complete');
        Route::patch('/{id}/cancel', [ReservationController::class, 'waiterRequestsCancel'])->name('waiter-requests.cancel');
    });

    // Dropdown/List Routes
    Route::get('/customers', [ReservationController::class, 'getCustomers'])->name('customers.list');
    Route::get('/areas-list', [ReservationController::class, 'getAreas'])->name('areas.list');
    Route::get('/tables-list', [ReservationController::class, 'getTables'])->name('tables.list');
    Route::get('/areas/{id}/tables', [ReservationController::class, 'getAvailableTablesByArea'])->name('areas.tables');
    Route::get('/waiters', [ReservationController::class, 'getWaiters'])->name('waiters.list');
    Route::get('/reservation-statuses', [ReservationController::class, 'getReservationStatuses'])->name('reservation-statuses.list');
});

