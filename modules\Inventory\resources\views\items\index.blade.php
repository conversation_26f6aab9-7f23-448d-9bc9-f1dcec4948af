@extends('layouts.master')

@section('title', 'إدارة المواد')

@section('page-header')
<!-- Breadcrumb -->
<nav class="flex mb-8" aria-label="Breadcrumb">
    <ol class="inline-flex items-center space-x-1 md:space-x-3 rtl:space-x-reverse">
        <li class="inline-flex items-center">
            <a href="{{ route('dashboard') }}" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                <i class="fas fa-home ml-2"></i>
                الرئيسية
            </a>
        </li>
        <li>
            <div class="flex items-center">
                <i class="fas fa-chevron-left text-gray-400 mx-2"></i>
                <a href="#" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">المخزون</a>
            </div>
        </li>
        <li aria-current="page">
            <div class="flex items-center">
                <i class="fas fa-chevron-left text-gray-400 mx-2"></i>
                <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">إدارة المواد</span>
            </div>
        </li>
    </ol>
</nav>
@endsection

@section('content')
<div class="space-y-6">
    <!-- Page Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 flex items-center gap-3">
                        <div class="w-10 h-10 bg-gradient-to-r from-purple-600 to-purple-700 rounded-lg flex items-center justify-center">
                            <i class="fas fa-boxes text-white"></i>
                        </div>
                        إدارة المواد
                    </h1>
                    <p class="mt-1 text-sm text-gray-600">إدارة وتتبع جميع المواد والمنتجات في المخزون</p>
                </div>
                <div class="flex gap-2">
                    <button type="button" onclick="exportItems()" class="px-4 py-2 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center gap-2">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                    <button type="button" onclick="importItems()" class="px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2">
                        <i class="fas fa-upload"></i>
                        استيراد
                    </button>
                    <button type="button" onclick="openModal('addItemModal')" class="px-6 py-3 bg-gradient-to-r from-purple-600 to-purple-700 text-white font-medium rounded-lg hover:from-purple-700 hover:to-purple-800 transition-all duration-200 flex items-center gap-2 shadow-lg hover:shadow-xl">
                        <i class="fas fa-plus"></i>
                        إضافة مادة جديدة
                    </button>
                </div>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="categoryFilter" class="block text-sm font-medium text-gray-700 mb-2">الفئة</label>
                    <select id="categoryFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        <option value="">جميع الفئات</option>
                        <option value="ingredients">المكونات</option>
                        <option value="beverages">المشروبات</option>
                        <option value="packaging">التعبئة والتغليف</option>
                        <option value="cleaning">مواد التنظيف</option>
                        <option value="equipment">المعدات</option>
                        <option value="general">عام</option>
                    </select>
                </div>
                <div>
                    <label for="stockFilter" class="block text-sm font-medium text-gray-700 mb-2">حالة المخزون</label>
                    <select id="stockFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        <option value="">جميع الحالات</option>
                        <option value="in_stock">متوفر</option>
                        <option value="low_stock">مخزون منخفض</option>
                        <option value="out_of_stock">نفد المخزون</option>
                    </select>
                </div>
                <div>
                    <label for="unitFilter" class="block text-sm font-medium text-gray-700 mb-2">الوحدة</label>
                    <select id="unitFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        <option value="">جميع الوحدات</option>
                        <option value="kg">كيلوجرام</option>
                        <option value="g">جرام</option>
                        <option value="l">لتر</option>
                        <option value="ml">مليلتر</option>
                        <option value="piece">قطعة</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button type="button" id="resetFilters" class="w-full px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg font-medium transition-colors duration-200 flex items-center justify-center gap-2">
                        <i class="fas fa-undo"></i>
                        إعادة تعيين
                    </button>
                </div>
            </div>
        </div>

        <!-- DataTable Controls -->
        <div class="p-6 border-b border-gray-200">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div class="flex flex-col sm:flex-row gap-4">
                    <!-- Global Search -->
                    <div class="relative">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <input type="text" id="globalSearch" placeholder="البحث في المواد..." class="w-full sm:w-64 pl-3 pr-10 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                    </div>
                </div>

                <!-- Export Buttons -->
                <div class="flex gap-2">
                    <button type="button" id="exportExcel" class="px-3 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center gap-2">
                        <i class="fas fa-file-excel"></i>
                        Excel
                    </button>
                    <button type="button" id="exportPdf" class="px-3 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 transition-colors duration-200 flex items-center gap-2">
                        <i class="fas fa-file-pdf"></i>
                        PDF
                    </button>
                    <button type="button" id="printTable" class="px-3 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 transition-colors duration-200 flex items-center gap-2">
                        <i class="fas fa-print"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>
        </div>
    </div>

    <!-- Items Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <div class="flex justify-between items-center">
                <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                    <i class="fas fa-list text-purple-600"></i>
                    قائمة المواد
                </h2>
                <button type="button" onclick="refreshTable()" class="px-3 py-2 bg-purple-600 text-white text-sm font-medium rounded-lg hover:bg-purple-700 transition-colors duration-200 flex items-center gap-2">
                    <i class="fas fa-sync-alt"></i>
                    تحديث
                </button>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table id="itemsTable" class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الصورة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">اسم المادة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الرمز</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الفئة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المخزون الحالي</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحد الأدنى</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحد الأقصى</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التكلفة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">آخر تحديث</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <!-- DataTable will populate this -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modals -->
@include('inventory::items.modals')

@endsection

@push('styles')
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.dataTables.min.css">
<style>
    /* Custom DataTable styling for Tailwind */
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_processing,
    .dataTables_wrapper .dataTables_paginate {
        color: #374151;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.5rem 0.75rem;
        margin: 0 0.125rem;
        border-radius: 0.375rem;
        border: 1px solid #d1d5db;
        background: white;
        color: #374151;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background: #f3f4f6;
        border-color: #9ca3af;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background: #9333ea;
        border-color: #9333ea;
        color: white;
    }

    .item-image {
        width: 40px;
        height: 40px;
        object-fit: cover;
        border-radius: 6px;
        border: 1px solid #e5e7eb;
    }

    .stock-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-left: 8px;
    }

    .stock-indicator.high { background-color: #10b981; }
    .stock-indicator.medium { background-color: #f59e0b; }
    .stock-indicator.low { background-color: #ef4444; }
    .stock-indicator.out { background-color: #6b7280; }
</style>
@endpush

@push('scripts')
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>

<!-- Modal Functions -->
<script>
function openModal(modalId) {
    document.getElementById(modalId).classList.remove('hidden');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

// Close modal when clicking outside
document.addEventListener('click', function(event) {
    const modals = ['addItemModal', 'editItemModal', 'viewItemModal', 'updateStockModal', 'importItemsModal'];
    modals.forEach(modalId => {
        const modal = document.getElementById(modalId);
        if (event.target === modal) {
            closeModal(modalId);
        }
    });
});

// Close modal with Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        const modals = ['addItemModal', 'editItemModal', 'viewItemModal', 'updateStockModal', 'importItemsModal'];
        modals.forEach(modalId => {
            const modal = document.getElementById(modalId);
            if (!modal.classList.contains('hidden')) {
                closeModal(modalId);
            }
        });
    }
});

let itemsTable;

$(document).ready(function() {
    itemsTable = $('#itemsTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("inventory.api.items.datatable") }}',
            data: function(d) {
                d.category = $('#categoryFilter').val();
                d.stock_status = $('#stockFilter').val();
                d.unit = $('#unitFilter').val();
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            {
                data: 'image',
                name: 'image',
                orderable: false,
                searchable: false,
                render: function(data, type, row) {
                    if (data) {
                        return `<img src="${data}" class="item-image" alt="صورة المادة">`;
                    }
                    return `<div class="item-image bg-gray-100 flex items-center justify-center">
                        <i class="fas fa-box text-gray-400"></i>
                    </div>`;
                }
            },
            { data: 'product.name', name: 'product.name' },
            { data: 'product.sku', name: 'product.sku' },
            {
                data: 'product.category',
                name: 'product.category',
                render: function(data) {
                    const categories = {
                        'ingredients': 'المكونات',
                        'beverages': 'المشروبات',
                        'packaging': 'التعبئة والتغليف',
                        'cleaning': 'مواد التنظيف',
                        'equipment': 'المعدات',
                        'general': 'عام'
                    };
                    return categories[data] || data;
                }
            },
            {
                data: 'current_stock',
                name: 'current_stock',
                render: function(data, type, row) {
                    const indicator = getStockIndicator(data, row.minimum_stock, row.maximum_stock);
                    return `<span class="stock-indicator ${indicator}"></span>${data} ${row.product.unit?.symbol || ''}`;
                }
            },
            { data: 'minimum_stock', name: 'minimum_stock' },
            { data: 'maximum_stock', name: 'maximum_stock' },
            {
                data: 'cost_per_unit',
                name: 'cost_per_unit',
                render: function(data) {
                    return formatCurrency(data);
                }
            },
            {
                data: 'status',
                name: 'status',
                render: function(data, type, row) {
                    return getStatusBadge(row.current_stock, row.minimum_stock);
                }
            },
            {
                data: 'last_updated',
                name: 'last_updated',
                render: function(data) {
                    return formatDate(data);
                }
            },
            {
                data: 'actions',
                name: 'actions',
                orderable: false,
                searchable: false,
                render: function(data, type, row) {
                    return `
                        <div class="flex gap-1">
                            <button type="button" class="px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700" onclick="viewItem(${row.id})" title="عرض">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button type="button" class="px-2 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700" onclick="editItem(${row.id})" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button type="button" class="px-2 py-1 bg-yellow-600 text-white text-xs rounded hover:bg-yellow-700" onclick="updateStock(${row.id})" title="تحديث المخزون">
                                <i class="fas fa-cube"></i>
                            </button>
                            <button type="button" class="px-2 py-1 bg-purple-600 text-white text-xs rounded hover:bg-purple-700" onclick="viewMovements(${row.id})" title="الحركات">
                                <i class="fas fa-history"></i>
                            </button>
                            <button type="button" class="px-2 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700" onclick="deleteItem(${row.id})" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    `;
                }
            }
        ],
        order: [[2, 'asc']],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json'
        },
        responsive: true,
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        dom: 'Bfrtip',
        buttons: [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ]
    });

    // Enhanced Search and Filter Functionality
    $('#globalSearch').on('keyup', function() {
        itemsTable.search(this.value).draw();
    });

    // Export Functions
    $('#exportExcel').on('click', function() {
        itemsTable.button('.buttons-excel').trigger();
    });

    $('#exportPdf').on('click', function() {
        itemsTable.button('.buttons-pdf').trigger();
    });

    $('#printTable').on('click', function() {
        itemsTable.button('.buttons-print').trigger();
    });

    // Filter change handlers
    $('#categoryFilter, #stockFilter, #unitFilter').change(function() {
        itemsTable.draw();
    });

    // Reset filters
    $('#resetFilters').click(function() {
        $('#categoryFilter, #stockFilter, #unitFilter').val('');
        itemsTable.draw();
    });
});

function getStockIndicator(current, minimum, maximum) {
    if (current <= 0) return 'out';
    if (current <= minimum) return 'low';
    if (maximum && current >= maximum * 0.8) return 'high';
    return 'medium';
}

function getStatusBadge(current, minimum) {
    if (current <= 0) {
        return '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">نفد المخزون</span>';
    } else if (current <= minimum) {
        return '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">مخزون منخفض</span>';
    } else {
        return '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">متوفر</span>';
    }
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
    }).format(amount);
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('ar-SA');
}

function refreshTable() {
    itemsTable.ajax.reload();
}

function viewItem(id) {
    $.get(`{{ route('inventory.api.items.show', ':id') }}`.replace(':id', id))
        .done(function(data) {
            populateViewModal(data);
            openModal('viewItemModal');
        })
        .fail(function() {
            showToast('حدث خطأ أثناء جلب بيانات المادة', 'error');
        });
}

function editItem(id) {
    $.get(`{{ route('inventory.api.items.show', ':id') }}`.replace(':id', id))
        .done(function(data) {
            populateEditModal(data);
            openModal('editItemModal');
        })
        .fail(function() {
            showToast('حدث خطأ أثناء جلب بيانات المادة', 'error');
        });
}

function updateStock(id) {
    $('#updateStockModal').data('item-id', id);
    openModal('updateStockModal');
}

function viewMovements(id) {
    window.location.href = `{{ route('inventory.stock.movements.item', ':id') }}`.replace(':id', id);
}

function deleteItem(id) {
    if (confirm('هل أنت متأكد من حذف هذه المادة؟')) {
        $.ajax({
            url: `{{ route('inventory.api.items.destroy', ':id') }}`.replace(':id', id),
            type: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function() {
                showToast('تم حذف المادة بنجاح', 'success');
                itemsTable.ajax.reload();
            },
            error: function(xhr) {
                const message = xhr.responseJSON?.message || 'حدث خطأ أثناء حذف المادة';
                showToast(message, 'error');
            }
        });
    }
}

function exportItems() {
    window.location.href = '{{ route("inventory.api.export") }}?format=excel';
}

function importItems() {
    openModal('importItemsModal');
}

function populateViewModal(data) {
    // Populate view modal with item data
    // Implementation depends on modal structure
}

function populateEditModal(data) {
    // Populate edit modal with item data
    // Implementation depends on modal structure
}

// Toast notification function
window.showToast = function(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg text-white font-medium ${
        type === 'success' ? 'bg-green-600' :
        type === 'error' ? 'bg-red-600' :
        type === 'warning' ? 'bg-yellow-600' : 'bg-blue-600'
    }`;
    toast.textContent = message;
    document.body.appendChild(toast);

    setTimeout(() => {
        toast.remove();
    }, 3000);
};
</script>
@endpush
