<?php

namespace Modules\Transaction\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;
use Modules\Transaction\Services\TransactionService;
use Modules\Transaction\Services\PaymentService;

class TransactionServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register the TransactionService
        $this->app->singleton(TransactionService::class, function ($app) {
            return new TransactionService();
        });

        // Register the PaymentService
        $this->app->singleton(PaymentService::class, function ($app) {
            return new PaymentService(
                $app->make(TransactionService::class)
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Load routes
        $this->loadRoutes();
        
        // Load views
        $this->loadViewsFrom(__DIR__ . '/../resources/views', 'transaction');
        
        // Load migrations
        $this->loadMigrationsFrom(__DIR__ . '/../../../database/migrations');
        
        // Register middleware if needed
        // $router = $this->app['router'];
        // $router->aliasMiddleware('transaction.access', \Modules\Transaction\Http\Middleware\TransactionAccessMiddleware::class);
        
        // Publish config if needed
        // $this->publishes([
        //     __DIR__ . '/../config/transaction.php' => config_path('transaction.php'),
        // ], 'transaction-config');
    }

    /**
     * Load module routes
     */
    protected function loadRoutes(): void
    {
        // Load API routes
        if (file_exists(__DIR__ . '/../routes/api.php')) {
            Route::group([
                'middleware' => 'api',
                'prefix' => 'api',
                'namespace' => 'Modules\\Transaction\\Http\\Controllers',
            ], function () {
                require __DIR__ . '/../routes/api.php';
            });
        }

        // Load Web routes
        if (file_exists(__DIR__ . '/../routes/web.php')) {
            Route::group([
                'middleware' => 'web',
                'namespace' => 'Modules\\Transaction\\Http\\Controllers',
            ], function () {
                require __DIR__ . '/../routes/web.php';
            });
        }
    }
}
