<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_methods', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->nullable()->constrained('tenants')->onDelete('cascade');
            $table->string('name', 100)->comment('Payment method name (cash, card, mobile_payment, etc.)');
            $table->string('code', 50)->comment('Unique code for payment method within tenant');
            $table->text('description')->nullable()->comment('Payment method description');
            $table->boolean('is_active')->default(true)->comment('Whether payment method is active');
            $table->json('configuration')->nullable()->comment('Payment method specific configuration');
            $table->integer('sort_order')->default(0)->comment('Display order');
            $table->timestamps();

            // Unique constraints within tenant (null tenant_id for global methods)
            $table->unique(['tenant_id', 'code']);

            // Indexes for better performance
            $table->index(['tenant_id', 'is_active', 'sort_order']);
            $table->index(['is_active', 'sort_order']);
            $table->index('code');
        });

        // Insert default payment methods
        DB::table('payment_methods')->insert([
            [
                'name' => 'Cash',
                'code' => 'cash',
                'description' => 'Cash payment',
                'is_active' => true,
                'sort_order' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Credit Card',
                'code' => 'credit_card',
                'description' => 'Credit card payment',
                'is_active' => true,
                'sort_order' => 2,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Debit Card',
                'code' => 'debit_card',
                'description' => 'Debit card payment',
                'is_active' => true,
                'sort_order' => 3,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Mobile Payment',
                'code' => 'mobile_payment',
                'description' => 'Mobile payment (Apple Pay, Google Pay, etc.)',
                'is_active' => true,
                'sort_order' => 4,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Bank Transfer',
                'code' => 'bank_transfer',
                'description' => 'Bank transfer payment',
                'is_active' => true,
                'sort_order' => 5,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'PayPal',
                'code' => 'paypal',
                'description' => 'PayPal payment',
                'is_active' => true,
                'sort_order' => 6,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Stripe',
                'code' => 'stripe',
                'description' => 'Stripe payment',
                'is_active' => true,
                'sort_order' => 7,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Manual',
                'code' => 'manual',
                'description' => 'Manual payment entry',
                'is_active' => true,
                'sort_order' => 8,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_methods');
    }
};
