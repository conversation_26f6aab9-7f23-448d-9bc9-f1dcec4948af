<?php

namespace Modules\Delivery\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DeliveryZoneResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'branch_id' => $this->branch_id,
            'coordinates' => $this->getFormattedCoordinates(),
            'address' => $this->address,
            'city' => $this->city,
            'state' => $this->state,
            'postal_code' => $this->postal_code,
            'country' => $this->country,
            'delivery_fee' => $this->delivery_fee,
            'minimum_order_amount' => $this->minimum_order_amount,
            'estimated_delivery_time' => $this->estimated_delivery_time,
            'is_active' => $this->is_active,
            'priority' => $this->priority,
            'description' => $this->description,
            'color' => $this->color,
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),

            // Relationships
            'branch' => $this->whenLoaded('branch', function () {
                return [
                    'id' => $this->branch->id,
                    'name' => $this->branch->name,
                    'address' => $this->branch->address,
                    'phone' => $this->branch->phone,
                    'latitude' => $this->branch->latitude,
                    'longitude' => $this->branch->longitude,
                ];
            }),

            'active_deliveries' => $this->whenLoaded('activeDeliveries', function () {
                return DeliveryAssignmentResource::collection($this->activeDeliveries);
            }),

            'recent_deliveries' => $this->whenLoaded('recentDeliveries', function () {
                return DeliveryAssignmentResource::collection($this->recentDeliveries);
            }),

            // Computed fields
            'status_label' => $this->getStatusLabel(),
            'priority_label' => $this->getPriorityLabel(),
            'area_km2' => $this->getAreaInKm2(),
            'center_point' => $this->getCenterPoint(),
            'bounding_box' => $this->getBoundingBox(),
            'active_deliveries_count' => $this->getActiveDeliveriesCount(),
            'total_deliveries_today' => $this->getTotalDeliveriesToday(),
            'average_delivery_time' => $this->getAverageDeliveryTime(),
            'delivery_success_rate' => $this->getDeliverySuccessRate(),
            'peak_hours' => $this->getPeakHours(),
            'estimated_delivery_window' => $this->getEstimatedDeliveryWindow(),
        ];
    }

    /**
     * Get formatted coordinates
     */
    private function getFormattedCoordinates(): array
    {
        if (!$this->coordinates) {
            return [];
        }

        $coordinates = is_string($this->coordinates) 
            ? json_decode($this->coordinates, true) 
            : $this->coordinates;

        return array_map(function ($coord) {
            // Handle both formats: lat/lng (from model) and latitude/longitude
            $latitude = $coord['latitude'] ?? $coord['lat'] ?? ($coord[1] ?? 0);
            $longitude = $coord['longitude'] ?? $coord['lng'] ?? ($coord[0] ?? 0);
            
            return [
                'latitude' => (float) $latitude,
                'longitude' => (float) $longitude,
            ];
        }, $coordinates);
    }

    /**
     * Get status label
     */
    private function getStatusLabel(): string
    {
        return $this->is_active ? 'Active' : 'Inactive';
    }

    /**
     * Get priority label
     */
    private function getPriorityLabel(): string
    {
        return match ($this->priority) {
            1 => 'Low Priority',
            2 => 'Normal Priority',
            3 => 'High Priority',
            4 => 'Critical Priority',
            default => 'Normal Priority',
        };
    }

    /**
     * Calculate area in square kilometers
     */
    private function getAreaInKm2(): float
    {
        $coordinates = $this->getFormattedCoordinates();
        
        if (count($coordinates) < 3) {
            return 0;
        }

        // Using the shoelace formula for polygon area
        $area = 0;
        $n = count($coordinates);

        for ($i = 0; $i < $n; $i++) {
            $j = ($i + 1) % $n;
            $area += $coordinates[$i]['longitude'] * $coordinates[$j]['latitude'];
            $area -= $coordinates[$j]['longitude'] * $coordinates[$i]['latitude'];
        }

        $area = abs($area) / 2;

        // Convert from degrees to km² (approximate)
        $kmPerDegree = 111.32; // km per degree at equator
        $areaKm2 = $area * pow($kmPerDegree, 2);

        return round($areaKm2, 2);
    }

    /**
     * Get center point of the zone
     */
    private function getCenterPoint(): array
    {
        $coordinates = $this->getFormattedCoordinates();
        
        if (empty($coordinates)) {
            return ['latitude' => 0, 'longitude' => 0];
        }

        $latSum = array_sum(array_column($coordinates, 'latitude'));
        $lonSum = array_sum(array_column($coordinates, 'longitude'));
        $count = count($coordinates);

        return [
            'latitude' => round($latSum / $count, 6),
            'longitude' => round($lonSum / $count, 6),
        ];
    }

    /**
     * Get bounding box of the zone
     */
    private function getBoundingBox(): array
    {
        $coordinates = $this->getFormattedCoordinates();
        
        if (empty($coordinates)) {
            return [
                'north' => 0, 'south' => 0,
                'east' => 0, 'west' => 0,
            ];
        }

        $latitudes = array_column($coordinates, 'latitude');
        $longitudes = array_column($coordinates, 'longitude');

        return [
            'north' => max($latitudes),
            'south' => min($latitudes),
            'east' => max($longitudes),
            'west' => min($longitudes),
        ];
    }

    /**
     * Get active deliveries count
     */
    private function getActiveDeliveriesCount(): int
    {
        return $this->deliveryAssignments()
            ->whereIn('status', ['assigned', 'picked_up', 'in_transit'])
            ->count();
    }

    /**
     * Get total deliveries for today
     */
    private function getTotalDeliveriesToday(): int
    {
        return $this->deliveryAssignments()
            ->whereDate('created_at', today())
            ->count();
    }

    /**
     * Get average delivery time in minutes
     */
    private function getAverageDeliveryTime(): ?float
    {
        $deliveries = $this->deliveryAssignments()
            ->where('status', 'delivered')
            ->whereNotNull('picked_up_at')
            ->whereNotNull('delivered_at')
            ->get();

        if ($deliveries->isEmpty()) {
            return null;
        }

        $totalMinutes = $deliveries->sum(function ($delivery) {
            return $delivery->picked_up_at->diffInMinutes($delivery->delivered_at);
        });

        return round($totalMinutes / $deliveries->count(), 1);
    }

    /**
     * Get delivery success rate percentage
     */
    private function getDeliverySuccessRate(): float
    {
        $totalDeliveries = $this->deliveryAssignments()
            ->whereIn('status', ['delivered', 'failed', 'cancelled'])
            ->count();

        if ($totalDeliveries === 0) {
            return 0;
        }

        $successfulDeliveries = $this->deliveryAssignments()
            ->where('status', 'delivered')
            ->count();

        return round(($successfulDeliveries / $totalDeliveries) * 100, 1);
    }

    /**
     * Get peak delivery hours
     */
    private function getPeakHours(): array
    {
        $deliveries = $this->deliveryAssignments()
            ->where('status', 'delivered')
            ->whereDate('created_at', '>=', now()->subDays(30))
            ->get();

        $hourCounts = [];
        
        foreach ($deliveries as $delivery) {
            $hour = $delivery->created_at->format('H');
            $hourCounts[$hour] = ($hourCounts[$hour] ?? 0) + 1;
        }

        arsort($hourCounts);
        
        return array_slice(array_keys($hourCounts), 0, 3, true);
    }

    /**
     * Get estimated delivery window
     */
    private function getEstimatedDeliveryWindow(): array
    {
        $baseTime = $this->estimated_delivery_time;
        $bufferMinutes = 10; // 10-minute buffer

        return [
            'min_minutes' => max(0, $baseTime - $bufferMinutes),
            'max_minutes' => $baseTime + $bufferMinutes,
            'formatted' => sprintf(
                '%d-%d minutes',
                max(0, $baseTime - $bufferMinutes),
                $baseTime + $bufferMinutes
            ),
        ];
    }
}