@extends('layouts.master')

@section('title', 'الوصفات')

@section('css')
    <!-- DataTables CSS -->
    <link href="{{ URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css') }}" rel="stylesheet" />
    <link href="{{ URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css') }}" rel="stylesheet">
    <link href="{{ URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css') }}" rel="stylesheet" />
    <link href="{{ URL::asset('assets/plugins/datatable/css/jquery.dataTables.min.css') }}" rel="stylesheet">
    <link href="{{ URL::asset('assets/plugins/datatable/css/responsive.dataTables.min.css') }}" rel="stylesheet">
    <link href="{{ URL::asset('assets/plugins/select2/css/select2.min.css') }}" rel="stylesheet">
@endsection

@section('page-header')
    <!-- breadcrumb -->
    <div class="breadcrumb-header justify-content-between">
        <div class="my-auto">
            <div class="d-flex">
                <h4 class="content-title mb-0 my-auto">المخزون</h4>
                <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ الوصفات</span>
            </div>
        </div>
    </div>
    <!-- breadcrumb -->
@endsection

@section('content')
    <!-- row -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card mg-b-20">
                <div class="card-header pb-0">
                    <div class="d-flex justify-content-between">
                        <h4 class="card-title mg-b-0">الوصفات</h4>
                        <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addRecipeModal">
                            <i class="fas fa-plus"></i> إضافة وصفة جديدة
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="recipesTable" class="table key-buttons text-md-nowrap">
                            <thead>
                                <tr>
                                    <th class="border-bottom-0">#</th>
                                    <th class="border-bottom-0">اسم الوصفة</th>
                                    <th class="border-bottom-0">الوصف</th>
                                    <th class="border-bottom-0">عدد المكونات</th>
                                    <th class="border-bottom-0">التكلفة الإجمالية</th>
                                    <th class="border-bottom-0">الحالة</th>
                                    <th class="border-bottom-0">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- row closed -->

        @include('Inventory::recipes.modals')
@endsection

@section('js')
    <!-- DataTables JS -->
    <script src="{{ URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js') }}"></script>
    <script src="{{ URL::asset('assets/plugins/datatable/js/dataTables.dataTables.min.js') }}"></script>
    <script src="{{ URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js') }}"></script>
    <script src="{{ URL::asset('assets/plugins/datatable/js/responsive.dataTables.min.js') }}"></script>
    <script src="{{ URL::asset('assets/plugins/datatable/js/jquery.dataTables.js') }}"></script>
    <script src="{{ URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js') }}"></script>
    <script src="{{ URL::asset('assets/plugins/datatable/js/dataTables.buttons.min.js') }}"></script>
    <script src="{{ URL::asset('assets/plugins/datatable/js/buttons.bootstrap4.min.js') }}"></script>
    <script src="{{ URL::asset('assets/plugins/select2/js/select2.min.js') }}"></script>

    <script>
        $(document).ready(function() {
            // Initialize DataTable
            var table = $('#recipesTable').DataTable({
                processing: true,
                serverSide: true,
                ajax: "{{ route('inventory.api.recipes.datatable') }}",
                columns: [
                    {data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false},
                    {data: 'name', name: 'name'},
                    {data: 'description', name: 'description'},
                    {data: 'ingredients_count', name: 'ingredients_count'},
                    {data: 'total_cost', name: 'total_cost'},
                    {data: 'status', name: 'status'},
                    {data: 'action', name: 'action', orderable: false, searchable: false}
                ],
                language: {
                    url: "{{ asset('assets/plugins/datatable/Arabic.json') }}"
                }
            });

            // Initialize Select2
            $('.select2').select2();

            // Load products
            loadProducts();

            // Add Recipe
            $('#addRecipeForm').on('submit', function(e) {
                e.preventDefault();
                var formData = new FormData(this);
                
                // Add ingredients data
                var ingredients = [];
                $('#ingredientsContainer .ingredient-row').each(function() {
                    var productId = $(this).find('.ingredient-product').val();
                    var quantity = $(this).find('.ingredient-quantity').val();
                    var unitId = $(this).find('.ingredient-unit').val();
                    
                    if (productId && quantity && unitId) {
                        ingredients.push({
                            product_id: productId,
                            quantity: quantity,
                            unit_id: unitId
                        });
                    }
                });
                
                formData.append('ingredients', JSON.stringify(ingredients));
                
                $.ajax({
                    url: "{{ route('inventory.api.recipes.store') }}",
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        $('#addRecipeModal').modal('hide');
                        table.ajax.reload();
                        showAlert('success', 'تم إضافة الوصفة بنجاح');
                        $('#addRecipeForm')[0].reset();
                        $('#ingredientsContainer').empty();
                        addIngredientRow();
                    },
                    error: function(xhr) {
                        var errors = xhr.responseJSON.errors;
                        var errorMessage = '';
                        $.each(errors, function(key, value) {
                            errorMessage += value[0] + '<br>';
                        });
                        showAlert('error', errorMessage);
                    }
                });
            });

            // Edit Recipe
            $(document).on('click', '.edit-recipe', function() {
                var id = $(this).data('id');
                
                $.ajax({
                    url: "{{ route('inventory.api.recipes.show', ':id') }}".replace(':id', id),
                    type: 'GET',
                    success: function(response) {
                        $('#edit_recipe_id').val(response.id);
                        $('#edit_name').val(response.name);
                        $('#edit_description').val(response.description);
                        $('#edit_is_active').prop('checked', response.is_active);
                        
                        // Load ingredients
                        $('#editIngredientsContainer').empty();
                        if (response.ingredients && response.ingredients.length > 0) {
                            $.each(response.ingredients, function(index, ingredient) {
                                addEditIngredientRow(ingredient);
                            });
                        } else {
                            addEditIngredientRow();
                        }
                        
                        $('#editRecipeModal').modal('show');
                    }
                });
            });

            // Update Recipe
            $('#editRecipeForm').on('submit', function(e) {
                e.preventDefault();
                var id = $('#edit_recipe_id').val();
                var formData = new FormData(this);
                
                // Add ingredients data
                var ingredients = [];
                $('#editIngredientsContainer .ingredient-row').each(function() {
                    var productId = $(this).find('.ingredient-product').val();
                    var quantity = $(this).find('.ingredient-quantity').val();
                    var unitId = $(this).find('.ingredient-unit').val();
                    
                    if (productId && quantity && unitId) {
                        ingredients.push({
                            product_id: productId,
                            quantity: quantity,
                            unit_id: unitId
                        });
                    }
                });
                
                formData.append('ingredients', JSON.stringify(ingredients));
                
                $.ajax({
                    url: "{{ route('inventory.api.recipes.update', ':id') }}".replace(':id', id),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        $('#editRecipeModal').modal('hide');
                        table.ajax.reload();
                        showAlert('success', 'تم تحديث الوصفة بنجاح');
                    },
                    error: function(xhr) {
                        var errors = xhr.responseJSON.errors;
                        var errorMessage = '';
                        $.each(errors, function(key, value) {
                            errorMessage += value[0] + '<br>';
                        });
                        showAlert('error', errorMessage);
                    }
                });
            });

            // Delete Recipe
            $(document).on('click', '.delete-recipe', function() {
                var id = $(this).data('id');
                var name = $(this).data('name');
                
                if (confirm('هل أنت متأكد من حذف الوصفة "' + name + '"؟')) {
                    $.ajax({
                        url: "{{ route('inventory.api.recipes.destroy', ':id') }}".replace(':id', id),
                        type: 'DELETE',
                        data: {
                            _token: '{{ csrf_token() }}'
                        },
                        success: function(response) {
                            table.ajax.reload();
                            showAlert('success', 'تم حذف الوصفة بنجاح');
                        },
                        error: function(xhr) {
                            showAlert('error', xhr.responseJSON.message || 'حدث خطأ أثناء الحذف');
                        }
                    });
                }
            });

            // Add ingredient row
            $(document).on('click', '.add-ingredient', function() {
                addIngredientRow();
            });

            $(document).on('click', '.add-edit-ingredient', function() {
                addEditIngredientRow();
            });

            // Remove ingredient row
            $(document).on('click', '.remove-ingredient', function() {
                $(this).closest('.ingredient-row').remove();
            });

            // Handle product change to load units
            $(document).on('change', '.ingredient-product', function() {
                var productId = $(this).val();
                var unitSelect = $(this).closest('.ingredient-row').find('.ingredient-unit');
                
                if (productId) {
                    loadProductUnits(productId, unitSelect);
                } else {
                    unitSelect.html('<option value="">اختر الوحدة</option>');
                }
            });

            function addIngredientRow(ingredient = null) {
                var row = `
                    <div class="ingredient-row mb-3">
                        <div class="row">
                            <div class="col-md-4">
                                <select class="form-control select2 ingredient-product" name="ingredients[product_id][]" required>
                                    <option value="">اختر المنتج</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <input type="number" class="form-control ingredient-quantity" name="ingredients[quantity][]" 
                                       placeholder="الكمية" step="0.001" min="0" required>
                            </div>
                            <div class="col-md-3">
                                <select class="form-control ingredient-unit" name="ingredients[unit_id][]" required>
                                    <option value="">اختر الوحدة</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="button" class="btn btn-success add-ingredient">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <button type="button" class="btn btn-danger remove-ingredient">
                                    <i class="fas fa-minus"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                
                $('#ingredientsContainer').append(row);
                
                // Load products for the new row
                var productSelect = $('#ingredientsContainer .ingredient-row:last .ingredient-product');
                loadProductsForSelect(productSelect);
                
                if (ingredient) {
                    productSelect.val(ingredient.product_id).trigger('change');
                    $('#ingredientsContainer .ingredient-row:last .ingredient-quantity').val(ingredient.quantity);
                    setTimeout(function() {
                        $('#ingredientsContainer .ingredient-row:last .ingredient-unit').val(ingredient.unit_id);
                    }, 500);
                }
            }

            function addEditIngredientRow(ingredient = null) {
                var row = `
                    <div class="ingredient-row mb-3">
                        <div class="row">
                            <div class="col-md-4">
                                <select class="form-control select2 ingredient-product" name="ingredients[product_id][]" required>
                                    <option value="">اختر المنتج</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <input type="number" class="form-control ingredient-quantity" name="ingredients[quantity][]" 
                                       placeholder="الكمية" step="0.001" min="0" required>
                            </div>
                            <div class="col-md-3">
                                <select class="form-control ingredient-unit" name="ingredients[unit_id][]" required>
                                    <option value="">اختر الوحدة</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="button" class="btn btn-success add-edit-ingredient">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <button type="button" class="btn btn-danger remove-ingredient">
                                    <i class="fas fa-minus"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                
                $('#editIngredientsContainer').append(row);
                
                // Load products for the new row
                var productSelect = $('#editIngredientsContainer .ingredient-row:last .ingredient-product');
                loadProductsForSelect(productSelect);
                
                if (ingredient) {
                    productSelect.val(ingredient.product_id).trigger('change');
                    $('#editIngredientsContainer .ingredient-row:last .ingredient-quantity').val(ingredient.quantity);
                    setTimeout(function() {
                        $('#editIngredientsContainer .ingredient-row:last .ingredient-unit').val(ingredient.unit_id);
                    }, 500);
                }
            }

            function loadProducts() {
                $.ajax({
                    url: "{{ route('inventory.api.recipes.products') }}",
                    type: 'GET',
                    success: function(response) {
                        window.productsData = response;
                    }
                });
            }

            function loadProductsForSelect(selectElement) {
                if (window.productsData) {
                    var options = '<option value="">اختر المنتج</option>';
                    $.each(window.productsData, function(index, product) {
                        options += '<option value="' + product.id + '">' + product.name + '</option>';
                    });
                    selectElement.html(options);
                }
            }

            function loadProductUnits(productId, unitSelect) {
                var product = window.productsData.find(p => p.id == productId);
                if (product && product.unit) {
                    var options = '<option value="' + product.unit.id + '">' + product.unit.name + ' (' + product.unit.abbreviation + ')</option>';
                    unitSelect.html(options);
                }
            }

            function showAlert(type, message) {
                var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
                var alert = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                           message +
                           '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                           '<span aria-hidden="true">&times;</span>' +
                           '</button>' +
                           '</div>';
                
                $('.card-body').prepend(alert);
                
                setTimeout(function() {
                    $('.alert').fadeOut();
                }, 5000);
            }

            // Initialize with one ingredient row
            addIngredientRow();
        });
    </script>
@endsection