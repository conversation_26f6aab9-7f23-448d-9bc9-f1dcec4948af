<?php

namespace Modules\Inventory\Services;

use App\Models\Supplier;
use App\Models\Product;
use App\Models\PurchaseOrder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Exception;

class SupplierService
{
    /**
     * Get all suppliers with filtering and pagination
     */
    public function getAllSuppliers(array $filters = [])
    {
        $query = Supplier::query();

        // Apply filters
        if (isset($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('email', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('phone', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('contact_person', 'like', '%' . $filters['search'] . '%');
            });
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['category'])) {
            $query->where('category', $filters['category']);
        }

        if (isset($filters['rating_min'])) {
            $query->where('rating', '>=', $filters['rating_min']);
        }

        return $query->withCount(['purchaseOrders', 'products'])
            ->orderBy($filters['sort_by'] ?? 'name', $filters['sort_order'] ?? 'asc')
            ->paginate($filters['per_page'] ?? 15);
    }

    /**
     * Get supplier by ID
     */
    public function getSupplierById(string $id)
    {
        return Supplier::with([
            'purchaseOrders' => function ($query) {
                $query->latest()->limit(10);
            },
            'products' => function ($query) {
                $query->limit(20);
            }
        ])->findOrFail($id);
    }

    /**
     * Create new supplier
     */
    public function createSupplier(array $data)
    {
        DB::beginTransaction();
        try {
            $supplier = Supplier::create([
                'name' => $data['name'],
                'email' => $data['email'] ?? null,
                'phone' => $data['phone'] ?? null,
                'address' => $data['address'] ?? null,
                'city' => $data['city'] ?? null,
                'state' => $data['state'] ?? null,
                'postal_code' => $data['postal_code'] ?? null,
                'country' => $data['country'] ?? null,
                'contact_person' => $data['contact_person'] ?? null,
                'contact_phone' => $data['contact_phone'] ?? null,
                'contact_email' => $data['contact_email'] ?? null,
                'category' => $data['category'] ?? 'general',
                'payment_terms' => $data['payment_terms'] ?? null,
                'credit_limit' => $data['credit_limit'] ?? 0,
                'tax_number' => $data['tax_number'] ?? null,
                'website' => $data['website'] ?? null,
                'notes' => $data['notes'] ?? null,
                'status' => $data['status'] ?? 'active',
                'rating' => $data['rating'] ?? 0,
                'created_by' => Auth::id(),
            ]);

            DB::commit();
            return $supplier;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Update supplier
     */
    public function updateSupplier(string $id, array $data)
    {
        DB::beginTransaction();
        try {
            $supplier = Supplier::findOrFail($id);
            
            $supplier->update([
                'name' => $data['name'] ?? $supplier->name,
                'email' => $data['email'] ?? $supplier->email,
                'phone' => $data['phone'] ?? $supplier->phone,
                'address' => $data['address'] ?? $supplier->address,
                'city' => $data['city'] ?? $supplier->city,
                'state' => $data['state'] ?? $supplier->state,
                'postal_code' => $data['postal_code'] ?? $supplier->postal_code,
                'country' => $data['country'] ?? $supplier->country,
                'contact_person' => $data['contact_person'] ?? $supplier->contact_person,
                'contact_phone' => $data['contact_phone'] ?? $supplier->contact_phone,
                'contact_email' => $data['contact_email'] ?? $supplier->contact_email,
                'category' => $data['category'] ?? $supplier->category,
                'payment_terms' => $data['payment_terms'] ?? $supplier->payment_terms,
                'credit_limit' => $data['credit_limit'] ?? $supplier->credit_limit,
                'tax_number' => $data['tax_number'] ?? $supplier->tax_number,
                'website' => $data['website'] ?? $supplier->website,
                'notes' => $data['notes'] ?? $supplier->notes,
                'status' => $data['status'] ?? $supplier->status,
                'rating' => $data['rating'] ?? $supplier->rating,
                'updated_by' => Auth::id(),
            ]);

            DB::commit();
            return $supplier;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Delete supplier
     */
    public function deleteSupplier(string $id)
    {
        $supplier = Supplier::findOrFail($id);
        
        // Check if supplier has active purchase orders
        $activePOs = $supplier->purchaseOrders()
            ->whereIn('status', ['pending', 'approved', 'partial'])
            ->count();
            
        if ($activePOs > 0) {
            throw new Exception('Cannot delete supplier with active purchase orders.');
        }

        $supplier->delete();
    }

    /**
     * Get supplier products
     */
    public function getSupplierProducts(string $id)
    {
        $supplier = Supplier::findOrFail($id);
        
        return $supplier->products()
            ->with(['unit', 'branchInventories' => function ($query) {
                $query->where('branch_id', $this->getCurrentBranchId());
            }])
            ->paginate(20);
    }

    /**
     * Get supplier purchase orders
     */
    public function getSupplierPurchaseOrders(string $id)
    {
        $supplier = Supplier::findOrFail($id);
        
        return $supplier->purchaseOrders()
            ->with(['branch', 'items.product'])
            ->orderBy('created_at', 'desc')
            ->paginate(15);
    }

    /**
     * Get supplier performance metrics
     */
    public function getSupplierPerformance(string $id)
    {
        $supplier = Supplier::findOrFail($id);
        
        $purchaseOrders = $supplier->purchaseOrders();
        
        $totalOrders = $purchaseOrders->count();
        $completedOrders = $purchaseOrders->where('status', 'completed')->count();
        $totalValue = $purchaseOrders->sum('total_amount');
        $averageDeliveryTime = $this->calculateAverageDeliveryTime($id);
        $onTimeDeliveryRate = $this->calculateOnTimeDeliveryRate($id);
        $qualityRating = $this->calculateQualityRating($id);
        
        return [
            'total_orders' => $totalOrders,
            'completed_orders' => $completedOrders,
            'completion_rate' => $totalOrders > 0 ? ($completedOrders / $totalOrders) * 100 : 0,
            'total_value' => $totalValue,
            'average_order_value' => $totalOrders > 0 ? $totalValue / $totalOrders : 0,
            'average_delivery_time' => $averageDeliveryTime,
            'on_time_delivery_rate' => $onTimeDeliveryRate,
            'quality_rating' => $qualityRating,
            'overall_rating' => $supplier->rating,
            'last_order_date' => $purchaseOrders->latest()->value('created_at'),
            'payment_terms' => $supplier->payment_terms,
            'credit_utilization' => $this->calculateCreditUtilization($id),
        ];
    }

    /**
     * Add product to supplier
     */
    public function addProductToSupplier(string $supplierId, array $productData)
    {
        $supplier = Supplier::findOrFail($supplierId);
        
        // Create or find product
        $product = Product::firstOrCreate(
            ['sku' => $productData['sku']],
            [
                'name' => $productData['name'],
                'description' => $productData['description'] ?? null,
                'category' => $productData['category'] ?? 'general',
                'unit_id' => $productData['unit_id'],
                'is_active' => true,
            ]
        );
        
        // Attach to supplier if not already attached
        if (!$supplier->products()->where('product_id', $product->id)->exists()) {
            $supplier->products()->attach($product->id, [
                'supplier_sku' => $productData['supplier_sku'] ?? null,
                'unit_cost' => $productData['unit_cost'] ?? 0,
                'minimum_order_quantity' => $productData['minimum_order_quantity'] ?? 1,
                'lead_time_days' => $productData['lead_time_days'] ?? 0,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
        
        return $product;
    }

    /**
     * Get supplier categories
     */
    public function getSupplierCategories()
    {
        return Supplier::distinct('category')
            ->whereNotNull('category')
            ->pluck('category')
            ->sort()
            ->values();
    }

    /**
     * Get supplier analytics for overview cards
     */
    public function getSupplierAnalytics()
    {
        $totalSuppliers = Supplier::count();
        $activeSuppliers = Supplier::where('status', 'active')->count();
        $pendingSuppliers = Supplier::where('status', 'pending')->count();
        
        // Count total products from all suppliers
        $totalProducts = DB::table('supplier_products')
            ->distinct('product_id')
            ->count();
        
        return [
            'total_suppliers' => $totalSuppliers,
            'active_suppliers' => $activeSuppliers,
            'pending_suppliers' => $pendingSuppliers,
            'total_products' => $totalProducts,
        ];
    }

    /**
     * Private helper methods
     */
    private function getCurrentBranchId()
    {
        return Auth::user()->branch_id ?? 1;
    }

    private function calculateAverageDeliveryTime(string $supplierId)
    {
        $completedOrders = PurchaseOrder::where('supplier_id', $supplierId)
            ->where('status', 'completed')
            ->whereNotNull('delivered_at')
            ->get();
            
        if ($completedOrders->isEmpty()) {
            return 0;
        }
        
        $totalDays = $completedOrders->sum(function ($order) {
            return $order->created_at->diffInDays($order->delivered_at);
        });
        
        return round($totalDays / $completedOrders->count(), 1);
    }

    private function calculateOnTimeDeliveryRate(string $supplierId)
    {
        $completedOrders = PurchaseOrder::where('supplier_id', $supplierId)
            ->where('status', 'completed')
            ->whereNotNull('delivered_at')
            ->whereNotNull('expected_delivery_date')
            ->get();
            
        if ($completedOrders->isEmpty()) {
            return 0;
        }
        
        $onTimeOrders = $completedOrders->filter(function ($order) {
            return $order->delivered_at <= $order->expected_delivery_date;
        })->count();
        
        return round(($onTimeOrders / $completedOrders->count()) * 100, 1);
    }

    private function calculateQualityRating(string $supplierId)
    {
        // This would typically be based on quality feedback from received orders
        // For now, return the supplier's overall rating
        return Supplier::find($supplierId)->rating ?? 0;
    }

    private function calculateCreditUtilization(string $supplierId)
    {
        $supplier = Supplier::find($supplierId);
        
        if (!$supplier || $supplier->credit_limit <= 0) {
            return 0;
        }
        
        $outstandingAmount = PurchaseOrder::where('supplier_id', $supplierId)
            ->whereIn('status', ['approved', 'partial'])
            ->sum('total_amount');
            
        return round(($outstandingAmount / $supplier->credit_limit) * 100, 1);
    }
}