<?php

require_once 'vendor/autoload.php';

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

function testEndpoint($kernel, $url, $method = 'GET', $headers = [], $description = '') {
    echo "\n=== $description ===\n";
    echo "Testing: $method $url\n";
    
    $request = Request::create($url, $method);
    
    // Add headers
    foreach ($headers as $key => $value) {
        $request->headers->set($key, $value);
    }
    
    try {
        $response = $kernel->handle($request);
        echo "Status Code: " . $response->getStatusCode() . "\n";
        echo "Response: " . $response->getContent() . "\n";
        
        $kernel->terminate($request, $response);
        
        return $response->getStatusCode();
    } catch (Exception $e) {
        echo "Exception: " . $e->getMessage() . "\n";
        return 500;
    }
}

// Test public featured offers endpoint (should work without auth)
testEndpoint($kernel, '/menu/offers/featured', 'GET', [
    'Accept' => 'application/json',
    'Content-Type' => 'application/json'
], 'PUBLIC FEATURED OFFERS TEST');

// Test authenticated featured offers endpoint (will fail without proper auth)
testEndpoint($kernel, '/api/menu/offers/featured', 'GET', [
    'Authorization' => 'Bearer test-token',
    'Accept' => 'application/json',
    'Content-Type' => 'application/json'
], 'AUTHENTICATED FEATURED OFFERS TEST');

// Test other public endpoints
testEndpoint($kernel, '/menu/categories/public', 'GET', [
    'Accept' => 'application/json'
], 'PUBLIC CATEGORIES TEST');

testEndpoint($kernel, '/menu/menus/public', 'GET', [
    'Accept' => 'application/json'
], 'PUBLIC MENUS TEST');

testEndpoint($kernel, '/menu/menu-items/public', 'GET', [
    'Accept' => 'application/json'
], 'PUBLIC MENU ITEMS TEST');

testEndpoint($kernel, '/menu/banners/display', 'GET', [
    'Accept' => 'application/json'
], 'PUBLIC BANNERS TEST');

testEndpoint($kernel, '/menu/events/upcoming', 'GET', [
    'Accept' => 'application/json'
], 'PUBLIC EVENTS TEST');

echo "\n=== TEST COMPLETE ===\n";
echo "Public endpoints should return 200 with success messages.\n";
echo "Authenticated endpoints will return 401 without proper authentication.\n";