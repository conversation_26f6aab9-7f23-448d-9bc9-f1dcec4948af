<?php

namespace Modules\Delivery\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\Orders\Models\Order;
use App\Models\User;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Facades\DB;

class DeliveryReviewWebController extends Controller
{
    /**
     * Display a listing of delivery reviews.
     */
    public function index()
    {
        $deliveryPersonnel = User::whereHas('roles', function ($query) {
            $query->where('name', 'delivery_personnel');
        })->where('status', 'active')->get();
        
        return view('delivery::reviews.index', compact('deliveryPersonnel'));
    }

    /**
     * Display the specified review.
     */
    public function show($orderId)
    {
        $order = Order::with(['customer', 'deliveryPersonnel', 'deliveryReview'])
            ->findOrFail($orderId);
        
        return view('delivery::reviews.show', compact('order'));
    }

    /**
     * Get reviews data for DataTable.
     */
    public function getReviewsData(Request $request)
    {
        $query = Order::with(['customer', 'deliveryPersonnel'])
            ->whereNotNull('delivery_rating')
            ->select(['id', 'order_number', 'customer_id', 'delivery_personnel_id', 
                     'delivery_rating', 'delivery_review', 'delivery_date', 'created_at']);

        // Apply filters
        if ($request->filled('delivery_personnel_id')) {
            $query->where('delivery_personnel_id', $request->delivery_personnel_id);
        }

        if ($request->filled('rating')) {
            $query->where('delivery_rating', $request->rating);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('delivery_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('delivery_date', '<=', $request->date_to);
        }

        return DataTables::of($query)
            ->addIndexColumn()
            ->addColumn('order_number', function ($order) {
                return '#' . $order->order_number;
            })
            ->addColumn('customer_name', function ($order) {
                return $order->customer ? $order->customer->name : '-';
            })
            ->addColumn('delivery_personnel_name', function ($order) {
                return $order->deliveryPersonnel ? $order->deliveryPersonnel->name : '-';
            })
            ->addColumn('rating_stars', function ($order) {
                $stars = '';
                for ($i = 1; $i <= 5; $i++) {
                    if ($i <= $order->delivery_rating) {
                        $stars .= '<i class="fa fa-star text-warning"></i>';
                    } else {
                        $stars .= '<i class="fa fa-star-o text-muted"></i>';
                    }
                }
                return '<div class="rating-stars">' . $stars . ' <span class="ml-2">(' . $order->delivery_rating . '/5)</span></div>';
            })
            ->addColumn('review_text', function ($order) {
                if ($order->delivery_review) {
                    $review = strlen($order->delivery_review) > 100 
                        ? substr($order->delivery_review, 0, 100) . '...' 
                        : $order->delivery_review;
                    return '<div class="review-text" title="' . htmlspecialchars($order->delivery_review) . '">' . $review . '</div>';
                }
                return '<span class="text-muted">لا يوجد تعليق</span>';
            })
            ->addColumn('delivery_date_formatted', function ($order) {
                return $order->delivery_date ? $order->delivery_date->format('Y-m-d H:i') : '-';
            })
            ->addColumn('action', function ($order) {
                $actions = '<div class="btn-group" role="group">';
                $actions .= '<a href="' . route('delivery.reviews.show', $order->id) . '" class="btn btn-sm btn-info" title="عرض التفاصيل"><i class="fa fa-eye"></i></a>';
                $actions .= '<a href="' . route('orders.show', $order->id) . '" class="btn btn-sm btn-primary" title="عرض الطلب"><i class="fa fa-shopping-cart"></i></a>';
                $actions .= '</div>';
                
                return $actions;
            })
            ->rawColumns(['rating_stars', 'review_text', 'action'])
            ->make(true);
    }

    /**
     * Get delivery statistics.
     */
    public function getStatistics(Request $request)
    {
        $dateFrom = $request->date_from ?? now()->subDays(30)->format('Y-m-d');
        $dateTo = $request->date_to ?? now()->format('Y-m-d');

        $stats = [
            'total_reviews' => Order::whereNotNull('delivery_rating')
                ->whereBetween('delivery_date', [$dateFrom, $dateTo])
                ->count(),
            
            'average_rating' => Order::whereNotNull('delivery_rating')
                ->whereBetween('delivery_date', [$dateFrom, $dateTo])
                ->avg('delivery_rating'),
            
            'rating_distribution' => Order::whereNotNull('delivery_rating')
                ->whereBetween('delivery_date', [$dateFrom, $dateTo])
                ->select('delivery_rating', DB::raw('count(*) as count'))
                ->groupBy('delivery_rating')
                ->orderBy('delivery_rating', 'desc')
                ->get(),
            
            'top_rated_personnel' => Order::whereNotNull('delivery_rating')
                ->whereBetween('delivery_date', [$dateFrom, $dateTo])
                ->with('deliveryPersonnel')
                ->select('delivery_personnel_id', DB::raw('AVG(delivery_rating) as avg_rating'), DB::raw('COUNT(*) as total_deliveries'))
                ->groupBy('delivery_personnel_id')
                ->having('total_deliveries', '>=', 5)
                ->orderBy('avg_rating', 'desc')
                ->limit(10)
                ->get(),
            
            'recent_reviews' => Order::whereNotNull('delivery_rating')
                ->with(['customer', 'deliveryPersonnel'])
                ->whereBetween('delivery_date', [$dateFrom, $dateTo])
                ->orderBy('delivery_date', 'desc')
                ->limit(5)
                ->get()
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Get personnel performance report.
     */
    public function getPersonnelPerformance(Request $request, $personnelId)
    {
        $dateFrom = $request->date_from ?? now()->subDays(30)->format('Y-m-d');
        $dateTo = $request->date_to ?? now()->format('Y-m-d');

        $personnel = User::findOrFail($personnelId);
        
        $performance = [
            'personnel' => $personnel,
            'total_deliveries' => Order::where('delivery_personnel_id', $personnelId)
                ->whereBetween('delivery_date', [$dateFrom, $dateTo])
                ->count(),
            
            'total_reviews' => Order::where('delivery_personnel_id', $personnelId)
                ->whereNotNull('delivery_rating')
                ->whereBetween('delivery_date', [$dateFrom, $dateTo])
                ->count(),
            
            'average_rating' => Order::where('delivery_personnel_id', $personnelId)
                ->whereNotNull('delivery_rating')
                ->whereBetween('delivery_date', [$dateFrom, $dateTo])
                ->avg('delivery_rating'),
            
            'rating_distribution' => Order::where('delivery_personnel_id', $personnelId)
                ->whereNotNull('delivery_rating')
                ->whereBetween('delivery_date', [$dateFrom, $dateTo])
                ->select('delivery_rating', DB::raw('count(*) as count'))
                ->groupBy('delivery_rating')
                ->orderBy('delivery_rating', 'desc')
                ->get(),
            
            'recent_reviews' => Order::where('delivery_personnel_id', $personnelId)
                ->whereNotNull('delivery_rating')
                ->with('customer')
                ->whereBetween('delivery_date', [$dateFrom, $dateTo])
                ->orderBy('delivery_date', 'desc')
                ->limit(10)
                ->get()
        ];

        return response()->json([
            'success' => true,
            'data' => $performance
        ]);
    }
}