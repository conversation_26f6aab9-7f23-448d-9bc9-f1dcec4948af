<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('offers', function (Blueprint $table) {
            // Add missing columns that are required by StoreOfferRequest
            $table->decimal('min_order_amount', 10, 2)->nullable()->after('max_discount_amount');
            $table->integer('buy_quantity')->nullable()->after('min_order_amount');
            $table->integer('get_quantity')->nullable()->after('buy_quantity');
            $table->json('applicable_items')->nullable()->after('get_quantity');
            $table->json('excluded_items')->nullable()->after('applicable_items');
            $table->json('applicable_categories')->nullable()->after('excluded_items');
            $table->json('excluded_categories')->nullable()->after('applicable_categories');
            $table->json('combo_items')->nullable()->after('excluded_categories');
            $table->decimal('combo_price', 10, 2)->nullable()->after('combo_items');
            $table->string('promo_code', 50)->nullable()->unique()->after('combo_price');
            $table->boolean('requires_promo_code')->default(false)->after('promo_code');
            $table->time('start_time')->nullable()->after('end_date');
            $table->time('end_time')->nullable()->after('start_time');
            $table->json('available_days')->nullable()->after('end_time');
            $table->json('customer_eligibility')->nullable()->after('available_days');
            $table->integer('usage_limit_per_customer')->nullable()->after('customer_eligibility');
            $table->integer('total_usage_limit')->nullable()->after('usage_limit_per_customer');
            $table->boolean('is_stackable')->default(false)->after('total_usage_limit');
            $table->json('stackable_with')->nullable()->after('is_stackable');
            $table->string('priority')->after('stackable_with')->default('medium');
            $table->text('terms_conditions')->nullable()->after('priority');
            $table->string('image_url')->nullable()->after('terms_conditions');
            $table->string('banner_image')->nullable()->after('image_url');
            $table->json('notification_settings')->nullable()->after('banner_image');
            $table->boolean('auto_apply')->default(false)->after('notification_settings');
            $table->json('auto_apply_conditions')->nullable()->after('auto_apply');
            $table->boolean('is_featured')->default(false)->after('auto_apply_conditions');
            $table->boolean('is_public')->default(false)->after('is_featured');
            $table->integer('current_usage_count')->default(0)->after('is_public');
            $table->integer('sort_order')->default(0)->after('current_usage_count');
            $table->json('analytics_data')->nullable()->after('sort_order');
            $table->json('metadata')->nullable()->after('analytics_data');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('offers', function (Blueprint $table) {
            $table->dropColumn([
                'min_order_amount', 'buy_quantity', 'get_quantity', 'applicable_items',
                'excluded_items', 'applicable_categories', 'excluded_categories', 'combo_items',
                'combo_price', 'promo_code', 'requires_promo_code', 'start_time', 'end_time',
                'available_days', 'customer_eligibility', 'usage_limit_per_customer',
                'total_usage_limit', 'is_stackable', 'stackable_with', 'priority',
                'terms_conditions', 'image_url', 'banner_image', 'notification_settings',
                'auto_apply', 'auto_apply_conditions', 'is_featured', 'is_public',
                'current_usage_count', 'sort_order', 'analytics_data', 'metadata'
            ]);
        });
    }
};
