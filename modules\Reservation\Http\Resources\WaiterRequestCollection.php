<?php

namespace Modules\Reservation\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class WaiterRequestCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
            'meta' => [
                'total' => $this->collection->count(),
                'status_counts' => [
                    'pending' => $this->collection->where('status', 'pending')->count(),
                    'completed' => $this->collection->where('status', 'completed')->count(),
                    'cancelled' => $this->collection->where('status', 'cancelled')->count(),
                ],
            ],
        ];
    }
}