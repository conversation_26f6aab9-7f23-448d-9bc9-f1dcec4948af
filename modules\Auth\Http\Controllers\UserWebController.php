<?php

namespace Modules\Auth\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Tenant;
use App\Models\Branch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class UserWebController extends Controller
{
    /**
     * Display a listing of users.
     */
    public function index()
    {
        $tenants = Tenant::where('status', 'active')->get();
        $branches = Branch::where('status', 'active')->get();
        $roles = Role::all();
        
        return view('Auth::users.index', compact('tenants', 'branches', 'roles'));
    }

    /**
     * Show the form for creating a new user.
     */
    public function create()
    {
        $tenants = Tenant::where('status', 'active')->get();
        $branches = Branch::where('status', 'active')->get();
        $roles = Role::all();
        
        return view('Auth::users.create', compact('tenants', 'branches', 'roles'));
    }

    /**
     * Store a newly created user.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:8|confirmed',
            'tenant_id' => 'required|exists:tenants,id',
            'branch_id' => 'nullable|exists:branches,id',
            'employee_id' => 'nullable|string|unique:users,employee_id',
            'position' => 'nullable|string|max:100',
            'department' => 'nullable|string|max:100',
            'hourly_rate' => 'nullable|numeric|min:0',
            'salary' => 'nullable|numeric|min:0',
            'base_salary' => 'nullable|numeric|min:0',
            'roles' => 'nullable|array',
            'roles.*' => 'exists:roles,name',
        ]);

        try {
            DB::beginTransaction();

            $userData = $request->except(['password_confirmation', 'roles']);
            $userData['password'] = Hash::make($request->password);
            $userData['is_active'] = true;
            $userData['tenant_id'] = $request->tenant_id;

            $user = User::create($userData);

            // Assign roles if provided
            if ($request->filled('roles')) {
                $user->assignRole($request->roles);
            }

            DB::commit();
            
            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'تم إنشاء المستخدم بنجاح'
                ]);
            }
            
            return redirect()->route('users.index')
                ->with('success', 'تم إنشاء المستخدم بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'حدث خطأ أثناء إنشاء المستخدم: ' . $e->getMessage(),
                    'errors' => $e->getMessage()
                ], 422);
            }
            
            return back()->withInput()
                ->with('error', 'حدث خطأ أثناء إنشاء المستخدم: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified user.
     */
    public function show(User $user)
    {
        $user->load(['tenant', 'branch', 'roles', 'permissions']);
        
        if (request()->ajax()) {
            return response()->json([
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'tenant_id' => $user->tenant_id,
                'branch_id' => $user->branch_id,
                'employee_id' => $user->employee_id,
                'position' => $user->position,
                'department' => $user->department,
                'hourly_rate' => $user->hourly_rate,
                'salary' => $user->salary,
                'base_salary' => $user->base_salary,
                'is_active' => $user->is_active,
                'roles' => $user->roles,
                'tenant' => $user->tenant,
                'branch' => $user->branch,
            ]);
        }
        
        return view('Auth::users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified user.
     */
    public function edit(User $user)
    {
        $tenants = Tenant::where('status', 'active')->get();
        $branches = Branch::where('status', 'active')->get();
        $roles = Role::all();
        $userRoles = $user->roles->pluck('name')->toArray();
        
        return view('Auth::users.edit', compact('user', 'tenants', 'branches', 'roles', 'userRoles'));
    }

    /**
     * Update the specified user.
     */
    public function update(Request $request, User $user)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $user->id,
            'password' => 'nullable|string|min:8|confirmed',
            'tenant_id' => 'required|exists:tenants,id',
            'branch_id' => 'nullable|exists:branches,id',
            'employee_id' => 'nullable|string|unique:users,employee_id,' . $user->id,
            'position' => 'nullable|string|max:100',
            'department' => 'nullable|string|max:100',
            'hourly_rate' => 'nullable|numeric|min:0',
            'salary' => 'nullable|numeric|min:0',
            'base_salary' => 'nullable|numeric|min:0',
            'roles' => 'nullable|array',
            'roles.*' => 'exists:roles,name',
        ]);

        try {
            DB::beginTransaction();

            $userData = $request->except(['password_confirmation', 'roles']);
            
            if ($request->filled('password')) {
                $userData['password'] = Hash::make($request->password);
            } else {
                unset($userData['password']);
            }

            $user->update($userData);

            // Sync roles
            if ($request->has('roles')) {
                $user->syncRoles($request->roles ?? []);
            }

            DB::commit();
            
            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'تم تحديث بيانات المستخدم بنجاح'
                ]);
            }
            
            return redirect()->route('users.show', $user)
                ->with('success', 'تم تحديث بيانات المستخدم بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'حدث خطأ أثناء تحديث المستخدم: ' . $e->getMessage(),
                    'errors' => $e->getMessage()
                ], 422);
            }
            
            return back()->withInput()
                ->with('error', 'حدث خطأ أثناء تحديث المستخدم: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified user.
     */
    public function destroy(User $user)
    {
        try {
            // Check if user has any related data that prevents deletion
            // Add your business logic here

            $user->delete();
            
            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'تم حذف المستخدم بنجاح'
                ]);
            }
            
            return redirect()->route('users.index')
                ->with('success', 'تم حذف المستخدم بنجاح');
        } catch (\Exception $e) {
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'حدث خطأ أثناء حذف المستخدم: ' . $e->getMessage()
                ], 500);
            }
            
            return back()->with('error', 'حدث خطأ أثناء حذف المستخدم: ' . $e->getMessage());
        }
    }

    /**
     * Activate user.
     */
    public function activate(User $user)
    {
        try {
            $user->update(['is_active' => true]);
            
            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'تم تفعيل المستخدم بنجاح'
                ]);
            }
            
            return back()->with('success', 'تم تفعيل المستخدم بنجاح');
        } catch (\Exception $e) {
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'حدث خطأ أثناء تفعيل المستخدم: ' . $e->getMessage()
                ], 500);
            }
            
            return back()->with('error', 'حدث خطأ أثناء تفعيل المستخدم: ' . $e->getMessage());
        }
    }

    /**
     * Deactivate user.
     */
    public function deactivate(User $user)
    {
        try {
            $user->update(['is_active' => false]);
            
            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'تم إلغاء تفعيل المستخدم بنجاح'
                ]);
            }
            
            return back()->with('success', 'تم إلغاء تفعيل المستخدم بنجاح');
        } catch (\Exception $e) {
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'حدث خطأ أثناء إلغاء تفعيل المستخدم: ' . $e->getMessage()
                ], 500);
            }
            
            return back()->with('error', 'حدث خطأ أثناء إلغاء تفعيل المستخدم: ' . $e->getMessage());
        }
    }

    /**
     * Reset user password.
     */
    public function resetPassword(User $user)
    {
        try {
            $newPassword = 'password123'; // You might want to generate a random password
            $user->update(['password' => Hash::make($newPassword)]);
            
            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'تم إعادة تعيين كلمة المرور بنجاح. كلمة المرور الجديدة: ' . $newPassword
                ]);
            }
            
            return back()->with('success', 'تم إعادة تعيين كلمة المرور بنجاح. كلمة المرور الجديدة: ' . $newPassword);
        } catch (\Exception $e) {
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'حدث خطأ أثناء إعادة تعيين كلمة المرور: ' . $e->getMessage()
                ], 500);
            }
            
            return back()->with('error', 'حدث خطأ أثناء إعادة تعيين كلمة المرور: ' . $e->getMessage());
        }
    }

    /**
     * Get users data for DataTable.
     */
    public function getUsersData(Request $request)
    {
        $query = User::with(['tenant', 'branch', 'roles'])
            ->select([
                'id',
                'name', 
                'email',
                'tenant_id',
                'branch_id',
                'salary',
                'is_active',
                // 'email_verified_at',
                // 'created_at',
                // 'updated_at'
            ]);
        // Apply filters
        if ($request->filled('tenant_id')) {
            $query->where('tenant_id', $request->tenant_id);
        }

        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        if ($request->filled('role')) {
            $query->whereHas('roles', function ($q) use ($request) {
                $q->where('name', $request->role);
            });
        }

        if ($request->filled('is_active')) {
            $query->where('is_active', $request->is_active);
        }

        return DataTables::of($query)
            ->addIndexColumn()
            ->addColumn('tenant_name', function ($user) {
                return $user->tenant ? $user->tenant->name : '-';
            })
            ->addColumn('branch_name', function ($user) {
                return $user->branch ? $user->branch->name : '-';
            })
            ->addColumn('roles', function ($user) {
                return $user->roles->map(function ($role) {
                    return '<span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">' . $role->name . '</span>';
                })->implode(' ');
            })
            ->addColumn('status_badge', function ($user) {
                return $user->is_active 
                    ? '<span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">نشط</span>'
                    : '<span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">غير نشط</span>';
            })

           ->addColumn('action', function ($user) {
                return '
                <div class="flex flex-wrap gap-1 justify-end">
                    <a href="' . route('users.show', $user) . '" class="bg-blue-500 hover:bg-blue-600 text-white text-xs px-2 py-1 rounded" title="عرض">
                        <i class="fa fa-eye"></i>
                    </a>
                    <button onclick="editUser(' . $user->id . ')" class="bg-yellow-500 hover:bg-yellow-600 text-white text-xs px-2 py-1 rounded" title="تعديل">
                        <i class="fa fa-edit"></i>
                    </button>
                    <button onclick="assignRoles(' . $user->id . ')" class="bg-indigo-500 hover:bg-indigo-600 text-white text-xs px-2 py-1 rounded" title="تعيين الأدوار">
                        <i class="fa fa-users"></i>
                    </button>
                    ' . ($user->is_active
                        ? '<button onclick="changeStatus(' . $user->id . ', \'deactivate\')" class="bg-orange-500 hover:bg-orange-600 text-white text-xs px-2 py-1 rounded" title="إلغاء تفعيل"><i class="fa fa-pause"></i></button>'
                        : '<button onclick="changeStatus(' . $user->id . ', \'activate\')" class="bg-green-500 hover:bg-green-600 text-white text-xs px-2 py-1 rounded" title="تفعيل"><i class="fa fa-play"></i></button>'
                    ) . '
                    <button onclick="resetPassword(' . $user->id . ')" class="bg-gray-500 hover:bg-gray-600 text-white text-xs px-2 py-1 rounded" title="إعادة تعيين كلمة المرور">
                        <i class="fa fa-key"></i>
                    </button>
                    <button onclick="deleteUser(' . $user->id . ')" class="bg-red-500 hover:bg-red-600 text-white text-xs px-2 py-1 rounded" title="حذف">
                        <i class="fa fa-trash"></i>
                    </button>
                </div>';
            })

            ->rawColumns(['roles', 'status_badge', 'action'])
            ->make(true);
    }

    /**
     * Show user roles page.
     */
    public function roles(User $user)
    {
        $roles = Role::all();
        $userRoles = $user->roles->pluck('name')->toArray();
        
        if (request()->ajax()) {
            return response()->json([
                'user' => $user,
                'roles' => $roles,
                'userRoles' => $userRoles
            ]);
        }
        
        return view('Auth::users.roles', compact('user', 'roles', 'userRoles'));
    }

    /**
     * Assign roles to user.
     */
    public function assignRoles(Request $request, User $user)
    {
        $request->validate([
            'roles' => 'nullable|array',
            'roles.*' => 'exists:roles,name',
        ]);

        try {
            $user->syncRoles($request->roles ?? []);
            
            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'تم تحديث أدوار المستخدم بنجاح'
                ]);
            }
            
            return back()->with('success', 'تم تحديث أدوار المستخدم بنجاح');
        } catch (\Exception $e) {
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'حدث خطأ أثناء تحديث الأدوار: ' . $e->getMessage()
                ], 500);
            }
            
            return back()->with('error', 'حدث خطأ أثناء تحديث الأدوار: ' . $e->getMessage());
        }
    }

    /**
     * Show user permissions page.
     */
    public function permissions(User $user)
    {
        $permissions = Permission::all()->groupBy(function ($permission) {
            return explode('.', $permission->name)[0] ?? 'other';
        });
        
        $userPermissions = $user->getAllPermissions()->pluck('name')->toArray();
        
        return view('Auth::users.permissions', compact('user', 'permissions', 'userPermissions'));
    }

    /**
     * Assign permissions to user.
     */
    public function assignPermissions(Request $request, User $user)
    {
        $request->validate([
            'permissions' => 'nullable|array',
            'permissions.*' => 'exists:permissions,name',
        ]);

        try {
            $user->syncPermissions($request->permissions ?? []);
            
            return back()->with('success', 'تم تحديث صلاحيات المستخدم بنجاح');
        } catch (\Exception $e) {
            return back()->with('error', 'حدث خطأ أثناء تحديث الصلاحيات: ' . $e->getMessage());
        }
    }
}
