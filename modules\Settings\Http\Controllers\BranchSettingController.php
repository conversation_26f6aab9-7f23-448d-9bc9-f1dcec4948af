<?php

namespace Modules\Settings\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Settings\BranchSetting;
use App\Services\SettingsService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class BranchSettingController extends Controller
{
    protected $settingsService;

    public function __construct(SettingsService $settingsService)
    {
        $this->settingsService = $settingsService;
    }

    public function index(Request $request): JsonResponse
    {
        $tenantId = $request->user()->tenant_id;
        $branchId = $request->input('branch_id');
        $category = $request->input('category');

        $branchSettings = $this->settingsService->getBranchSettings($tenantId, $branchId, $category);

        return response()->json([
            'success' => true,
            'data' => $branchSettings
        ]);
    }

    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'branch_id' => 'required|exists:branches,id',
            'category' => 'required|string|max:50',
            'key' => 'required|string|max:100',
            'value' => 'required',
            'data_type' => 'required|in:string,integer,float,boolean,json,array',
            'description' => 'nullable|string|max:255',
            'is_public' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $tenantId = $request->user()->tenant_id;

            // Check if setting already exists
            $existing = BranchSetting::where('tenant_id', $tenantId)
                ->where('branch_id', $request->input('branch_id'))
                ->where('category', $request->input('category'))
                ->where('key', $request->input('key'))
                ->first();

            if ($existing) {
                return response()->json([
                    'success' => false,
                    'message' => 'Setting already exists for this branch and category'
                ], 409);
            }

            $branchSetting = BranchSetting::create([
                'tenant_id' => $tenantId,
                'branch_id' => $request->input('branch_id'),
                'category' => $request->input('category'),
                'key' => $request->input('key'),
                'value' => $request->input('value'),
                'data_type' => $request->input('data_type'),
                'description' => $request->input('description'),
                'is_public' => $request->input('is_public', false),
            ]);

            return response()->json([
                'success' => true,
                'data' => $branchSetting,
                'message' => 'Branch setting created successfully'
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create branch setting: ' . $e->getMessage()
            ], 500);
        }
    }

    public function show(Request $request, $id): JsonResponse
    {
        $tenantId = $request->user()->tenant_id;
        
        $branchSetting = BranchSetting::where('tenant_id', $tenantId)
            ->where('id', $id)
            ->first();

        if (!$branchSetting) {
            return response()->json([
                'success' => false,
                'message' => 'Branch setting not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $branchSetting
        ]);
    }

    public function update(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'value' => 'required',
            'description' => 'nullable|string|max:255',
            'is_public' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $tenantId = $request->user()->tenant_id;
            
            $branchSetting = BranchSetting::where('tenant_id', $tenantId)
                ->where('id', $id)
                ->first();

            if (!$branchSetting) {
                return response()->json([
                    'success' => false,
                    'message' => 'Branch setting not found'
                ], 404);
            }

            $branchSetting->update($request->only([
                'value', 'description', 'is_public'
            ]));

            return response()->json([
                'success' => true,
                'data' => $branchSetting,
                'message' => 'Branch setting updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update branch setting: ' . $e->getMessage()
            ], 500);
        }
    }

    public function destroy(Request $request, $id): JsonResponse
    {
        try {
            $tenantId = $request->user()->tenant_id;
            
            $branchSetting = BranchSetting::where('tenant_id', $tenantId)
                ->where('id', $id)
                ->first();

            if (!$branchSetting) {
                return response()->json([
                    'success' => false,
                    'message' => 'Branch setting not found'
                ], 404);
            }

            $branchSetting->delete();

            return response()->json([
                'success' => true,
                'message' => 'Branch setting deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete branch setting: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getByCategory(Request $request, $branchId, $category): JsonResponse
    {
        $tenantId = $request->user()->tenant_id;
        
        $settings = BranchSetting::where('tenant_id', $tenantId)
            ->where('branch_id', $branchId)
            ->where('category', $category)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $settings
        ]);
    }

    public function bulkUpdate(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'branch_id' => 'required|exists:branches,id',
            'settings' => 'required|array',
            'settings.*.category' => 'required|string|max:50',
            'settings.*.key' => 'required|string|max:100',
            'settings.*.value' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $tenantId = $request->user()->tenant_id;
            $branchId = $request->input('branch_id');
            $settings = $request->input('settings');
            $updated = [];

            foreach ($settings as $settingData) {
                $branchSetting = BranchSetting::where('tenant_id', $tenantId)
                    ->where('branch_id', $branchId)
                    ->where('category', $settingData['category'])
                    ->where('key', $settingData['key'])
                    ->first();

                if ($branchSetting) {
                    $branchSetting->update(['value' => $settingData['value']]);
                    $updated[] = $branchSetting;
                } else {
                    // Create new setting if it doesn't exist
                    $newSetting = BranchSetting::create([
                        'tenant_id' => $tenantId,
                        'branch_id' => $branchId,
                        'category' => $settingData['category'],
                        'key' => $settingData['key'],
                        'value' => $settingData['value'],
                        'data_type' => $this->detectDataType($settingData['value']),
                    ]);
                    $updated[] = $newSetting;
                }
            }

            return response()->json([
                'success' => true,
                'data' => $updated,
                'message' => 'Branch settings updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update branch settings: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getCategories(Request $request): JsonResponse
    {
        $categories = BranchSetting::getDefaultCategories();

        return response()->json([
            'success' => true,
            'data' => $categories
        ]);
    }

    public function initializeDefaults(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'branch_id' => 'required|exists:branches,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $tenantId = $request->user()->tenant_id;
            $branchId = $request->input('branch_id');

            $defaults = BranchSetting::getDefaultSettings($tenantId, $branchId);
            $created = [];

            foreach ($defaults as $default) {
                $existing = BranchSetting::where('tenant_id', $tenantId)
                    ->where('branch_id', $branchId)
                    ->where('category', $default['category'])
                    ->where('key', $default['key'])
                    ->first();

                if (!$existing) {
                    $created[] = BranchSetting::create($default);
                }
            }

            return response()->json([
                'success' => true,
                'data' => $created,
                'message' => 'Default branch settings initialized successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to initialize defaults: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getOperatingHours(Request $request, $branchId): JsonResponse
    {
        $tenantId = $request->user()->tenant_id;
        
        $operatingHours = BranchSetting::where('tenant_id', $tenantId)
            ->where('branch_id', $branchId)
            ->where('category', 'operating_hours')
            ->get()
            ->keyBy('key');

        return response()->json([
            'success' => true,
            'data' => $operatingHours
        ]);
    }

    public function updateOperatingHours(Request $request, $branchId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'operating_hours' => 'required|array',
            'operating_hours.*.day' => 'required|in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'operating_hours.*.open_time' => 'required|date_format:H:i',
            'operating_hours.*.close_time' => 'required|date_format:H:i',
            'operating_hours.*.is_closed' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $tenantId = $request->user()->tenant_id;
            $operatingHours = $request->input('operating_hours');
            $updated = [];

            foreach ($operatingHours as $hours) {
                $day = $hours['day'];
                $value = [
                    'open_time' => $hours['open_time'],
                    'close_time' => $hours['close_time'],
                    'is_closed' => $hours['is_closed'] ?? false,
                ];

                $setting = BranchSetting::updateOrCreate(
                    [
                        'tenant_id' => $tenantId,
                        'branch_id' => $branchId,
                        'category' => 'operating_hours',
                        'key' => $day,
                    ],
                    [
                        'value' => $value,
                        'data_type' => 'json',
                    ]
                );

                $updated[] = $setting;
            }

            return response()->json([
                'success' => true,
                'data' => $updated,
                'message' => 'Operating hours updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update operating hours: ' . $e->getMessage()
            ], 500);
        }
    }

    private function detectDataType($value): string
    {
        if (is_bool($value)) {
            return 'boolean';
        } elseif (is_int($value)) {
            return 'integer';
        } elseif (is_float($value)) {
            return 'float';
        } elseif (is_array($value)) {
            return 'array';
        } else {
            return 'string';
        }
    }
}
