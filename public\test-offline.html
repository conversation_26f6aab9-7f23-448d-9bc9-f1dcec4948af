<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POS Offline Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 20px;
            font-weight: bold;
            z-index: 1000;
        }
        .connection-status.online {
            background: #28a745;
            color: white;
        }
        .connection-status.offline {
            background: #dc3545;
            color: white;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
    </style>
</head>
<body>
    <div class="connection-status" id="connectionStatus">
        <span id="connectionText">Online</span>
    </div>

    <h1>POS Offline Functionality Test</h1>
    
    <div class="test-container">
        <h2>Service Worker Status</h2>
        <div id="swStatus" class="status info">Checking service worker...</div>
        <button onclick="registerServiceWorker()">Register Service Worker</button>
        <button onclick="unregisterServiceWorker()">Unregister Service Worker</button>
    </div>

    <div class="test-container">
        <h2>Storage Test</h2>
        <div id="storageStatus" class="status info">Testing storage...</div>
        <button onclick="testStorage()">Test IndexedDB Storage</button>
        <button onclick="clearStorage()">Clear Storage</button>
    </div>

    <div class="test-container">
        <h2>Offline Order Test</h2>
        <div id="orderStatus" class="status info">Ready to test orders</div>
        <button onclick="createTestOrder()">Create Test Order</button>
        <button onclick="syncOrders()">Sync Pending Orders</button>
        <button onclick="viewPendingOrders()">View Pending Orders</button>
    </div>

    <div class="test-container">
        <h2>Network Test</h2>
        <div id="networkStatus" class="status info">Ready to test network</div>
        <button onclick="testOnlineAPI()">Test Online API</button>
        <button onclick="simulateOffline()">Simulate Offline</button>
        <button onclick="simulateOnline()">Simulate Online</button>
    </div>

    <div class="test-container">
        <h2>Test Log</h2>
        <div id="testLog" class="log"></div>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <script>
        let posStorage = null;
        let posSync = null;
        let testOrderCounter = 1;

        // Initialize
        document.addEventListener('DOMContentLoaded', async () => {
            updateConnectionStatus();
            await initializeModules();
            checkServiceWorker();
            
            // Listen for connection changes
            window.addEventListener('online', updateConnectionStatus);
            window.addEventListener('offline', updateConnectionStatus);
        });

        function updateConnectionStatus() {
            const status = document.getElementById('connectionStatus');
            const text = document.getElementById('connectionText');
            
            if (navigator.onLine) {
                status.className = 'connection-status online';
                text.textContent = 'Online';
            } else {
                status.className = 'connection-status offline';
                text.textContent = 'Offline';
            }
        }

        async function initializeModules() {
            try {
                // Initialize storage
                posStorage = new POSStorage();
                await posStorage.init();
                
                // Initialize sync
                posSync = new POSSync(posStorage);
                
                log('✅ POS modules initialized successfully');
                updateStatus('storageStatus', 'Storage modules ready', 'success');
            } catch (error) {
                log('❌ Failed to initialize POS modules: ' + error.message);
                updateStatus('storageStatus', 'Failed to initialize: ' + error.message, 'error');
            }
        }

        async function registerServiceWorker() {
            if ('serviceWorker' in navigator) {
                try {
                    const registration = await navigator.serviceWorker.register('/pos-sw.js');
                    log('✅ Service Worker registered: ' + registration.scope);
                    updateStatus('swStatus', 'Service Worker registered successfully', 'success');
                } catch (error) {
                    log('❌ Service Worker registration failed: ' + error.message);
                    updateStatus('swStatus', 'Registration failed: ' + error.message, 'error');
                }
            } else {
                log('❌ Service Worker not supported');
                updateStatus('swStatus', 'Service Worker not supported', 'error');
            }
        }

        async function unregisterServiceWorker() {
            if ('serviceWorker' in navigator) {
                try {
                    const registrations = await navigator.serviceWorker.getRegistrations();
                    for (let registration of registrations) {
                        await registration.unregister();
                    }
                    log('✅ Service Worker unregistered');
                    updateStatus('swStatus', 'Service Worker unregistered', 'info');
                } catch (error) {
                    log('❌ Failed to unregister Service Worker: ' + error.message);
                    updateStatus('swStatus', 'Unregistration failed: ' + error.message, 'error');
                }
            }
        }

        function checkServiceWorker() {
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.getRegistrations().then(registrations => {
                    if (registrations.length > 0) {
                        log('✅ Service Worker already registered');
                        updateStatus('swStatus', 'Service Worker is active', 'success');
                    } else {
                        log('ℹ️ No Service Worker registered');
                        updateStatus('swStatus', 'No Service Worker registered', 'warning');
                    }
                });
            } else {
                updateStatus('swStatus', 'Service Worker not supported', 'error');
            }
        }

        async function testStorage() {
            if (!posStorage) {
                updateStatus('storageStatus', 'Storage not initialized', 'error');
                return;
            }

            try {
                // Test menu items storage
                const testMenuItems = [
                    { id: 1, name: 'Test Burger', base_price: 12.99, category: 'Main' },
                    { id: 2, name: 'Test Pizza', base_price: 15.99, category: 'Main' }
                ];
                
                await posStorage.saveMenuItems(testMenuItems);
                const savedItems = await posStorage.getMenuItems();
                
                log('✅ Storage test passed: ' + savedItems.length + ' items saved/retrieved');
                updateStatus('storageStatus', 'Storage working correctly', 'success');
                
                // Test stats
                const stats = await posStorage.getStorageStats();
                log('📊 Storage stats: ' + JSON.stringify(stats));
                
            } catch (error) {
                log('❌ Storage test failed: ' + error.message);
                updateStatus('storageStatus', 'Storage test failed: ' + error.message, 'error');
            }
        }

        async function clearStorage() {
            if (!posStorage) {
                updateStatus('storageStatus', 'Storage not initialized', 'error');
                return;
            }

            try {
                await posStorage.clearStore('menuItems');
                await posStorage.clearStore('pendingOrders');
                await posStorage.clearStore('syncQueue');
                
                log('✅ Storage cleared');
                updateStatus('storageStatus', 'Storage cleared', 'info');
            } catch (error) {
                log('❌ Failed to clear storage: ' + error.message);
                updateStatus('storageStatus', 'Clear failed: ' + error.message, 'error');
            }
        }

        async function createTestOrder() {
            if (!posStorage) {
                updateStatus('orderStatus', 'Storage not initialized', 'error');
                return;
            }

            try {
                const testOrder = {
                    id: 'test_order_' + testOrderCounter++,
                    order_type: 'dine_in',
                    customer_id: null,
                    table_id: 1,
                    subtotal: 25.98,
                    tax_amount: 2.60,
                    discount_amount: 0,
                    total_amount: 28.58,
                    status: 'pending_sync',
                    items: [
                        {
                            menu_item_id: 1,
                            menu_item_name: 'Test Burger',
                            quantity: 1,
                            unit_price: 12.99,
                            subtotal: 12.99
                        },
                        {
                            menu_item_id: 2,
                            menu_item_name: 'Test Pizza',
                            quantity: 1,
                            unit_price: 15.99,
                            subtotal: 15.99
                        }
                    ],
                    created_at: new Date().toISOString()
                };

                await posStorage.savePendingOrder(testOrder);
                log('✅ Test order created: ' + testOrder.id);
                updateStatus('orderStatus', 'Test order created successfully', 'success');
                
            } catch (error) {
                log('❌ Failed to create test order: ' + error.message);
                updateStatus('orderStatus', 'Order creation failed: ' + error.message, 'error');
            }
        }

        async function viewPendingOrders() {
            if (!posStorage) {
                updateStatus('orderStatus', 'Storage not initialized', 'error');
                return;
            }

            try {
                const pendingOrders = await posStorage.getPendingOrders();
                log('📋 Pending orders: ' + pendingOrders.length);
                
                pendingOrders.forEach((order, index) => {
                    log(`  ${index + 1}. ${order.id} - $${order.total_amount} (${order.status})`);
                });
                
                updateStatus('orderStatus', `Found ${pendingOrders.length} pending orders`, 'info');
                
            } catch (error) {
                log('❌ Failed to view pending orders: ' + error.message);
                updateStatus('orderStatus', 'Failed to view orders: ' + error.message, 'error');
            }
        }

        async function syncOrders() {
            if (!posSync) {
                updateStatus('orderStatus', 'Sync not initialized', 'error');
                return;
            }

            try {
                log('🔄 Starting sync...');
                updateStatus('orderStatus', 'Syncing orders...', 'info');
                
                await posSync.syncAll();
                
                log('✅ Sync completed');
                updateStatus('orderStatus', 'Sync completed successfully', 'success');
                
            } catch (error) {
                log('❌ Sync failed: ' + error.message);
                updateStatus('orderStatus', 'Sync failed: ' + error.message, 'error');
            }
        }

        async function testOnlineAPI() {
            try {
                log('🌐 Testing API connection...');
                updateStatus('networkStatus', 'Testing API...', 'info');
                
                const response = await fetch('/api/pos/form-data', {
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log('✅ API test successful');
                    updateStatus('networkStatus', 'API connection working', 'success');
                } else {
                    log('❌ API test failed: ' + response.status);
                    updateStatus('networkStatus', 'API test failed: ' + response.status, 'error');
                }
                
            } catch (error) {
                log('❌ API test error: ' + error.message);
                updateStatus('networkStatus', 'API error: ' + error.message, 'error');
            }
        }

        function simulateOffline() {
            // This is just for UI testing - actual offline simulation requires dev tools
            log('⚠️ Simulating offline mode (UI only)');
            updateStatus('networkStatus', 'Offline mode simulated', 'warning');
            
            // Trigger offline event
            window.dispatchEvent(new Event('offline'));
        }

        function simulateOnline() {
            log('✅ Simulating online mode');
            updateStatus('networkStatus', 'Online mode simulated', 'success');
            
            // Trigger online event
            window.dispatchEvent(new Event('online'));
        }

        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = 'status ' + type;
        }

        function log(message) {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
        }
    </script>

    <!-- Load POS modules -->
    <script src="/assets/js/pos/pos-storage.js"></script>
    <script src="/assets/js/pos/pos-sync.js"></script>
</body>
</html>
