<?php

namespace Modules\Reservation\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Models\Table;
use Exception;

class QRCodeController extends Controller
{
    /**
     * Validate QR code.
     */
    public function validateQR(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'qr_code' => 'required|string',
            ]);

            $table = Table::where('qr_code', $request->qr_code)->first();

            if (!$table) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid QR code'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'table_id' => $table->id,
                    'table_number' => $table->table_number,
                    'area' => $table->area->name ?? null,
                    'branch' => $table->branch->name ?? null,
                ],
                'message' => 'QR code validated successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to validate QR code',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Batch generate QR codes.
     */
    public function batchGenerate(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'table_ids' => 'required|array',
                'table_ids.*' => 'exists:tables,id',
            ]);

            $results = [];
            foreach ($request->table_ids as $tableId) {
                $table = Table::find($tableId);
                if ($table) {
                    // Generate QR code if not exists
                    if (!$table->qr_code) {
                        $table->qr_code = 'TBL_' . $table->id . '_' . time();
                        $table->save();
                    }
                    
                    $results[] = [
                        'table_id' => $table->id,
                        'qr_code' => $table->qr_code,
                        'table_number' => $table->table_number,
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'data' => $results,
                'message' => 'QR codes generated successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate QR codes',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get QR content for a table.
     */
    public function getQRContent(int $tableId): JsonResponse
    {
        try {
            $table = Table::with(['area', 'branch'])->find($tableId);

            if (!$table) {
                return response()->json([
                    'success' => false,
                    'message' => 'Table not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'qr_code' => $table->qr_code,
                    'table_id' => $table->id,
                    'table_number' => $table->table_number,
                    'area' => $table->area->name ?? null,
                    'branch' => $table->branch->name ?? null,
                    'url' => url("/restaurant/table/{$table->qr_code}"),
                ],
                'message' => 'QR content retrieved successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve QR content',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate table QR code.
     */
    public function generateTableQR(int $tableId): JsonResponse
    {
        try {
            $table = Table::find($tableId);

            if (!$table) {
                return response()->json([
                    'success' => false,
                    'message' => 'Table not found'
                ], 404);
            }

            // Generate new QR code
            $table->qr_code = 'TBL_' . $table->id . '_' . time();
            $table->save();

            return response()->json([
                'success' => true,
                'data' => [
                    'qr_code' => $table->qr_code,
                    'table_id' => $table->id,
                    'url' => url("/restaurant/table/{$table->qr_code}"),
                ],
                'message' => 'Table QR code generated successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate table QR code',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate menu QR code.
     */
    public function generateMenuQR(int $tableId): JsonResponse
    {
        try {
            $table = Table::find($tableId);

            if (!$table) {
                return response()->json([
                    'success' => false,
                    'message' => 'Table not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'qr_code' => $table->qr_code,
                    'table_id' => $table->id,
                    'menu_url' => url("/restaurant/table/{$table->qr_code}/menu"),
                ],
                'message' => 'Menu QR code generated successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate menu QR code',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate order QR code.
     */
    public function generateOrderQR(int $tableId): JsonResponse
    {
        try {
            $table = Table::find($tableId);

            if (!$table) {
                return response()->json([
                    'success' => false,
                    'message' => 'Table not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'qr_code' => $table->qr_code,
                    'table_id' => $table->id,
                    'order_url' => url("/restaurant/table/{$table->qr_code}/order"),
                ],
                'message' => 'Order QR code generated successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate order QR code',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}