<?php

namespace Modules\Delivery\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class UpdateDeliveryStatusRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'status' => 'required|in:pending,assigned,picked_up,in_transit,delivered,cancelled,failed',
            'notes' => 'nullable|string|max:1000',
            'delivery_time' => 'nullable|date',
            'customer_signature' => 'nullable|string',
            'delivery_photo' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'failure_reason' => 'nullable|string|max:500',
            'customer_rating' => 'nullable|integer|between:1,5',
            'customer_feedback' => 'nullable|string|max:1000',
            'tip_amount' => 'nullable|numeric|min:0',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'status.required' => 'Status is required',
            'status.in' => 'Status must be one of: pending, assigned, picked_up, in_transit, delivered, cancelled, failed',
            'notes.max' => 'Notes cannot exceed 1000 characters',
            'delivery_time.date' => 'Delivery time must be a valid date',
            'delivery_photo.image' => 'Delivery photo must be an image',
            'delivery_photo.mimes' => 'Delivery photo must be a JPEG, PNG, or JPG file',
            'delivery_photo.max' => 'Delivery photo cannot exceed 2MB',
            'failure_reason.max' => 'Failure reason cannot exceed 500 characters',
            'customer_rating.between' => 'Customer rating must be between 1 and 5',
            'customer_feedback.max' => 'Customer feedback cannot exceed 1000 characters',
            'tip_amount.min' => 'Tip amount cannot be negative',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $status = $this->input('status');

            // Validate required fields based on status
            switch ($status) {
                case 'delivered':
                    if (!$this->has('delivery_time')) {
                        $validator->errors()->add('delivery_time', 'Delivery time is required when status is delivered');
                    }
                    break;

                case 'failed':
                case 'cancelled':
                    if (!$this->has('failure_reason') && !$this->has('notes')) {
                        $validator->errors()->add('failure_reason', 'Failure reason or notes are required when delivery is failed or cancelled');
                    }
                    break;
            }

            // Validate status transition
            if ($this->route('id')) {
                $assignment = \Modules\Delivery\Models\DeliveryAssignment::find($this->route('id'));
                if ($assignment && !$this->isValidStatusTransition($assignment->status, $status)) {
                    $validator->errors()->add('status', 'Invalid status transition from ' . $assignment->status . ' to ' . $status);
                }
            }
        });
    }

    /**
     * Check if status transition is valid
     */
    private function isValidStatusTransition(string $currentStatus, string $newStatus): bool
    {
        $validTransitions = [
            'pending' => ['assigned', 'cancelled'],
            'assigned' => ['picked_up', 'cancelled'],
            'picked_up' => ['in_transit', 'cancelled', 'failed'],
            'in_transit' => ['delivered', 'failed'],
            'delivered' => [], // Final state
            'cancelled' => [], // Final state
            'failed' => ['assigned'], // Can be reassigned
        ];

        return in_array($newStatus, $validTransitions[$currentStatus] ?? []);
    }
}