<?php

namespace Modules\Delivery\Services;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Modules\Delivery\Models\DeliveryAssignment;
use Modules\Delivery\Models\DeliveryPersonnel;
use Modules\Delivery\Models\DeliveryReview;

class DeliveryReviewService
{
    /**
     * Create a new delivery review
     */
    public function createReview(array $data): DeliveryReview
    {
        DB::beginTransaction();
        
        try {
            $assignment = DeliveryAssignment::with('deliveryPersonnel')
                ->findOrFail($data['assignment_id']);
            
            // Check if review already exists
            if ($assignment->review()->exists()) {
                throw new \Exception('Review already exists for this delivery');
            }
            
            // Create the review
            $review = DeliveryReview::create([
                'delivery_assignment_id' => $data['assignment_id'],
                'customer_id' => $assignment->order->customer_id,
                'rating' => $data['rating'],
                'comment' => $data['comment'] ?? null,
                'review_categories' => $data['review_categories'] ?? null,
                'is_anonymous' => $data['is_anonymous'] ?? false,
                'is_verified' => false,
            ]);
            
            // Update personnel rating
            if ($assignment->deliveryPersonnel) {
                $review->updatePersonnelRating();
            }
            
            DB::commit();
            
            Log::info('Delivery review created', [
                'review_id' => $review->id,
                'assignment_id' => $assignment->id,
                'rating' => $data['rating']
            ]);
            
            return $review->load(['deliveryAssignment', 'customer']);
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create delivery review', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }
    
    /**
     * Get all delivery reviews with pagination
     */
    public function getReviews(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = DeliveryReview::with(['deliveryAssignment', 'customer', 'deliveryPersonnel'])
            ->latest();
        
        // Apply filters
        if (isset($filters['rating'])) {
            $query->where('rating', $filters['rating']);
        }
        
        if (isset($filters['verified'])) {
            $query->where('is_verified', $filters['verified']);
        }
        
        if (isset($filters['personnel_id'])) {
            $query->whereHas('deliveryAssignment', function ($q) use ($filters) {
                $q->where('delivery_personnel_id', $filters['personnel_id']);
            });
        }
        
        if (isset($filters['customer_id'])) {
            $query->where('customer_id', $filters['customer_id']);
        }
        
        return $query->paginate($perPage);
    }
    
    /**
     * Get a specific review by ID
     */
    public function getReviewById(int $id): DeliveryReview
    {
        return DeliveryReview::with(['deliveryAssignment', 'customer', 'deliveryPersonnel'])
            ->findOrFail($id);
    }
    
    /**
     * Get all reviews for a specific delivery personnel
     */
    public function getPersonnelReviews(int $personnelId, array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = DeliveryReview::with(['deliveryAssignment', 'customer'])
            ->whereHas('deliveryAssignment', function ($q) use ($personnelId) {
                $q->where('delivery_personnel_id', $personnelId);
            })
            ->latest();
        
        // Apply filters
        if (isset($filters['rating'])) {
            $query->where('rating', $filters['rating']);
        }
        
        if (isset($filters['verified'])) {
            $query->where('is_verified', $filters['verified']);
        }
        
        return $query->paginate($perPage);
    }
    
    /**
     * Verify a review
     */
    public function verifyReview(int $reviewId): DeliveryReview
    {
        $review = DeliveryReview::findOrFail($reviewId);
        
        if ($review->is_verified) {
            throw new \Exception('Review is already verified');
        }
        
        $review->verify();
        
        Log::info('Delivery review verified', ['review_id' => $review->id]);
        
        return $review->fresh();
    }
}