<?php $__env->startSection('title', 'إدارة التنويعات'); ?>

<?php $__env->startPush('styles'); ?>
<meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
    /* Custom DataTables Tailwind Styling */
    .dataTables_wrapper {
        font-family: inherit;
    }

    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_processing,
    .dataTables_wrapper .dataTables_paginate {
        color: #374151;
        font-size: 0.875rem;
    }

    .dataTables_wrapper .dataTables_length select,
    .dataTables_wrapper .dataTables_filter input {
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }

    .dataTables_wrapper .dataTables_filter input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.5rem 0.75rem !important;
        margin: 0 0.125rem !important;
        border-radius: 0.375rem !important;
        border: 1px solid #d1d5db !important;
        background: white !important;
        color: #374151 !important;
        text-decoration: none !important;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background: #f3f4f6 !important;
        border-color: #9ca3af !important;
        color: #374151 !important;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background: #3b82f6 !important;
        border-color: #3b82f6 !important;
        color: white !important;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
        background: #f9fafb !important;
        color: #9ca3af !important;
        border-color: #e5e7eb !important;
    }

    table.dataTable {
        border-collapse: separate !important;
        border-spacing: 0 !important;
    }

    table.dataTable thead th {
        background-color: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
        font-weight: 600;
        color: #374151;
        padding: 0.75rem;
    }

    table.dataTable tbody td {
        padding: 0.75rem;
        border-bottom: 1px solid #f3f4f6;
    }

    table.dataTable tbody tr:hover {
        background-color: #f9fafb;
    }

    .select2-container--default .select2-selection--single {
        height: 42px;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        padding: 0.5rem;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 26px;
        padding-left: 0;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 40px;
    }

    .modal-backdrop {
        background-color: rgba(0, 0, 0, 0.5);
    }

    .animate-fadeIn {
        animation: fadeIn 0.3s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: scale(0.95); }
        to { opacity: 1; transform: scale(1); }
    }

    .animate-slideDown {
        animation: slideDown 0.3s ease-in-out;
    }

    @keyframes slideDown {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<!-- Page Header -->
<div class="mb-6">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">إدارة التنويعات</h1>
            <p class="mt-1 text-sm text-gray-500">إدارة جميع تنويعات عناصر القائمة</p>
        </div>
        <div class="mt-4 sm:mt-0">
            <button type="button" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150" id="add-variant-btn">
                <i class="fas fa-plus mr-2"></i>
                إضافة تنويع جديد
            </button>
        </div>
    </div>
</div>

<!-- Filters Card -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
    <div class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">فلاتر البحث</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- Menu Item Filter -->
            <div>
                <label for="filter_menu_item" class="block text-sm font-medium text-gray-700 mb-2">عنصر القائمة</label>
                <select id="filter_menu_item" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <option value="">جميع عناصر القائمة</option>
                </select>
            </div>

            <!-- Status Filter -->
            <div>
                <label for="filter_status" class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                <select id="filter_status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <option value="">جميع الحالات</option>
                    <option value="1">نشط</option>
                    <option value="0">غير نشط</option>
                </select>
            </div>

            <!-- Default Filter -->
            <div>
                <label for="filter_default" class="block text-sm font-medium text-gray-700 mb-2">النوع</label>
                <select id="filter_default" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <option value="">جميع الأنواع</option>
                    <option value="1">افتراضي</option>
                    <option value="0">عادي</option>
                </select>
            </div>

            <!-- Actions -->
            <div class="flex items-end space-x-2 space-x-reverse">
                <button type="button" id="apply-filters" class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                    تطبيق الفلاتر
                </button>
                <button type="button" id="clear-filters" class="px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                    مسح الفلاتر
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Variants Table Card -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6">
        <div class="overflow-x-auto">
            <table id="variants-table" class="min-w-full divide-y divide-gray-200">
                <thead>
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">عنصر القائمة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">اسم التنويع</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الكود</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تعديل السعر</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">النوع</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الإنشاء</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <!-- Data will be loaded via DataTables -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add/Edit Variant Modal -->
<div id="variant-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white animate-fadeIn">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between pb-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900" id="modal-title">إضافة تنويع جديد</h3>
                <button type="button" class="text-gray-400 hover:text-gray-600 focus:outline-none" id="close-modal">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Modal Body -->
            <form id="variant-form" class="mt-6">
                <input type="hidden" id="variant_id" name="variant_id">
                <input type="hidden" name="_token" value="<?php echo e(csrf_token()); ?>">

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Menu Item -->
                    <div class="md:col-span-2">
                        <label for="menu_item_id" class="block text-sm font-medium text-gray-700 mb-2">
                            عنصر القائمة <span class="text-red-500">*</span>
                        </label>
                        <select id="menu_item_id" name="menu_item_id" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" required>
                            <option value="">اختر عنصر القائمة</option>
                        </select>
                        <div class="text-red-500 text-sm mt-1 hidden" id="menu_item_id_error"></div>
                    </div>

                    <!-- Variant Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                            اسم التنويع <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="name" name="name" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="مثال: حجم كبير" required>
                        <div class="text-red-500 text-sm mt-1 hidden" id="name_error"></div>
                    </div>

                    <!-- Variant Code -->
                    <div>
                        <label for="code" class="block text-sm font-medium text-gray-700 mb-2">
                            الكود
                        </label>
                        <input type="text" id="code" name="code" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="سيتم إنشاؤه تلقائياً">
                        <div class="text-red-500 text-sm mt-1 hidden" id="code_error"></div>
                    </div>

                    <!-- Price Modifier -->
                    <div>
                        <label for="price_modifier" class="block text-sm font-medium text-gray-700 mb-2">
                            تعديل السعر <span class="text-red-500">*</span>
                        </label>
                        <input type="number" id="price_modifier" name="price_modifier" step="0.01" min="0" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="0.00" required>
                        <div class="text-red-500 text-sm mt-1 hidden" id="price_modifier_error"></div>
                    </div>

                    <!-- Cost Modifier -->
                    <div>
                        <label for="cost_modifier" class="block text-sm font-medium text-gray-700 mb-2">
                            تعديل التكلفة
                        </label>
                        <input type="number" id="cost_modifier" name="cost_modifier" step="0.01" min="0" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="0.00">
                        <div class="text-red-500 text-sm mt-1 hidden" id="cost_modifier_error"></div>
                    </div>

                    <!-- Sort Order -->
                    <div>
                        <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-2">
                            ترتيب العرض
                        </label>
                        <input type="number" id="sort_order" name="sort_order" min="0" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="1">
                        <div class="text-red-500 text-sm mt-1 hidden" id="sort_order_error"></div>
                    </div>

                    <!-- Is Default -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">النوع</label>
                        <div class="flex items-center">
                            <input type="checkbox" id="is_default" name="is_default" value="1" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <label for="is_default" class="mr-2 text-sm text-gray-700">تنويع افتراضي</label>
                        </div>
                        <div class="text-red-500 text-sm mt-1 hidden" id="is_default_error"></div>
                    </div>

                    <!-- Is Active -->
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                        <div class="flex items-center">
                            <input type="checkbox" id="is_active" name="is_active" value="1" checked class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <label for="is_active" class="mr-2 text-sm text-gray-700">نشط</label>
                        </div>
                        <div class="text-red-500 text-sm mt-1 hidden" id="is_active_error"></div>
                    </div>
                </div>

                <!-- Modal Footer -->
                <div class="flex items-center justify-end pt-6 border-t border-gray-200 mt-6 space-x-2 space-x-reverse">
                    <button type="button" id="cancel-btn" class="px-4 py-2 bg-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                        إلغاء
                    </button>
                    <button type="submit" id="save-btn" class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        <span class="btn-text">حفظ</span>
                        <span class="btn-loading hidden">
                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            جاري الحفظ...
                        </span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View Variant Modal -->
<div id="view-variant-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white animate-fadeIn">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between pb-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">تفاصيل التنويع</h3>
                <button type="button" class="text-gray-400 hover:text-gray-600 focus:outline-none" id="close-view-modal">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Modal Body -->
            <div class="mt-6" id="variant-details">
                <!-- Details will be loaded here -->
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
$(document).ready(function() {
    // CSRF Token Setup
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Load menu items for dropdowns
    loadMenuItems();

    // Initialize DataTable
    var table = $('#variants-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '<?php echo e(route("menu.variants.index")); ?>',
            data: function(d) {
                d.menu_item_id = $('#filter_menu_item').val();
                d.status = $('#filter_status').val();
                d.is_default = $('#filter_default').val();
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'menu_item', name: 'menu_item', orderable: false },
            { data: 'name', name: 'name' },
            { data: 'code', name: 'code' },
            { data: 'price_modifier', name: 'price_modifier' },
            { data: 'is_default', name: 'is_default', orderable: false },
            { data: 'is_active', name: 'is_active', orderable: false },
            { data: 'created_at', name: 'created_at' },
            { data: 'action', name: 'action', orderable: false, searchable: false }
        ],
        order: [[2, 'asc']],
        pageLength: 25,
        responsive: true,
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json'
        },
        drawCallback: function() {
            // Reinitialize tooltips or other UI elements if needed
        }
    });

    // Load menu items function
    function loadMenuItems() {
        $.ajax({
            url: '<?php echo e(route("menu.variants.menu-items-list")); ?>',
            type: 'GET',
            success: function(response) {
                var options = '<option value="">اختر عنصر القائمة</option>';
                var filterOptions = '<option value="">جميع عناصر القائمة</option>';

                // Check if response has data and it's an array
                if (response && response.data && Array.isArray(response.data)) {
                    $.each(response.data, function(index, item) {
                        if (item && item.id && item.name) {
                            options += '<option value="' + item.id + '">' + item.name + '</option>';
                            filterOptions += '<option value="' + item.id + '">' + item.name + '</option>';
                        }
                    });
                } else {
                    console.warn('Invalid menu items response:', response);
                }

                $('#menu_item_id').html(options);
                $('#filter_menu_item').html(filterOptions);
            },
            error: function(xhr, status, error) {
                console.error('Error loading menu items:', {
                    status: status,
                    error: error,
                    response: xhr.responseText
                });

                // Show user-friendly error message
                Swal.fire({
                    title: 'خطأ!',
                    text: 'حدث خطأ أثناء تحميل عناصر القائمة',
                    icon: 'error',
                    confirmButtonText: 'موافق'
                });
            }
        });
    }

    // Filter Events
    $('#apply-filters').click(function() {
        table.ajax.reload();
    });

    $('#clear-filters').click(function() {
        $('#filter_menu_item').val('');
        $('#filter_status').val('');
        $('#filter_default').val('');
        table.ajax.reload();
    });

    // Add Variant Button
    $('#add-variant-btn').click(function() {
        resetForm();
        $('#modal-title').text('إضافة تنويع جديد');
        $('#variant-modal').removeClass('hidden');
    });

    // Edit Variant Button
    $(document).on('click', '.edit-variant', function() {
        var variantId = $(this).data('id');
        editVariant(variantId);
    });

    // Show Variant Button
    $(document).on('click', '.show-variant', function() {
        var variantId = $(this).data('id');
        showVariant(variantId);
    });

    // Delete Variant Button
    $(document).on('click', '.delete-variant', function() {
        var variantId = $(this).data('id');
        deleteVariant(variantId);
    });

    // Modal Close Events
    $('#close-modal, #cancel-btn').click(function() {
        $('#variant-modal').addClass('hidden');
    });

    $('#close-view-modal').click(function() {
        $('#view-variant-modal').addClass('hidden');
    });

    // Form Submit
    $('#variant-form').submit(function(e) {
        e.preventDefault();
        saveVariant();
    });

    // Functions
    function resetForm() {
        $('#variant-form')[0].reset();
        $('#variant_id').val('');
        $('.text-red-500').addClass('hidden');
        $('#is_active').prop('checked', true);
        $('#is_default').prop('checked', false);
    }

    function editVariant(id) {
        $.ajax({
            url: '<?php echo e(route("menu.variants.index")); ?>/' + id + '/edit',
            type: 'GET',
            success: function(response) {
                if (response.success && response.data) {
                    var variant = response.data;
                    $('#variant_id').val(variant.id);
                    $('#menu_item_id').val(variant.menu_item_id);
                    $('#name').val(variant.name);
                    $('#code').val(variant.code);
                    $('#price_modifier').val(variant.price_modifier);
                    $('#cost_modifier').val(variant.cost_modifier);
                    $('#sort_order').val(variant.sort_order);
                    $('#is_default').prop('checked', variant.is_default == 1);
                    $('#is_active').prop('checked', variant.is_active == 1);

                    $('#modal-title').text('تعديل التنويع');
                    $('#variant-modal').removeClass('hidden');
                } else {
                    Swal.fire({
                        title: 'خطأ!',
                        text: response.message || 'حدث خطأ أثناء تحميل بيانات التنويع',
                        icon: 'error',
                        confirmButtonText: 'موافق'
                    });
                }
            },
            error: function(xhr) {
                var response = JSON.parse(xhr.responseText);
                Swal.fire({
                    title: 'خطأ!',
                    text: response.message || 'حدث خطأ أثناء تحميل بيانات التنويع',
                    icon: 'error',
                    confirmButtonText: 'موافق'
                });
            }
        });
    }

    function showVariant(id) {
        $.ajax({
            url: '<?php echo e(route("menu.variants.index")); ?>/' + id,
            type: 'GET',
            success: function(response) {
                if (response.success && response.data) {
                    var variant = response.data;
                    var html = `
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">عنصر القائمة</label>
                                <p class="mt-1 text-sm text-gray-900">${variant.menu_item ? variant.menu_item.name : 'غير محدد'}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">اسم التنويع</label>
                                <p class="mt-1 text-sm text-gray-900">${variant.name}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">الكود</label>
                                <p class="mt-1 text-sm text-gray-900">${variant.code}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">تعديل السعر</label>
                                <p class="mt-1 text-sm text-gray-900">${parseFloat(variant.price_modifier).toFixed(2)} ريال</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">تعديل التكلفة</label>
                                <p class="mt-1 text-sm text-gray-900">${parseFloat(variant.cost_modifier || 0).toFixed(2)} ريال</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">ترتيب العرض</label>
                                <p class="mt-1 text-sm text-gray-900">${variant.sort_order || 0}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">النوع</label>
                                <p class="mt-1 text-sm text-gray-900">
                                    ${variant.is_default ?
                                        '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">افتراضي</span>' :
                                        '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">عادي</span>'
                                    }
                                </p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">الحالة</label>
                                <p class="mt-1 text-sm text-gray-900">
                                    ${variant.is_active ?
                                        '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">نشط</span>' :
                                        '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">غير نشط</span>'
                                    }
                                </p>
                            </div>
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700">تاريخ الإنشاء</label>
                                <p class="mt-1 text-sm text-gray-900">${new Date(variant.created_at).toLocaleString('ar-SA')}</p>
                            </div>
                        </div>
                    `;
                    $('#variant-details').html(html);
                    $('#view-variant-modal').removeClass('hidden');
                } else {
                    Swal.fire({
                        title: 'خطأ!',
                        text: response.message || 'حدث خطأ أثناء تحميل بيانات التنويع',
                        icon: 'error',
                        confirmButtonText: 'موافق'
                    });
                }
            },
            error: function(xhr) {
                var response = JSON.parse(xhr.responseText);
                Swal.fire({
                    title: 'خطأ!',
                    text: response.message || 'حدث خطأ أثناء تحميل بيانات التنويع',
                    icon: 'error',
                    confirmButtonText: 'موافق'
                });
            }
        });
    }

    function deleteVariant(id) {
        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: 'لن تتمكن من التراجع عن هذا الإجراء!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'نعم، احذف!',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?php echo e(route("menu.variants.index")); ?>/' + id,
                    type: 'DELETE',
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'تم الحذف!',
                                text: response.message,
                                icon: 'success',
                                confirmButtonText: 'موافق'
                            });
                            table.ajax.reload();
                        } else {
                            Swal.fire({
                                title: 'خطأ!',
                                text: response.message || 'حدث خطأ أثناء حذف التنويع',
                                icon: 'error',
                                confirmButtonText: 'موافق'
                            });
                        }
                    },
                    error: function(xhr) {
                        var response = JSON.parse(xhr.responseText);
                        Swal.fire({
                            title: 'خطأ!',
                            text: response.message || 'حدث خطأ أثناء حذف التنويع',
                            icon: 'error',
                            confirmButtonText: 'موافق'
                        });
                    }
                });
            }
        });
    }

    function saveVariant() {
        // Clear previous errors
        $('.text-red-500').addClass('hidden');

        // Show loading state
        $('#save-btn .btn-text').addClass('hidden');
        $('#save-btn .btn-loading').removeClass('hidden');
        $('#save-btn').prop('disabled', true);

        var formData = new FormData($('#variant-form')[0]);
        var variantId = $('#variant_id').val();
        var url = variantId ?
            '<?php echo e(route("menu.variants.index")); ?>/' + variantId :
            '<?php echo e(route("menu.variants.store")); ?>';
        var method = variantId ? 'PUT' : 'POST';

        if (method === 'PUT') {
            formData.append('_method', 'PUT');
        }

        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    Swal.fire({
                        title: 'نجح!',
                        text: response.message,
                        icon: 'success',
                        confirmButtonText: 'موافق'
                    });
                    $('#variant-modal').addClass('hidden');
                    table.ajax.reload();
                } else {
                    Swal.fire({
                        title: 'خطأ!',
                        text: response.message || 'حدث خطأ أثناء حفظ التنويع',
                        icon: 'error',
                        confirmButtonText: 'موافق'
                    });
                }
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    // Validation errors
                    var errors = xhr.responseJSON.errors;
                    $.each(errors, function(field, messages) {
                        $('#' + field + '_error').text(messages[0]).removeClass('hidden');
                    });
                } else {
                    var response = xhr.responseJSON;
                    Swal.fire({
                        title: 'خطأ!',
                        text: response.message || 'حدث خطأ أثناء حفظ التنويع',
                        icon: 'error',
                        confirmButtonText: 'موافق'
                    });
                }
            },
            complete: function() {
                // Hide loading state
                $('#save-btn .btn-text').removeClass('hidden');
                $('#save-btn .btn-loading').addClass('hidden');
                $('#save-btn').prop('disabled', false);
            }
        });
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Menu\Providers/../resources/views/variations.blade.php ENDPATH**/ ?>