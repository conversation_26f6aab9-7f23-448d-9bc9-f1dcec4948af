<?php

namespace Modules\Customer\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class RedeemLoyaltyPointsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'points' => 'required|numeric|min:0.01',
            'reason' => 'nullable|string|max:255',
            'order_id' => 'nullable|integer|exists:orders,id',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'points.required' => 'Points amount is required.',
            'points.numeric' => 'Points must be a valid number.',
            'points.min' => 'Points must be at least 0.01.',
            'order_id.exists' => 'The selected order does not exist.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'order_id' => 'order',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Add custom validation to check if customer has enough points
        $this->merge([
            'customer_id' => $this->route('customer')->id ?? null,
        ]);
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $customer = $this->route('customer');
            $points = $this->input('points');

            if ($customer && $points > $customer->loyalty_points) {
                $validator->errors()->add('points', 'Insufficient loyalty points. Available: ' . $customer->loyalty_points);
            }
        });
    }
}