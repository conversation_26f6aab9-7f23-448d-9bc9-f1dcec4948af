@extends('layouts.master')

@push('styles')
<meta name="csrf-token" content="{{ csrf_token() }}">
<!-- Material Design Icons -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@mdi/font@7.2.96/css/materialdesignicons.min.css">
<style>
:root {
    --primary: #2563eb;
    --primary-hover: #1d4ed8;
    --success: #059669;
    --success-hover: #047857;
    --warning: #d97706;
    --warning-hover: #b45309;
    --danger: #dc2626;
    --danger-hover: #b91c1c;
    --info: #0891b2;
    --info-hover: #0e7490;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --white: #ffffff;
}

.pos-dashboard {
    background-color: var(--gray-50);
    min-height: 100vh;
    padding: 2rem 1rem;
}

/* Header Section */
.dashboard-header {
    background-color: var(--white);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
    border: 1px solid var(--gray-200);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.header-info h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0 0 0.5rem 0;
}

.header-info p {
    color: var(--gray-600);
    margin: 0;
    font-size: 1rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.btn-primary-custom {
    background-color: var(--primary);
    color: var(--white);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    font-size: 0.95rem;
}

.btn-primary-custom:hover {
    background-color: var(--primary-hover);
    color: var(--white);
    text-decoration: none;
    transform: translateY(-1px);
}

.btn-outline-custom {
    background-color: transparent;
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    font-size: 0.95rem;
}

.btn-outline-custom:hover {
    background-color: var(--gray-100);
    color: var(--gray-800);
    text-decoration: none;
    border-color: var(--gray-400);
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stats-card {
    background-color: var(--white);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid var(--gray-200);
    transition: all 0.2s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
}

.stats-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.stats-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: var(--white);
}

.stats-icon.primary { background-color: var(--primary); }
.stats-icon.success { background-color: var(--success); }
.stats-icon.warning { background-color: var(--warning); }
.stats-icon.info { background-color: var(--info); }

.stats-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
}

.stats-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
}

/* Main Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 2rem;
}

@media (max-width: 1024px) {
    .content-grid {
        grid-template-columns: 1fr;
    }
}

/* Quick Actions */
.quick-actions {
    background-color: var(--white);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid var(--gray-200);
    height: fit-content;
}

.section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0 0 1.5rem 0;
}

.action-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 8px;
    text-decoration: none;
    color: var(--gray-700);
    transition: all 0.2s ease;
    margin-bottom: 0.5rem;
    border: 1px solid transparent;
}

.action-item:hover {
    background-color: var(--gray-50);
    color: var(--gray-900);
    text-decoration: none;
    border-color: var(--gray-200);
    transform: translateX(4px);
}

.action-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: var(--white);
    flex-shrink: 0;
}

.action-content h4 {
    font-size: 0.95rem;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
    color: inherit;
}

.action-content p {
    font-size: 0.8rem;
    color: var(--gray-500);
    margin: 0;
}

/* Recent Orders */
.recent-orders {
    background-color: var(--white);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid var(--gray-200);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.order-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.order-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid var(--gray-200);
    border-radius: 8px;
    transition: all 0.2s ease;
}

.order-item:hover {
    border-color: var(--gray-300);
    background-color: var(--gray-50);
}

.order-avatar {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    background-color: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-weight: 600;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.order-details {
    flex: 1;
    min-width: 0;
}

.order-number {
    font-weight: 600;
    color: var(--gray-900);
    margin: 0 0 0.25rem 0;
    font-size: 0.95rem;
}

.order-meta {
    font-size: 0.8rem;
    color: var(--gray-500);
    margin: 0;
}

.order-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-shrink: 0;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.status-pending {
    background-color: #fef3c7;
    color: #92400e;
}

.status-confirmed {
    background-color: #d1fae5;
    color: #065f46;
}

.status-preparing {
    background-color: #dbeafe;
    color: #1e40af;
}

.status-ready {
    background-color: #e0e7ff;
    color: #5b21b6;
}

.status-served {
    background-color: #f3e8ff;
    color: #6b21a8;
}

.btn-sm {
    padding: 0.5rem;
    border-radius: 6px;
    border: none;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-success {
    background-color: var(--success);
    color: var(--white);
}

.btn-success:hover {
    background-color: var(--success-hover);
}

.btn-outline-primary {
    background-color: transparent;
    color: var(--primary);
    border: 1px solid var(--primary);
}

.btn-outline-primary:hover {
    background-color: var(--primary);
    color: var(--white);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
}

.empty-icon {
    font-size: 4rem;
    color: var(--gray-400);
    margin-bottom: 1rem;
}

.empty-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-700);
    margin: 0 0 0.5rem 0;
}

.empty-description {
    color: var(--gray-500);
    margin: 0 0 2rem 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .pos-dashboard {
        padding: 1rem 0.5rem;
    }
    
    .dashboard-header {
        padding: 1.5rem;
    }
    
    .header-content {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .header-actions {
        width: 100%;
        justify-content: flex-start;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .hero-title {
        font-size: 1.75rem;
    }
    
    .order-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }
    
    .order-actions {
        width: 100%;
        justify-content: flex-end;
    }
}

/* Loading Animation */
.loading-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
</style>
@endpush



@section('content')
<div class="pos-dashboard">
    <!-- Header Section -->
    <div class="dashboard-header">
        <div class="header-content">
            <div class="header-info">
                <h1>Point of Sale Dashboard</h1>
                <p>Manage your restaurant orders and track daily performance</p>
            </div>
            <div class="header-actions">
                <a href="{{ route('pos.create') }}" class="btn-primary-custom">
                    <i class="mdi mdi-plus"></i>
                    New Order
                </a>
                <a href="{{ route('orders.index') }}" class="btn-outline-custom">
                    <i class="mdi mdi-format-list-bulleted"></i>
                    View All Orders
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-grid">
        <div class="stats-card">
            <div class="stats-header">
                <div class="stats-icon primary">
                    <i class="mdi mdi-receipt"></i>
                </div>
                <div class="stats-label">Today's Orders</div>
            </div>
            <div class="stats-value">{{ $recentOrders->where('created_at', '>=', today())->count() }}</div>
        </div>

        <div class="stats-card">
            <div class="stats-header">
                <div class="stats-icon warning">
                    <i class="mdi mdi-clock-outline"></i>
                </div>
                <div class="stats-label">Pending Orders</div>
            </div>
            <div class="stats-value">{{ $recentOrders->where('status', 'pending')->count() }}</div>
        </div>

        <div class="stats-card">
            <div class="stats-header">
                <div class="stats-icon info">
                    <i class="mdi mdi-chef-hat"></i>
                </div>
                <div class="stats-label">Preparing</div>
            </div>
            <div class="stats-value">{{ $recentOrders->where('status', 'preparing')->count() }}</div>
        </div>

        <div class="stats-card">
            <div class="stats-header">
                <div class="stats-icon success">
                    <i class="mdi mdi-currency-usd"></i>
                </div>
                <div class="stats-label">Today's Revenue</div>
            </div>
            <div class="stats-value">${{ number_format($recentOrders->where('created_at', '>=', today())->sum('total_amount'), 0) }}</div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="content-grid">
        <!-- Quick Actions -->
        <div class="quick-actions">
            <h3 class="section-title">Quick Actions</h3>
            
            <a href="{{ route('pos.create') }}" class="action-item">
                <div class="action-icon primary">
                    <i class="mdi mdi-plus"></i>
                </div>
                <div class="action-content">
                    <h4>Create New Order</h4>
                    <p>Start a new customer order</p>
                </div>
            </a>

            <a href="{{ route('orders.index') }}" class="action-item">
                <div class="action-icon success">
                    <i class="mdi mdi-format-list-bulleted"></i>
                </div>
                <div class="action-content">
                    <h4>Manage Orders</h4>
                    <p>View and update existing orders</p>
                </div>
            </a>

            <a href="#" class="action-item">
                <div class="action-icon info">
                    <i class="mdi mdi-chart-line"></i>
                </div>
                <div class="action-content">
                    <h4>Sales Reports</h4>
                    <p>View detailed analytics</p>
                </div>
            </a>

            <a href="#" class="action-item">
                <div class="action-icon warning">
                    <i class="mdi mdi-cog"></i>
                </div>
                <div class="action-content">
                    <h4>Settings</h4>
                    <p>Configure system preferences</p>
                </div>
            </a>
        </div>

        <!-- Recent Orders -->
        <div class="recent-orders">
            <div class="section-header">
                <h3 class="section-title">Recent Orders</h3>
                <a href="{{ route('orders.index') }}" class="btn-outline-custom">
                    <i class="mdi mdi-arrow-right"></i>
                    View All
                </a>
            </div>

            @if($recentOrders->count() > 0)
                <div class="order-list">
                    @foreach($recentOrders as $order)
                    <div class="order-item">
                        <div class="order-avatar">
                            #{{ substr($order->order_number, -3) }}
                        </div>
                        <div class="order-details">
                            <h4 class="order-number">#{{ $order->order_number }}</h4>
                            <p class="order-meta">
                                {{ $order->customer ? $order->customer->first_name . ' ' . $order->customer->last_name : 'Walk-in Customer' }} • 
                                {{ $order->items->count() }} items • 
                                ${{ number_format($order->total_amount, 2) }} • 
                                {{ $order->created_at->format('h:i A') }}
                            </p>
                        </div>
                        <div class="order-actions">
                            <span class="status-badge status-{{ $order->status }}">
                                {{ ucfirst($order->status) }}
                            </span>
                            @if($order->status === 'pending')
                                <button class="btn-sm btn-success confirm-order-btn" data-order-id="{{ $order->id }}" title="Confirm Order">
                                    <i class="mdi mdi-check"></i>
                                </button>
                            @endif
                            <a href="{{ route('orders.show', $order->id) }}" class="btn-sm btn-outline-primary" title="View Details">
                                <i class="mdi mdi-eye"></i>
                            </a>
                        </div>
                    </div>
                    @endforeach
                </div>
            @else
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="mdi mdi-receipt"></i>
                    </div>
                    <h3 class="empty-title">No Orders Yet</h3>
                    <p class="empty-description">Create your first order to get started</p>
                    <a href="{{ route('pos.create') }}" class="btn-primary-custom">
                        <i class="mdi mdi-plus"></i>
                        Create First Order
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
$(document).ready(function() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Order confirmation functionality
    $('.confirm-order-btn').on('click', function() {
        const orderId = $(this).data('order-id');
        const button = $(this);
        const originalContent = button.html();
        
        // Show loading state
        button.prop('disabled', true).html('<i class="mdi mdi-loading loading-spin"></i>');
        
        $.ajax({
            url: `/api/orders/${orderId}/confirm`,
            method: 'POST',
            success: function(response) {
                if (response.success) {
                    // Extract order information
                    const orderNumber = response.data.order.order_number || orderId;
                    let kotNumber = null;
                    
                    // Check if KOT was created successfully
                    if (response.data.kot_orders && response.data.kot_orders.length > 0) {
                        kotNumber = response.data.kot_orders[0].kot_number || response.data.kot_orders[0].id;
                    }
                    
                    // Show success notification
                    showOrderConfirmationToast(orderNumber, kotNumber);
                    
                    // Update the order status display
                    const orderItem = button.closest('.order-item');
                    orderItem.find('.status-badge')
                        .removeClass('status-pending')
                        .addClass('status-confirmed')
                        .text('Confirmed');
                    
                    // Remove the confirm button
                    button.fadeOut();
                } else {
                    showOrderErrorToast(response.message || "Failed to confirm order");
                }
                
                // Restore button state
                button.prop('disabled', false).html(originalContent);
            },
            error: function(xhr) {
                console.error('Error confirming order:', xhr);
                let errorMessage = "Failed to confirm order";
                
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                
                showOrderErrorToast(errorMessage);
                
                // Restore button
                button.prop('disabled', false).html(originalContent);
            }
        });
    });

    // Toast notification functions (you'll need to implement these based on your notification system)
    function showOrderConfirmationToast(orderNumber, kotNumber) {
        // Implement your success toast notification here
        console.log('Order confirmed:', orderNumber, kotNumber);
    }

    function showOrderErrorToast(message) {
        // Implement your error toast notification here
        console.error('Order error:', message);
    }
});
</script>
@endpush
