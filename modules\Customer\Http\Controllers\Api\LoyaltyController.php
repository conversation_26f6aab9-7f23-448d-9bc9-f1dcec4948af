<?php

namespace Modules\Customer\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use Modules\Customer\Services\LoyaltyService;
use Modules\Customer\Http\Requests\AddLoyaltyPointsRequest;
use Modules\Customer\Http\Requests\RedeemLoyaltyPointsRequest;
use Modules\Customer\Http\Resources\LoyaltyTransactionResource;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Helpers\ResponseHelper;

class LoyaltyController extends Controller
{
    protected LoyaltyService $loyaltyService;

    public function __construct(LoyaltyService $loyaltyService)
    {
        $this->loyaltyService = $loyaltyService;
    }

    /**
     * Add loyalty points to customer
     */
    public function addPoints(AddLoyaltyPointsRequest $request, Customer $customer): JsonResponse
    {
        try {
            $transaction = $this->loyaltyService->addPoints(
                $customer,
                $request->points,
                $request->reason ?? 'Points added via API',
                $request->order_id
            );
            
            return ResponseHelper::success(
                'Loyalty points added successfully',
                new LoyaltyTransactionResource($transaction),
                201
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to add loyalty points: ' . $e->getMessage()
            );
        }
    }

    /**
     * Redeem loyalty points from customer
     */
    public function redeemPoints(RedeemLoyaltyPointsRequest $request, Customer $customer): JsonResponse
    {
        try {
            $transaction = $this->loyaltyService->redeemPoints(
                $customer,
                $request->points,
                $request->reason ?? 'Points redeemed via API',
                $request->order_id
            );
            
            return ResponseHelper::success(
                'Loyalty points redeemed successfully',
                new LoyaltyTransactionResource($transaction),
                201
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to redeem loyalty points: ' . $e->getMessage()
            );
        }
    }

    /**
     * Get customer loyalty history
     */
    public function history(Customer $customer): JsonResponse
    {
        try {
            $history = $this->loyaltyService->getLoyaltyHistory($customer);
            
            return ResponseHelper::success(
                'Loyalty history retrieved',
                LoyaltyTransactionResource::collection($history)
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to get loyalty history: ' . $e->getMessage()
            );
        }
    }

    /**
     * Get customer current loyalty points
     */
    public function getPoints(Customer $customer): JsonResponse
    {
        try {
            $points = $this->loyaltyService->getCurrentPoints($customer);
            $stats = $this->loyaltyService->getCustomerLoyaltyStats($customer);
            
            return ResponseHelper::success(
                'Loyalty points retrieved',
                [
                    'current_points' => $points,
                    'stats' => $stats
                ]
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to get loyalty points: ' . $e->getMessage()
            );
        }
    }

    /**
     * Calculate points for order amount
     */
    public function calculatePoints(Request $request): JsonResponse
    {
        try {
            $amount = $request->get('amount', 0);
            $points = $this->loyaltyService->calculatePointsForOrder($amount);
            
            return ResponseHelper::success(
                'Points calculated',
                ['points' => $points, 'amount' => $amount]
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to calculate points: ' . $e->getMessage()
            );
        }
    }

    /**
     * Calculate discount for points
     */
    public function calculateDiscount(Request $request): JsonResponse
    {
        try {
            $points = $request->get('points', 0);
            $discount = $this->loyaltyService->calculateDiscountForPoints($points);
            
            return ResponseHelper::success(
                'Discount calculated',
                ['discount' => $discount, 'points' => $points]
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to calculate discount: ' . $e->getMessage()
            );
        }
    }

    /**
     * Get top loyalty customers
     */
    public function topCustomers(Request $request): JsonResponse
    {
        try {
            $limit = $request->get('limit', 10);
            $customers = $this->loyaltyService->getTopLoyaltyCustomers($limit);
            
            return ResponseHelper::success(
                'Top loyalty customers retrieved',
                $customers->map(function ($customer) {
                    return [
                        'id' => $customer->id,
                        'name' => $customer->first_name . ' ' . $customer->last_name,
                        'email' => $customer->email,
                        'phone' => $customer->phone,
                        'loyalty_points' => $customer->loyalty_points,
                    ];
                })
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to get top customers: ' . $e->getMessage()
            );
        }
    }
}