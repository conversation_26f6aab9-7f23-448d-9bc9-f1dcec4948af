<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reservations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('branch_id')->nullable()->constrained('branches')->onDelete('cascade');
            $table->foreignId('customer_id')->nullable()->constrained('customers');
            $table->foreignId('table_id')->nullable()->constrained('tables');
            $table->string('reservation_number', 50)->nullable();
            $table->string('customer_name')->nullable();
            $table->string('customer_phone', 20)->nullable();
            $table->string('customer_email')->nullable();
            $table->integer('party_size')->nullable();
            $table->datetime('reservation_datetime')->nullable();
            $table->integer('duration_minutes')->nullable()->default(120);
            $table->text('special_requests')->nullable();
            $table->text('notes')->nullable();
            $table->foreignId('created_by')->nullable()->constrained('users');
            $table->timestamp('seated_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->foreignId('area_id')->nullable()->constrained('areas')->onDelete('set null');
            $table->foreignId('reservation_status_id')->nullable()->constrained('reservation_statuses')->onDelete('set null');
            $table->timestamps();
            
            $table->unique(['branch_id', 'reservation_number']);
            
            // Performance indexes
            $table->index(['branch_id', 'reservation_datetime']);
            $table->index(['customer_id', 'reservation_datetime']);
            $table->index(['table_id', 'reservation_datetime']);
            $table->index(['customer_phone']);
            $table->index(['reservation_status_id', 'reservation_datetime']);
            $table->index(['area_id', 'reservation_datetime']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reservations');
    }
};