<?php

use Illuminate\Support\Facades\Route;
use Modules\Inventory\Http\Controllers\InventoryController;
use Modules\Inventory\Http\Controllers\InventoryWebController;
use Modules\Inventory\Http\Controllers\SupplierController;
use Modules\Inventory\Http\Controllers\PurchaseOrderController;
use Modules\Inventory\Http\Controllers\InventoryCategoryController;
use Modules\Inventory\Http\Controllers\UnitController;
use Modules\Inventory\Http\Controllers\RecipeController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::middleware(['web', 'auth'])->prefix('inventory')->name('inventory.')->group(function () {

    // Dashboard
    Route::get('/', [InventoryWebController::class, 'index'])->name('index');

    // Units Management
    Route::get('/units', [UnitController::class, 'index'])->name('units.index');

    // Inventory Categories Management
    Route::get('/categories', [InventoryCategoryController::class, 'index'])->name('categories.index');

    // Inventory Items Management (Web Views)
    Route::get('/items', [InventoryWebController::class, 'items'])->name('items.index');
    Route::get('/items/create', [InventoryWebController::class, 'create'])->name('items.create');
    Route::get('/items/{id}', [InventoryWebController::class, 'show'])->name('items.show');
    Route::get('/items/{id}/edit', [InventoryWebController::class, 'edit'])->name('items.edit');

    // Stock Management Views
    Route::get('/stock', [InventoryWebController::class, 'stock'])->name('stock.index');
    Route::get('/stock/low-stock', [InventoryWebController::class, 'lowStock'])->name('stock.low-stock');
    Route::get('/stock/alerts', [InventoryWebController::class, 'alerts'])->name('stock.alerts');
    Route::get('/stock/movements', [InventoryWebController::class, 'movements'])->name('stock.movements');
    Route::get('/stock/movements/{id}', [InventoryWebController::class, 'itemMovements'])->name('stock.movements.item');

    // Recipes Management
    Route::get('/recipes', [RecipeController::class, 'index'])->name('recipes.index');

    // Suppliers Management (Web Views)
    Route::get('/suppliers', [InventoryWebController::class, 'suppliers'])->name('suppliers.index');
    Route::get('/suppliers/create', [InventoryWebController::class, 'createSupplier'])->name('suppliers.create');
    Route::get('/suppliers/{id}', [InventoryWebController::class, 'showSupplier'])->name('suppliers.show');
    Route::get('/suppliers/{id}/edit', [InventoryWebController::class, 'editSupplier'])->name('suppliers.edit');
    Route::get('/suppliers/{id}/products', [InventoryWebController::class, 'supplierProducts'])->name('suppliers.products');

    // Purchase Orders Management (Web Views)
    Route::get('/purchase-orders', [InventoryWebController::class, 'purchaseOrders'])->name('purchase-orders.index');
    Route::get('/purchase-orders/create', [InventoryWebController::class, 'createPurchaseOrder'])->name('purchase-orders.create');
    Route::get('/purchase-orders/{id}', [InventoryWebController::class, 'showPurchaseOrder'])->name('purchase-orders.show');
    Route::get('/purchase-orders/{id}/edit', [InventoryWebController::class, 'editPurchaseOrder'])->name('purchase-orders.edit');
    Route::get('/purchase-orders/{id}/print', [InventoryWebController::class, 'printPurchaseOrder'])->name('purchase-orders.print');
    Route::get('/purchase-orders/supplier/{id}', [InventoryWebController::class, 'purchaseOrdersBySupplier'])->name('purchase-orders.supplier');

    // Analytics and Reports
    Route::get('/analytics', [InventoryWebController::class, 'analytics'])->name('analytics');
    Route::get('/reports', [InventoryWebController::class, 'reports'])->name('reports');

    // API Routes for DataTables and AJAX operations
    Route::prefix('api')->name('api.')->group(function () {
        // Units API
        Route::get('/units/datatable', [UnitController::class, 'datatable'])->name('units.datatable');
        Route::post('/units', [UnitController::class, 'store'])->name('units.store');
        Route::get('/units/{id}', [UnitController::class, 'show'])->name('units.show');
        Route::put('/units/{id}', [UnitController::class, 'update'])->name('units.update');
        Route::delete('/units/{id}', [UnitController::class, 'destroy'])->name('units.destroy');
        Route::get('/units-list', [UnitController::class, 'getUnits'])->name('units.list');

        // Categories API
        Route::get('/categories/datatable', [InventoryCategoryController::class, 'datatable'])->name('categories.datatable');
        Route::post('/categories', [InventoryCategoryController::class, 'store'])->name('categories.store');
        Route::get('/categories/{id}', [InventoryCategoryController::class, 'show'])->name('categories.show');
        Route::put('/categories/{id}', [InventoryCategoryController::class, 'update'])->name('categories.update');
        Route::delete('/categories/{id}', [InventoryCategoryController::class, 'destroy'])->name('categories.destroy');
        Route::get('/categories-list', [InventoryCategoryController::class, 'getCategories'])->name('categories.list');

        // Recipes API
        Route::get('/recipes/datatable', [RecipeController::class, 'datatable'])->name('recipes.datatable');
        Route::post('/recipes', [RecipeController::class, 'store'])->name('recipes.store');
        Route::get('/recipes/{id}', [RecipeController::class, 'show'])->name('recipes.show');
        Route::put('/recipes/{id}', [RecipeController::class, 'update'])->name('recipes.update');
        Route::delete('/recipes/{id}', [RecipeController::class, 'destroy'])->name('recipes.destroy');
        Route::get('/recipes-list', [RecipeController::class, 'getRecipes'])->name('recipes.list');
        Route::get('/recipe-products', [RecipeController::class, 'getProducts'])->name('recipes.products');

        // Items API
        Route::get('/items/datatable', [InventoryController::class, 'datatable'])->name('items.datatable');
        Route::post('/items', [InventoryController::class, 'store'])->name('items.store');
        Route::get('/items/{id}', [InventoryController::class, 'show'])->name('items.show');
        Route::put('/items/{id}', [InventoryController::class, 'update'])->name('items.update');
        Route::delete('/items/{id}', [InventoryController::class, 'destroy'])->name('items.destroy');

        // Stock Management API
        Route::get('/stock/datatable', [\Modules\Inventory\Http\Controllers\StockController::class, 'datatable'])->name('stock.datatable');
        Route::get('/stock/export', [\Modules\Inventory\Http\Controllers\StockController::class, 'export'])->name('stock.export');
        Route::get('/stock/report', [\Modules\Inventory\Http\Controllers\StockController::class, 'report'])->name('stock.report');
        Route::get('/stock/movements/datatable', [\Modules\Inventory\Http\Controllers\StockController::class, 'movementsDatatable'])->name('stock.movements.datatable');
        Route::get('/stock/movements/timeline', [\Modules\Inventory\Http\Controllers\StockController::class, 'movementsTimeline'])->name('stock.movements.timeline');
        Route::get('/stock/movements/export', [\Modules\Inventory\Http\Controllers\StockController::class, 'exportMovements'])->name('stock.movements.export');
        Route::get('/stock/movements/report', [\Modules\Inventory\Http\Controllers\StockController::class, 'movementsReport'])->name('stock.movements.report');
        Route::post('/stock/{id}/update', [\Modules\Inventory\Http\Controllers\StockController::class, 'updateStock'])->name('stock.update');
        Route::post('/items/{id}/update-stock', [InventoryController::class, 'updateStock'])->name('items.update-stock');
        Route::get('/items/{id}/movements', [InventoryController::class, 'getMovements'])->name('items.movements');
        Route::get('/low-stock', [InventoryController::class, 'getLowStockItems'])->name('low-stock');
        Route::post('/bulk-update', [InventoryController::class, 'bulkUpdateStock'])->name('bulk-update');

        // Suppliers API
        Route::get('/suppliers/datatable', [SupplierController::class, 'datatable'])->name('suppliers.datatable');
        Route::post('/suppliers', [SupplierController::class, 'store'])->name('suppliers.store');
        Route::get('/suppliers/{id}', [SupplierController::class, 'show'])->name('suppliers.show');
        Route::put('/suppliers/{id}', [SupplierController::class, 'update'])->name('suppliers.update');
        Route::delete('/suppliers/{id}', [SupplierController::class, 'destroy'])->name('suppliers.destroy');
        Route::get('/suppliers/cities', [SupplierController::class, 'getCities'])->name('suppliers.cities');
        Route::get('/suppliers/cards', [SupplierController::class, 'getCards'])->name('suppliers.cards');
        Route::get('/suppliers/export', [SupplierController::class, 'export'])->name('suppliers.export');

        // Purchase Orders API
        Route::get('/purchase-orders/datatable', [PurchaseOrderController::class, 'datatable'])->name('purchase-orders.datatable');
        Route::post('/purchase-orders', [PurchaseOrderController::class, 'store'])->name('purchase-orders.store');
        Route::get('/purchase-orders/{id}', [PurchaseOrderController::class, 'show'])->name('purchase-orders.show');
        Route::put('/purchase-orders/{id}', [PurchaseOrderController::class, 'update'])->name('purchase-orders.update');
        Route::delete('/purchase-orders/{id}', [PurchaseOrderController::class, 'destroy'])->name('purchase-orders.destroy');
        Route::get('/purchase-orders/cards', [PurchaseOrderController::class, 'getCards'])->name('purchase-orders.cards');
        Route::post('/purchase-orders/{id}/duplicate', [PurchaseOrderController::class, 'duplicate'])->name('purchase-orders.duplicate');
        Route::get('/purchase-orders/export', [PurchaseOrderController::class, 'export'])->name('purchase-orders.export');
        Route::get('/purchase-orders/report', [PurchaseOrderController::class, 'report'])->name('purchase-orders.report');
        Route::put('/purchase-orders/{id}/update-status', [PurchaseOrderController::class, 'updateStatus'])->name('purchase-orders.update-status');
        Route::post('/purchase-orders/{id}/receive', [PurchaseOrderController::class, 'receive'])->name('purchase-orders.receive');
        Route::post('/purchase-orders/bulk-action', [PurchaseOrderController::class, 'bulkAction'])->name('purchase-orders.bulk-action');
        Route::get('/purchase-orders/{id}/items', [PurchaseOrderController::class, 'getItems'])->name('purchase-orders.items');
        Route::get('/purchase-orders/{id}/history', [PurchaseOrderController::class, 'getHistory'])->name('purchase-orders.history');

        // Utility APIs
        Route::get('/categories', [InventoryController::class, 'getCategories'])->name('categories');
        Route::get('/units', [InventoryController::class, 'getUnits'])->name('units');
        Route::get('/items', [InventoryController::class, 'getItems'])->name('items');
        Route::get('/suppliers', [SupplierController::class, 'getSuppliers'])->name('suppliers');
        Route::get('/analytics', [InventoryController::class, 'getAnalytics'])->name('analytics');
        Route::get('/reorder-suggestions', [InventoryController::class, 'getReorderSuggestions'])->name('reorder-suggestions');
        Route::get('/export', [InventoryController::class, 'export'])->name('export');
        Route::post('/import', [InventoryController::class, 'import'])->name('import');
    });
});
