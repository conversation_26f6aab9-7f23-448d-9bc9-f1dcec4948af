@extends('layouts.master')

@section('css')
<meta name="csrf-token" content="{{ csrf_token() }}">
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
<!-- Select2 CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">
<!-- SweetAlert2 CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
<!-- Color Picker CSS -->
<link href="{{URL::asset('assets/plugins/spectrum-colorpicker/spectrum.css')}}" rel="stylesheet">

<style>
/* Custom DataTables Tailwind Styling */
.dataTables_wrapper {
    @apply bg-white rounded-lg shadow-sm;
}

.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_processing,
.dataTables_wrapper .dataTables_paginate {
    @apply text-gray-700;
}

.dataTables_wrapper .dataTables_length select,
.dataTables_wrapper .dataTables_filter input {
    @apply border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.dataTables_wrapper .dataTables_filter input:focus {
    @apply ring-2 ring-blue-500 border-transparent;
}

/* Pagination Styling */
.dataTables_wrapper .dataTables_paginate .paginate_button {
    @apply px-3 py-2 mx-1 text-sm text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 hover:text-gray-900 transition-colors duration-200;
    border: 1px solid #d1d5db !important;
    background: white !important;
    color: #374151 !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    @apply bg-gray-50 text-gray-900;
    background: #f9fafb !important;
    color: #111827 !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    @apply bg-blue-600 text-white border-blue-600;
    background: #2563eb !important;
    color: white !important;
    border-color: #2563eb !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
    @apply opacity-50 cursor-not-allowed;
    opacity: 0.5 !important;
}

table.dataTable {
    @apply w-full border-collapse;
}

table.dataTable thead th {
    @apply bg-gray-50 text-gray-900 font-semibold text-sm px-6 py-4 text-right border-b border-gray-200;
    background-color: #f9fafb !important;
    border-bottom: 1px solid #e5e7eb !important;
}

table.dataTable tbody td {
    @apply px-6 py-4 text-sm text-gray-900 border-b border-gray-100;
    border-bottom: 1px solid #f3f4f6 !important;
}

table.dataTable tbody tr:hover {
    @apply bg-gray-50;
    background-color: #f9fafb !important;
}

/* Modal Animation */
.modal.fade .modal-dialog {
    transform: translateY(-50px);
    transition: transform 0.3s ease-out;
}

.modal.show .modal-dialog {
    transform: translateY(0);
}
</style>
@endsection

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">إدارة البانرات</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ البانرات</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-info btn-icon ml-2" id="add-banner-btn">
                <i class="mdi mdi-plus"></i>
            </button>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<!-- row -->
<div class="row">
    <div class="col-xl-12">
        <div class="card mg-b-20">
            <div class="card-header pb-0">
                <div class="d-flex justify-content-between">
                    <h4 class="card-title mg-b-0">قائمة البانرات</h4>
                    <i class="mdi mdi-dots-horizontal text-gray"></i>
                </div>
                <p class="tx-12 tx-gray-500 mb-2">إدارة جميع بانرات المطعم</p>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="banners-table" class="table key-buttons text-md-nowrap">
                        <thead>
                            <tr>
                                <th class="border-bottom-0">#</th>
                                <th class="border-bottom-0">العنوان</th>
                                <th class="border-bottom-0">النوع</th>
                                <th class="border-bottom-0">الموضع</th>
                                <th class="border-bottom-0">تاريخ البداية</th>
                                <th class="border-bottom-0">تاريخ النهاية</th>
                                <th class="border-bottom-0">الحالة</th>
                                <th class="border-bottom-0">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- row closed -->

<!-- Add/Edit Banner Modal -->
<div class="modal fade" id="bannerModal" tabindex="-1" role="dialog" aria-labelledby="bannerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bannerModalLabel">إضافة بانر جديد</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="bannerForm" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" id="banner_id" name="banner_id">
                    
                    <!-- Basic Information -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">المعلومات الأساسية</h6>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="title">عنوان البانر <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="title_en">عنوان البانر (إنجليزي)</label>
                                <input type="text" class="form-control" id="title_en" name="title_en">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="description">الوصف</label>
                                <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Display Settings -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary mb-3 mt-4">إعدادات العرض</h6>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="banner_type">نوع البانر <span class="text-danger">*</span></label>
                                <select class="form-control" id="banner_type" name="banner_type" required>
                                    <option value="">اختر نوع البانر</option>
                                    <option value="promotional">ترويجي</option>
                                    <option value="informational">إعلامي</option>
                                    <option value="seasonal">موسمي</option>
                                    <option value="event">فعالية</option>
                                    <option value="offer">عرض</option>
                                    <option value="announcement">إعلان</option>
                                </select>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="position">الموضع <span class="text-danger">*</span></label>
                                <select class="form-control" id="position" name="position" required>
                                    <option value="">اختر الموضع</option>
                                    <option value="header">الرأس</option>
                                    <option value="footer">التذييل</option>
                                    <option value="sidebar">الشريط الجانبي</option>
                                    <option value="hero">البطل</option>
                                    <option value="popup">نافذة منبثقة</option>
                                    <option value="inline">داخل المحتوى</option>
                                </select>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="display_location">مكان العرض</label>
                                <select class="form-control" id="display_location" name="display_location">
                                    <option value="">جميع الصفحات</option>
                                    <option value="home">الصفحة الرئيسية</option>
                                    <option value="menu">صفحة القائمة</option>
                                    <option value="checkout">صفحة الدفع</option>
                                    <option value="profile">صفحة الملف الشخصي</option>
                                </select>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Content -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary mb-3 mt-4">المحتوى</h6>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="image_url">صورة البانر</label>
                                <input type="file" class="form-control" id="image_url" name="image_url" accept="image/*">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="background_color">لون الخلفية</label>
                                <input type="text" class="form-control colorpicker" id="background_color" name="background_color">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="text_color">لون النص</label>
                                <input type="text" class="form-control colorpicker" id="text_color" name="text_color">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="button_text">نص الزر</label>
                                <input type="text" class="form-control" id="button_text" name="button_text">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="button_url">رابط الزر</label>
                                <input type="url" class="form-control" id="button_url" name="button_url">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="button_color">لون الزر</label>
                                <input type="text" class="form-control colorpicker" id="button_color" name="button_color">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Scheduling -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary mb-3 mt-4">الجدولة</h6>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="start_date">تاريخ البداية</label>
                                <input type="date" class="form-control" id="start_date" name="start_date">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="start_time">وقت البداية</label>
                                <input type="time" class="form-control" id="start_time" name="start_time">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="end_date">تاريخ النهاية</label>
                                <input type="date" class="form-control" id="end_date" name="end_date">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="end_time">وقت النهاية</label>
                                <input type="time" class="form-control" id="end_time" name="end_time">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Limits -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary mb-3 mt-4">حدود الأداء</h6>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="max_impressions">الحد الأقصى للمشاهدات</label>
                                <input type="number" class="form-control" id="max_impressions" name="max_impressions" min="0">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="max_clicks">الحد الأقصى للنقرات</label>
                                <input type="number" class="form-control" id="max_clicks" name="max_clicks" min="0">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="priority">الأولوية</label>
                                <input type="number" class="form-control" id="priority" name="priority" min="0" max="10" value="5">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Settings -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary mb-3 mt-4">الإعدادات</h6>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" checked>
                                    <label class="form-check-label" for="is_active">
                                        نشط
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" value="1">
                                    <label class="form-check-label" for="is_featured">
                                        مميز
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_animated" name="is_animated" value="1">
                                    <label class="form-check-label" for="is_animated">
                                        متحرك
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="auto_hide" name="auto_hide" value="1">
                                    <label class="form-check-label" for="auto_hide">
                                        إخفاء تلقائي
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary" id="save-banner-btn">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Show Banner Modal -->
<div class="modal fade" id="showBannerModal" tabindex="-1" role="dialog" aria-labelledby="showBannerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="showBannerModalLabel">تفاصيل البانر</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>عنوان البانر:</strong></label>
                            <p id="show_title"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>النوع:</strong></label>
                            <p id="show_banner_type"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>الموضع:</strong></label>
                            <p id="show_position"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>مكان العرض:</strong></label>
                            <p id="show_display_location"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label><strong>الوصف:</strong></label>
                            <p id="show_description"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>تاريخ البداية:</strong></label>
                            <p id="show_start_date"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>تاريخ النهاية:</strong></label>
                            <p id="show_end_date"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>الحالة:</strong></label>
                            <p id="show_status"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>مميز:</strong></label>
                            <p id="show_featured"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>المشاهدات:</strong></label>
                            <p id="show_impressions"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>النقرات:</strong></label>
                            <p id="show_clicks"></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<!-- Color Picker JS -->
<script src="{{URL::asset('assets/plugins/spectrum-colorpicker/spectrum.js')}}"></script>

<script>
$(document).ready(function() {
    // Setup CSRF token for AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Initialize Select2
    $('.select2').select2({
        theme: 'default',
        width: '100%'
    });

    // Initialize Color Picker
    $('.colorpicker').spectrum({
        showInput: true,
        className: "full-spectrum",
        showInitial: true,
        showPalette: true,
        showSelectionPalette: true,
        maxSelectionSize: 10,
        preferredFormat: "hex",
        localStorageKey: "spectrum.demo",
        palette: [
            ["rgb(0, 0, 0)", "rgb(67, 67, 67)", "rgb(102, 102, 102)",
            "rgb(204, 204, 204)", "rgb(217, 217, 217)","rgb(255, 255, 255)"],
            ["rgb(152, 0, 0)", "rgb(255, 0, 0)", "rgb(255, 153, 0)",
            "rgb(255, 255, 0)", "rgb(0, 255, 0)", "rgb(0, 255, 255)",
            "rgb(74, 134, 232)", "rgb(0, 0, 255)", "rgb(153, 0, 255)",
            "rgb(255, 0, 255)"]
        ]
    });

    // Initialize DataTable
    var bannersTable = $('#banners-table').DataTable({
        processing: true,
        serverSide: true,
        order: [[0, 'desc']],
        pageLength: 25,
        dom: '<"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4"<"flex items-center mb-4 sm:mb-0"l><"flex items-center"f>>rtip',
        ajax: {
            url: '{{ route("api.menu.banners.index") }}',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        },
        columns: [
            {data: 'id', name: 'id'},
            {data: 'title', name: 'title'},
            {data: 'banner_type', name: 'banner_type'},
            {data: 'position', name: 'position'},
            {data: 'start_date', name: 'start_date'},
            {data: 'end_date', name: 'end_date'},
            {data: 'is_active', name: 'is_active', render: function(data) {
                return data ? '<span class="badge badge-success">نشط</span>' : '<span class="badge badge-danger">غير نشط</span>';
            }},
            {data: 'action', name: 'action', orderable: false, searchable: false}
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json',
            processing: '<div class="flex items-center justify-center"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div><span class="mr-2">جاري التحميل...</span></div>'
        },
        initComplete: function() {
            // Apply custom styling after DataTable initialization
            $('.dataTables_wrapper').addClass('bg-white rounded-lg shadow-sm');
            $('.dataTables_length select').addClass('form-select');
            $('.dataTables_filter input').addClass('form-input').attr('placeholder', 'البحث...');
        }
    });

    // Add Banner Button
    $('#add-banner-btn').click(function() {
        $('#bannerForm')[0].reset();
        $('#banner_id').val('');
        $('#bannerModalLabel').text('إضافة بانر جديد');
        $('#bannerModal').modal('show');
    });

    // Submit Banner Form
    $('#bannerForm').submit(function(e) {
        e.preventDefault();
        
        // Clear previous errors
        $('.is-invalid').removeClass('is-invalid');
        $('.invalid-feedback').text('');
        
        var formData = new FormData(this);
        var bannerId = $('#banner_id').val();
        var url = bannerId ? '{{ route("api.menu.banners.update", ":id") }}'.replace(':id', bannerId) : '{{ route("api.menu.banners.store") }}';
        var method = bannerId ? 'PUT' : 'POST';
        
        if (method === 'PUT') {
            formData.append('_method', 'PUT');
        }

        // Show loading state
        const submitBtn = $('#save-banner-btn');
        const originalText = submitBtn.text();
        submitBtn.prop('disabled', true).text('جاري الحفظ...');

        $.ajax({
            url: url,
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    $('#bannerModal').modal('hide');
                    bannersTable.ajax.reload();
                    
                    Swal.fire({
                        title: 'نجح!',
                        text: response.message || 'تم حفظ البانر بنجاح',
                        icon: 'success',
                        confirmButtonText: 'موافق',
                        timer: 3000,
                        timerProgressBar: true
                    });
                }
            },
            error: function(xhr) {
                console.error('Banner save error:', xhr);
                
                if (xhr.status === 422) {
                    // Validation errors
                    const errors = xhr.responseJSON?.errors || {};
                    let errorMessages = [];
                    
                    $.each(errors, function(key, value) {
                        $('#' + key).addClass('is-invalid');
                        $('#' + key).siblings('.invalid-feedback').text(value[0]);
                        errorMessages.push(value[0]);
                    });
                    
                    if (errorMessages.length > 0) {
                        Swal.fire({
                            title: 'خطأ في البيانات',
                            html: errorMessages.join('<br>'),
                            icon: 'error',
                            confirmButtonText: 'موافق'
                        });
                    }
                } else {
                    // General error
                    Swal.fire({
                        title: 'خطأ!',
                        text: xhr.responseJSON?.message || 'حدث خطأ أثناء حفظ البانر',
                        icon: 'error',
                        confirmButtonText: 'موافق'
                    });
                }
            },
            complete: function() {
                // Reset button state
                submitBtn.prop('disabled', false).text(originalText);
            }
        });
    });

    // Edit Banner
    $(document).on('click', '.edit-banner', function() {
        var bannerId = $(this).data('id');
        
        $.ajax({
            url: '{{ route("api.menu.banners.show", ":id") }}'.replace(':id', bannerId),
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    var banner = response.data;
                    $('#banner_id').val(banner.id);
                    $('#title').val(banner.title);
                    $('#title_en').val(banner.title_en);
                    $('#description').val(banner.description);
                    $('#banner_type').val(banner.banner_type);
                    $('#position').val(banner.position);
                    $('#display_location').val(banner.display_location);
                    $('#background_color').spectrum("set", banner.background_color);
                    $('#text_color').spectrum("set", banner.text_color);
                    $('#button_text').val(banner.button_text);
                    $('#button_url').val(banner.button_url);
                    $('#button_color').spectrum("set", banner.button_color);
                    $('#start_date').val(banner.start_date);
                    $('#start_time').val(banner.start_time);
                    $('#end_date').val(banner.end_date);
                    $('#end_time').val(banner.end_time);
                    $('#max_impressions').val(banner.max_impressions);
                    $('#max_clicks').val(banner.max_clicks);
                    $('#priority').val(banner.priority);
                    $('#is_active').prop('checked', banner.is_active);
                    $('#is_featured').prop('checked', banner.is_featured);
                    $('#is_animated').prop('checked', banner.is_animated);
                    $('#auto_hide').prop('checked', banner.auto_hide);
                    
                    $('#bannerModalLabel').text('تعديل البانر');
                    $('#bannerModal').modal('show');
                }
            }
        });
    });

    // Show Banner
    $(document).on('click', '.show-banner', function() {
        var bannerId = $(this).data('id');
        
        $.ajax({
            url: '{{ route("api.menu.banners.show", ":id") }}'.replace(':id', bannerId),
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    var banner = response.data;
                    $('#show_title').text(banner.title);
                    $('#show_banner_type').text(banner.banner_type);
                    $('#show_position').text(banner.position);
                    $('#show_display_location').text(banner.display_location || 'جميع الصفحات');
                    $('#show_description').text(banner.description || 'غير محدد');
                    $('#show_start_date').text(banner.start_date + ' ' + (banner.start_time || ''));
                    $('#show_end_date').text(banner.end_date + ' ' + (banner.end_time || ''));
                    $('#show_status').html(banner.is_active ? '<span class="badge badge-success">نشط</span>' : '<span class="badge badge-danger">غير نشط</span>');
                    $('#show_featured').html(banner.is_featured ? '<span class="badge badge-info">مميز</span>' : '<span class="badge badge-secondary">عادي</span>');
                    $('#show_impressions').text(banner.impressions_count || 0);
                    $('#show_clicks').text(banner.clicks_count || 0);
                    
                    $('#showBannerModal').modal('show');
                }
            }
        });
    });

    // Delete Banner
    $(document).on('click', '.delete-banner', function() {
        var bannerId = $(this).data('id');
        
        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: 'لن تتمكن من التراجع عن هذا الإجراء!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#ef4444',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'نعم، احذف!',
            cancelButtonText: 'إلغاء',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                // Show loading
                Swal.fire({
                    title: 'جاري الحذف...',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                $.ajax({
                    url: '{{ route("api.menu.banners.destroy", ":id") }}'.replace(':id', bannerId),
                    method: 'DELETE',
                    success: function(response) {
                        if (response.success) {
                            bannersTable.ajax.reload();
                            Swal.fire({
                                title: 'تم الحذف!',
                                text: response.message || 'تم حذف البانر بنجاح',
                                icon: 'success',
                                confirmButtonText: 'موافق',
                                timer: 3000,
                                timerProgressBar: true
                            });
                        }
                    },
                    error: function(xhr) {
                        console.error('Banner delete error:', xhr);
                        Swal.fire({
                            title: 'خطأ!',
                            text: xhr.responseJSON?.message || 'حدث خطأ أثناء حذف البانر',
                            icon: 'error',
                            confirmButtonText: 'موافق'
                        });
                    }
                });
            }
        });
    });

    // Toggle Banner Status
    $(document).on('click', '.toggle-status', function() {
        var bannerId = $(this).data('id');
        
        $.ajax({
            url: '{{ route("api.menu.banners.toggle-status", ":id") }}'.replace(':id', bannerId),
            method: 'POST',
            success: function(response) {
                if (response.success) {
                    bannersTable.ajax.reload();
                    Swal.fire({
                        title: 'نجح!',
                        text: response.message || 'تم تغيير حالة البانر بنجاح',
                        icon: 'success',
                        confirmButtonText: 'موافق',
                        timer: 3000,
                        timerProgressBar: true
                    });
                }
            },
            error: function(xhr) {
                console.error('Banner toggle status error:', xhr);
                Swal.fire({
                    title: 'خطأ!',
                    text: xhr.responseJSON?.message || 'حدث خطأ أثناء تغيير حالة البانر',
                    icon: 'error',
                    confirmButtonText: 'موافق'
                });
            }
        });
    });
});
</script>
@endpush