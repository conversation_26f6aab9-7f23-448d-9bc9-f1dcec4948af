@extends('layouts.master')

@section('css')
<link href="{{ URL::asset('assets/plugins/select2/css/select2.min.css') }}" rel="stylesheet">
@endsection

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">إدارة موظفي التوصيل</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ تعديل موظف</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <a href="{{ route('delivery.personnel.index') }}" class="btn btn-secondary">
                <i class="fa fa-arrow-left"></i> العودة للقائمة
            </a>
        </div>
        <div class="pr-1 mb-3 mb-xl-0">
            <a href="{{ route('delivery.personnel.show', $personnel->id) }}" class="btn btn-info">
                <i class="fa fa-eye"></i> عرض التفاصيل
            </a>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')

@if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <strong>نجح!</strong> {{ session('success') }}
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
@endif

@if(session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <strong>خطأ!</strong> {{ session('error') }}
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
@endif

<!-- row -->
<div class="row">
    <div class="col-lg-12 col-md-12">
        <div class="card">
            <div class="card-body">
                <div class="main-content-label mg-b-5">
                    تعديل موظف التوصيل: {{ $personnel->user->name }}
                </div>
                <p class="mg-b-20">قم بتعديل البيانات المطلوبة</p>

                <form action="{{ route('delivery.personnel.update', $personnel->id) }}" method="POST" id="personnelForm" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <!-- Personal Information -->
                        <div class="col-md-12">
                            <h6 class="mg-b-10">المعلومات الشخصية</h6>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name', $personnel->user->name) }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                                <input type="tel" class="form-control @error('phone') is-invalid @enderror" 
                                       id="phone" name="phone" value="{{ old('phone', $personnel->user->phone) }}" required>
                                @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                       id="email" name="email" value="{{ old('email', $personnel->user->email) }}">
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="national_id" class="form-label">رقم الهوية الوطنية <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('national_id') is-invalid @enderror" 
                                       id="national_id" name="national_id" value="{{ old('national_id', $personnel->national_id) }}" required>
                                @error('national_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="date_of_birth" class="form-label">تاريخ الميلاد</label>
                                <input type="date" class="form-control @error('date_of_birth') is-invalid @enderror" 
                                       id="date_of_birth" name="date_of_birth" value="{{ old('date_of_birth', $personnel->date_of_birth) }}">
                                @error('date_of_birth')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="branch_id" class="form-label">الفرع <span class="text-danger">*</span></label>
                                @if(auth()->user()->branch_id)
                                    <!-- If user has a specific branch, show it as readonly -->
                                    <input type="text" class="form-control" value="{{ auth()->user()->branch->name }}" readonly>
                                    <input type="hidden" name="branch_id" value="{{ auth()->user()->branch_id }}">
                                @else
                                    <!-- If user can select branch -->
                                    <select class="form-control select2 @error('branch_id') is-invalid @enderror" 
                                            id="branch_id" name="branch_id" required>
                                        <option value="">اختر الفرع</option>
                                        @foreach($branches as $branch)
                                            <option value="{{ $branch->id }}" {{ old('branch_id', $personnel->branch_id) == $branch->id ? 'selected' : '' }}>
                                                {{ $branch->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                @endif
                                @error('branch_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Vehicle Information -->
                        <div class="col-md-12">
                            <h6 class="mg-t-20 mg-b-10">معلومات المركبة</h6>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="vehicle_type" class="form-label">نوع المركبة <span class="text-danger">*</span></label>
                                <select class="form-control @error('vehicle_type') is-invalid @enderror" 
                                        id="vehicle_type" name="vehicle_type" required>
                                    <option value="">اختر نوع المركبة</option>
                                    <option value="motorcycle" {{ old('vehicle_type', $personnel->vehicle_type) == 'motorcycle' ? 'selected' : '' }}>دراجة نارية</option>
                                    <option value="car" {{ old('vehicle_type', $personnel->vehicle_type) == 'car' ? 'selected' : '' }}>سيارة</option>
                                    <option value="bicycle" {{ old('vehicle_type', $personnel->vehicle_type) == 'bicycle' ? 'selected' : '' }}>دراجة هوائية</option>
                                    <option value="scooter" {{ old('vehicle_type', $personnel->vehicle_type) == 'scooter' ? 'selected' : '' }}>سكوتر</option>
                                </select>
                                @error('vehicle_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="vehicle_model" class="form-label">موديل المركبة</label>
                                <input type="text" class="form-control @error('vehicle_model') is-invalid @enderror" 
                                       id="vehicle_model" name="vehicle_model" value="{{ old('vehicle_model', $personnel->vehicle_model) }}">
                                @error('vehicle_model')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="vehicle_plate_number" class="form-label">رقم اللوحة</label>
                                <input type="text" class="form-control @error('vehicle_plate_number') is-invalid @enderror" 
                                       id="vehicle_plate_number" name="vehicle_plate_number" value="{{ old('vehicle_plate_number', $personnel->vehicle_plate_number) }}">
                                @error('vehicle_plate_number')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="vehicle_color" class="form-label">لون المركبة</label>
                                <input type="text" class="form-control @error('vehicle_color') is-invalid @enderror" 
                                       id="vehicle_color" name="vehicle_color" value="{{ old('vehicle_color', $personnel->vehicle_color) }}">
                                @error('vehicle_color')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- License Information -->
                        <div class="col-md-12">
                            <h6 class="mg-t-20 mg-b-10">معلومات الرخصة</h6>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="driving_license_number" class="form-label">رقم رخصة القيادة</label>
                                <input type="text" class="form-control @error('driving_license_number') is-invalid @enderror" 
                                       id="driving_license_number" name="driving_license_number" value="{{ old('driving_license_number', $personnel->driving_license_number) }}">
                                @error('driving_license_number')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="license_expiry_date" class="form-label">تاريخ انتهاء الرخصة</label>
                                <input type="date" class="form-control @error('license_expiry_date') is-invalid @enderror" 
                                       id="license_expiry_date" name="license_expiry_date" value="{{ old('license_expiry_date', $personnel->license_expiry_date) }}">
                                @error('license_expiry_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Emergency Contact -->
                        <div class="col-md-12">
                            <h6 class="mg-t-20 mg-b-10">جهة الاتصال في حالات الطوارئ</h6>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="emergency_contact_name" class="form-label">اسم جهة الاتصال</label>
                                <input type="text" class="form-control @error('emergency_contact_name') is-invalid @enderror" 
                                       id="emergency_contact_name" name="emergency_contact_name" value="{{ old('emergency_contact_name', $personnel->emergency_contact_name) }}">
                                @error('emergency_contact_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="emergency_contact_phone" class="form-label">رقم هاتف جهة الاتصال</label>
                                <input type="tel" class="form-control @error('emergency_contact_phone') is-invalid @enderror" 
                                       id="emergency_contact_phone" name="emergency_contact_phone" value="{{ old('emergency_contact_phone', $personnel->emergency_contact_phone) }}">
                                @error('emergency_contact_phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Status and Settings -->
                        <div class="col-md-12">
                            <h6 class="mg-t-20 mg-b-10">الإعدادات</h6>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="status" class="form-label">الحالة</label>
                                <select class="form-control @error('status') is-invalid @enderror" id="status" name="status">
                                    <option value="active" {{ old('status', $personnel->status) == 'active' ? 'selected' : '' }}>نشط</option>
                                    <option value="inactive" {{ old('status', $personnel->status) == 'inactive' ? 'selected' : '' }}>غير نشط</option>
                                    <option value="busy" {{ old('status', $personnel->status) == 'busy' ? 'selected' : '' }}>مشغول</option>
                                </select>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="max_concurrent_orders" class="form-label">الحد الأقصى للطلبات المتزامنة</label>
                                <input type="number" min="1" max="10" 
                                       class="form-control @error('max_concurrent_orders') is-invalid @enderror" 
                                       id="max_concurrent_orders" name="max_concurrent_orders" 
                                       value="{{ old('max_concurrent_orders', $personnel->max_concurrent_orders ?? 3) }}">
                                @error('max_concurrent_orders')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Password Change -->
                        <div class="col-md-12">
                            <h6 class="mg-t-20 mg-b-10">تغيير كلمة المرور (اختياري)</h6>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="password" class="form-label">كلمة المرور الجديدة</label>
                                <input type="password" class="form-control @error('password') is-invalid @enderror" 
                                       id="password" name="password">
                                <small class="form-text text-muted">اتركه فارغاً إذا كنت لا تريد تغيير كلمة المرور</small>
                                @error('password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="password_confirmation" class="form-label">تأكيد كلمة المرور</label>
                                <input type="password" class="form-control" 
                                       id="password_confirmation" name="password_confirmation">
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control @error('notes') is-invalid @enderror" 
                                          id="notes" name="notes" rows="3">{{ old('notes', $personnel->notes) }}</textarea>
                                @error('notes')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="mg-t-30">
                        <button type="submit" class="btn btn-primary pd-x-20">
                            <i class="fa fa-save"></i> حفظ التعديلات
                        </button>
                        <a href="{{ route('delivery.personnel.show', $personnel->id) }}" class="btn btn-info pd-x-20">
                            <i class="fa fa-eye"></i> عرض التفاصيل
                        </a>
                        <a href="{{ route('delivery.personnel.index') }}" class="btn btn-secondary pd-x-20">
                            <i class="fa fa-times"></i> إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<!-- /row -->

@endsection

@section('js')
<script src="{{ URL::asset('assets/plugins/select2/js/select2.min.js') }}"></script>

<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        placeholder: 'اختر الفرع',
        allowClear: true
    });

    // Phone number formatting
    $('#phone, #emergency_contact_phone').on('input', function() {
        let value = $(this).val().replace(/\D/g, '');
        if (value.length > 0) {
            if (value.startsWith('966')) {
                // Saudi number format
                value = value.replace(/(\d{3})(\d{2})(\d{3})(\d{4})/, '$1 $2 $3 $4');
            } else if (value.length === 10) {
                // Local number format
                value = value.replace(/(\d{3})(\d{3})(\d{4})/, '$1 $2 $3');
            }
        }
        $(this).val(value);
    });

    // National ID formatting (Saudi format)
    $('#national_id').on('input', function() {
        let value = $(this).val().replace(/\D/g, '');
        if (value.length > 10) {
            value = value.substring(0, 10);
        }
        $(this).val(value);
    });

    // Password confirmation validation
    $('#password_confirmation').on('input', function() {
        const password = $('#password').val();
        const confirmation = $(this).val();
        
        if (password && confirmation && password !== confirmation) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">كلمة المرور غير متطابقة</div>');
            }
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });

    // Form validation
    $('#personnelForm').on('submit', function(e) {
        let isValid = true;
        
        // Check required fields
        $('input[required], select[required]').each(function() {
            if (!$(this).val()) {
                $(this).addClass('is-invalid');
                isValid = false;
            } else {
                $(this).removeClass('is-invalid');
            }
        });

        // Validate email format if provided
        const email = $('#email').val();
        if (email && !isValidEmail(email)) {
            $('#email').addClass('is-invalid');
            isValid = false;
        }

        // Validate phone number
        const phone = $('#phone').val();
        if (phone && phone.replace(/\D/g, '').length < 10) {
            $('#phone').addClass('is-invalid');
            isValid = false;
        }

        // Validate national ID (should be 10 digits for Saudi)
        const nationalId = $('#national_id').val();
        if (nationalId && nationalId.replace(/\D/g, '').length !== 10) {
            $('#national_id').addClass('is-invalid');
            isValid = false;
        }

        // Validate password confirmation
        const password = $('#password').val();
        const confirmation = $('#password_confirmation').val();
        if (password && password !== confirmation) {
            $('#password_confirmation').addClass('is-invalid');
            isValid = false;
        }

        if (!isValid) {
            e.preventDefault();
            alert('يرجى تصحيح الأخطاء في النموذج');
        }
    });

    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
});
</script>
@endsection