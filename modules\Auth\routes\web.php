<?php

use Illuminate\Support\Facades\Route;
use Modules\Auth\Http\Controllers\AuthController;
use Modules\Auth\Http\Controllers\UserWebController;
use Modules\Auth\Http\Controllers\RoleWebController;
use Modules\Auth\Http\Controllers\PermissionWebController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::middleware('web')->group(function () {
    // Add your web routes here
});

// User Management Web Routes
Route::prefix('admin/users')->name('users.')->middleware(['auth', 'web'])->group(function () {
    Route::get('/', [UserWebController::class, 'index'])->name('index');
    Route::get('/create', [UserWebController::class, 'create'])->name('create');
    Route::post('/', [UserWebController::class, 'store'])->name('store');
    Route::get('/{user}', [UserWebController::class, 'show'])->name('show');
    Route::get('/{user}/edit', [UserWebController::class, 'edit'])->name('edit');
    Route::put('/{user}', [UserWebController::class, 'update'])->name('update');
    Route::delete('/{user}', [UserWebController::class, 'destroy'])->name('destroy');

    // User status management
    Route::post('/{user}/activate', [UserWebController::class, 'activate'])->name('activate');
    Route::post('/{user}/deactivate', [UserWebController::class, 'deactivate'])->name('deactivate');
    Route::post('/{user}/reset-password', [UserWebController::class, 'resetPassword'])->name('reset-password');

    // DataTable AJAX endpoints
    Route::get('/data/list', [UserWebController::class, 'getUsersData'])->name('data');

    // User roles management
    Route::get('/{user}/roles', [UserWebController::class, 'roles'])->name('roles');
    Route::post('/{user}/roles', [UserWebController::class, 'assignRoles'])->name('roles.assign');
    Route::get('/{user}/roles/data', [UserWebController::class, 'getUserRolesData'])->name('roles.data');

    // User permissions
    Route::get('/{user}/permissions', [UserWebController::class, 'permissions'])->name('permissions');
    Route::post('/{user}/permissions', [UserWebController::class, 'assignPermissions'])->name('permissions.assign');
});

// Role Management Web Routes
Route::prefix('admin/roles')->name('roles.')->middleware(['auth', 'web'])->group(function () {
    Route::get('/', [RoleWebController::class, 'index'])->name('index');
    Route::get('/create', [RoleWebController::class, 'create'])->name('create');
    Route::post('/', [RoleWebController::class, 'store'])->name('store');
    Route::get('/{role}', [RoleWebController::class, 'show'])->name('show');
    Route::get('/{role}/edit', [RoleWebController::class, 'edit'])->name('edit');
    Route::put('/{role}', [RoleWebController::class, 'update'])->name('update');
    Route::delete('/{role}', [RoleWebController::class, 'destroy'])->name('destroy');

    // DataTable AJAX endpoints
    Route::get('/data/list', [RoleWebController::class, 'getRolesData'])->name('data');

    // Role permissions management
    Route::get('/{role}/permissions', [RoleWebController::class, 'permissions'])->name('permissions');
    Route::post('/{role}/permissions', [RoleWebController::class, 'assignPermissions'])->name('permissions.assign');
    Route::get('/{role}/permissions/data', [RoleWebController::class, 'getRolePermissionsData'])->name('permissions.data');

    // Role users
    Route::get('/{role}/users', [RoleWebController::class, 'users'])->name('users');
    Route::get('/{role}/users/data', [RoleWebController::class, 'getRoleUsersData'])->name('users.data');
});

// Permission Management Web Routes
Route::prefix('admin/permissions')->name('permissions.')->middleware(['auth', 'web'])->group(function () {
    Route::get('/', [PermissionWebController::class, 'index'])->name('index');
    Route::get('/create', [PermissionWebController::class, 'create'])->name('create');
    Route::post('/', [PermissionWebController::class, 'store'])->name('store');
    Route::get('/{permission}', [PermissionWebController::class, 'show'])->name('show');
    Route::get('/{permission}/edit', [PermissionWebController::class, 'edit'])->name('edit');
    Route::put('/{permission}', [PermissionWebController::class, 'update'])->name('update');
    Route::delete('/{permission}', [PermissionWebController::class, 'destroy'])->name('destroy');

    // DataTable AJAX endpoints
    Route::get('/data/list', [PermissionWebController::class, 'getPermissionsData'])->name('data');

    // Bulk operations
    Route::post('/bulk-create', [PermissionWebController::class, 'bulkCreate'])->name('bulk-create');
    Route::post('/bulk-delete', [PermissionWebController::class, 'bulkDelete'])->name('bulk-delete');

    // Permission groups
    Route::get('/groups/list', [PermissionWebController::class, 'groups'])->name('groups');
    Route::get('/groups/{group}/permissions', [PermissionWebController::class, 'groupPermissions'])->name('groups.permissions');
});
