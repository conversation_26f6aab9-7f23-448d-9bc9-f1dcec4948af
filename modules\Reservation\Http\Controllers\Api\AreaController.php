<?php

namespace Modules\Reservation\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Modules\Reservation\Services\AreaService;
use Modules\Reservation\Http\Requests\CreateAreaRequest;
use Modules\Reservation\Http\Requests\UpdateAreaRequest;
use Modules\Reservation\Http\Resources\AreaResource;
use Modules\Reservation\Http\Resources\AreaCollection;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Exception;

class AreaController extends Controller
{
    protected AreaService $areaService;

    public function __construct(AreaService $areaService)
    {
        $this->areaService = $areaService;
    }

    /**
     * Display a listing of areas.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $areas = $this->areaService->getAllAreas($user->branch_id);

            return response()->json([
                'success' => true,
                'data' => new AreaCollection($areas),
                'message' => 'Areas retrieved successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve areas',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created area.
     */
    public function store(CreateAreaRequest $request): JsonResponse
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id || !$user->tenant_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch or tenant'
                ], 400);
            }

            $data = $request->validated();
            $data['tenant_id'] = $user->tenant_id;
            $data['branch_id'] = $user->branch_id;
            
            $area = $this->areaService->createArea($data);

            return response()->json([
                'success' => true,
                'data' => new AreaResource($area),
                'message' => 'Area created successfully'
            ], 201);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create area',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Display the specified area.
     */
    public function show(int $id): JsonResponse
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $area = $this->areaService->getAreaByIdForBranch($id, $user->branch_id);

            if (!$area) {
                return response()->json([
                    'success' => false,
                    'message' => 'Area not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => new AreaResource($area),
                'message' => 'Area retrieved successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve area',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified area.
     */
    public function update(UpdateAreaRequest $request, int $id): JsonResponse
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $area = $this->areaService->updateAreaForBranch($id, $request->validated(), $user->branch_id);

            if (!$area) {
                return response()->json([
                    'success' => false,
                    'message' => 'Area not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => new AreaResource($area),
                'message' => 'Area updated successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update area',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Remove the specified area.
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $user = auth()->user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $deleted = $this->areaService->deleteAreaForBranch($id, $user->branch_id);

            if (!$deleted) {
                return response()->json([
                    'success' => false,
                    'message' => 'Area not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Area deleted successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete area',
                'error' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get tables in the specified area.
     */
    public function tables(int $id): JsonResponse
    {
        try {
            $tables = $this->areaService->getAreaTables($id);

            return response()->json([
                'success' => true,
                'data' => $tables,
                'message' => 'Area tables retrieved successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve area tables',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get statistics for the specified area.
     */
    public function statistics(int $id): JsonResponse
    {
        try {
            $stats = $this->areaService->getAreaStats($id);

            return response()->json([
                'success' => true,
                'data' => $stats,
                'message' => 'Area statistics retrieved successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve area statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}