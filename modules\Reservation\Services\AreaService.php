<?php

namespace Modules\Reservation\Services;

use App\Models\Area;
use App\Models\Table;
use Modules\Reservation\Contracts\AreaServiceInterface;
use Illuminate\Database\Eloquent\Collection;
use Exception;

class AreaService implements AreaServiceInterface
{
    /**
     * Get all areas for a branch.
     */
    public function getAllAreas(int $branchId = null): Collection
    {
        $query = Area::with(['tables']);
        
        if ($branchId) {
            $query->where('branch_id', $branchId);
        }
        
        return $query->orderBy('name')->get();
    }

    /**
     * Create a new area.
     */
    public function createArea(array $data): Area
    {
        return Area::create($data);
    }

    /**
     * Get area by ID.
     */
    public function getAreaById(int $id): ?Area
    {
        return Area::with(['tables', 'branch'])->find($id);
    }

    /**
     * Get area by ID for a specific branch.
     */
    public function getAreaByIdForBranch(int $id, int $branchId): ?Area
    {
        return Area::with(['tables', 'branch'])
            ->where('id', $id)
            ->where('branch_id', $branchId)
            ->first();
    }

    /**
     * Update area.
     */
    public function updateArea(int $id, array $data): Area
    {
        $area = Area::findOrFail($id);
        $area->update($data);
        
        return $area->load(['tables', 'branch']);
    }

    /**
     * Update area for a specific branch.
     */
    public function updateAreaForBranch(int $id, array $data, int $branchId): ?Area
    {
        $area = Area::where('id', $id)
            ->where('branch_id', $branchId)
            ->first();
        
        if (!$area) {
            return null;
        }
        
        $area->update($data);
        
        return $area->load(['tables', 'branch']);
    }

    /**
     * Delete area.
     */
    public function deleteArea(int $id): bool
    {
        $area = Area::findOrFail($id);
        
        // Check if area has tables
        if ($area->tables()->count() > 0) {
            throw new Exception('Cannot delete area that contains tables. Please move or delete tables first.');
        }
        
        return $area->delete();
    }

    /**
     * Delete area for a specific branch.
     */
    public function deleteAreaForBranch(int $id, int $branchId): bool
    {
        $area = Area::where('id', $id)
            ->where('branch_id', $branchId)
            ->first();
        
        if (!$area) {
            return false;
        }
        
        // Check if area has tables
        if ($area->tables()->count() > 0) {
            throw new Exception('Cannot delete area that contains tables. Please move or delete tables first.');
        }
        
        return $area->delete();
    }

    /**
     * Get tables in an area.
     */
    public function getAreaTables(int $areaId): Collection
    {
        return Table::where('area_id', $areaId)
            ->with(['reservations' => function ($query) {
                $query->whereIn('reservation_status_id', function ($q) {
                    $q->select('id')
                        ->from('reservation_statuses')
                        ->whereIn('name', ['confirmed', 'seated']);
                })->orderBy('reservation_datetime');
            }])
            ->orderBy('table_number')
            ->get();
    }

    /**
     * Get area statistics.
     */
    public function getAreaStats(int $areaId): array
    {
        $area = Area::with(['tables'])->findOrFail($areaId);
        
        $totalTables = $area->tables->count();
        $totalCapacity = $area->tables->sum('seating_capacity');
        
        $statusCounts = $area->tables->groupBy('status')->map->count();
        
        $occupancyRate = $totalTables > 0 
            ? round(($statusCounts->get('occupied', 0) / $totalTables) * 100, 1)
            : 0;
        
        return [
            'area_name' => $area->name,
            'total_tables' => $totalTables,
            'total_capacity' => $totalCapacity,
            'available_tables' => $statusCounts->get('available', 0),
            'occupied_tables' => $statusCounts->get('occupied', 0),
            'reserved_tables' => $statusCounts->get('reserved', 0),
            'cleaning_tables' => $statusCounts->get('cleaning', 0),
            'out_of_order_tables' => $statusCounts->get('out_of_order', 0),
            'occupancy_rate' => $occupancyRate,
        ];
    }
}