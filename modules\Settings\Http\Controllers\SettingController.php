<?php

namespace Modules\Settings\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Validation\ValidationException;
use Modules\Settings\Services\SettingService;

class SettingController extends Controller
{
    protected $service;

    public function __construct(SettingService $service)
    {
        $this->service = $service;
    }
    

    public function index(Request $request)
    {
        try {
            $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
                'category' => 'nullable|string|max:50',
            ]);

            $filters = $request->only(['tenant_id', 'branch_id', 'category']);
            $settings = $this->service->getAll($filters);
            
            return response()->json([
                'success' => true,
                'data' => $settings,
                'message' => 'Settings retrieved successfully'
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function show(Request $request, $key)
    {
        try {
            $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
                'category' => 'nullable|string|max:50',
            ]);

            $tenantId = $request->get('tenant_id');
            $branchId = $request->get('branch_id');
            $category = $request->get('category');
            
            $setting = $this->service->get($key, $tenantId, $branchId, $category);
            
            if (!$setting) {
                return response()->json([
                    'success' => false,
                    'message' => 'Setting not found'
                ], 404);
            }
            
            return response()->json([
                'success' => true,
                'data' => $setting,
                'message' => 'Setting retrieved successfully'
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve setting',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function store(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
                'category' => 'required|string|max:50',
                'key' => 'required|string|max:100',
                'value' => 'nullable',
                'data_type' => 'nullable|string|in:string,integer,boolean,json',
                'description' => 'nullable|string',
                'is_public' => 'nullable|boolean',
                'logo_url' => 'nullable|url',
                'support_email' => 'nullable|email',
                'support_phone' => 'nullable|string|max:20',
                'address' => 'nullable|string',
                'website_url' => 'nullable|url',
                'social_links' => 'nullable|array',
            ]);

            $setting = $this->service->set($validatedData);
            
            return response()->json([
                'success' => true,
                'data' => $setting,
                'message' => 'Setting created successfully'
            ], 201);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create setting',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function update(Request $request, $key)
    {
        try {
            $validatedData = $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
                'category' => 'nullable|string|max:50',
                'value' => 'nullable',
                'data_type' => 'nullable|string|in:string,integer,boolean,json',
                'description' => 'nullable|string',
                'is_public' => 'nullable|boolean',
                'logo_url' => 'nullable|url',
                'support_email' => 'nullable|email',
                'support_phone' => 'nullable|string|max:20',
                'address' => 'nullable|string',
                'website_url' => 'nullable|url',
                'social_links' => 'nullable|array',
            ]);

            $validatedData['key'] = $key;
            $setting = $this->service->set($validatedData);
            
            return response()->json([
                'success' => true,
                'data' => $setting,
                'message' => 'Setting updated successfully'
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update setting',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function destroy(Request $request, $key)
    {
        try {
            $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
                'category' => 'nullable|string|max:50',
            ]);

            $tenantId = $request->get('tenant_id');
            $branchId = $request->get('branch_id');
            $category = $request->get('category');
            
            $deleted = $this->service->delete($key, $tenantId, $branchId, $category);
            
            if (!$deleted) {
                return response()->json([
                    'success' => false,
                    'message' => 'Setting not found or could not be deleted'
                ], 404);
            }
            
            return response()->json([
                'success' => true,
                'message' => 'Setting deleted successfully'
            ], 204);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete setting',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    // ========== Specialized Setting Category Endpoints ==========

    /**
     * Get KOT (Kitchen Order Ticket) settings
     */
    public function getKotSettings(Request $request)
    {
        try {
            $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
            ]);

            $settings = $this->service->getKotSettings(
                $request->get('tenant_id'),
                $request->get('branch_id')
            );

            return response()->json([
                'success' => true,
                'data' => $settings,
                'message' => 'KOT settings retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve KOT settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create/Update KOT settings
     */
    public function setKotSettings(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
                'settings' => 'required|array',
                'settings.*' => 'required'
            ]);

            $settings = $this->service->setKotSettings(
                $validatedData['settings'],
                $request->get('tenant_id'),
                $request->get('branch_id')
            );

            return response()->json([
                'success' => true,
                'data' => $settings,
                'message' => 'KOT settings updated successfully'
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update KOT settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete KOT setting
     */
    public function deleteKotSetting(Request $request, $key)
    {
        try {
            $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
            ]);

            $deleted = $this->service->delete(
                $key,
                $request->get('tenant_id'),
                $request->get('branch_id'),
                'kot_settings'
            );

            if (!$deleted) {
                return response()->json([
                    'success' => false,
                    'message' => 'KOT setting not found or could not be deleted'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'KOT setting deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete KOT setting',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get language settings
     */
    public function getLanguageSettings(Request $request)
    {
        try {
            $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
            ]);

            $settings = $this->service->getLanguageSettings(
                $request->get('tenant_id'),
                $request->get('branch_id')
            );

            return response()->json([
                'success' => true,
                'data' => $settings,
                'message' => 'Language settings retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve language settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create/Update language settings
     */
    public function setLanguageSettings(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
                'settings' => 'required|array',
                'settings.*' => 'required'
            ]);

            $settings = $this->service->setLanguageSettings(
                $validatedData['settings'],
                $request->get('tenant_id'),
                $request->get('branch_id')
            );

            return response()->json([
                'success' => true,
                'data' => $settings,
                'message' => 'Language settings updated successfully'
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update language settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete language setting
     */
    public function deleteLanguageSetting(Request $request, $key)
    {
        try {
            $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
            ]);

            $deleted = $this->service->delete(
                $key,
                $request->get('tenant_id'),
                $request->get('branch_id'),
                'language_settings'
            );

            if (!$deleted) {
                return response()->json([
                    'success' => false,
                    'message' => 'Language setting not found or could not be deleted'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Language setting deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete language setting',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get notification settings
     */
    public function getNotificationSettings(Request $request)
    {
        try {
            $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
            ]);

            $settings = $this->service->getNotificationSettings(
                $request->get('tenant_id'),
                $request->get('branch_id')
            );

            return response()->json([
                'success' => true,
                'data' => $settings,
                'message' => 'Notification settings retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve notification settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create/Update notification settings
     */
    public function setNotificationSettings(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
                'settings' => 'required|array',
                'settings.*' => 'required'
            ]);

            $settings = $this->service->setNotificationSettings(
                $validatedData['settings'],
                $request->get('tenant_id'),
                $request->get('branch_id')
            );

            return response()->json([
                'success' => true,
                'data' => $settings,
                'message' => 'Notification settings updated successfully'
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update notification settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete notification setting
     */
    public function deleteNotificationSetting(Request $request, $key)
    {
        try {
            $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
            ]);

            $deleted = $this->service->delete(
                $key,
                $request->get('tenant_id'),
                $request->get('branch_id'),
                'notification_settings'
            );

            if (!$deleted) {
                return response()->json([
                    'success' => false,
                    'message' => 'Notification setting not found or could not be deleted'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Notification setting deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete notification setting',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get Pusher settings
     */
    public function getPusherSettings(Request $request)
    {
        try {
            $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
            ]);

            $settings = $this->service->getPusherSettings(
                $request->get('tenant_id'),
                $request->get('branch_id')
            );

            return response()->json([
                'success' => true,
                'data' => $settings,
                'message' => 'Pusher settings retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve Pusher settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create/Update Pusher settings
     */
    public function setPusherSettings(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
                'settings' => 'required|array',
                'settings.*' => 'required'
            ]);

            $settings = $this->service->setPusherSettings(
                $validatedData['settings'],
                $request->get('tenant_id'),
                $request->get('branch_id')
            );

            return response()->json([
                'success' => true,
                'data' => $settings,
                'message' => 'Pusher settings updated successfully'
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update Pusher settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete Pusher setting
     */
    public function deletePusherSetting(Request $request, $key)
    {
        try {
            $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
            ]);

            $deleted = $this->service->delete(
                $key,
                $request->get('tenant_id'),
                $request->get('branch_id'),
                'pusher_settings'
            );

            if (!$deleted) {
                return response()->json([
                    'success' => false,
                    'message' => 'Pusher setting not found or could not be deleted'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Pusher setting deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete Pusher setting',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get receipt settings
     */
    public function getReceiptSettings(Request $request)
    {
        try {
            $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
            ]);

            $settings = $this->service->getReceiptSettings(
                $request->get('tenant_id'),
                $request->get('branch_id')
            );

            return response()->json([
                'success' => true,
                'data' => $settings,
                'message' => 'Receipt settings retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve receipt settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create/Update receipt settings
     */
    public function setReceiptSettings(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
                'settings' => 'required|array',
                'settings.*' => 'required'
            ]);

            $settings = $this->service->setReceiptSettings(
                $validatedData['settings'],
                $request->get('tenant_id'),
                $request->get('branch_id')
            );

            return response()->json([
                'success' => true,
                'data' => $settings,
                'message' => 'Receipt settings updated successfully'
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update receipt settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete receipt setting
     */
    public function deleteReceiptSetting(Request $request, $key)
    {
        try {
            $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
            ]);

            $deleted = $this->service->delete(
                $key,
                $request->get('tenant_id'),
                $request->get('branch_id'),
                'receipt_settings'
            );

            if (!$deleted) {
                return response()->json([
                    'success' => false,
                    'message' => 'Receipt setting not found or could not be deleted'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Receipt setting deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete receipt setting',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get reservation settings
     */
    public function getReservationSettings(Request $request)
    {
        try {
            $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
            ]);

            $settings = $this->service->getReservationSettings(
                $request->get('tenant_id'),
                $request->get('branch_id')
            );

            return response()->json([
                'success' => true,
                'data' => $settings,
                'message' => 'Reservation settings retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve reservation settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create/Update reservation settings
     */
    public function setReservationSettings(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
                'settings' => 'required|array',
                'settings.*' => 'required'
            ]);

            $settings = $this->service->setReservationSettings(
                $validatedData['settings'],
                $request->get('tenant_id'),
                $request->get('branch_id')
            );

            return response()->json([
                'success' => true,
                'data' => $settings,
                'message' => 'Reservation settings updated successfully'
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update reservation settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete reservation setting
     */
    public function deleteReservationSetting(Request $request, $key)
    {
        try {
            $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
            ]);

            $deleted = $this->service->delete(
                $key,
                $request->get('tenant_id'),
                $request->get('branch_id'),
                'reservation_settings'
            );

            if (!$deleted) {
                return response()->json([
                    'success' => false,
                    'message' => 'Reservation setting not found or could not be deleted'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Reservation setting deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete reservation setting',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get branch delivery settings
     */
    public function getBranchDeliverySettings(Request $request)
    {
        try {
            $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
            ]);

            $settings = $this->service->getBranchDeliverySettings(
                $request->get('tenant_id'),
                $request->get('branch_id')
            );

            return response()->json([
                'success' => true,
                'data' => $settings,
                'message' => 'Branch delivery settings retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve branch delivery settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create/Update branch delivery settings
     */
    public function setBranchDeliverySettings(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
                'settings' => 'required|array',
                'settings.*' => 'required'
            ]);

            $settings = $this->service->setBranchDeliverySettings(
                $validatedData['settings'],
                $request->get('tenant_id'),
                $request->get('branch_id')
            );

            return response()->json([
                'success' => true,
                'data' => $settings,
                'message' => 'Branch delivery settings updated successfully'
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update branch delivery settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete branch delivery setting
     */
    public function deleteBranchDeliverySetting(Request $request, $key)
    {
        try {
            $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
            ]);

            $deleted = $this->service->delete(
                $key,
                $request->get('tenant_id'),
                $request->get('branch_id'),
                'branch_delivery_settings'
            );

            if (!$deleted) {
                return response()->json([
                    'success' => false,
                    'message' => 'Branch delivery setting not found or could not be deleted'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Branch delivery setting deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete branch delivery setting',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get email settings
     */
    public function getEmailSettings(Request $request)
    {
        try {
            $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
            ]);

            $settings = $this->service->getEmailSettings(
                $request->get('tenant_id'),
                $request->get('branch_id')
            );

            return response()->json([
                'success' => true,
                'data' => $settings,
                'message' => 'Email settings retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve email settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create/Update email settings
     */
    public function setEmailSettings(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
                'settings' => 'required|array',
                'settings.*' => 'required'
            ]);

            $settings = $this->service->setEmailSettings(
                $validatedData['settings'],
                $request->get('tenant_id'),
                $request->get('branch_id')
            );

            return response()->json([
                'success' => true,
                'data' => $settings,
                'message' => 'Email settings updated successfully'
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update email settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete email setting
     */
    public function deleteEmailSetting(Request $request, $key)
    {
        try {
            $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
            ]);

            $deleted = $this->service->delete(
                $key,
                $request->get('tenant_id'),
                $request->get('branch_id'),
                'email_settings'
            );

            if (!$deleted) {
                return response()->json([
                    'success' => false,
                    'message' => 'Email setting not found or could not be deleted'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Email setting deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete email setting',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get file storage settings
     */
    public function getFileStorageSettings(Request $request)
    {
        try {
            $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
            ]);

            $settings = $this->service->getFileStorageSettings(
                $request->get('tenant_id'),
                $request->get('branch_id')
            );

            return response()->json([
                'success' => true,
                'data' => $settings,
                'message' => 'File storage settings retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve file storage settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create/Update file storage settings
     */
    public function setFileStorageSettings(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
                'settings' => 'required|array',
                'settings.*' => 'required'
            ]);

            $settings = $this->service->setFileStorageSettings(
                $validatedData['settings'],
                $request->get('tenant_id'),
                $request->get('branch_id')
            );

            return response()->json([
                'success' => true,
                'data' => $settings,
                'message' => 'File storage settings updated successfully'
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update file storage settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete file storage setting
     */
    public function deleteFileStorageSetting(Request $request, $key)
    {
        try {
            $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
            ]);

            $deleted = $this->service->delete(
                $key,
                $request->get('tenant_id'),
                $request->get('branch_id'),
                'file_storage_settings'
            );

            if (!$deleted) {
                return response()->json([
                    'success' => false,
                    'message' => 'File storage setting not found or could not be deleted'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'File storage setting deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete file storage setting',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get front FAQ settings
     */
    public function getFrontFaqSettings(Request $request)
    {
        try {
            $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
            ]);

            $settings = $this->service->getFrontFaqSettings(
                $request->get('tenant_id'),
                $request->get('branch_id')
            );

            return response()->json([
                'success' => true,
                'data' => $settings,
                'message' => 'Front FAQ settings retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve front FAQ settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create/Update front FAQ settings
     */
    public function setFrontFaqSettings(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
                'settings' => 'required|array',
                'settings.*' => 'required'
            ]);

            $settings = $this->service->setFrontFaqSettings(
                $validatedData['settings'],
                $request->get('tenant_id'),
                $request->get('branch_id')
            );

            return response()->json([
                'success' => true,
                'data' => $settings,
                'message' => 'Front FAQ settings updated successfully'
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update front FAQ settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete front FAQ setting
     */
    public function deleteFrontFaqSetting(Request $request, $key)
    {
        try {
            $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
            ]);

            $deleted = $this->service->delete(
                $key,
                $request->get('tenant_id'),
                $request->get('branch_id'),
                'front_faq_settings'
            );

            if (!$deleted) {
                return response()->json([
                    'success' => false,
                    'message' => 'Front FAQ setting not found or could not be deleted'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Front FAQ setting deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete front FAQ setting',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get front review settings
     */
    public function getFrontReviewSettings(Request $request)
    {
        try {
            $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
            ]);

            $settings = $this->service->getFrontReviewSettings(
                $request->get('tenant_id'),
                $request->get('branch_id')
            );

            return response()->json([
                'success' => true,
                'data' => $settings,
                'message' => 'Front review settings retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve front review settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create/Update front review settings
     */
    public function setFrontReviewSettings(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
                'settings' => 'required|array',
                'settings.*' => 'required'
            ]);

            $settings = $this->service->setFrontReviewSettings(
                $validatedData['settings'],
                $request->get('tenant_id'),
                $request->get('branch_id')
            );

            return response()->json([
                'success' => true,
                'data' => $settings,
                'message' => 'Front review settings updated successfully'
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update front review settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete front review setting
     */
    public function deleteFrontReviewSetting(Request $request, $key)
    {
        try {
            $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
            ]);

            $deleted = $this->service->delete(
                $key,
                $request->get('tenant_id'),
                $request->get('branch_id'),
                'front_review_settings'
            );

            if (!$deleted) {
                return response()->json([
                    'success' => false,
                    'message' => 'Front review setting not found or could not be deleted'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Front review setting deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete front review setting',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all available setting categories
     */
    public function getAvailableCategories()
    {
        try {
            $categories = $this->service->getAvailableCategories();

            return response()->json([
                'success' => true,
                'data' => $categories,
                'message' => 'Available categories retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve available categories',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk update settings for a category
     */
    public function bulkUpdateCategory(Request $request, $category)
    {
        try {
            $request->validate([
                'tenant_id' => 'nullable|integer|exists:tenants,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
                'settings' => 'required|array',
                'settings.*' => 'required'
            ]);

            $settings = $this->service->bulkUpdateCategory(
                $category,
                $request->get('settings'),
                $request->get('tenant_id'),
                $request->get('branch_id')
            );

            return response()->json([
                'success' => true,
                'data' => $settings,
                'message' => 'Settings updated successfully'
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to bulk update settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}