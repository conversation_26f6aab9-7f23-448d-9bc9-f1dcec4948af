/**
 * POS Core Module - Main POS functionality and coordination
 * Handles initialization, event coordination, and core POS operations
 */

class POSCore {
    constructor() {
        this.isOnline = navigator.onLine;
        this.storage = null;
        this.sync = null;
        this.ui = null;
        this.currentOrder = {
            items: [],
            subtotal: 0,
            tax_amount: 0,
            discount_amount: 0,
            total_amount: 0,
            order_discount: null
        };
        this.menuItems = [];
        this.customers = [];
        this.tables = [];
        
        this.init();
    }

    /**
     * Initialize POS Core
     */
    async init() {
        try {
            console.log('Initializing POS Core...');
            
            // Initialize storage
            this.storage = new POSStorage();
            await this.storage.init();
            
            // Initialize sync
            this.sync = new POSSync(this.storage);
            
            // Initialize UI
            this.ui = new POSUI(this);
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Load initial data
            await this.loadInitialData();
            
            // Setup service worker
            await this.setupServiceWorker();
            
            console.log('POS Core initialized successfully');
        } catch (error) {
            console.error('Failed to initialize POS Core:', error);
            this.showError('Failed to initialize POS system');
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Online/offline events
        window.addEventListener('online', () => this.handleOnlineStatus(true));
        window.addEventListener('offline', () => this.handleOnlineStatus(false));
        
        // Service worker messages
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.addEventListener('message', (event) => {
                this.handleServiceWorkerMessage(event.data);
            });
        }
        
        // Beforeunload event for saving pending data
        window.addEventListener('beforeunload', () => {
            this.savePendingData();
        });
    }

    /**
     * Handle online/offline status changes
     */
    handleOnlineStatus(isOnline) {
        this.isOnline = isOnline;
        this.ui.updateConnectionStatus(isOnline);
        
        if (isOnline) {
            console.log('Connection restored - starting sync...');
            this.sync.syncAll();
        } else {
            console.log('Connection lost - switching to offline mode');
        }
    }

    /**
     * Handle service worker messages
     */
    handleServiceWorkerMessage(data) {
        switch (data.type) {
            case 'BACKGROUND_SYNC':
                if (data.action === 'sync-all') {
                    this.sync.syncAll();
                }
                break;
            case 'CACHE_UPDATED':
                console.log('Cache updated:', data.cacheName);
                break;
            default:
                console.log('Unknown service worker message:', data);
        }
    }

    /**
     * Load initial data
     */
    async loadInitialData() {
        try {
            // Try to load from server first if online
            if (this.isOnline) {
                await this.loadDataFromServer();
            } else {
                // Load from local storage
                await this.loadDataFromStorage();
            }
        } catch (error) {
            console.error('Failed to load initial data:', error);
            // Fallback to storage
            await this.loadDataFromStorage();
        }
    }

    /**
     * Load data from server with smart caching
     */
    async loadDataFromServer() {
        try {
            console.log('Loading data from server...');

            // Load menu items with priority-based loading
            await this.loadMenuItemsWithPriority();

            // Load customers
            const customersResponse = await fetch('/api/customers', {
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                }
            });
            if (customersResponse.ok) {
                this.customers = await customersResponse.json();
                await this.storage.saveCustomers(this.customers);
            }

            // Load tables
            const tablesResponse = await fetch('/api/reservation/tables', {
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                }
            });
            if (tablesResponse.ok) {
                this.tables = await tablesResponse.json();
                await this.storage.saveTables(this.tables);
            }

            console.log('Data loaded from server successfully');
        } catch (error) {
            console.error('Failed to load data from server:', error);
            throw error;
        }
    }

    /**
     * Load menu items with priority-based caching
     */
    async loadMenuItemsWithPriority() {
        try {
            // First, load basic menu items (without full details)
            const basicResponse = await fetch('/api/pos/form-data', {
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                }
            });

            if (basicResponse.ok) {
                const data = await basicResponse.json();
                this.menuItems = data.data.menu_items || [];

                // Process items for search optimization
                this.processMenuItemsForSearch();

                // Save to storage
                await this.storage.saveMenuItems(this.menuItems);

                // Load popular items first
                await this.preloadPopularItems();

                console.log(`Loaded ${this.menuItems.length} menu items`);
            }
        } catch (error) {
            console.error('Failed to load menu items:', error);
            throw error;
        }
    }

    /**
     * Process menu items for optimized searching
     */
    processMenuItemsForSearch() {
        this.menuItems.forEach(item => {
            // Create searchable text
            item._searchText = [
                item.name,
                item.short_description || '',
                item.category?.name || '',
                item.variants?.map(v => v.name).join(' ') || '',
                item.addons?.map(a => a.name).join(' ') || ''
            ].join(' ').toLowerCase();

            // Add popularity score (can be enhanced with real data)
            item._popularity = item.order_count || Math.floor(Math.random() * 100);

            // Add last accessed timestamp
            item._lastAccessed = Date.now();
        });
    }

    /**
     * Preload popular menu items
     */
    async preloadPopularItems() {
        try {
            // Sort by popularity and get top 20
            const popularItems = [...this.menuItems]
                .sort((a, b) => (b._popularity || 0) - (a._popularity || 0))
                .slice(0, 20);

            // Preload these items if lazy loader is available
            if (window.posLazyLoader) {
                const itemIds = popularItems.map(item => item.id.toString());
                await window.posLazyLoader.preloadCriticalItems(itemIds);
            }

            console.log(`Preloaded ${popularItems.length} popular items`);
        } catch (error) {
            console.error('Failed to preload popular items:', error);
        }
    }

    /**
     * Load data from local storage
     */
    async loadDataFromStorage() {
        try {
            console.log('Loading data from storage...');
            
            this.menuItems = await this.storage.getMenuItems() || [];
            this.customers = await this.storage.getCustomers() || [];
            this.tables = await this.storage.getTables() || [];
            
            console.log('Data loaded from storage successfully');
        } catch (error) {
            console.error('Failed to load data from storage:', error);
        }
    }

    /**
     * Setup service worker
     */
    async setupServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                const registration = await navigator.serviceWorker.register('/pos-sw.js');
                console.log('Service Worker registered:', registration);
                
                // Enable background sync if supported
                if ('sync' in window.ServiceWorkerRegistration.prototype) {
                    console.log('Background sync supported');
                }
            } catch (error) {
                console.error('Service Worker registration failed:', error);
            }
        }
    }

    /**
     * Add item to current order
     */
    addToOrder(itemId, quantity = 1, variant = null, addons = [], notes = '') {
        const menuItem = this.menuItems.find(item => item.id == itemId);
        if (!menuItem) {
            this.showError('Menu item not found');
            return false;
        }

        const orderItem = {
            menu_item_id: itemId,
            menu_item_name: menuItem.name,
            quantity: quantity,
            base_price: parseFloat(menuItem.base_price),
            variant_id: variant ? variant.id : null,
            variant_name: variant ? variant.name : null,
            variant_price: variant ? parseFloat(variant.price) : 0,
            addons: addons || [],
            notes: notes,
            total_price: this.calculateItemTotal(menuItem, quantity, variant, addons)
        };

        this.currentOrder.items.push(orderItem);
        this.calculateOrderTotals();
        this.ui.updateOrderDisplay();
        
        return true;
    }

    /**
     * Calculate item total price
     */
    calculateItemTotal(menuItem, quantity, variant, addons) {
        let basePrice = parseFloat(menuItem.base_price);
        let variantPrice = variant ? parseFloat(variant.price) : 0;
        let addonsTotal = addons.reduce((total, addon) => total + parseFloat(addon.price), 0);
        
        return (basePrice + variantPrice + addonsTotal) * quantity;
    }

    /**
     * Calculate order totals
     */
    calculateOrderTotals() {
        this.currentOrder.subtotal = this.currentOrder.items.reduce((total, item) => total + item.total_price, 0);
        this.currentOrder.tax_amount = this.currentOrder.subtotal * 0.1; // 10% tax
        this.currentOrder.total_amount = this.currentOrder.subtotal + this.currentOrder.tax_amount - this.currentOrder.discount_amount;
    }

    /**
     * Submit order
     */
    async submitOrder(orderData) {
        try {
            if (this.isOnline) {
                // Submit directly to server
                return await this.submitOrderToServer(orderData);
            } else {
                // Save to local storage for later sync
                return await this.saveOrderOffline(orderData);
            }
        } catch (error) {
            console.error('Failed to submit order:', error);
            // Fallback to offline storage
            return await this.saveOrderOffline(orderData);
        }
    }

    /**
     * Submit order to server
     */
    async submitOrderToServer(orderData) {
        const response = await fetch('/pos/orders/store', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(orderData)
        });

        if (!response.ok) {
            throw new Error('Failed to submit order to server');
        }

        return await response.json();
    }

    /**
     * Save order offline
     */
    async saveOrderOffline(orderData) {
        const offlineOrder = {
            ...orderData,
            id: 'offline_' + Date.now(),
            status: 'pending_sync',
            created_at: new Date().toISOString()
        };

        await this.storage.savePendingOrder(offlineOrder);
        
        // Register for background sync
        if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
            const registration = await navigator.serviceWorker.ready;
            await registration.sync.register('pos-sync');
        }

        return {
            success: true,
            message: 'Order saved offline. Will sync when connection is restored.',
            order: offlineOrder
        };
    }

    /**
     * Save pending data before page unload
     */
    savePendingData() {
        if (this.currentOrder.items.length > 0) {
            localStorage.setItem('pos_pending_order', JSON.stringify(this.currentOrder));
        }
    }

    /**
     * Show error message
     */
    showError(message) {
        if (this.ui) {
            this.ui.showError(message);
        } else {
            alert(message);
        }
    }

    /**
     * Show success message
     */
    showSuccess(message) {
        if (this.ui) {
            this.ui.showSuccess(message);
        } else {
            alert(message);
        }
    }
}

// Initialize POS Core when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.posCore = new POSCore();
});
