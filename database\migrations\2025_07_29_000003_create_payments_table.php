<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('tenants')->onDelete('cascade');
            $table->foreignId('transaction_id')->constrained('transactions')->onDelete('cascade');
            $table->foreignId('payment_method_id')->constrained('payment_methods')->onDelete('restrict');
            $table->string('payment_number', 50)->comment('Unique payment reference number within tenant');
            $table->decimal('amount', 10, 2)->comment('Payment amount');
            $table->enum('status', ['pending', 'completed', 'failed', 'cancelled', 'refunded'])
                ->default('pending')
                ->comment('Payment status');
            $table->datetime('payment_date')->comment('Date and time of payment');
            $table->string('reference_number', 100)->nullable()->comment('External reference number (bank ref, card ref, etc.)');
            $table->json('payment_details')->nullable()->comment('Payment method specific details');
            $table->text('notes')->nullable()->comment('Payment notes');
            $table->decimal('change_amount', 10, 2)->default(0)->comment('Change given (for cash payments)');
            $table->foreignId('processed_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            // Unique constraints within tenant
            $table->unique(['tenant_id', 'payment_number']);

            // Indexes for better performance
            $table->index(['tenant_id', 'transaction_id', 'status']);
            $table->index(['transaction_id', 'status']);
            $table->index(['payment_method_id', 'status']);
            $table->index('payment_date');
            $table->index('payment_number');
            $table->index('reference_number');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
