<?php

use Illuminate\Support\Facades\Route;
use Modules\HR\Http\Controllers\HRController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::middleware(['web', 'auth'])->group(function () {
    // HR Staff Management Routes
    Route::prefix('hr')->name('hr.')->group(function () {
        // Staff Management
        Route::prefix('staff')->name('staff.')->group(function () {
            Route::get('/', [HRController::class, 'staffIndex'])->name('index');
            Route::get('/data', [HRController::class, 'staffData'])->name('data');
            Route::get('/{id}', [HRController::class, 'staffShow'])->name('show');
            Route::post('/', [HRController::class, 'staffStore'])->name('store');
            Route::put('/{id}', [HRController::class, 'staffUpdate'])->name('update');
            Route::delete('/{id}', [HRController::class, 'staffDestroy'])->name('destroy');
        });

        // Attendance Management
        Route::prefix('attendance')->name('attendance.')->group(function () {
            Route::get('/', [HRController::class, 'attendanceIndex'])->name('index');
            Route::get('/data', [HRController::class, 'attendanceData'])->name('data');
            Route::get('/check-in-out', [HRController::class, 'checkInOutView'])->name('check-in-out');
        });

        // Schedule Management
        Route::prefix('schedule')->name('schedule.')->group(function () {
            Route::get('/', [HRController::class, 'scheduleIndex'])->name('index');
            Route::get('/data', [HRController::class, 'scheduleData'])->name('data');
        });

        // Payroll Management
        Route::prefix('payroll')->name('payroll.')->group(function () {
            Route::get('/', [HRController::class, 'payrollIndex'])->name('index');
            Route::get('/data', [HRController::class, 'payrollData'])->name('data');
        });

        // Leave Management
        Route::prefix('leave')->name('leave.')->group(function () {
            Route::get('/', [HRController::class, 'leaveIndex'])->name('index');
            Route::get('/data', [HRController::class, 'leaveData'])->name('data');
        });
    });
});
