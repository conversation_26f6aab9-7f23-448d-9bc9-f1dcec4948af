@extends('layouts.master')

@section('title', 'إدارة الإضافات')

@push('styles')
<meta name="csrf-token" content="{{ csrf_token() }}">
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
    /* Custom DataTables Tailwind Styling */
    .dataTables_wrapper {
        font-family: inherit;
    }
    
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_processing,
    .dataTables_wrapper .dataTables_paginate {
        color: #374151;
        font-size: 0.875rem;
    }
    
    .dataTables_wrapper .dataTables_length select,
    .dataTables_wrapper .dataTables_filter input {
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
    
    .dataTables_wrapper .dataTables_filter input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.5rem 0.75rem !important;
        margin: 0 0.125rem !important;
        border-radius: 0.375rem !important;
        border: 1px solid #d1d5db !important;
        background: white !important;
        color: #374151 !important;
        text-decoration: none !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background: #f3f4f6 !important;
        border-color: #9ca3af !important;
        color: #374151 !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background: #3b82f6 !important;
        border-color: #3b82f6 !important;
        color: white !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
        background: #f9fafb !important;
        color: #9ca3af !important;
        border-color: #e5e7eb !important;
    }
    
    table.dataTable {
        border-collapse: separate !important;
        border-spacing: 0 !important;
    }
    
    table.dataTable thead th {
        background-color: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
        font-weight: 600;
        color: #374151;
        padding: 0.75rem;
    }
    
    table.dataTable tbody td {
        padding: 0.75rem;
        border-bottom: 1px solid #f3f4f6;
    }
    
    table.dataTable tbody tr:hover {
        background-color: #f9fafb;
    }
    
    .select2-container--default .select2-selection--single {
        height: 42px;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        padding: 0.5rem;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 26px;
        padding-left: 0;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 40px;
    }
    
    .modal-backdrop {
        background-color: rgba(0, 0, 0, 0.5);
    }
    
    .animate-fadeIn {
        animation: fadeIn 0.3s ease-in-out;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: scale(0.95); }
        to { opacity: 1; transform: scale(1); }
    }
    
    .animate-slideDown {
        animation: slideDown 0.3s ease-in-out;
    }
    
    @keyframes slideDown {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>
@endpush

@section('content')
<!-- Page Header -->
<div class="mb-6">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">إدارة الإضافات</h1>
            <p class="mt-1 text-sm text-gray-500">إدارة جميع الإضافات المتاحة</p>
        </div>
        <div class="mt-4 sm:mt-0">
            <button type="button" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150" id="add-addon-btn">
                <i class="fas fa-plus mr-2"></i>
                إضافة إضافة جديدة
            </button>
        </div>
    </div>
</div>

<!-- Filters Section -->
<div class="bg-white shadow-sm rounded-lg mb-6">
    <div class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">فلاتر البحث</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- Menu Item Filter -->
            <div>
                <label for="filter_menu_item" class="block text-sm font-medium text-gray-700 mb-2">عنصر القائمة</label>
                <select id="filter_menu_item" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    <option value="">جميع عناصر القائمة</option>
                </select>
            </div>

            <!-- Status Filter -->
            <div>
                <label for="filter_status" class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                <select id="filter_status" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    <option value="">جميع الحالات</option>
                    <option value="1">نشط</option>
                    <option value="0">غير نشط</option>
                </select>
            </div>

            <!-- Required Filter -->
            <div>
                <label for="filter_required" class="block text-sm font-medium text-gray-700 mb-2">مطلوب</label>
                <select id="filter_required" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    <option value="">الكل</option>
                    <option value="1">مطلوب</option>
                    <option value="0">اختياري</option>
                </select>
            </div>

            <!-- Reset Filters -->
            <div class="flex items-end">
                <button type="button" id="reset-filters" class="w-full px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    <i class="fas fa-undo mr-2"></i>
                    إعادة تعيين
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="bg-white shadow-sm rounded-lg">
    <div class="p-6">
        <div class="overflow-x-auto">
            <table id="addons-table" class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">اسم الإضافة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">عنصر القائمة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">مجموعة الإضافة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">السعر</th>
                        {{-- <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">مطلوب</th> --}}
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                        {{-- <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الإنشاء</th> --}}
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add/Edit Addon Modal -->
<div id="addonModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="addonModalLabel" aria-hidden="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg leading-6 font-medium text-gray-900" id="addonModalLabel">إضافة إضافة جديدة</h3>
                    <button type="button" class="text-gray-400 hover:text-gray-600" id="close-addon-modal">
                        <span class="sr-only">إغلاق</span>
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form id="addonForm">
                    <input type="hidden" id="addon_id" name="addon_id">
                    
                    <!-- Basic Information -->
                    <div class="bg-gray-50 rounded-lg p-4 mb-4">
                        <h4 class="text-md font-medium text-gray-900 mb-3">المعلومات الأساسية</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="menu_item_id" class="block text-sm font-medium text-gray-700 mb-1">عنصر القائمة <span class="text-red-500">*</span></label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" id="menu_item_id" name="menu_item_id" required>
                                    <option value="">اختر عنصر القائمة</option>
                                </select>
                                <div class="text-red-500 text-sm mt-1 hidden" id="menu_item_id_error"></div>
                            </div>
                            <div>
                                <label for="addon_group_name" class="block text-sm font-medium text-gray-700 mb-1">اسم مجموعة الإضافة</label>
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" id="addon_group_name" name="addon_group_name">
                                <div class="text-red-500 text-sm mt-1 hidden" id="addon_group_name_error"></div>
                            </div>
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 mb-1">اسم الإضافة <span class="text-red-500">*</span></label>
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" id="name" name="name" required>
                                <div class="text-red-500 text-sm mt-1 hidden" id="name_error"></div>
                            </div>
                            <div>
                                <label for="code" class="block text-sm font-medium text-gray-700 mb-1">الكود <small class="text-gray-500">(يتم إنشاؤه تلقائياً)</small></label>
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-100" id="code" name="code" placeholder="سيتم إنشاؤه تلقائياً من الاسم" readonly>
                                <p class="text-xs text-gray-500 mt-1">سيتم إنشاء الكود تلقائياً بناءً على اسم الإضافة</p>
                                <div class="text-red-500 text-sm mt-1 hidden" id="code_error"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Pricing Information -->
                    <div class="bg-gray-50 rounded-lg p-4 mb-4">
                        <h4 class="text-md font-medium text-gray-900 mb-3">معلومات التسعير</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="price" class="block text-sm font-medium text-gray-700 mb-1">السعر <span class="text-red-500">*</span></label>
                                <input type="number" step="0.01" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" id="price" name="price" required>
                                <div class="text-red-500 text-sm mt-1 hidden" id="price_error"></div>
                            </div>
                            <div>
                                <label for="cost" class="block text-sm font-medium text-gray-700 mb-1">التكلفة</label>
                                <input type="number" step="0.01" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" id="cost" name="cost">
                                <div class="text-red-500 text-sm mt-1 hidden" id="cost_error"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Settings -->
                    <div class="bg-gray-50 rounded-lg p-4 mb-4">
                        <h4 class="text-md font-medium text-gray-900 mb-3">الإعدادات</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="is_required" class="block text-sm font-medium text-gray-700 mb-1">مطلوب</label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" id="is_required" name="is_required">
                                    <option value="0">اختياري</option>
                                    <option value="1">مطلوب</option>
                                </select>
                            </div>
                            <div>
                                <label for="max_quantity" class="block text-sm font-medium text-gray-700 mb-1">الحد الأقصى للكمية</label>
                                <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" id="max_quantity" name="max_quantity" min="1">
                                <div class="text-red-500 text-sm mt-1 hidden" id="max_quantity_error"></div>
                            </div>
                            <div>
                                <label for="is_active" class="block text-sm font-medium text-gray-700 mb-1">الحالة</label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" id="is_active" name="is_active">
                                    <option value="1">نشط</option>
                                    <option value="0">غير نشط</option>
                                </select>
                            </div>
                            <div>
                                <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-1">ترتيب العرض</label>
                                <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" id="sort_order" name="sort_order" min="0">
                                <div class="text-red-500 text-sm mt-1 hidden" id="sort_order_error"></div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="submit" form="addonForm" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm" id="save-addon-btn">
                    حفظ
                </button>
                <button type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm" id="cancel-addon-btn">
                    إلغاء
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Show Addon Modal -->
<div id="showAddonModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="showAddonModalLabel" aria-hidden="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg leading-6 font-medium text-gray-900" id="showAddonModalLabel">تفاصيل الإضافة</h3>
                    <button type="button" class="text-gray-400 hover:text-gray-600" id="close-show-addon-modal">
                        <span class="sr-only">إغلاق</span>
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <!-- Basic Information -->
                <div class="bg-gray-50 rounded-lg p-4 mb-4">
                    <h4 class="text-md font-medium text-gray-900 mb-3">المعلومات الأساسية</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">عنصر القائمة</label>
                            <p class="text-sm text-gray-900 bg-white px-3 py-2 border border-gray-200 rounded-md" id="show_menu_item">-</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">اسم مجموعة الإضافة</label>
                            <p class="text-sm text-gray-900 bg-white px-3 py-2 border border-gray-200 rounded-md" id="show_addon_group_name">-</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">اسم الإضافة</label>
                            <p class="text-sm text-gray-900 bg-white px-3 py-2 border border-gray-200 rounded-md" id="show_name">-</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">كود الإضافة</label>
                            <p class="text-sm text-gray-900 bg-white px-3 py-2 border border-gray-200 rounded-md" id="show_code">-</p>
                        </div>
                    </div>
                </div>

                <!-- Pricing Information -->
                <div class="bg-gray-50 rounded-lg p-4 mb-4">
                    <h4 class="text-md font-medium text-gray-900 mb-3">معلومات التسعير</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">السعر</label>
                            <p class="text-sm text-gray-900 bg-white px-3 py-2 border border-gray-200 rounded-md" id="show_price">-</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">التكلفة</label>
                            <p class="text-sm text-gray-900 bg-white px-3 py-2 border border-gray-200 rounded-md" id="show_cost">-</p>
                        </div>
                    </div>
                </div>

                <!-- Settings & Status -->
                <div class="bg-gray-50 rounded-lg p-4 mb-4">
                    <h4 class="text-md font-medium text-gray-900 mb-3">الإعدادات والحالة</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">مطلوب</label>
                            <p class="text-sm text-gray-900 bg-white px-3 py-2 border border-gray-200 rounded-md" id="show_is_required">-</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">الحد الأقصى للكمية</label>
                            <p class="text-sm text-gray-900 bg-white px-3 py-2 border border-gray-200 rounded-md" id="show_max_quantity">-</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">الحالة</label>
                            <p class="text-sm text-gray-900 bg-white px-3 py-2 border border-gray-200 rounded-md" id="show_status">-</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">ترتيب العرض</label>
                            <p class="text-sm text-gray-900 bg-white px-3 py-2 border border-gray-200 rounded-md" id="show_sort_order">-</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">تاريخ الإنشاء</label>
                            <p class="text-sm text-gray-900 bg-white px-3 py-2 border border-gray-200 rounded-md" id="show_created_at">-</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">تاريخ التحديث</label>
                            <p class="text-sm text-gray-900 bg-white px-3 py-2 border border-gray-200 rounded-md" id="show_updated_at">-</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" class="w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:w-auto sm:text-sm" id="close-show-addon-modal-btn">
                    إغلاق
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.tailwindcss.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
$(document).ready(function() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Initialize Select2
    $('.select2').select2({
        theme: 'default',
        width: '100%'
    });

    // Load menu items for filter and form
    function loadMenuItems() {
        $.ajax({
            url: '{{ route("addons.menu-items-list") }}',
            type: 'GET',
            success: function(response) {
                var options = '<option value="">اختر عنصر القائمة</option>';
                var filterOptions = '<option value="">جميع عناصر القائمة</option>';

                // Check if response has data and it's an array
                if (response && response.data && Array.isArray(response.data)) {
                    $.each(response.data, function(index, item) {
                        if (item && item.id && item.name) {
                            options += '<option value="' + item.id + '">' + item.name + '</option>';
                            filterOptions += '<option value="' + item.id + '">' + item.name + '</option>';
                        }
                    });
                } else {
                    console.warn('Invalid menu items response:', response);
                }

                $('#menu_item_id').html(options);
                $('#filter_menu_item').html(filterOptions);
            },
            error: function(xhr, status, error) {
                console.error('Error loading menu items:', {
                    status: status,
                    error: error,
                    response: xhr.responseText
                });

                // Show user-friendly error message
                Swal.fire({
                    title: 'خطأ!',
                    text: 'حدث خطأ أثناء تحميل عناصر القائمة',
                    icon: 'error',
                    confirmButtonText: 'موافق'
                });
            }
        });
    }

    // Load menu items on page load
    loadMenuItems();

    // Initialize DataTable with server-side processing
    var table = $('#addons-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("addons.index") }}',
            data: function(d) {
                d.menu_item_id = $('#filter_menu_item').val();
                d.status = $('#filter_status').val();
                d.is_required = $('#filter_required').val();
            },
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'name', name: 'name' },
            { data: 'menu_item', name: 'menu_item.name',orderable: false,searchable: false },
            { data: 'addon_group_name', name: 'addon_group_name' },
            { data: 'price', name: 'price' },
            // { data: 'is_required', name: 'is_required', orderable: false },
            { data: 'is_active', name: 'is_active', orderable: false },
            // { data: 'created_at', name: 'created_at' },
            { data: 'action', name: 'action', orderable: false, searchable: false }
        ],
        order: [[0, 'desc']],
        pageLength: 25,
        responsive: true,
        dom: '<"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4"<"mb-2 sm:mb-0"l><"mb-2 sm:mb-0"f>>rtip',
        language: {
            processing: '<div class="flex justify-center items-center"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div></div>',
            search: "بحث:",
            lengthMenu: "عرض _MENU_ سجلات",
            info: "عرض _START_ إلى _END_ من _TOTAL_ سجل",
            infoEmpty: "عرض 0 إلى 0 من 0 سجل",
            infoFiltered: "(تمت التصفية من _MAX_ سجل)",
            emptyTable: 'لم يتم العثور على إضافات',
            zeroRecords: 'لم يتم العثور على إضافات مطابقة',
            paginate: {
                first: "الأول",
                last: "الأخير", 
                next: "التالي",
                previous: "السابق"
            }
        },
        initComplete: function() {
            // Apply custom styling after DataTable initialization
            $('.dataTables_wrapper').addClass('bg-white rounded-lg shadow-sm');
        }
    });

    // Filter change events
    $('#filter_menu_item, #filter_status, #filter_required').change(function() {
        table.ajax.reload();
    });

    // Reset filters
    $('#reset-filters').click(function() {
        $('#filter_menu_item').val('');
        $('#filter_status').val('');
        $('#filter_required').val('');
        table.ajax.reload();
    });

    // Modal functions
    function showModal(modalId) {
        $('#' + modalId).removeClass('hidden');
    }

    function hideModal(modalId) {
        $('#' + modalId).addClass('hidden');
    }

    // Add Addon Button
    $('#add-addon-btn').click(function() {
        $('#addonForm')[0].reset();
        $('#addon_id').val('');
        $('#menu_item_id').val('');
        $('#addonModalLabel').text('إضافة إضافة جديدة');
        clearErrors();
        $('#code').val(''); // Clear code field for new addons
        showModal('addonModal');
    });

    // Close modal buttons
    $('#close-addon-modal, #cancel-addon-btn').click(function() {
        hideModal('addonModal');
    });

    $('#close-show-addon-modal, #close-show-addon-modal-btn').click(function() {
        hideModal('showAddonModal');
    });

    // Close modal when clicking outside
    $(document).on('click', function(e) {
        if ($(e.target).hasClass('fixed') && $(e.target).hasClass('inset-0')) {
            hideModal('addonModal');
            hideModal('showAddonModal');
        }
    });

    // Generate code preview when name changes
    $('#name').on('input', function() {
        var name = $(this).val();
        if (name && !$('#addon_id').val()) { // Only for new addons
            var code = generateCodeFromName(name, 'ADD');
            $('#code').val(code);
        }
    });

    // Function to generate code from name
    function generateCodeFromName(name, prefix) {
        // Remove Arabic diacritics and special characters
        var cleanName = name.replace(/[\u064B-\u065F\u0670\u06D6-\u06ED]/g, '');
        
        // Map common Arabic words to English
        var arabicToEnglish = {
            'جبن': 'CHEESE',
            'خضار': 'VEGETABLES',
            'صوص': 'SAUCE',
            'توابل': 'SPICES',
            'إضافي': 'EXTRA',
            'كبير': 'LARGE',
            'صغير': 'SMALL',
            'متوسط': 'MEDIUM',
            'حار': 'SPICY',
            'بارد': 'COLD',
            'ساخن': 'HOT',
            'مقلي': 'FRIED',
            'مشوي': 'GRILLED',
            'مسلوق': 'BOILED'
        };
        
        var words = cleanName.split(' ');
        var codeWords = [];
        
        words.forEach(function(word) {
            word = word.trim();
            if (word) {
                if (arabicToEnglish[word]) {
                    codeWords.push(arabicToEnglish[word]);
                } else {
                    // Take first 3 characters and convert to uppercase
                    codeWords.push(word.substring(0, 3).toUpperCase());
                }
            }
        });
        
        var baseCode = codeWords.join('_');
        return prefix + '_' + baseCode;
    }

    // Clear form errors
    function clearErrors() {
        $('.border-red-500').removeClass('border-red-500').addClass('border-gray-300');
        $('.text-red-500').addClass('hidden');
    }

    // Show form errors
    function showErrors(errors) {
        clearErrors();
        $.each(errors, function(field, messages) {
            $('#' + field).removeClass('border-gray-300').addClass('border-red-500');
            $('#' + field + '_error').removeClass('hidden').text(messages[0]);
        });
    }

    // Edit Addon Button
    $(document).on('click', '.edit-addon', function() {
        var id = $(this).data('id');
        
        $.ajax({
            url: '{{ route("addons.edit", ":id") }}'.replace(':id', id),
            type: 'GET',
            success: function(response) {
                var addon = response.data;
                $('#addon_id').val(addon.id);
                $('#menu_item_id').val(addon.menu_item_id);
                $('#addon_group_name').val(addon.addon_group_name);
                $('#name').val(addon.name);
                $('#code').val(addon.code);
                $('#price').val(addon.price);
                $('#cost').val(addon.cost);
                $('#is_required').val(addon.is_required);
                $('#max_quantity').val(addon.max_quantity);
                $('#is_active').val(addon.is_active);
                $('#sort_order').val(addon.sort_order);
                $('#addonModalLabel').text('تعديل الإضافة');
                clearErrors();
                showModal('addonModal');
            },
            error: function(xhr) {
                Swal.fire('خطأ!', 'حدث خطأ أثناء جلب بيانات الإضافة', 'error');
            }
        });
    });

    // Show Addon Button
    $(document).on('click', '.show-addon', function() {
        var id = $(this).data('id');
        
        $.ajax({
            url: '{{ route("addons.show", ":id") }}'.replace(':id', id),
            type: 'GET',
            success: function(response) {
                var addon = response.data;
                $('#show_menu_item').text(addon.menu_item ? addon.menu_item.name : '-');
                $('#show_addon_group_name').text(addon.addon_group_name || '-');
                $('#show_name').text(addon.name || '-');
                $('#show_code').text(addon.code || '-');
                $('#show_price').text(addon.price ? addon.price + ' ريال' : '-');
                $('#show_cost').text(addon.cost ? addon.cost + ' ريال' : '-');
                $('#show_is_required').html(addon.is_required == 1 ? '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">مطلوب</span>' : '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">اختياري</span>');
                $('#show_max_quantity').text(addon.max_quantity || '-');
                $('#show_status').html(addon.is_active == 1 ? '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">نشط</span>' : '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">غير نشط</span>');
                $('#show_sort_order').text(addon.sort_order || '-');
                $('#show_created_at').text(new Date(addon.created_at).toLocaleDateString('ar-SA'));
                $('#show_updated_at').text(new Date(addon.updated_at).toLocaleDateString('ar-SA'));
                showModal('showAddonModal');
            },
            error: function(xhr) {
                Swal.fire('خطأ!', 'حدث خطأ أثناء جلب بيانات الإضافة', 'error');
            }
        });
    });

    // Delete Addon Button
    $(document).on('click', '.delete-addon', function() {
        var id = $(this).data('id');
        
        Swal.fire({
            title: "هل أنت متأكد؟",
            text: "لن تتمكن من التراجع عن هذا الإجراء!",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#ef4444",
            cancelButtonColor: "#6b7280",
            confirmButtonText: "نعم، احذف!",
            cancelButtonText: "إلغاء"
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '{{ route("addons.destroy", ":id") }}'.replace(':id', id),
                    type: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        Swal.fire("تم الحذف!", "تم حذف الإضافة بنجاح.", "success");
                        table.ajax.reload();
                    },
                    error: function(xhr) {
                        Swal.fire("خطأ!", "حدث خطأ أثناء حذف الإضافة", "error");
                    }
                });
            }
        });
    });

    // Save Addon Form
    $('#addonForm').submit(function(e) {
        e.preventDefault();

        var formData = new FormData(this);
        var id = $('#addon_id').val();
        var url = id ? '{{ route("addons.update", ":id") }}'.replace(':id', id) : '{{ route("addons.store") }}';
        var method = id ? 'PUT' : 'POST';

        // Add CSRF token to form data
        formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

        if (method === 'PUT') {
            formData.append('_method', 'PUT');
        }
        
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                hideModal('addonModal');
                Swal.fire("نجح!", id ? "تم تحديث الإضافة بنجاح" : "تم إضافة الإضافة بنجاح", "success");
                table.ajax.reload();
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    var errors = xhr.responseJSON.errors;
                    showErrors(errors);
                } else {
                    Swal.fire("خطأ!", "حدث خطأ أثناء حفظ الإضافة", "error");
                }
            }
        });
    });
});
</script>
@endpush