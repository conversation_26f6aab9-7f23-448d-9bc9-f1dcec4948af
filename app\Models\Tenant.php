<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Tenant extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'code',
        'business_type',
        'country_id',
        'primary_contact_name',
        'contact_email',
        'contact_phone',
        'business_address',
        'latitude',
        'longitude',
        'tax_number',
        'business_license',
        'logo_url',
        'website_url',
        'social_media',
        'business_hours',
        'timezone',
        'date_format',
        'currency_code',
        'language_code',
        'status',
        'trial_ends_at',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'social_media' => 'array',
            'business_hours' => 'array',
            'latitude' => 'decimal:7',
            'longitude' => 'decimal:7',
            'trial_ends_at' => 'datetime',
        ];
    }

    // Relationships
    public function country()
    {
        return $this->belongsTo(Country::class);
    }

    public function subscriptions()
    {
        return $this->hasMany(TenantSubscription::class);
    }

    public function branches()
    {
        return $this->hasMany(Branch::class);
    }

    public function menus()
    {
        return $this->hasMany(Menu::class);
    }

    public function menuCategories()
    {
        return $this->hasManyThrough(MenuCategory::class, Menu::class);
    }

    public function menuItems()
    {
        return $this->hasManyThrough(MenuItem::class, Menu::class);
    }

    public function recipes()
    {
        return $this->hasMany(Recipe::class);
    }

    public function products()
    {
        return $this->hasMany(Product::class);
    }

    public function suppliers()
    {
        return $this->hasMany(Supplier::class);
    }

    public function customers()
    {
        return $this->hasMany(Customer::class);
    }

    public function discounts()
    {
        return $this->hasMany(Discount::class);
    }

    public function loyaltyPrograms()
    {
        return $this->hasMany(LoyaltyProgram::class);
    }

    public function reports()
    {
        return $this->hasMany(Report::class);
    }

    public function notifications()
    {
        return $this->hasMany(Notification::class);
    }

    public function settings()
    {
        return $this->hasMany(Setting::class);
    }

    public function auditLogs()
    {
        return $this->hasMany(AuditLog::class);
    }

    public function shifts()
    {
        return $this->hasMany(Shift::class);
    }

    public function shiftTypes()
    {
        return $this->hasMany(ShiftType::class);
    }

    public function shiftAssignments()
    {
        return $this->hasMany(ShiftAssignment::class);
    }

    public function timesheets()
    {
        return $this->hasMany(Timesheet::class);
    }

    public function staffAttendances()
    {
        return $this->hasMany(StaffAttendance::class);
    }

    public function leaveRequests()
    {
        return $this->hasMany(LeaveRequest::class);
    }

    public function users()
    {
        return $this->hasOne(User::class);
    }
}