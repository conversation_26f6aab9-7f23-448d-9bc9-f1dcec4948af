@extends('layouts.,master')

@push('inventory-styles')
<style>
.analytics-card {
    border-radius: 15px;
    border: none;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    margin-bottom: 25px;
    overflow: hidden;
}

.analytics-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.analytics-card .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 20px;
}

.analytics-card .card-body {
    padding: 25px;
}

.kpi-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
}

.kpi-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: rotate(45deg);
}

.kpi-value {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.kpi-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.kpi-icon {
    position: absolute;
    top: 20px;
    left: 20px;
    font-size: 2rem;
    opacity: 0.3;
}

.chart-container {
    position: relative;
    height: 400px;
    margin: 20px 0;
}

.chart-small {
    height: 250px;
}

.trend-indicator {
    display: inline-flex;
    align-items: center;
    font-size: 0.85rem;
    margin-top: 5px;
}

.trend-up {
    color: #28a745;
}

.trend-down {
    color: #dc3545;
}

.trend-neutral {
    color: #6c757d;
}

.filter-panel {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 25px;
    border: 1px solid #e9ecef;
}

.analytics-table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.analytics-table th {
    background: #f8f9fa;
    border: none;
    font-weight: 600;
    color: #495057;
}

.analytics-table td {
    border: none;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.progress-custom {
    height: 8px;
    border-radius: 4px;
    background: #e9ecef;
}

.progress-custom .progress-bar {
    border-radius: 4px;
}

.alert-custom {
    border-radius: 10px;
    border: none;
    padding: 15px 20px;
}

.metric-comparison {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #e9ecef;
}

.metric-comparison:last-child {
    border-bottom: none;
}
</style>
@endpush

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">المخزون</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ تحليلات المخزون</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-success" onclick="exportAnalytics()">
                <i class="mdi mdi-download"></i> تصدير التقرير
            </button>
        </div>
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-info" onclick="scheduleReport()">
                <i class="mdi mdi-calendar-clock"></i> جدولة التقرير
            </button>
        </div>
        <div class="pr-1 mb-3 mb-xl-0">
            <button type="button" class="btn btn-primary" onclick="refreshAnalytics()">
                <i class="mdi mdi-refresh"></i> تحديث البيانات
            </button>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<!-- Filter Panel -->
<div class="filter-panel">
    <div class="row">
        <div class="col-md-3">
            <div class="form-group">
                <label>الفترة الزمنية</label>
                <select id="period-filter" class="form-control">
                    <option value="7">آخر 7 أيام</option>
                    <option value="30" selected>آخر 30 يوم</option>
                    <option value="90">آخر 3 أشهر</option>
                    <option value="365">آخر سنة</option>
                    <option value="custom">فترة مخصصة</option>
                </select>
            </div>
        </div>
        <div class="col-md-2" id="custom-date-from" style="display: none;">
            <div class="form-group">
                <label>من تاريخ</label>
                <input type="date" id="date-from" class="form-control">
            </div>
        </div>
        <div class="col-md-2" id="custom-date-to" style="display: none;">
            <div class="form-group">
                <label>إلى تاريخ</label>
                <input type="date" id="date-to" class="form-control">
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group">
                <label>الفرع</label>
                <select id="branch-filter" class="form-control">
                    <option value="">جميع الفروع</option>
                </select>
            </div>
        </div>
        <div class="col-md-2">
            <div class="form-group">
                <label>الفئة</label>
                <select id="category-filter" class="form-control">
                    <option value="">جميع الفئات</option>
                </select>
            </div>
        </div>
        <div class="col-md-2">
            <div class="form-group">
                <label>&nbsp;</label>
                <button type="button" class="btn btn-primary btn-block" onclick="applyFilters()">
                    <i class="mdi mdi-filter"></i> تطبيق الفلاتر
                </button>
            </div>
        </div>
    </div>
</div>

<!-- KPI Cards -->
<div class="row">
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
        <div class="kpi-card">
            <i class="mdi mdi-package-variant kpi-icon"></i>
            <div class="kpi-value" id="total-items">{{ $kpis['total_items'] ?? 0 }}</div>
            <div class="kpi-label">إجمالي المواد</div>
            <div class="trend-indicator trend-up">
                <i class="mdi mdi-trending-up"></i>
                <span>+5.2% من الشهر الماضي</span>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
        <div class="kpi-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <i class="mdi mdi-currency-usd kpi-icon"></i>
            <div class="kpi-value" id="inventory-value">{{ number_format($kpis['inventory_value'] ?? 0, 2) }}</div>
            <div class="kpi-label">قيمة المخزون (ريال)</div>
            <div class="trend-indicator trend-up">
                <i class="mdi mdi-trending-up"></i>
                <span>+12.8% من الشهر الماضي</span>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
        <div class="kpi-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
            <i class="mdi mdi-alert-circle kpi-icon"></i>
            <div class="kpi-value text-warning" id="low-stock-items">{{ $kpis['low_stock_items'] ?? 0 }}</div>
            <div class="kpi-label">مواد منخفضة المخزون</div>
            <div class="trend-indicator trend-down">
                <i class="mdi mdi-trending-down"></i>
                <span>-3.1% من الشهر الماضي</span>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
        <div class="kpi-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
            <i class="mdi mdi-chart-line kpi-icon"></i>
            <div class="kpi-value" id="turnover-rate">{{ number_format($kpis['turnover_rate'] ?? 0, 1) }}</div>
            <div class="kpi-label">معدل دوران المخزون</div>
            <div class="trend-indicator trend-up">
                <i class="mdi mdi-trending-up"></i>
                <span>+8.4% من الشهر الماضي</span>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row">
    <!-- Inventory Value Trend -->
    <div class="col-xl-8 col-lg-12">
        <div class="analytics-card">
            <div class="card-header">
                <h5 class="mb-0">اتجاه قيمة المخزون</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="inventoryValueChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Stock Status Distribution -->
    <div class="col-xl-4 col-lg-12">
        <div class="analytics-card">
            <div class="card-header">
                <h5 class="mb-0">توزيع حالة المخزون</h5>
            </div>
            <div class="card-body">
                <div class="chart-container chart-small">
                    <canvas id="stockStatusChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Second Charts Row -->
<div class="row">
    <!-- Top Categories by Value -->
    <div class="col-xl-6 col-lg-12">
        <div class="analytics-card">
            <div class="card-header">
                <h5 class="mb-0">أعلى الفئات قيمة</h5>
            </div>
            <div class="card-body">
                <div class="chart-container chart-small">
                    <canvas id="topCategoriesChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Stock Movement Trend -->
    <div class="col-xl-6 col-lg-12">
        <div class="analytics-card">
            <div class="card-header">
                <h5 class="mb-0">اتجاه حركة المخزون</h5>
            </div>
            <div class="card-body">
                <div class="chart-container chart-small">
                    <canvas id="stockMovementChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Analytics Tables -->
<div class="row">
    <!-- Top Items by Value -->
    <div class="col-xl-6 col-lg-12">
        <div class="analytics-card">
            <div class="card-header">
                <h5 class="mb-0">أعلى المواد قيمة</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table analytics-table">
                        <thead>
                            <tr>
                                <th>المادة</th>
                                <th>الكمية</th>
                                <th>القيمة</th>
                                <th>النسبة</th>
                            </tr>
                        </thead>
                        <tbody id="top-items-table">
                            <!-- Data will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Low Stock Alerts -->
    <div class="col-xl-6 col-lg-12">
        <div class="analytics-card">
            <div class="card-header">
                <h5 class="mb-0">تنبيهات المخزون المنخفض</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table analytics-table">
                        <thead>
                            <tr>
                                <th>المادة</th>
                                <th>المخزون الحالي</th>
                                <th>الحد الأدنى</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody id="low-stock-table">
                            <!-- Data will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Performance Metrics -->
<div class="row">
    <div class="col-xl-12">
        <div class="analytics-card">
            <div class="card-header">
                <h5 class="mb-0">مؤشرات الأداء</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="metric-comparison">
                            <div>
                                <h6 class="mb-1">معدل دوران المخزون</h6>
                                <small class="text-muted">مرات في السنة</small>
                            </div>
                            <div class="text-right">
                                <h5 class="mb-0 text-primary" id="turnover-metric">{{ number_format($metrics['turnover_rate'] ?? 0, 1) }}</h5>
                                <small class="text-success">+8.4%</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="metric-comparison">
                            <div>
                                <h6 class="mb-1">متوسط فترة التخزين</h6>
                                <small class="text-muted">أيام</small>
                            </div>
                            <div class="text-right">
                                <h5 class="mb-0 text-info" id="storage-days">{{ number_format($metrics['avg_storage_days'] ?? 0) }}</h5>
                                <small class="text-danger">+2.1%</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="metric-comparison">
                            <div>
                                <h6 class="mb-1">دقة المخزون</h6>
                                <small class="text-muted">نسبة مئوية</small>
                            </div>
                            <div class="text-right">
                                <h5 class="mb-0 text-success" id="accuracy-rate">{{ number_format($metrics['accuracy_rate'] ?? 0, 1) }}%</h5>
                                <small class="text-success">+1.2%</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="metric-comparison">
                            <div>
                                <h6 class="mb-1">تكلفة التخزين</h6>
                                <small class="text-muted">ريال شهرياً</small>
                            </div>
                            <div class="text-right">
                                <h5 class="mb-0 text-warning" id="storage-cost">{{ number_format($metrics['storage_cost'] ?? 0, 2) }}</h5>
                                <small class="text-danger">+5.7%</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@push('inventory-scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
let charts = {};

$(document).ready(function() {
    initializeCharts();
    loadAnalyticsData();
    
    // Period filter change handler
    $('#period-filter').on('change', function() {
        if ($(this).val() === 'custom') {
            $('#custom-date-from, #custom-date-to').show();
        } else {
            $('#custom-date-from, #custom-date-to').hide();
        }
    });
});

function initializeCharts() {
    // Inventory Value Trend Chart
    const inventoryValueCtx = document.getElementById('inventoryValueChart').getContext('2d');
    charts.inventoryValue = new Chart(inventoryValueCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'قيمة المخزون',
                data: [],
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString() + ' ريال';
                        }
                    }
                }
            }
        }
    });
    
    // Stock Status Distribution Chart
    const stockStatusCtx = document.getElementById('stockStatusChart').getContext('2d');
    charts.stockStatus = new Chart(stockStatusCtx, {
        type: 'doughnut',
        data: {
            labels: ['مخزون طبيعي', 'مخزون منخفض', 'مخزون حرج', 'نفد المخزون'],
            datasets: [{
                data: [],
                backgroundColor: [
                    '#28a745',
                    '#ffc107',
                    '#fd7e14',
                    '#dc3545'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    
    // Top Categories Chart
    const topCategoriesCtx = document.getElementById('topCategoriesChart').getContext('2d');
    charts.topCategories = new Chart(topCategoriesCtx, {
        type: 'bar',
        data: {
            labels: [],
            datasets: [{
                label: 'القيمة',
                data: [],
                backgroundColor: 'rgba(102, 126, 234, 0.8)',
                borderColor: '#667eea',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString() + ' ريال';
                        }
                    }
                }
            }
        }
    });
    
    // Stock Movement Chart
    const stockMovementCtx = document.getElementById('stockMovementChart').getContext('2d');
    charts.stockMovement = new Chart(stockMovementCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'الوارد',
                    data: [],
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    borderWidth: 2,
                    fill: false
                },
                {
                    label: 'الصادر',
                    data: [],
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    borderWidth: 2,
                    fill: false
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function loadAnalyticsData() {
    const filters = getFilters();
    
    $.get('{{ route("inventory.api.analytics.data") }}', filters)
        .done(function(data) {
            updateCharts(data);
            updateTables(data);
            updateKPIs(data);
        })
        .fail(function() {
            InventoryModule.showError('حدث خطأ أثناء جلب بيانات التحليلات');
        });
}

function updateCharts(data) {
    // Update Inventory Value Chart
    charts.inventoryValue.data.labels = data.inventory_trend.labels;
    charts.inventoryValue.data.datasets[0].data = data.inventory_trend.values;
    charts.inventoryValue.update();
    
    // Update Stock Status Chart
    charts.stockStatus.data.datasets[0].data = [
        data.stock_status.normal,
        data.stock_status.low,
        data.stock_status.critical,
        data.stock_status.out_of_stock
    ];
    charts.stockStatus.update();
    
    // Update Top Categories Chart
    charts.topCategories.data.labels = data.top_categories.labels;
    charts.topCategories.data.datasets[0].data = data.top_categories.values;
    charts.topCategories.update();
    
    // Update Stock Movement Chart
    charts.stockMovement.data.labels = data.stock_movement.labels;
    charts.stockMovement.data.datasets[0].data = data.stock_movement.inbound;
    charts.stockMovement.data.datasets[1].data = data.stock_movement.outbound;
    charts.stockMovement.update();
}

function updateTables(data) {
    // Update Top Items Table
    let topItemsHtml = '';
    data.top_items.forEach(function(item) {
        topItemsHtml += `
            <tr>
                <td>${item.name}</td>
                <td>${item.quantity} ${item.unit || ''}</td>
                <td>${InventoryModule.formatCurrency(item.value)}</td>
                <td>
                    <div class="progress progress-custom">
                        <div class="progress-bar bg-primary" style="width: ${item.percentage}%"></div>
                    </div>
                    <small>${item.percentage}%</small>
                </td>
            </tr>
        `;
    });
    $('#top-items-table').html(topItemsHtml);
    
    // Update Low Stock Table
    let lowStockHtml = '';
    data.low_stock_items.forEach(function(item) {
        const statusClass = item.current_stock <= 0 ? 'danger' : 
                           item.current_stock <= item.critical_level ? 'danger' : 'warning';
        const statusText = item.current_stock <= 0 ? 'نفد المخزون' : 
                          item.current_stock <= item.critical_level ? 'حرج' : 'منخفض';
        
        lowStockHtml += `
            <tr>
                <td>${item.name}</td>
                <td>${item.current_stock} ${item.unit || ''}</td>
                <td>${item.min_level} ${item.unit || ''}</td>
                <td><span class="badge badge-${statusClass}">${statusText}</span></td>
            </tr>
        `;
    });
    $('#low-stock-table').html(lowStockHtml);
}

function updateKPIs(data) {
    $('#total-items').text(data.kpis.total_items.toLocaleString());
    $('#inventory-value').text(InventoryModule.formatCurrency(data.kpis.inventory_value));
    $('#low-stock-items').text(data.kpis.low_stock_items);
    $('#turnover-rate').text(data.kpis.turnover_rate.toFixed(1));
    
    // Update performance metrics
    $('#turnover-metric').text(data.metrics.turnover_rate.toFixed(1));
    $('#storage-days').text(Math.round(data.metrics.avg_storage_days));
    $('#accuracy-rate').text(data.metrics.accuracy_rate.toFixed(1) + '%');
    $('#storage-cost').text(InventoryModule.formatCurrency(data.metrics.storage_cost));
}

function getFilters() {
    return {
        period: $('#period-filter').val(),
        date_from: $('#date-from').val(),
        date_to: $('#date-to').val(),
        branch_id: $('#branch-filter').val(),
        category_id: $('#category-filter').val()
    };
}

function applyFilters() {
    loadAnalyticsData();
}

function refreshAnalytics() {
    InventoryModule.showLoading($('.content'));
    loadAnalyticsData();
    setTimeout(function() {
        InventoryModule.hideLoading($('.content'));
        InventoryModule.showSuccess('تم تحديث البيانات بنجاح');
    }, 1000);
}

function exportAnalytics() {
    const filters = getFilters();
    const queryString = new URLSearchParams(filters).toString();
    window.location.href = `{{ route("inventory.api.analytics.export") }}?${queryString}`;
}

function scheduleReport() {
    // Open schedule report modal
    $('#scheduleReportModal').modal('show');
}
</script>
@endpush
