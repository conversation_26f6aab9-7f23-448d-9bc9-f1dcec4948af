<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('delivery_personnel', function (Blueprint $table) {
            // Add tenant_id column after id
            $table->foreignId('tenant_id')->after('id')->constrained('tenants')->onDelete('cascade');
            
            // Add index for better performance
            $table->index(['tenant_id', 'status']);
            $table->index(['tenant_id', 'branch_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('delivery_personnel', function (Blueprint $table) {
            $table->dropForeign(['tenant_id']);
            $table->dropIndex(['tenant_id', 'status']);
            $table->dropIndex(['tenant_id', 'branch_id']);
            $table->dropColumn('tenant_id');
        });
    }
};