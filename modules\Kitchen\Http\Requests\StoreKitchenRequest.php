<?php

namespace Modules\Kitchen\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Helpers\BranchHelper;

class StoreKitchenRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization should be handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'code' => ['nullable', 'string', 'max:255', 'unique:kitchens,code'],
            'description' => ['nullable', 'string'],
            'station_type' => [
                'required',
                'string',
                Rule::in(['hot', 'cold', 'grill', 'fryer', 'salad', 'dessert', 'beverage', 'prep', 'main', 'other'])
            ],
            'max_concurrent_orders' => ['nullable', 'integer', 'min:1', 'max:100'],
            'average_prep_time_minutes' => ['nullable', 'integer', 'min:1', 'max:300'],
            'is_active' => ['boolean'],
            'display_order' => ['nullable', 'integer', 'min:0'],
            'manager_id' => ['nullable', 'integer', 'exists:users,id'],
            'equipment_list' => ['nullable', 'array'],
            'equipment_list.*' => ['string', 'max:255'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Kitchen name is required.',
            'name.max' => 'Kitchen name cannot exceed 255 characters.',
            'code.unique' => 'This kitchen code is already taken.',
            'station_type.required' => 'Please select a station type.',
            'station_type.in' => 'Invalid station type selected.',
            'max_concurrent_orders.min' => 'Maximum concurrent orders must be at least 1.',
            'max_concurrent_orders.max' => 'Maximum concurrent orders cannot exceed 100.',
            'average_prep_time_minutes.min' => 'Average prep time must be at least 1 minute.',
            'average_prep_time_minutes.max' => 'Average prep time cannot exceed 300 minutes.',
            'manager_id.exists' => 'The selected manager does not exist.',
            'equipment_list.*.max' => 'Equipment name cannot exceed 255 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name' => 'kitchen name',
            'code' => 'kitchen code',
            'station_type' => 'station type',
            'max_concurrent_orders' => 'maximum concurrent orders',
            'average_prep_time_minutes' => 'average prep time',
            'manager_id' => 'manager',
            'equipment_list.*' => 'equipment',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Add branch_id and tenant_id from authenticated user
        $branchId = BranchHelper::getCurrentBranchId();
        $tenantId = BranchHelper::getCurrentTenantId();

        if ($branchId) {
            $this->merge(['branch_id' => $branchId]);
        }

        if ($tenantId) {
            $this->merge(['tenant_id' => $tenantId]);
        }

        // Set default values
        if (!$this->has('is_active')) {
            $this->merge(['is_active' => true]);
        }

        if (!$this->has('display_order')) {
            $this->merge(['display_order' => 0]);
        }

        // Clean up equipment list
        if ($this->has('equipment_list') && is_array($this->equipment_list)) {
            $this->merge([
                'equipment_list' => array_filter($this->equipment_list, function ($item) {
                    return !empty(trim($item));
                })
            ]);
        }
    }
}
