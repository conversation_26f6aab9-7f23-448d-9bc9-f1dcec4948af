<?php

namespace Modules\Kitchen\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class KitchenMenuItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'tenant_id' => $this->tenant_id,
            'kitchen_id' => $this->kitchen_id,
            'menu_item_id' => $this->menu_item_id,
            'prep_time_minutes' => $this->prep_time_minutes,
            'priority_level' => $this->priority_level,
            'is_active' => $this->is_active,
            'special_instructions' => $this->special_instructions,
            'assigned_by' => $this->assigned_by,
            'assigned_at' => $this->assigned_at?->toISOString(),
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),

            // Relationships
            'kitchen' => $this->whenLoaded('kitchen', function () {
                return [
                    'id' => $this->kitchen->id,
                    'name' => $this->kitchen->name,
                    'code' => $this->kitchen->code,
                    'station_type' => $this->kitchen->station_type,
                ];
            }),

            'menu_item' => $this->whenLoaded('menuItem', function () {
                return [
                    'id' => $this->menuItem->id,
                    'name' => $this->menuItem->name,
                    'description' => $this->menuItem->description,
                    'price' => $this->menuItem->base_price,
                    'category' => $this->menuItem->category?->name,
                    'image_url' => $this->menuItem->image_url,
                    'is_active' => $this->menuItem->is_active,
                ];
            }),

            'assigned_by_user' => $this->whenLoaded('assignedBy', function () {
                return [
                    'id' => $this->assignedBy->id,
                    'name' => $this->assignedBy->name,
                ];
            }),

            // Status badge for UI
            'status_badge' => [
                'class' => $this->is_active ? 'badge-success' : 'badge-danger',
                'text' => $this->is_active ? 'Active' : 'Inactive',
            ],

            // Time display
            'prep_time_display' => $this->prep_time_minutes ? 
                $this->prep_time_minutes . ' min' : 'Not set',

            // Priority display
            'priority_display' => $this->priority_level ? 
                'Level ' . $this->priority_level : 'Not set',

            // Timestamps formatted for display
            'formatted_timestamps' => [
                'assigned_at' => $this->assigned_at?->format('M d, Y H:i'),
                'created_at' => $this->created_at?->format('M d, Y H:i'),
                'updated_at' => $this->updated_at?->format('M d, Y H:i'),
            ],
        ];
    }
}
