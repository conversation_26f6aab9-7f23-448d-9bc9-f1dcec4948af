@extends('layouts.master')

@section('title', 'إدارة الموظفين')

@section('content')
<div class="min-h-screen bg-gray-50 py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <!-- Page Header with Gradient -->
        <div class="bg-gradient-to-r from-amber-500 to-orange-600 rounded-lg shadow-lg p-6 mb-6">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div class="flex-1 min-w-0">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                                <i class="fas fa-users text-white text-xl"></i>
                            </div>
                        </div>
                        <div class="mr-4 flex-1">
                            <h1 class="text-2xl font-bold text-white">إدارة الموظفين</h1>
                            <p class="text-amber-100">إدارة بيانات الموظفين والمعلومات الشخصية</p>
                        </div>
                    </div>
                </div>
                <div class="mt-4 flex-shrink-0 flex md:mt-0 md:mr-4">
                    <button onclick="openModal('addStaffModal')" class="bg-white text-amber-600 px-4 py-2 rounded-lg font-medium hover:bg-amber-50 transition-colors duration-200 flex items-center">
                        <i class="fas fa-plus ml-2"></i>
                        إضافة موظف جديد
                    </button>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <!-- Total Staff Card -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-amber-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-users text-amber-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="mr-4 flex-1 text-right">
                        <p class="text-sm font-medium text-gray-600">إجمالي الموظفين</p>
                        <p class="text-2xl font-bold text-amber-600" id="total-staff">{{ $analytics['total_staff'] ?? 0 }}</p>
                    </div>
                </div>
            </div>

            <!-- Active Staff Card -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-user-check text-green-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="mr-4 flex-1 text-right">
                        <p class="text-sm font-medium text-gray-600">الموظفين النشطين</p>
                        <p class="text-2xl font-bold text-green-600" id="active-staff">{{ $analytics['active_staff'] ?? 0 }}</p>
                    </div>
                </div>
            </div>

            <!-- Present Today Card -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-calendar-check text-blue-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="mr-4 flex-1 text-right">
                        <p class="text-sm font-medium text-gray-600">الحاضرين اليوم</p>
                        <p class="text-2xl font-bold text-blue-600" id="present-today">{{ $analytics['present_today'] ?? 0 }}</p>
                    </div>
                </div>
            </div>

            <!-- On Leave Card -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-user-times text-red-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="mr-4 flex-1 text-right">
                        <p class="text-sm font-medium text-gray-600">في إجازة</p>
                        <p class="text-2xl font-bold text-red-600" id="on-leave">{{ $analytics['on_leave'] ?? 0 }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- Search Input -->
                <div class="lg:col-span-2">
                    <label for="search-input" class="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <input type="text" id="search-input" class="block w-full pr-10 pl-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-amber-500 focus:border-amber-500" placeholder="البحث بالاسم، الهاتف، أو البريد الإلكتروني...">
                    </div>
                </div>

                <!-- Role Filter -->
                <div>
                    <label for="role-filter" class="block text-sm font-medium text-gray-700 mb-2">الدور الوظيفي</label>
                    <select id="role-filter" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500">
                        <option value="">جميع الأدوار</option>
                        <option value="manager">مدير</option>
                        <option value="cashier">كاشير</option>
                        <option value="waiter">نادل</option>
                        <option value="chef">طباخ</option>
                        <option value="delivery">توصيل</option>
                    </select>
                </div>

                <!-- Branch Filter -->
                <div>
                    <label for="branch-filter" class="block text-sm font-medium text-gray-700 mb-2">الفرع</label>
                    <select id="branch-filter" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500">
                        <option value="">جميع الفروع</option>
                        @foreach($branches ?? [] as $branch)
                            <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                        @endforeach
                    </select>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-wrap gap-2 mt-4 pt-4 border-t border-gray-200">
                <button onclick="exportData('excel')" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center">
                    <i class="fas fa-file-excel ml-2"></i>
                    تصدير Excel
                </button>
                <button onclick="exportData('pdf')" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors duration-200 flex items-center">
                    <i class="fas fa-file-pdf ml-2"></i>
                    تصدير PDF
                </button>
                <button onclick="printData()" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors duration-200 flex items-center">
                    <i class="fas fa-print ml-2"></i>
                    طباعة
                </button>
                <button onclick="refreshData()" class="bg-amber-600 text-white px-4 py-2 rounded-lg hover:bg-amber-700 transition-colors duration-200 flex items-center">
                    <i class="fas fa-sync-alt ml-2"></i>
                    تحديث
                </button>
            </div>
        </div>

        <!-- Staff Table -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">قائمة الموظفين</h3>
            </div>
            <div class="overflow-x-auto">
                <table id="staff-table" class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الموظف</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الدور</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الفرع</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الهاتف</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">آخر حضور</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- Data will be loaded via DataTables -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Include Modals -->
@include('hr::staff.modals')

@endsection

@push('styles')
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.tailwindcss.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.tailwindcss.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.tailwindcss.min.css">

<!-- Font Awesome -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<!-- Custom Styles -->
<style>
    .dataTables_wrapper .dataTables_length select,
    .dataTables_wrapper .dataTables_filter input {
        @apply border-gray-300 rounded-md shadow-sm focus:border-amber-500 focus:ring-amber-500;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        @apply px-3 py-2 ml-1 text-sm leading-tight text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-100 hover:text-gray-700;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        @apply bg-amber-500 text-white border-amber-500 hover:bg-amber-600;
    }
    
    .toast {
        @apply fixed top-4 left-4 bg-white border border-gray-200 rounded-lg shadow-lg p-4 z-50 transform transition-transform duration-300;
    }
    
    .toast.success {
        @apply border-green-200 bg-green-50;
    }
    
    .toast.error {
        @apply border-red-200 bg-red-50;
    }
</style>
@endpush

@push('scripts')
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.tailwindcss.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>

<script>
// Modal functions
function openModal(modalId) {
    document.getElementById(modalId).classList.remove('hidden');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

// Toast notification function
function showToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.innerHTML = `
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <i class="fas ${type === 'success' ? 'fa-check-circle text-green-400' : 'fa-exclamation-circle text-red-400'}"></i>
            </div>
            <div class="mr-3">
                <p class="text-sm font-medium ${type === 'success' ? 'text-green-800' : 'text-red-800'}">${message}</p>
            </div>
            <div class="mr-auto pl-3">
                <button onclick="this.parentElement.parentElement.parentElement.remove()" class="inline-flex ${type === 'success' ? 'text-green-400 hover:text-green-600' : 'text-red-400 hover:text-red-600'}">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    `;
    document.body.appendChild(toast);
    
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 5000);
}

$(document).ready(function() {
    // Initialize DataTable
    var table = $('#staff-table').DataTable({
        processing: true,
        serverSide: true,
        responsive: true,
        ajax: {
            url: '{{ route("hr.staff.data") }}',
            data: function(d) {
                d.search = $('#search-input').val();
                d.role = $('#role-filter').val();
                d.branch_id = $('#branch-filter').val();
            }
        },
        columns: [
            {
                data: 'name',
                name: 'name',
                render: function(data, type, row) {
                    return `
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-10 w-10">
                                <img class="h-10 w-10 rounded-full object-cover" src="${row.avatar || '/images/default-avatar.png'}" alt="${data}">
                            </div>
                            <div class="mr-4">
                                <div class="text-sm font-medium text-gray-900">${data}</div>
                                <div class="text-sm text-gray-500">${row.email}</div>
                            </div>
                        </div>
                    `;
                }
            },
            {
                data: 'role',
                name: 'role',
                render: function(data) {
                    const roleColors = {
                        'manager': 'bg-purple-100 text-purple-800',
                        'cashier': 'bg-blue-100 text-blue-800',
                        'waiter': 'bg-green-100 text-green-800',
                        'chef': 'bg-orange-100 text-orange-800',
                        'delivery': 'bg-yellow-100 text-yellow-800'
                    };
                    const roleNames = {
                        'manager': 'مدير',
                        'cashier': 'كاشير',
                        'waiter': 'نادل',
                        'chef': 'طباخ',
                        'delivery': 'توصيل'
                    };
                    return `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${roleColors[data] || 'bg-gray-100 text-gray-800'}">${roleNames[data] || data}</span>`;
                }
            },
            {data: 'branch_name', name: 'branch.name'},
            {data: 'phone', name: 'phone'},
            {
                data: 'status',
                name: 'status',
                render: function(data) {
                    const statusColors = {
                        'active': 'bg-green-100 text-green-800',
                        'inactive': 'bg-red-100 text-red-800',
                        'suspended': 'bg-yellow-100 text-yellow-800'
                    };
                    const statusNames = {
                        'active': 'نشط',
                        'inactive': 'غير نشط',
                        'suspended': 'معلق'
                    };
                    return `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColors[data] || 'bg-gray-100 text-gray-800'}">${statusNames[data] || data}</span>`;
                }
            },
            {data: 'last_attendance', name: 'last_attendance'},
            {
                data: 'actions',
                name: 'actions',
                orderable: false,
                searchable: false,
                render: function(data, type, row) {
                    return `
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <button onclick="viewStaff(${row.id})" class="text-blue-600 hover:text-blue-900" title="عرض">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button onclick="editStaff(${row.id})" class="text-amber-600 hover:text-amber-900" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button onclick="deleteStaff(${row.id})" class="text-red-600 hover:text-red-900" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    `;
                }
            }
        ],
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'excel',
                text: '<i class="fas fa-file-excel ml-1"></i> Excel',
                className: 'bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700'
            },
            {
                extend: 'pdf',
                text: '<i class="fas fa-file-pdf ml-1"></i> PDF',
                className: 'bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700'
            },
            {
                extend: 'print',
                text: '<i class="fas fa-print ml-1"></i> طباعة',
                className: 'bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700'
            }
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json'
        },
        order: [[0, 'asc']]
    });

    // Filter event handlers
    $('#search-input, #role-filter, #branch-filter').on('change keyup', function() {
        table.draw();
    });
});

// Export functions
function exportData(format) {
    if (format === 'excel') {
        $('#staff-table').DataTable().button('.buttons-excel').trigger();
    } else if (format === 'pdf') {
        $('#staff-table').DataTable().button('.buttons-pdf').trigger();
    }
}

function printData() {
    $('#staff-table').DataTable().button('.buttons-print').trigger();
}

function refreshData() {
    $('#staff-table').DataTable().ajax.reload();
    showToast('تم تحديث البيانات بنجاح');
}

// Staff management functions
function viewStaff(id) {
    // Implementation for viewing staff details
    window.location.href = `/hr/staff/${id}`;
}

function editStaff(id) {
    // Implementation for editing staff
    // Load staff data and open edit modal
    openModal('editStaffModal');
}

function deleteStaff(id) {
    if (confirm('هل أنت متأكد من حذف هذا الموظف؟')) {
        // Implementation for deleting staff
        showToast('تم حذف الموظف بنجاح');
        $('#staff-table').DataTable().ajax.reload();
    }
}
</script>
@endpush
