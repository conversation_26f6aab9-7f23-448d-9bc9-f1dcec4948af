<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tables', function (Blueprint $table) {
            $table->id();
            $table->foreignId('branch_id')->nullable()->constrained('branches')->onDelete('cascade');
            $table->foreignId('area_id')->nullable()->constrained('areas')->onDelete('set null');
            $table->string('table_number', 20)->nullable();
            $table->string('table_name')->nullable();
            $table->integer('seating_capacity')->nullable();
            $table->string('section', 50)->nullable()->comment('Indoor, outdoor, VIP, etc.');
            $table->enum('status', ['available', 'occupied', 'reserved', 'cleaning', 'out_of_order'])->nullable()->default('available');
            $table->string('qr_code')->nullable()->comment('For QR code ordering');
            $table->json('position_coordinates')->nullable()->comment('X,Y coordinates for floor plan');
            $table->text('notes')->nullable();
            $table->boolean('is_active')->nullable()->default(true);
            $table->timestamps();
            
            // $table->unique(['branch_id', 'table_number']);
            
            // Performance indexes
            $table->index(['branch_id', 'status']);
            $table->index(['area_id', 'status']);
            $table->index(['section', 'status']);
            $table->index(['seating_capacity']);
            $table->index(['is_active', 'status']);
            $table->index(['qr_code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tables');
    }
};