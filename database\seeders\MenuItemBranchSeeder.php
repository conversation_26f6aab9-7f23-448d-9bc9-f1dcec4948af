<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\MenuItemBranch;

class MenuItemBranchSeeder extends Seeder
{
    public function run()
    {
        $menuItem = \App\Models\MenuItem::first();
        $branch = \App\Models\Branch::first();

        if (!$menuItem || !$branch) {
            $this->command->info('No menu item or branch found. Skipping MenuItemBranchSeeder.');
            return;
        }

        MenuItemBranch::create([
            'menu_item_id' => $menuItem->id,
            'branch_id' => $branch->id,
            'menu_item_id' => 1,
            'branch_id' => 1,
            // Add other required fields here
        ]);
    }
}