<!-- View Customer Modal -->
<div class="modal fade" id="viewCustomerModal" tabindex="-1" aria-labelledby="viewCustomerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewCustomerModalLabel">
                    <i class="fas fa-user me-2"></i>Customer Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <!-- Customer Info Card -->
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-user me-2"></i>Customer Information
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="text-center mb-3">
                                    <div class="avatar-lg mx-auto mb-3">
                                        <div class="avatar-title bg-primary rounded-circle fs-2" id="view_customer_avatar">
                                            <!-- Avatar initials will be set by JS -->
                                        </div>
                                    </div>
                                    <h5 id="view_customer_name" class="mb-1"><!-- Name will be set by JS --></h5>
                                    <span id="view_customer_tier" class="badge"><!-- Tier will be set by JS --></span>
                                </div>
                                
                                <div class="customer-details">
                                    <div class="detail-item mb-2">
                                        <strong>Email:</strong>
                                        <span id="view_customer_email"><!-- Email will be set by JS --></span>
                                    </div>
                                    <div class="detail-item mb-2">
                                        <strong>Phone:</strong>
                                        <span id="view_customer_phone"><!-- Phone will be set by JS --></span>
                                    </div>
                                    <div class="detail-item mb-2">
                                        <strong>Date of Birth:</strong>
                                        <span id="view_customer_dob"><!-- DOB will be set by JS --></span>
                                    </div>
                                    <div class="detail-item mb-2">
                                        <strong>Gender:</strong>
                                        <span id="view_customer_gender"><!-- Gender will be set by JS --></span>
                                    </div>
                                    <div class="detail-item mb-2">
                                        <strong>Address:</strong>
                                        <span id="view_customer_address"><!-- Address will be set by JS --></span>
                                    </div>
                                    <div class="detail-item mb-2">
                                        <strong>Status:</strong>
                                        <span id="view_customer_status"><!-- Status will be set by JS --></span>
                                    </div>
                                    <div class="detail-item mb-2">
                                        <strong>Member Since:</strong>
                                        <span id="view_customer_member_since"><!-- Member since will be set by JS --></span>
                                    </div>
                                    <div class="detail-item">
                                        <strong>Last Visit:</strong>
                                        <span id="view_customer_last_visit"><!-- Last visit will be set by JS --></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Statistics Card -->
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-chart-bar me-2"></i>Statistics
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6 mb-3">
                                        <div class="stat-item">
                                            <h4 id="view_total_orders" class="text-primary mb-1">0</h4>
                                            <small class="text-muted">Total Orders</small>
                                        </div>
                                    </div>
                                    <div class="col-6 mb-3">
                                        <div class="stat-item">
                                            <h4 id="view_total_spent" class="text-success mb-1">$0.00</h4>
                                            <small class="text-muted">Total Spent</small>
                                        </div>
                                    </div>
                                    <div class="col-6 mb-3">
                                        <div class="stat-item">
                                            <h4 id="view_avg_order" class="text-info mb-1">$0.00</h4>
                                            <small class="text-muted">Avg Order</small>
                                        </div>
                                    </div>
                                    <div class="col-6 mb-3">
                                        <div class="stat-item">
                                            <h4 id="view_loyalty_points" class="text-warning mb-1">0</h4>
                                            <small class="text-muted">Loyalty Points</small>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mt-3">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Visit Frequency:</span>
                                        <span id="view_visit_frequency" class="fw-bold">0/month</span>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>Customer Value:</span>
                                        <span id="view_customer_value" class="fw-bold">Regular</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Loyalty Card -->
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0">
                                    <i class="fas fa-star me-2"></i>Loyalty Program
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="text-center mb-3">
                                    <div class="loyalty-points-display">
                                        <h2 id="view_loyalty_points_large" class="text-warning mb-1">0</h2>
                                        <p class="text-muted mb-0">Available Points</p>
                                    </div>
                                </div>
                                
                                <div class="loyalty-actions">
                                    <button type="button" class="btn btn-success btn-sm w-100 mb-2" onclick="showAddPointsModal()">
                                        <i class="fas fa-plus me-2"></i>Add Points
                                    </button>
                                    <button type="button" class="btn btn-warning btn-sm w-100 mb-2" onclick="showRedeemPointsModal()">
                                        <i class="fas fa-minus me-2"></i>Redeem Points
                                    </button>
                                    <button type="button" class="btn btn-info btn-sm w-100" onclick="showLoyaltyHistory()">
                                        <i class="fas fa-history me-2"></i>View History
                                    </button>
                                </div>
                                
                                <div class="mt-3">
                                    <small class="text-muted">
                                        <strong>Next Tier:</strong> <span id="view_next_tier">Gold (500 pts needed)</span>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Orders -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-shopping-cart me-2"></i>Recent Orders
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table id="recentOrdersTable" class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Order #</th>
                                                <th>Date</th>
                                                <th>Amount</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- Orders will be populated by JS -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Notes -->
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-sticky-note me-2"></i>Notes
                                </h6>
                            </div>
                            <div class="card-body">
                                <p id="view_customer_notes" class="mb-0 text-muted">
                                    <!-- Notes will be set by JS -->
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Close
                </button>
                <button type="button" class="btn btn-warning" onclick="editCustomerFromView()">
                    <i class="fas fa-edit me-2"></i>Edit Customer
                </button>
                <button type="button" class="btn btn-info" onclick="manageLoyaltyFromView()">
                    <i class="fas fa-star me-2"></i>Manage Loyalty
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentViewCustomerId = null;

// Function to open view modal
function viewCustomer(customerId) {
    currentViewCustomerId = customerId;
    
    // Fetch customer data with relationships
    $.get(`/customers/${customerId}?include=orders,loyaltyTransactions`, function(customer) {
        // Basic info
        $('#view_customer_avatar').text(customer.first_name.charAt(0) + customer.last_name.charAt(0));
        $('#view_customer_name').text(customer.first_name + ' ' + customer.last_name);
        $('#view_customer_email').text(customer.email || 'Not provided');
        $('#view_customer_phone').text(customer.phone || 'Not provided');
        $('#view_customer_dob').text(customer.date_of_birth ? moment(customer.date_of_birth).format('MMM DD, YYYY') : 'Not provided');
        $('#view_customer_gender').text(customer.gender ? customer.gender.charAt(0).toUpperCase() + customer.gender.slice(1) : 'Not specified');
        $('#view_customer_address').text(customer.full_address || 'Not provided');
        $('#view_customer_member_since').text(moment(customer.created_at).format('MMM DD, YYYY'));
        $('#view_customer_last_visit').text(customer.last_visit ? moment(customer.last_visit).format('MMM DD, YYYY') : 'Never');
        
        // Status
        const statusBadge = customer.is_active ? 
            '<span class="badge bg-success">Active</span>' : 
            '<span class="badge bg-danger">Inactive</span>';
        $('#view_customer_status').html(statusBadge);
        
        // Tier
        const tierColors = {
            'Bronze': 'bg-secondary',
            'Silver': 'bg-light text-dark',
            'Gold': 'bg-warning',
            'Platinum': 'bg-dark'
        };
        const tierBadge = `<span class="badge ${tierColors[customer.tier] || 'bg-secondary'}">${customer.tier}</span>`;
        $('#view_customer_tier').html(tierBadge);
        
        // Statistics
        $('#view_total_orders').text(customer.total_orders || 0);
        $('#view_total_spent').text('$' + (customer.total_spent || 0).toFixed(2));
        $('#view_avg_order').text('$' + (customer.average_order_value || 0).toFixed(2));
        $('#view_loyalty_points').text(customer.loyalty_points || 0);
        $('#view_loyalty_points_large').text(customer.loyalty_points || 0);
        $('#view_visit_frequency').text((customer.visit_frequency || 0) + '/month');
        $('#view_customer_value').text(customer.customer_value || 'Regular');
        
        // Next tier calculation
        const nextTierInfo = calculateNextTier(customer.loyalty_points);
        $('#view_next_tier').text(nextTierInfo);
        
        // Notes
        $('#view_customer_notes').text(customer.notes || 'No notes available.');
        
        // Recent orders
        populateRecentOrders(customer.recent_orders || []);
        
        // Show modal
        $('#viewCustomerModal').modal('show');
    }).fail(function() {
        toastr.error('Failed to load customer data.');
    });
}

function calculateNextTier(currentPoints) {
    if (currentPoints < 500) {
        return `Silver (${500 - currentPoints} pts needed)`;
    } else if (currentPoints < 2000) {
        return `Gold (${2000 - currentPoints} pts needed)`;
    } else if (currentPoints < 5000) {
        return `Platinum (${5000 - currentPoints} pts needed)`;
    } else {
        return 'Platinum (Highest tier achieved)';
    }
}

function populateRecentOrders(orders) {
    const tbody = $('#recentOrdersTable tbody');
    tbody.empty();
    
    if (orders.length === 0) {
        tbody.append('<tr><td colspan="5" class="text-center text-muted">No recent orders</td></tr>');
        return;
    }
    
    orders.forEach(function(order) {
        const statusBadge = getOrderStatusBadge(order.status);
        const row = `
            <tr>
                <td>${order.order_number}</td>
                <td>${moment(order.created_at).format('MMM DD, YYYY')}</td>
                <td>$${order.total_amount.toFixed(2)}</td>
                <td>${statusBadge}</td>
                <td>
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewOrder(${order.id})">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

function getOrderStatusBadge(status) {
    const statusColors = {
        'pending': 'bg-warning',
        'confirmed': 'bg-info',
        'preparing': 'bg-primary',
        'ready': 'bg-success',
        'delivered': 'bg-success',
        'cancelled': 'bg-danger'
    };
    
    return `<span class="badge ${statusColors[status] || 'bg-secondary'}">${status.charAt(0).toUpperCase() + status.slice(1)}</span>`;
}

function editCustomerFromView() {
    $('#viewCustomerModal').modal('hide');
    setTimeout(() => {
        editCustomer(currentViewCustomerId);
    }, 300);
}

function manageLoyaltyFromView() {
    $('#viewCustomerModal').modal('hide');
    setTimeout(() => {
        manageLoyalty(currentViewCustomerId);
    }, 300);
}

function showAddPointsModal() {
    // Implementation for add points modal
    console.log('Show add points modal for customer:', currentViewCustomerId);
}

function showRedeemPointsModal() {
    // Implementation for redeem points modal
    console.log('Show redeem points modal for customer:', currentViewCustomerId);
}

function showLoyaltyHistory() {
    // Implementation for loyalty history modal
    console.log('Show loyalty history for customer:', currentViewCustomerId);
}

function viewOrder(orderId) {
    // Implementation for viewing order details
    console.log('View order:', orderId);
}
</script>

<style>
.avatar-lg {
    width: 80px;
    height: 80px;
}

.avatar-title {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.detail-item:last-child {
    border-bottom: none;
}

.stat-item h4 {
    font-size: 1.5rem;
    font-weight: bold;
}

.loyalty-points-display h2 {
    font-size: 2.5rem;
    font-weight: bold;
}
</style>