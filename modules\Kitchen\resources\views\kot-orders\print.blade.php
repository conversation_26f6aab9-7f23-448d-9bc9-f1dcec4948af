<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KOT Print - {{ $kotOrder->kot_number }}</title>
    <style>
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
        
        body {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            margin: 0;
            padding: 10px;
            background: white;
        }
        
        .kot-print {
            max-width: 300px;
            margin: 0 auto;
            border: 1px solid #000;
            padding: 10px;
        }
        
        .header {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
            margin-bottom: 10px;
        }
        
        .kot-number {
            font-size: 18px;
            font-weight: bold;
            margin: 5px 0;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin: 3px 0;
        }
        
        .items-section {
            border-top: 1px solid #000;
            border-bottom: 1px solid #000;
            padding: 10px 0;
            margin: 10px 0;
        }
        
        .item {
            margin: 5px 0;
            padding: 3px 0;
        }
        
        .item-name {
            font-weight: bold;
        }
        
        .item-details {
            font-size: 10px;
            color: #666;
            margin-left: 10px;
        }
        
        .footer {
            text-align: center;
            margin-top: 15px;
            font-size: 10px;
        }
        
        .status-priority {
            text-align: center;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .print-button {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .print-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <button class="print-button no-print" onclick="window.print()">
        <i class="fas fa-print"></i> Print
    </button>
    
    <div class="kot-print">
        <!-- Header -->
        <div class="header">
            <div style="font-size: 16px; font-weight: bold;">KITCHEN ORDER TICKET</div>
            <div class="kot-number">{{ $kotOrder->kot_number }}</div>
            <div>{{ $kotOrder->kitchen?->name ?? 'Kitchen' }}</div>
        </div>
        
        <!-- Order Information -->
        <div class="info-row">
            <span>Order #:</span>
            <span>{{ $kotOrder->order?->order_number ?? 'N/A' }}</span>
        </div>
        
        <div class="info-row">
            <span>Date:</span>
            <span>{{ $kotOrder->created_at->format('d/m/Y H:i') }}</span>
        </div>
        
        @if($kotOrder->order?->table_number)
        <div class="info-row">
            <span>Table:</span>
            <span>{{ $kotOrder->order->table_number }}</span>
        </div>
        @endif
        
        @if($kotOrder->order?->customer_name)
        <div class="info-row">
            <span>Customer:</span>
            <span>{{ $kotOrder->order->customer_name }}</span>
        </div>
        @endif
        
        <!-- Status and Priority -->
        <div class="status-priority">
            <div>STATUS: {{ strtoupper($kotOrder->status) }}</div>
            <div>PRIORITY: {{ strtoupper($kotOrder->priority) }}</div>
        </div>
        
        <!-- Items -->
        <div class="items-section">
            <div style="font-weight: bold; text-align: center; margin-bottom: 10px;">ITEMS</div>
            
            @forelse($kotOrder->items as $item)
                <div class="item">
                    <div class="item-name">
                        {{ $item->quantity }}x {{ $item->menu_item_name }}
                    </div>
                    
                    @if($item->special_instructions)
                        <div class="item-details">
                            Note: {{ $item->special_instructions }}
                        </div>
                    @endif
                    
                    @if($item->modifications)
                        <div class="item-details">
                            Modifications: {{ $item->modifications }}
                        </div>
                    @endif
                    
                    <div class="item-details">
                        Status: {{ ucfirst($item->status) }}
                        @if($item->prep_time_minutes)
                            | Prep Time: {{ $item->prep_time_minutes }} min
                        @endif
                    </div>
                </div>
            @empty
                <div style="text-align: center; font-style: italic;">No items</div>
            @endforelse
        </div>
        
        <!-- Special Instructions -->
        @if($kotOrder->special_instructions)
        <div style="margin: 10px 0;">
            <div style="font-weight: bold;">SPECIAL INSTRUCTIONS:</div>
            <div style="margin-top: 5px;">{{ $kotOrder->special_instructions }}</div>
        </div>
        @endif
        
        <!-- Assigned To -->
        @if($kotOrder->assignedTo)
        <div class="info-row">
            <span>Assigned To:</span>
            <span>{{ $kotOrder->assignedTo->name }}</span>
        </div>
        @endif
        
        <!-- Timing Information -->
        @if($kotOrder->estimated_prep_time)
        <div class="info-row">
            <span>Est. Prep Time:</span>
            <span>{{ $kotOrder->estimated_prep_time }} min</span>
        </div>
        @endif
        
        @if($kotOrder->started_at)
        <div class="info-row">
            <span>Started At:</span>
            <span>{{ $kotOrder->started_at->format('H:i') }}</span>
        </div>
        @endif
        
        <!-- Footer -->
        <div class="footer">
            <div>Printed: {{ now()->format('d/m/Y H:i:s') }}</div>
            <div>{{ config('app.name', 'Restaurant POS') }}</div>
        </div>
    </div>
    
    <script>
        // Auto print when page loads
        window.onload = function() {
            // Small delay to ensure content is loaded
            setTimeout(function() {
                window.print();
            }, 500);
        };
        
        // Close window after printing
        window.onafterprint = function() {
            window.close();
        };
    </script>
</body>
</html>