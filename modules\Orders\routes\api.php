<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Modules\Orders\Http\Controllers\Api\OrderController;
use Modules\Orders\Http\Controllers\Api\POSController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:sanctum')->name('apis.orders.')->group(function () {
    // Order CRUD operations
    Route::apiResource('orders', OrderController::class);
    
    // Order status management
    Route::patch('orders/{order}/status', [OrderController::class, 'updateStatus']);
    
    // Order confirmation
    Route::post('orders/{order}/confirm', [OrderController::class, 'confirmOrder']);
    
    // Order items management
    Route::post('orders/{order}/items', [OrderController::class, 'addOrderItem']);
    Route::delete('orders/{order}/items/{orderItem}', [OrderController::class, 'removeOrderItem']);
    Route::get('orders/{order}/items', [OrderController::class, 'getOrderItems']);
    
    // Menu item addons
    Route::get('menu-items/{menuItem}/addons', [OrderController::class, 'getMenuItemAddons']);
    
    // POS API routes
    Route::prefix('pos')->group(function() {
        Route::get('dashboard', [POSController::class, 'getDashboardData']);
        Route::get('form-data', [POSController::class, 'getFormData']);
        Route::post('orders', [POSController::class, 'store']);
        Route::get('menu-items/{menuItem}/addons', [POSController::class, 'getMenuItemAddons']);
        Route::get('menu-items/{menuItem}/details', [POSController::class, 'getMenuItemDetails']);
        Route::post('calculate-totals', [POSController::class, 'calculateOrderTotals']);
    });
});

// Public routes (no authentication required)
Route::get('order-statuses', [OrderController::class, 'getOrderStatuses']);
Route::get('order-types', [OrderController::class, 'getOrderTypes']);