<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON>les\Delivery\Http\Controllers\DeliveryController;
use Modules\Delivery\Http\Controllers\DeliveryPersonnelController;
use Modules\Delivery\Http\Controllers\DeliveryZoneController;
use Modules\Delivery\Http\Controllers\DeliveryTrackingController;
use Modules\Delivery\Http\Controllers\DeliveryAnalyticsController;

/*
|--------------------------------------------------------------------------
| Delivery API Routes
|--------------------------------------------------------------------------
|
| Here are the API routes for the Delivery module. These routes handle
| delivery personnel management, zones, assignments, tracking, reviews,
| and tips functionality.
|
*/


    // Delivery Personnel Management
    Route::prefix('delivery/personnel')->name('delivery.api.personnel.')->group(function () {
        Route::get('/', [DeliveryPersonnelController::class, 'index'])->name('index');
        Route::post('/', [DeliveryPersonnelController::class, 'store'])->name('store');
        Route::get('/{id}', [DeliveryPersonnelController::class, 'show'])->name('show');
        Route::put('/{id}', [DeliveryPersonnelController::class, 'update'])->name('update');
        Route::delete('/{id}', [DeliveryPersonnelController::class, 'destroy'])->name('delete');
        Route::post('/{id}/verify', [DeliveryPersonnelController::class, 'verify'])->name('verify');
        Route::get('/{id}/performance', [DeliveryPersonnelController::class, 'performance'])->name('performance');
        Route::get('/available/list', [DeliveryPersonnelController::class, 'available'])->name('available');
    });

    // Delivery Zones Management
    Route::prefix('delivery/zones')->name('delivery.api.zones.')->group(function () {
        Route::get('/', [DeliveryZoneController::class, 'index'])->name('index');
        Route::post('/', [DeliveryZoneController::class, 'store'])->name('store');
        Route::get('/{id}', [DeliveryZoneController::class, 'show'])->name('show');
        Route::put('/{id}', [DeliveryZoneController::class, 'update'])->name('update');
        Route::post('/check', [DeliveryZoneController::class, 'checkAvailability'])->name('check');
    });

    // Delivery Assignments Management
    Route::prefix('delivery/assignments')->name('delivery.api.assignments.')->group(function () {
        Route::get('/', [DeliveryController::class, 'index'])->name('index');
        Route::get('/{id}', [DeliveryController::class, 'show'])->name('show');
        Route::post('/assign', [DeliveryController::class, 'store'])->name('assign');
        Route::post('/auto-assign', [DeliveryController::class, 'autoAssign'])->name('auto-assign');
        Route::put('/{id}', [DeliveryController::class, 'update'])->name('update');
        Route::put('/{id}/status', [DeliveryController::class, 'updateStatus'])->name('update-status');
    });

    // Location Tracking
    Route::prefix('delivery/tracking')->name('delivery.api.tracking.')->group(function () {
        Route::post('/{assignmentId}/location', [DeliveryTrackingController::class, 'updateLocation'])->name('update-location');
        Route::get('/{assignmentId}/tracking', [DeliveryTrackingController::class, 'trackingHistory'])->name('data');
        Route::get('/{assignmentId}/current-location', [DeliveryTrackingController::class, 'latestLocation'])->name('current-location');
    });

    // Delivery Reviews
    Route::prefix('delivery/reviews')->name('delivery.api.reviews.')->group(function () {
        Route::get('/', [DeliveryController::class, 'getDeliveryReviews'])->name('index');
        Route::post('/', [DeliveryController::class, 'createDeliveryReview'])->name('store');
        Route::get('/{id}', [DeliveryController::class, 'getReviewById'])->name('show');
        Route::post('/{id}/verify', [DeliveryController::class, 'verifyReview'])->name('verify');
        Route::get('/personnel/{personnelId}', [DeliveryController::class, 'getPersonnelReviews'])->name('personnel');
    });

    // Delivery Tips (keeping with DeliveryController for now - will need methods added)
    Route::prefix('delivery/tips')->name('delivery.api.tips.')->group(function () {
        Route::get('/', [DeliveryController::class, 'getDeliveryTips'])->name('index');
        Route::post('/', [DeliveryController::class, 'createDeliveryTip'])->name('store');
        Route::get('/{id}', [DeliveryController::class, 'getDeliveryTips'])->name('show');
        Route::post('/{id}/process-payment', [DeliveryController::class, 'processPayment'])->name('process-payment');
        Route::get('/personnel/{personnelId}', [DeliveryController::class, 'getPersonnelTips'])->name('personnel');
    });

    // Analytics and Reports
    Route::prefix('delivery/analytics')->name('delivery.api.analytics.')->group(function () {
        Route::get('/stats', [DeliveryAnalyticsController::class, 'overview'])->name('stats');
        Route::get('/personnel/{personnelId}/performance', [DeliveryAnalyticsController::class, 'personnel'])->name('personnel-performance');
    });

    // Utility Routes
    Route::prefix('delivery/utils')->name('delivery.api.utils.')->group(function () {
        Route::post('/calculate-fee', [DeliveryController::class, 'calculateFee'])->name('calculate-fee');
        Route::get('/available-personnel', [DeliveryPersonnelController::class, 'available'])->name('available-personnel');
    });

    // Public Routes (no authentication required)
    Route::prefix('delivery/public')->name('delivery.api.public.')->group(function () {
        Route::post('/check-zone', [DeliveryZoneController::class, 'checkAvailability'])->name('check-zone');
        Route::post('/calculate-fee', [DeliveryZoneController::class, 'calculateFee'])->name('calculate-fee');
    });

    // Customer-specific routes (authenticated customers only)
    Route::prefix('delivery/customer')->name('delivery.api.customer.')->middleware(['auth:sanctum', 'customer'])->group(function () {
        Route::get('/my-deliveries', [DeliveryController::class, 'getCustomerDeliveries'])->name('my-deliveries');
        Route::get('/tracking/{assignmentId}', [DeliveryTrackingController::class, 'customerTracking'])->name('tracking');
        Route::post('/reviews', [DeliveryController::class, 'createDeliveryReview'])->name('create-review');
        Route::post('/tips', [DeliveryController::class, 'createDeliveryTip'])->name('create-tip');
    });

    // Personnel-specific routes (authenticated delivery personnel only)
    Route::prefix('delivery/personnel-app')->name('delivery.api.personnel-app.')->middleware(['auth:sanctum', 'delivery-personnel'])->group(function () {
        Route::get('/my-assignments', [DeliveryController::class, 'getPersonnelAssignments'])->name('my-assignments');
        Route::put('/assignments/{id}/status', [DeliveryController::class, 'updateStatus'])->name('update-status');
        Route::post('/assignments/{assignmentId}/location', [DeliveryTrackingController::class, 'updateLocation'])->name('update-location');
        Route::get('/my-performance', [DeliveryController::class, 'getMyPerformance'])->name('my-performance');
        Route::get('/my-earnings', [DeliveryController::class, 'getMyEarnings'])->name('my-earnings');
    });

    // Admin routes (authenticated admin users only)
    Route::prefix('delivery/admin')->name('delivery.api.admin.')->middleware(['auth:sanctum', 'admin'])->group(function () {
        Route::get('/dashboard', [DeliveryAnalyticsController::class, 'dashboard'])->name('dashboard');
        Route::get('/reports/daily', [DeliveryAnalyticsController::class, 'dailyTrends'])->name('daily-report');
        Route::get('/reports/weekly', [DeliveryAnalyticsController::class, 'weeklyTrends'])->name('weekly-report');
        Route::get('/reports/monthly', [DeliveryController::class, 'getMonthlyReport'])->name('monthly-report');
        Route::post('/personnel/{id}/suspend', [DeliveryPersonnelController::class, 'suspendPersonnel'])->name('suspend-personnel');
        Route::post('/personnel/{id}/activate', [DeliveryPersonnelController::class, 'activatePersonnel'])->name('activate-personnel');
    });

    // Webhook routes for external integrations
    Route::prefix('delivery/webhooks')->name('delivery.api.webhooks.')->group(function () {
        Route::post('/payment-status', [DeliveryController::class, 'handlePaymentWebhook'])->name('payment-status');
        Route::post('/location-update', [DeliveryTrackingController::class, 'handleLocationWebhook'])->name('location-update');
    });
