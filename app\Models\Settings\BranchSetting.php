<?php

namespace App\Models\Settings;

use App\Models\Tenant;
use App\Models\Branch;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Cache;

class BranchSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'tenant_id',
        'branch_id',
        'category',
        'key',
        'value',
        'data_type',
        'description',
    ];

    // Relationships
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    // Scopes
    public function scopeForTenant($query, $tenantId)
    {
        return $query->where('tenant_id', $tenantId);
    }

    public function scopeForBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByKey($query, $key)
    {
        return $query->where('key', $key);
    }

    // Static methods for easy access
    public static function get($key, $tenantId, $branchId, $category = 'general', $default = null)
    {
        $cacheKey = "branch_setting_{$tenantId}_{$branchId}_{$category}_{$key}";
        
        return Cache::remember($cacheKey, 1800, function () use ($key, $tenantId, $branchId, $category, $default) {
            $setting = static::where('tenant_id', $tenantId)
                ->where('branch_id', $branchId)
                ->where('category', $category)
                ->where('key', $key)
                ->first();
            
            if (!$setting) {
                return $default;
            }
            
            return static::castValue($setting->value, $setting->data_type);
        });
    }

    public static function set($key, $value, $tenantId, $branchId, $category = 'general', $description = null)
    {
        $dataType = static::detectDataType($value);

        $setting = static::updateOrCreate(
            [
                'tenant_id' => $tenantId,
                'branch_id' => $branchId,
                'category' => $category,
                'key' => $key,
            ],
            [
                'value' => is_array($value) ? json_encode($value) : $value,
                'data_type' => $dataType,
                'description' => $description,
            ]
        );

        // Clear cache
        $cacheKey = "branch_setting_{$tenantId}_{$branchId}_{$category}_{$key}";
        Cache::forget($cacheKey);

        return $setting;
    }

    public static function getByCategory($category, $tenantId, $branchId)
    {
        $cacheKey = "branch_settings_{$tenantId}_{$branchId}_{$category}";
        
        return Cache::remember($cacheKey, 1800, function () use ($category, $tenantId, $branchId) {
            $settings = static::where('tenant_id', $tenantId)
                ->where('branch_id', $branchId)
                ->where('category', $category)
                ->get();
            
            $result = [];
            foreach ($settings as $setting) {
                $result[$setting->key] = static::castValue($setting->value, $setting->data_type);
            }
            
            return $result;
        });
    }

    protected static function castValue($value, $dataType)
    {
        switch ($dataType) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'integer':
                return (int) $value;
            case 'float':
                return (float) $value;
            case 'json':
                return json_decode($value, true);
            case 'array':
                return is_string($value) ? json_decode($value, true) : $value;
            default:
                return $value;
        }
    }

    protected static function detectDataType($value)
    {
        if (is_bool($value)) {
            return 'boolean';
        } elseif (is_int($value)) {
            return 'integer';
        } elseif (is_float($value)) {
            return 'float';
        } elseif (is_array($value)) {
            return 'json';
        } else {
            return 'string';
        }
    }

    // Default branch settings categories and keys
    public static function getDefaultCategories(): array
    {
        return [
            'general' => [
                'restaurant_name' => 'اسم المطعم',
                'restaurant_logo' => 'شعار المطعم',
                'restaurant_address' => 'عنوان المطعم',
                'contact_phone' => 'رقم الهاتف',
                'contact_email' => 'البريد الإلكتروني',
                'default_language' => 'اللغة الافتراضية',
                'currency' => 'العملة المستخدمة',
                'timezone' => 'المنطقة الزمنية',
                'date_format' => 'تنسيق التاريخ',
                'time_format' => 'تنسيق الوقت',
            ],
            'pos' => [
                'show_tax_in_invoice' => 'إظهار الضريبة في الفاتورة',
                'minimum_order_amount' => 'الحد الأدنى للطلب',
                'sound_notification' => 'الجرس الصوتي للطلبات',
                'show_order_notes' => 'إظهار ملاحظات الطلب',
                'auto_print_receipt' => 'طباعة إيصال تلقائي',
                'require_customer_info' => 'طلب معلومات العميل',
            ],
            'operating_hours' => [
                'monday' => 'الاثنين',
                'tuesday' => 'الثلاثاء',
                'wednesday' => 'الأربعاء',
                'thursday' => 'الخميس',
                'friday' => 'الجمعة',
                'saturday' => 'السبت',
                'sunday' => 'الأحد',
            ],
            'tax' => [
                'default_tax_rate' => 'معدل الضريبة الافتراضي',
                'tax_inclusive' => 'الضريبة شاملة',
                'tax_number' => 'الرقم الضريبي',
            ],
        ];
    }
}
