<?php

namespace App\Models\Settings;

use App\Models\Tenant;
use App\Models\Branch;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PaymentSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'tenant_id',
        'branch_id',
        'payment_method',
        'is_enabled',
        'gateway_config',
        'min_amount',
        'max_amount',
        'transaction_fee',
        'fee_type',
        'display_order',
    ];

    protected $casts = [
        'is_enabled' => 'boolean',
        'gateway_config' => 'array',
        'min_amount' => 'decimal:2',
        'max_amount' => 'decimal:2',
        'transaction_fee' => 'decimal:2',
    ];

    // Relationships
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    // Scopes
    public function scopeEnabled($query)
    {
        return $query->where('is_enabled', true);
    }

    public function scopeForTenant($query, $tenantId)
    {
        return $query->where('tenant_id', $tenantId);
    }

    public function scopeForBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('display_order');
    }

    // Methods
    public function calculateFee($amount): float
    {
        if ($this->fee_type === 'percentage') {
            return ($amount * $this->transaction_fee) / 100;
        }
        
        return $this->transaction_fee;
    }

    public function isAmountValid($amount): bool
    {
        if ($this->min_amount && $amount < $this->min_amount) {
            return false;
        }
        
        if ($this->max_amount && $amount > $this->max_amount) {
            return false;
        }
        
        return true;
    }

    // Static methods
    public static function getAvailablePaymentMethods(): array
    {
        return [
            'cash' => [
                'name' => 'نقدي',
                'icon' => 'fe-dollar-sign',
                'requires_gateway' => false,
            ],
            'card' => [
                'name' => 'بطاقة ائتمان',
                'icon' => 'fe-credit-card',
                'requires_gateway' => true,
            ],
            'visa' => [
                'name' => 'فيزا',
                'icon' => 'fe-credit-card',
                'requires_gateway' => true,
            ],
            'mastercard' => [
                'name' => 'ماستركارد',
                'icon' => 'fe-credit-card',
                'requires_gateway' => true,
            ],
            'apple_pay' => [
                'name' => 'Apple Pay',
                'icon' => 'fe-smartphone',
                'requires_gateway' => true,
            ],
            'google_pay' => [
                'name' => 'Google Pay',
                'icon' => 'fe-smartphone',
                'requires_gateway' => true,
            ],
            'samsung_pay' => [
                'name' => 'Samsung Pay',
                'icon' => 'fe-smartphone',
                'requires_gateway' => true,
            ],
            'stc_pay' => [
                'name' => 'STC Pay',
                'icon' => 'fe-smartphone',
                'requires_gateway' => true,
            ],
            'bank_transfer' => [
                'name' => 'تحويل بنكي',
                'icon' => 'fe-send',
                'requires_gateway' => false,
            ],
        ];
    }

    public static function getDefaultSettings($tenantId, $branchId = null): array
    {
        return [
            [
                'tenant_id' => $tenantId,
                'branch_id' => $branchId,
                'payment_method' => 'cash',
                'is_enabled' => true,
                'min_amount' => 0,
                'max_amount' => null,
                'transaction_fee' => 0,
                'fee_type' => 'fixed',
                'display_order' => 1,
            ],
            [
                'tenant_id' => $tenantId,
                'branch_id' => $branchId,
                'payment_method' => 'card',
                'is_enabled' => false,
                'min_amount' => 10,
                'max_amount' => null,
                'transaction_fee' => 2.5,
                'fee_type' => 'percentage',
                'display_order' => 2,
            ],
            [
                'tenant_id' => $tenantId,
                'branch_id' => $branchId,
                'payment_method' => 'apple_pay',
                'is_enabled' => false,
                'min_amount' => 5,
                'max_amount' => 1000,
                'transaction_fee' => 1.5,
                'fee_type' => 'percentage',
                'display_order' => 3,
            ],
        ];
    }
}
