@extends('layouts.master')

@section('title', 'Security & Access Control')

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
@endpush

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- <PERSON> Header -->
    <div class="bg-gradient-to-r from-red-600 to-orange-600 rounded-lg shadow-lg p-6 mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                            <i class="fas fa-shield-alt text-white text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <h1 class="text-2xl font-bold text-white">Security & Access Control</h1>
                        <p class="text-red-100">Manage user permissions, authentication, and security settings</p>
                    </div>
                </div>
            </div>
            <div class="mt-4 md:mt-0">
                <button class="px-4 py-2 bg-white text-red-600 rounded-lg hover:bg-red-50 transition-colors duration-200 font-medium">
                    <i class="fas fa-key mr-2"></i>
                    Security Audit
                </button>
            </div>
        </div>
    </div>

    <!-- Security Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-users text-green-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Active Users</p>
                    <p class="text-2xl font-bold text-gray-900">24</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-user-shield text-blue-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Admin Users</p>
                    <p class="text-2xl font-bold text-gray-900">3</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-clock text-yellow-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Session Timeout</p>
                    <p class="text-2xl font-bold text-gray-900">30m</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Failed Logins</p>
                    <p class="text-2xl font-bold text-gray-900">2</p>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Authentication Settings -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-lock mr-2 text-blue-600"></i>
                Authentication Settings
            </h3>
            
            <form class="space-y-4">
                <div>
                    <label for="session_timeout" class="block text-sm font-medium text-gray-700 mb-2">
                        Session Timeout (minutes)
                    </label>
                    <input type="number" id="session_timeout" name="session_timeout" value="30" min="5" max="480"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label for="max_login_attempts" class="block text-sm font-medium text-gray-700 mb-2">
                        Max Login Attempts
                    </label>
                    <input type="number" id="max_login_attempts" name="max_login_attempts" value="5" min="3" max="10"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label for="lockout_duration" class="block text-sm font-medium text-gray-700 mb-2">
                        Account Lockout Duration (minutes)
                    </label>
                    <input type="number" id="lockout_duration" name="lockout_duration" value="15" min="5" max="60"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div>
                            <label for="require_2fa" class="text-sm font-medium text-gray-700">Two-Factor Authentication</label>
                            <p class="text-sm text-gray-500">Require 2FA for all admin users</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="require_2fa" name="require_2fa" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="force_password_change" class="text-sm font-medium text-gray-700">Force Password Change</label>
                            <p class="text-sm text-gray-500">Require password change every 90 days</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="force_password_change" name="force_password_change" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="remember_me" class="text-sm font-medium text-gray-700">Remember Me Option</label>
                            <p class="text-sm text-gray-500">Allow users to stay logged in</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="remember_me" name="remember_me" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                </div>

                <div class="pt-4">
                    <button type="submit" class="w-full px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700">
                        <i class="fas fa-save mr-2"></i>
                        Save Authentication Settings
                    </button>
                </div>
            </form>
        </div>

        <!-- Password Policy -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-key mr-2 text-green-600"></i>
                Password Policy
            </h3>
            
            <form class="space-y-4">
                <div>
                    <label for="min_length" class="block text-sm font-medium text-gray-700 mb-2">
                        Minimum Password Length
                    </label>
                    <input type="number" id="min_length" name="min_length" value="8" min="6" max="20"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                </div>

                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div>
                            <label for="require_uppercase" class="text-sm font-medium text-gray-700">Uppercase Letters</label>
                            <p class="text-sm text-gray-500">Require at least one uppercase letter</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="require_uppercase" name="require_uppercase" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="require_lowercase" class="text-sm font-medium text-gray-700">Lowercase Letters</label>
                            <p class="text-sm text-gray-500">Require at least one lowercase letter</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="require_lowercase" name="require_lowercase" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="require_numbers" class="text-sm font-medium text-gray-700">Numbers</label>
                            <p class="text-sm text-gray-500">Require at least one number</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="require_numbers" name="require_numbers" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="require_special" class="text-sm font-medium text-gray-700">Special Characters</label>
                            <p class="text-sm text-gray-500">Require at least one special character</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="require_special" name="require_special" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="prevent_reuse" class="text-sm font-medium text-gray-700">Prevent Password Reuse</label>
                            <p class="text-sm text-gray-500">Remember last 5 passwords</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="prevent_reuse" name="prevent_reuse" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                        </label>
                    </div>
                </div>

                <div class="pt-4">
                    <button type="submit" class="w-full px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-lg hover:bg-green-700">
                        <i class="fas fa-save mr-2"></i>
                        Save Password Policy
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- User Roles & Permissions -->
    <div class="mt-6 bg-white rounded-lg shadow-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fas fa-users-cog mr-2 text-purple-600"></i>
            User Roles & Permissions
        </h3>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Super Admin Role -->
            <div class="border border-gray-200 rounded-lg p-4">
                <div class="flex items-center justify-between mb-3">
                    <h4 class="font-medium text-gray-900">Super Admin</h4>
                    <span class="px-2 py-1 text-xs font-medium text-red-800 bg-red-100 rounded-full">Full Access</span>
                </div>
                <p class="text-sm text-gray-600 mb-3">Complete system access and control</p>
                <div class="space-y-2">
                    <div class="flex items-center text-sm">
                        <i class="fas fa-check text-green-500 mr-2"></i>
                        <span>System Settings</span>
                    </div>
                    <div class="flex items-center text-sm">
                        <i class="fas fa-check text-green-500 mr-2"></i>
                        <span>User Management</span>
                    </div>
                    <div class="flex items-center text-sm">
                        <i class="fas fa-check text-green-500 mr-2"></i>
                        <span>Financial Reports</span>
                    </div>
                    <div class="flex items-center text-sm">
                        <i class="fas fa-check text-green-500 mr-2"></i>
                        <span>All Modules</span>
                    </div>
                </div>
                <button class="mt-3 w-full px-3 py-2 text-sm font-medium text-red-700 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100">
                    Edit Permissions
                </button>
            </div>

            <!-- Manager Role -->
            <div class="border border-gray-200 rounded-lg p-4">
                <div class="flex items-center justify-between mb-3">
                    <h4 class="font-medium text-gray-900">Manager</h4>
                    <span class="px-2 py-1 text-xs font-medium text-blue-800 bg-blue-100 rounded-full">Limited Access</span>
                </div>
                <p class="text-sm text-gray-600 mb-3">Branch management and operations</p>
                <div class="space-y-2">
                    <div class="flex items-center text-sm">
                        <i class="fas fa-check text-green-500 mr-2"></i>
                        <span>Branch Settings</span>
                    </div>
                    <div class="flex items-center text-sm">
                        <i class="fas fa-check text-green-500 mr-2"></i>
                        <span>Staff Management</span>
                    </div>
                    <div class="flex items-center text-sm">
                        <i class="fas fa-check text-green-500 mr-2"></i>
                        <span>Reports</span>
                    </div>
                    <div class="flex items-center text-sm">
                        <i class="fas fa-times text-red-500 mr-2"></i>
                        <span>System Settings</span>
                    </div>
                </div>
                <button class="mt-3 w-full px-3 py-2 text-sm font-medium text-blue-700 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100">
                    Edit Permissions
                </button>
            </div>

            <!-- Staff Role -->
            <div class="border border-gray-200 rounded-lg p-4">
                <div class="flex items-center justify-between mb-3">
                    <h4 class="font-medium text-gray-900">Staff</h4>
                    <span class="px-2 py-1 text-xs font-medium text-green-800 bg-green-100 rounded-full">Basic Access</span>
                </div>
                <p class="text-sm text-gray-600 mb-3">Daily operations and POS access</p>
                <div class="space-y-2">
                    <div class="flex items-center text-sm">
                        <i class="fas fa-check text-green-500 mr-2"></i>
                        <span>POS System</span>
                    </div>
                    <div class="flex items-center text-sm">
                        <i class="fas fa-check text-green-500 mr-2"></i>
                        <span>Order Management</span>
                    </div>
                    <div class="flex items-center text-sm">
                        <i class="fas fa-times text-red-500 mr-2"></i>
                        <span>Settings</span>
                    </div>
                    <div class="flex items-center text-sm">
                        <i class="fas fa-times text-red-500 mr-2"></i>
                        <span>Reports</span>
                    </div>
                </div>
                <button class="mt-3 w-full px-3 py-2 text-sm font-medium text-green-700 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100">
                    Edit Permissions
                </button>
            </div>
        </div>

        <div class="mt-6 flex justify-end">
            <button class="px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-lg hover:bg-purple-700">
                <i class="fas fa-plus mr-2"></i>
                Create New Role
            </button>
        </div>
    </div>

    <!-- Security Logs -->
    <div class="mt-6 bg-white rounded-lg shadow-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fas fa-history mr-2 text-orange-600"></i>
            Recent Security Events
        </h3>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP Address</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Login Attempt</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><EMAIL></td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">*************</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2 minutes ago</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Success</span>
                        </td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Password Change</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><EMAIL></td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">192.168.1.105</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1 hour ago</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Success</span>
                        </td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Failed Login</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><EMAIL></td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">203.0.113.1</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">3 hours ago</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Failed</span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="mt-4 flex justify-end">
            <button class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                View All Logs
            </button>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Form submission handlers
    $('form').on('submit', function(e) {
        e.preventDefault();
        
        Swal.fire({
            icon: 'success',
            title: 'Success!',
            text: 'Security settings updated successfully.',
            timer: 2000,
            showConfirmButton: false
        });
    });

    // Role permission edit handlers
    $('button:contains("Edit Permissions")').on('click', function() {
        Swal.fire({
            title: 'Edit Role Permissions',
            text: 'This feature will open the role permission editor.',
            icon: 'info',
            confirmButtonText: 'OK'
        });
    });
});
</script>
@endpush