<?php

namespace App\Helpers;

class ResponseHelper
{
    /**
     * Return a success JSON response
     */
    public static function success($data = null, string $message = 'Success', int $status = 200): \Illuminate\Http\JsonResponse
    {
        $response = [
            'success' => true,
            'message' => $message,
        ];

        if ($data !== null) {
            $response['data'] = $data;
        }

        return response()->json($response, $status);
    }

    /**
     * Return an error JSON response
     */
    public static function error(string $message = 'Error', int $status = 400, $errors = null): \Illuminate\Http\JsonResponse
    {
        $response = [
            'success' => false,
            'message' => $message,
        ];

        if ($errors !== null) {
            $response['errors'] = $errors;
        }

        return response()->json($response, $status);
    }

    /**
     * Return a validation error response
     */
    public static function validationError($errors, string $message = 'Validation failed'): \Illuminate\Http\JsonResponse
    {
        return self::error($message, 422, $errors);
    }

    /**
     * Return a not found error response
     */
    public static function notFound(string $message = 'Resource not found'): \Illuminate\Http\JsonResponse
    {
        return self::error($message, 404);
    }

    /**
     * Return an unauthorized error response
     */
    public static function unauthorized(string $message = 'Unauthorized'): \Illuminate\Http\JsonResponse
    {
        return self::error($message, 401);
    }

    /**
     * Return a forbidden error response
     */
    public static function forbidden(string $message = 'Forbidden'): \Illuminate\Http\JsonResponse
    {
        return self::error($message, 403);
    }
}