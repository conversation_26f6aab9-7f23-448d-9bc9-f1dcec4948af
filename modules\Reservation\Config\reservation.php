<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default Reservation Duration
    |--------------------------------------------------------------------------
    |
    | The default duration for reservations in minutes.
    |
    */
    'default_duration' => env('RESERVATION_DEFAULT_DURATION', 120),

    /*
    |--------------------------------------------------------------------------
    | Advance Booking Limit
    |--------------------------------------------------------------------------
    |
    | Maximum days in advance that customers can make reservations.
    |
    */
    'advance_booking_days' => env('RESERVATION_ADVANCE_BOOKING_DAYS', 30),

    /*
    |--------------------------------------------------------------------------
    | Minimum Advance Notice
    |--------------------------------------------------------------------------
    |
    | Minimum hours in advance required for making a reservation.
    |
    */
    'minimum_advance_hours' => env('RESERVATION_MINIMUM_ADVANCE_HOURS', 2),

    /*
    |--------------------------------------------------------------------------
    | Auto-Cancel Time
    |--------------------------------------------------------------------------
    |
    | Minutes after reservation time to automatically cancel no-show reservations.
    |
    */
    'auto_cancel_minutes' => env('RESERVATION_AUTO_CANCEL_MINUTES', 15),

    /*
    |--------------------------------------------------------------------------
    | QR Code Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for QR code generation.
    |
    */
    'qr_code' => [
        'size' => env('QR_CODE_SIZE', 200),
        'format' => env('QR_CODE_FORMAT', 'png'),
        'error_correction' => env('QR_CODE_ERROR_CORRECTION', 'M'),
        'margin' => env('QR_CODE_MARGIN', 2),
    ],

    /*
    |--------------------------------------------------------------------------
    | Availability Buffer
    |--------------------------------------------------------------------------
    |
    | Buffer time in minutes between reservations for table turnover.
    |
    */
    'table_turnover_buffer' => env('RESERVATION_TURNOVER_BUFFER', 30),

    /*
    |--------------------------------------------------------------------------
    | Status Transitions
    |--------------------------------------------------------------------------
    |
    | Define which status transitions are allowed.
    |
    */
    'allowed_transitions' => [
        'pending' => ['confirmed', 'cancelled'],
        'confirmed' => ['seated', 'cancelled', 'no_show'],
        'seated' => ['completed', 'cancelled'],
        'completed' => [],
        'cancelled' => [],
        'no_show' => [],
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Settings
    |--------------------------------------------------------------------------
    |
    | Settings for reservation notifications.
    |
    */
    'notifications' => [
        'confirmation_enabled' => env('RESERVATION_CONFIRMATION_ENABLED', true),
        'reminder_enabled' => env('RESERVATION_REMINDER_ENABLED', true),
        'reminder_hours_before' => env('RESERVATION_REMINDER_HOURS', 2),
    ],

    /*
    |--------------------------------------------------------------------------
    | Business Hours
    |--------------------------------------------------------------------------
    |
    | Default business hours for reservations.
    |
    */
    'business_hours' => [
        'monday' => ['11:00', '22:00'],
        'tuesday' => ['11:00', '22:00'],
        'wednesday' => ['11:00', '22:00'],
        'thursday' => ['11:00', '22:00'],
        'friday' => ['11:00', '23:00'],
        'saturday' => ['10:00', '23:00'],
        'sunday' => ['10:00', '21:00'],
    ],
];