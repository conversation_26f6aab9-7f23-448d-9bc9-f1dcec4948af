<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Customer;
use App\Models\Tenant;

class CustomerSeeder extends Seeder
{
    public function run()
    {
   
            Customer::create([
                'first_name' => '<PERSON>',
                'last_name' => 'Doe',
                'email' => '<EMAIL>',
                'tenant_id' => \App\Models\Tenant::first()->id,
                // Add other required fields here
            ]);
        
    }
}