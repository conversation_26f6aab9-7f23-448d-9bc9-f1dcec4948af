<?php

namespace Modules\Menu\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * Store Event Request
 * 
 * Handles validation for creating new events in the restaurant system.
 * Validates event information, scheduling, pricing, participant limits,
 * and special requirements.
 * 
 * @package Modules\Menu\Http\Requests
 * <AUTHOR> POS Team
 * @version 1.0.0
 */
class StoreEventRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'tenant_id' => 'required|integer|exists:tenants,id',
            'branch_id' => 'required|integer|exists:branches,id',
            'name' => 'required|string|max:255',
            'code' => 'nullable|string|max:50|unique:events,code',
            'description' => 'nullable|string',
            'short_description' => 'nullable|string|max:500',
            'event_type' => 'required|string|in:dining,entertainment,special_occasion,workshop,tasting,private_party,corporate,seasonal,holiday,promotional',
            'image_urls' => 'nullable|array',
            'image_urls.*' => 'string|url',
            'banner_image' => 'nullable|string|url',
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'required|date|after_or_equal:start_date',
            'start_time' => 'nullable|date_format:H:i',
            'end_time' => 'nullable|date_format:H:i|after:start_time',
            'available_days' => 'nullable|array',
            'available_days.*' => 'integer|between:0,6',
            'location' => 'nullable|string|max:255',
            'price' => 'nullable|numeric|min:0',
            'max_participants' => 'nullable|integer|min:1',
            'requires_reservation' => 'boolean',
            'menu_items' => 'nullable|array',
            'menu_items.*' => 'integer|exists:menu_items,id',
            'special_menu' => 'nullable|array',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'discount_amount' => 'nullable|numeric|min:0',
            'discount_type' => 'nullable|string|in:percentage,fixed_amount,none',
            'terms_conditions' => 'nullable|array',
            'contact_info' => 'nullable|array',
            'booking_url' => 'nullable|string|url',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'is_recurring' => 'boolean',
            'recurrence_pattern' => 'nullable|array',
            'sort_order' => 'nullable|integer|min:0',
            'metadata' => 'nullable|array',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'tenant_id.required' => 'Tenant ID is required.',
            'tenant_id.exists' => 'Selected tenant does not exist.',
            'branch_id.required' => 'Branch ID is required.',
            'branch_id.exists' => 'Selected branch does not exist.',
            'name.required' => 'Event name is required.',
            'name.max' => 'Event name cannot exceed 255 characters.',
            'code.unique' => 'Event code must be unique.',
            'event_type.required' => 'Event type is required.',
            'event_type.in' => 'Invalid event type selected.',
            'start_date.required' => 'Start date is required.',
            'start_date.after_or_equal' => 'Start date cannot be in the past.',
            'end_date.required' => 'End date is required.',
            'end_date.after_or_equal' => 'End date must be after or equal to start date.',
            'start_time.date_format' => 'Start time must be in HH:MM format.',
            'end_time.date_format' => 'End time must be in HH:MM format.',
            'end_time.after' => 'End time must be after start time.',
            'available_days.*.between' => 'Available days must be between 0 (Sunday) and 6 (Saturday).',
            'price.numeric' => 'Price must be a valid number.',
            'price.min' => 'Price cannot be negative.',
            'max_participants.integer' => 'Maximum participants must be a valid number.',
            'max_participants.min' => 'Maximum participants must be at least 1.',
            'menu_items.*.exists' => 'Selected menu item does not exist.',
            'discount_percentage.numeric' => 'Discount percentage must be a valid number.',
            'discount_percentage.max' => 'Discount percentage cannot exceed 100%.',
            'discount_amount.numeric' => 'Discount amount must be a valid number.',
            'discount_amount.min' => 'Discount amount cannot be negative.',
            'discount_type.in' => 'Invalid discount type selected.',
            'booking_url.url' => 'Booking URL must be a valid URL.',
            'sort_order.min' => 'Sort order cannot be negative.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'tenant_id' => 'tenant',
            'branch_id' => 'branch',
            'event_type' => 'event type',
            'start_date' => 'start date',
            'end_date' => 'end date',
            'start_time' => 'start time',
            'end_time' => 'end time',
            'available_days' => 'available days',
            'max_participants' => 'maximum participants',
            'requires_reservation' => 'requires reservation',
            'menu_items' => 'menu items',
            'special_menu' => 'special menu',
            'discount_percentage' => 'discount percentage',
            'discount_amount' => 'discount amount',
            'discount_type' => 'discount type',
            'terms_conditions' => 'terms and conditions',
            'contact_info' => 'contact information',
            'booking_url' => 'booking URL',
            'is_featured' => 'featured status',
            'is_active' => 'active status',
            'is_recurring' => 'recurring status',
            'recurrence_pattern' => 'recurrence pattern',
            'sort_order' => 'sort order',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'is_featured' => $this->boolean('is_featured'),
            'is_active' => $this->boolean('is_active'),
            'is_recurring' => $this->boolean('is_recurring'),
            'requires_reservation' => $this->boolean('requires_reservation'),
        ]);
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Validate discount type consistency
            if ($this->discount_type === 'percentage' && !$this->discount_percentage) {
                $validator->errors()->add('discount_percentage', 'Discount percentage is required when discount type is percentage.');
            }

            if ($this->discount_type === 'fixed_amount' && !$this->discount_amount) {
                $validator->errors()->add('discount_amount', 'Discount amount is required when discount type is fixed amount.');
            }

            // Validate time consistency
            if ($this->start_time && $this->end_time) {
                $startTime = \Carbon\Carbon::createFromFormat('H:i', $this->start_time);
                $endTime = \Carbon\Carbon::createFromFormat('H:i', $this->end_time);
                
                if ($endTime->lte($startTime)) {
                    $validator->errors()->add('end_time', 'End time must be after start time.');
                }
            }

            // Validate recurring pattern
            if ($this->is_recurring && empty($this->recurrence_pattern)) {
                $validator->errors()->add('recurrence_pattern', 'Recurrence pattern is required for recurring events.');
            }

            // Validate reservation requirements
            if ($this->requires_reservation && !$this->max_participants) {
                $validator->errors()->add('max_participants', 'Maximum participants is required when reservations are required.');
            }
        });
    }
}