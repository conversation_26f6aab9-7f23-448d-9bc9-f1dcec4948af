<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('tenants')->onDelete('cascade');
            $table->string('name');
            $table->string('code', 50);
            $table->text('description')->nullable();
            $table->unsignedBigInteger('category_id')->nullable();
            $table->string('category', 100)->nullable()->comment('Raw material, packaging, etc.');
            $table->foreignId('unit_id')->nullable()->constrained('units');
            $table->string('barcode', 100)->nullable();
            $table->decimal('minimum_stock_level', 15, 3)->default(0);
            $table->decimal('maximum_stock_level', 15, 3)->nullable();
            $table->decimal('reorder_point', 15, 3)->nullable();
            $table->decimal('standard_cost', 10, 2)->nullable();
            $table->integer('shelf_life_days')->nullable()->comment('Expiry tracking');
            $table->text('storage_requirements')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->unique(['tenant_id', 'code']);
            
            // Foreign key constraints
            $table->foreign('category_id')->references('id')->on('inventory_categories')->onDelete('set null');
            
            // Performance indexes
            $table->index(['tenant_id', 'is_active']);
            $table->index(['category_id']);
            $table->index(['category', 'is_active']);
            $table->index(['barcode']);
            $table->index(['unit_id']);
            $table->index(['minimum_stock_level', 'reorder_point']);
            $table->index(['name']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};