# Authentication Module

This module provides a comprehensive authentication and authorization system using Laravel Sanctum for token-based authentication, including user registration, login, password management, role-based access control (RBAC), and permission management.

## Features

-   **Token-Based Authentication**: Secure API authentication using Laravel Sanctum tokens.
-   **User Management**: Register, login, logout, update profile, change password.
-   **Password Management**: Forgot password, reset password.
-   **Token Management**: Refresh access tokens, logout from all devices.
-   **Email Verification**: (Planned) User email verification.
-   **Role-Based Access Control (RBAC)**: Assign roles to users, manage roles (CRUD), assign permissions to roles.
-   **Permission Management**: Manage permissions (CRUD), assign permissions to users.
-   **Security Logging**: (Planned) Log security-related events.

## Authentication Method

This module exclusively uses **Laravel Sanctum** for API token authentication. All authentication endpoints return Bearer tokens that must be included in the `Authorization` header for protected routes.

**Authentication Header Format:**
```
Authorization: Bearer {your-token-here}
```

## Directory Structure

```
modules/Auth/
├── Http/
│   ├── Controllers/          # API controllers for authentication and authorization
│   │   ├── AuthController.php
│   │   ├── PermissionController.php
│   │   └── RoleController.php
│   ├── Middleware/           # Custom middleware (e.g., for roles/permissions)
│   ├── Requests/             # Form requests for validation
│   │   ├── ChangePasswordRequest.php
│   │   ├── ForgotPasswordRequest.php
│   │   ├── LoginRequest.php
│   │   ├── RefreshTokenRequest.php
│   │   ├── RegisterRequest.php
│   │   └── ResetPasswordRequest.php
│   └── Resources/            # API resources for data transformation
│       ├── AuthResource.php
│       └── UserResource.php
├── Providers/                # Service providers for module registration
│   └── AuthServiceProvider.php
├── Services/                 # Business logic for authentication and authorization
│   └── AuthService.php
├── Helpers/                  # Helper functions for common auth tasks
│   └── AuthHelper.php
├── docs/                     # Module-specific documentation
├── resources/                # Frontend resources (views, assets - if any)
│   └── views/
└── routes/                   # API and web routes
    ├── api.php
    └── web.php
```

## API Endpoints

All API endpoints are prefixed with `/api`.

### Public Routes (`/api/auth`)

-   `POST /api/auth/register`
    -   Description: Register a new user.
    -   Request Body: `RegisterRequest` fields (`name`, `email`, `password`, `password_confirmation`, `role`, `terms_accepted`).
-   `POST /api/auth/login`
    -   Description: Authenticate user and get access token.
    -   Request Body: `LoginRequest` fields (`email`, `password`, `remember_me`).
-   `POST /api/auth/forgot-password`
    -   Description: Send password reset link to user's email.
    -   Request Body: `ForgotPasswordRequest` fields (`email`).
-   `POST /api/auth/reset-password`
    -   Description: Reset user's password using a token.
    -   Request Body: `ResetPasswordRequest` fields (`token`, `email`, `password`, `password_confirmation`).

### Protected Routes (`/api/auth` - require `auth:sanctum` middleware)

-   `POST /api/auth/logout`
    -   Description: Logout current session (revoke current token).
-   `POST /api/auth/logout-all`
    -   Description: Logout from all devices (revoke all tokens).
-   `POST /api/auth/refresh-token`
    -   Description: Refresh access token.
-   `GET /api/auth/profile`
    -   Description: Get authenticated user's profile.
-   `PUT /api/auth/profile`
    -   Description: Update authenticated user's profile.
    -   Request Body: `name`, `email`, `current_password`, `password`, `password_confirmation`.
-   `POST /api/auth/change-password`
    -   Description: Change authenticated user's password.
    -   Request Body: `ChangePasswordRequest` fields (`current_password`, `password`, `password_confirmation`).
-   `POST /api/auth/verify-email`
    -   Description: Verify user's email address.
-   `POST /api/auth/resend-verification`
    -   Description: Resend email verification link.

### Admin Routes (`/api/auth/admin` - require `auth:sanctum` and `role:admin` middleware)

#### Role Management (`/api/auth/admin/roles`)

-   `GET /api/auth/admin/roles`
    -   Description: Get a paginated list of roles.
    -   Query Parameters: `search`, `per_page`.
-   `POST /api/auth/admin/roles`
    -   Description: Create a new role.
    -   Request Body: `name`, `permissions` (array of permission names).
-   `GET /api/auth/admin/roles/{role}`
    -   Description: Get details of a specific role.
-   `PUT /api/auth/admin/roles/{role}`
    -   Description: Update an existing role.
    -   Request Body: `name`, `permissions` (array of permission names).
-   `DELETE /api/auth/admin/roles/{role}`
    -   Description: Delete a role.
-   `POST /api/auth/admin/roles/{role}/permissions`
    -   Description: Assign permissions to a role.
    -   Request Body: `permissions` (array of permission names).
-   `DELETE /api/auth/admin/roles/{role}/permissions`
    -   Description: Revoke permissions from a role.
    -   Request Body: `permissions` (array of permission names).

#### Permission Management (`/api/auth/admin/permissions`)

-   `GET /api/auth/admin/permissions`
    -   Description: Get a paginated list of permissions.
    -   Query Parameters: `search`, `group`, `per_page`.
-   `POST /api/auth/admin/permissions`
    -   Description: Create a new permission.
    -   Request Body: `name`, `guard_name`.
-   `GET /api/auth/admin/permissions/{permission}`
    -   Description: Get details of a specific permission.
-   `PUT /api/auth/admin/permissions/{permission}`
    -   Description: Update an existing permission.
    -   Request Body: `name`, `guard_name`.
-   `DELETE /api/auth/admin/permissions/{permission}`
    -   Description: Delete a permission.
-   `GET /api/auth/admin/permissions/grouped`
    -   Description: Get permissions grouped by module/category.
-   `POST /api/auth/admin/permissions/bulk`
    -   Description: Bulk create permissions.
    -   Request Body: `permissions` (array of objects with `name` and `guard_name`).

#### User Management (`/api/auth/admin/users`)

-   `GET /api/auth/admin/users`
    -   Description: Get a list of users.
-   `POST /api/auth/admin/users/{user}/roles`
    -   Description: Assign roles to a user.
-   `DELETE /api/auth/admin/users/{user}/roles`
    -   Description: Revoke roles from a user.
-   `POST /api/auth/admin/users/{user}/permissions`
    -   Description: Assign permissions to a user.
-   `DELETE /api/auth/admin/users/{user}/permissions`
    -   Description: Revoke permissions from a user.

## Configuration

This module relies on Laravel's built-in authentication configuration (`config/auth.php`) and the `spatie/laravel-permission` package configuration (`config/permission.php`).

## Usage

To use this module, ensure it is registered in your application's `config/app.php` (if not auto-discovered) and run migrations to create necessary tables (users, roles, permissions, etc.).

Example of using the `AuthService`:

```php
use Modules\Auth\Services\AuthService;
use Illuminate\Http\Request;

class SomeController
{
    protected AuthService $authService;

    public function __construct(AuthService $authService)
    {
        $this->authService = $authService;
    }

    public function registerUser(Request $request)
    {
        $data = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:8|confirmed',
            'terms_accepted' => 'required|accepted',
        ]);

        $result = $this->authService->register($data);

        return response()->json($result, 201);
    }

    public function loginUser(Request $request)
    {
        $credentials = $request->validate([
            'email' => 'required|email',
            'password' => 'required|string',
        ]);

        $result = $this->authService->login($credentials);

        if (!$result) {
            return response()->json(['message' => 'Invalid credentials'], 401);
        }

        return response()->json($result);
    }

    public function getUserProfile(Request $request)
    {
        $user = $this->authService->getUserProfile($request->user());

        return response()->json($user);
    }
}
```

Example of using the `AuthHelper`:

```php
use Modules\Auth\Helpers\AuthHelper;

class SomeOtherClass
{
    public function checkUserRole()
    {
        if (AuthHelper::hasRole('admin')) {
            // User is an admin
        } else {
            // User is not an admin
        }

        if (AuthHelper::can('view-reports')) {
            // User has permission to view reports
        }
    }
}
```