@extends('layouts.master')

@section('title', 'المخزون المنخفض')

@section('breadcrumb')
<div class="bg-white rounded-lg shadow-sm border border-gray-200 px-6 py-4 mb-6">
    <nav class="flex" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3 rtl:space-x-reverse">
            <li class="inline-flex items-center">
                <a href="{{ route('dashboard') }}" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                    <i class="fas fa-home ml-2"></i>
                    الرئيسية
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <i class="fas fa-chevron-left text-gray-400 mx-2"></i>
                    <a href="{{ route('inventory.dashboard') }}" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">المخزون</a>
                </div>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <i class="fas fa-chevron-left text-gray-400 mx-2"></i>
                    <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">المخزون المنخفض</span>
                </div>
            </li>
        </ol>
    </nav>
</div>

@endsection

@section('content')
<div class="space-y-6">
    <!-- Page Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 flex items-center gap-3">
                        <div class="w-10 h-10 bg-gradient-to-r from-red-600 to-red-700 rounded-lg flex items-center justify-center">
                            <i class="fas fa-exclamation-triangle text-white"></i>
                        </div>
                        المخزون المنخفض
                    </h1>
                    <p class="mt-1 text-sm text-gray-600">مراقبة المواد التي تحتاج إلى إعادة تموين</p>
                </div>
                <div class="flex gap-2">
                    <button type="button" onclick="bulkReorder()" class="px-4 py-2 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center gap-2">
                        <i class="fas fa-shopping-cart"></i>
                        طلب إعادة تموين
                    </button>
                    <button type="button" onclick="exportLowStock()" class="px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2">
                        <i class="fas fa-file-export"></i>
                        تصدير التقرير
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Low Stock Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Out of Stock Card -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-times-circle text-red-600 text-xl"></i>
                    </div>
                </div>
                <div class="mr-4 flex-1 text-right">
                    <p class="text-sm font-medium text-gray-600">مخزون نافد</p>
                    <p class="text-2xl font-bold text-red-600">{{ $lowStockItems->where('current_stock', '<=', 0)->count() }}</p>
                </div>
            </div>
        </div>

        <!-- Low Stock Card -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-exclamation-circle text-yellow-600 text-xl"></i>
                    </div>
                </div>
                <div class="mr-4 flex-1 text-right">
                    <p class="text-sm font-medium text-gray-600">مخزون منخفض</p>
                    <p class="text-2xl font-bold text-yellow-600">{{ $lowStockItems->where('current_stock', '>', 0)->count() }}</p>
                </div>
            </div>
        </div>
        <!-- Total Items Card -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-boxes text-blue-600 text-xl"></i>
                    </div>
                </div>
                <div class="mr-4 flex-1 text-right">
                    <p class="text-sm font-medium text-gray-600">إجمالي المواد</p>
                    <p class="text-2xl font-bold text-blue-600">{{ $lowStockItems->count() }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Low Stock Items Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <div class="flex justify-between items-center">
                <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                    <i class="fas fa-list text-red-600"></i>
                    المواد ذات المخزون المنخفض
                </h2>
                <div class="flex gap-2">
                    <button type="button" onclick="refreshTable()" class="px-3 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                    <button type="button" onclick="bulkReorder()" class="px-3 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center gap-2">
                        <i class="fas fa-shopping-cart"></i>
                        طلب تموين
                    </button>
                </div>
            </div>
        </div>

        <div class="p-6">
            @if($lowStockItems && $lowStockItems->count() > 0)
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200" id="lowStockTable">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" id="select-all" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المادة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الكود</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الوحدة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المخزون الحالي</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحد الأدنى</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">آخر حركة</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($lowStockItems as $item)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" name="selected_items[]" value="{{ $item->id }}" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 item-checkbox">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">{{ $item->product->name }}</div>
                                    <div class="text-sm text-gray-500">{{ $item->product->category ?? 'عام' }}</div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    {{ $item->product->sku ?? 'N/A' }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $item->product->unit->name ?? 'وحدة' }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm font-bold {{ $item->current_stock <= 0 ? 'text-red-600' : 'text-yellow-600' }}">
                                    {{ $item->current_stock }} {{ $item->product->unit->symbol ?? '' }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $item->minimum_level }} {{ $item->product->unit->symbol ?? '' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($item->current_stock <= 0)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">نافد</span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">منخفض</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $item->last_updated ? $item->last_updated->format('Y-m-d') : 'غير محدد' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex gap-1">
                                    <button type="button" class="px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700" onclick="updateStock({{ $item->id }})" title="تحديث المخزون">
                                        <i class="fas fa-cube"></i>
                                    </button>
                                    <button type="button" class="px-2 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700" onclick="createPurchaseOrder({{ $item->id }})" title="طلب شراء">
                                        <i class="fas fa-shopping-cart"></i>
                                    </button>
                                    <button type="button" class="px-2 py-1 bg-gray-600 text-white text-xs rounded hover:bg-gray-700" onclick="viewHistory({{ $item->id }})" title="التاريخ">
                                        <i class="fas fa-history"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            @else
            <div class="text-center py-12">
                <div class="mb-4">
                    <i class="fas fa-check-circle text-green-500 text-6xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد مواد ذات مخزون منخفض</h3>
                <p class="text-gray-500 mb-4">جميع المواد في المخزون ضمن المستوى الطبيعي</p>
                <a href="{{ route('inventory.stock.index') }}" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200">
                    <i class="fas fa-cube mr-2"></i>
                    عرض جميع المواد
                </a>
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Stock Update Modal -->
<div id="stockUpdateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center pb-3">
            <h3 class="text-lg font-bold text-gray-900">تحديث المخزون</h3>
            <button type="button" onclick="closeModal('stockUpdateModal')" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <form id="stockUpdateForm">
            <div class="space-y-4">
                <input type="hidden" id="update_item_id">

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">نوع العملية</label>
                    <select id="update_type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        <option value="in">إضافة مخزون</option>
                        <option value="out">خصم مخزون</option>
                        <option value="adjustment">تعديل المخزون</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">الكمية</label>
                    <input type="number" id="update_quantity" min="1" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">السبب</label>
                    <input type="text" id="update_reason" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">ملاحظات</label>
                    <textarea id="update_notes" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                </div>
            </div>

            <div class="flex justify-end gap-2 mt-6">
                <button type="button" onclick="closeModal('stockUpdateModal')" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors duration-200">
                    إلغاء
                </button>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200">
                    تحديث المخزون
                </button>
            </div>
        </form>
    </div>
</div>

@endsection

@push('styles')
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
<style>
    /* Custom DataTable styling for Tailwind */
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_processing,
    .dataTables_wrapper .dataTables_paginate {
        color: #374151;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.5rem 0.75rem;
        margin: 0 0.125rem;
        border-radius: 0.375rem;
        border: 1px solid #d1d5db;
        background: white;
        color: #374151;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background: #f3f4f6;
        border-color: #9ca3af;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background: #dc2626;
        border-color: #dc2626;
        color: white;
    }
</style>
@endpush

@push('scripts')
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>

<!-- Modal Functions -->
<script>
function openModal(modalId) {
    document.getElementById(modalId).classList.remove('hidden');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

$(document).ready(function() {
    // Initialize DataTable
    $('#lowStockTable').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json'
        },
        responsive: true,
        order: [[4, 'asc']], // Order by current stock ascending
        columnDefs: [
            { orderable: false, targets: [0, 8] } // Disable ordering for checkbox and actions
        ],
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]]
    });

    // Select all checkbox functionality
    $('#select-all').change(function() {
        $('.item-checkbox').prop('checked', this.checked);
    });

    // Stock update form submission
    $('#stockUpdateForm').submit(function(e) {
        e.preventDefault();

        const itemId = $('#update_item_id').val();
        const formData = {
            type: $('#update_type').val(),
            quantity: $('#update_quantity').val(),
            reason: $('#update_reason').val(),
            notes: $('#update_notes').val(),
            _token: '{{ csrf_token() }}'
        };

        $.ajax({
            url: `/inventory/stock/${itemId}/update`,
            method: 'POST',
            data: formData,
            success: function(response) {
                closeModal('stockUpdateModal');
                showToast('تم تحديث المخزون بنجاح', 'success');
                location.reload(); // Refresh the page to show updated data
            },
            error: function(xhr) {
                showToast('حدث خطأ أثناء تحديث المخزون', 'error');
            }
        });
    });
});

function updateStock(itemId) {
    $('#update_item_id').val(itemId);
    openModal('stockUpdateModal');
}

function createPurchaseOrder(itemId) {
    // Redirect to purchase order creation with pre-filled item
    window.location.href = `/inventory/purchase-orders/create?item_id=${itemId}`;
}

function viewHistory(itemId) {
    // Redirect to item history page
    window.location.href = `/inventory/items/${itemId}/history`;
}

function bulkReorder() {
    const selectedItems = $('.item-checkbox:checked').map(function() {
        return this.value;
    }).get();

    if (selectedItems.length === 0) {
        showToast('يرجى اختيار مادة واحدة على الأقل', 'warning');
        return;
    }

    // Redirect to bulk purchase order creation
    const itemIds = selectedItems.join(',');
    window.location.href = `/inventory/purchase-orders/create?item_ids=${itemIds}`;
}

function exportLowStock() {
    window.location.href = '{{ route("inventory.stock.low-stock") }}?export=csv';
}

function refreshTable() {
    location.reload();
}

// Toast notification function
window.showToast = function(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg text-white font-medium ${
        type === 'success' ? 'bg-green-600' :
        type === 'error' ? 'bg-red-600' :
        type === 'warning' ? 'bg-yellow-600' : 'bg-blue-600'
    }`;
    toast.textContent = message;
    document.body.appendChild(toast);

    setTimeout(() => {
        toast.remove();
    }, 3000);
};
</script>
@endpush