<?php

namespace Modules\Menu\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Modules\Menu\Services\OfferService;
use Modules\Menu\Http\Requests\StoreOfferRequest;
use Modules\Menu\Http\Requests\UpdateOfferRequest;
use App\Helpers\BranchHelper;
use App\Helpers\ResponseHelper;

/**
 * Offer API Controller
 * 
 * Handles API requests for offer management in the restaurant system.
 * Provides endpoints for CRUD operations, offer application, and validation.
 * 
 * @package Modules\Menu\Http\Controllers\Api
 * <AUTHOR> POS Team
 * @version 1.0.0
 */
class OfferController extends Controller
{
    protected OfferService $offerService;

    public function __construct(OfferService $offerService)
    {
        $this->offerService = $offerService;
    }

    /**
     * Display a listing of offers
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            
            $filters = $request->only([
                'offer_type', 'discount_type', 'is_active', 'is_featured',
                'valid_from', 'valid_until', 'search', 'sort_by', 'sort_order'
            ]);

            $perPage = $request->get('per_page', 15);
            $offers = $this->offerService->getOffersForBranch($branchId, $filters, $perPage);

            return ResponseHelper::success('Offers retrieved successfully', $offers);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve offers: ' . $e->getMessage());
        }
    }

    /**
     * Store a newly created offer
     *
     * @param StoreOfferRequest $request
     * @return JsonResponse
     */
    public function store(StoreOfferRequest $request): JsonResponse
    {
        try {
            $data = BranchHelper::addBranchAndTenantIds($request->validated());
            $offer = $this->offerService->createOffer($data);

            return ResponseHelper::success('Offer created successfully', $offer, 201);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to create offer: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified offer
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $offer = $this->offerService->getOfferByIdForBranch($id, $branchId);

            if (!$offer) {
                return ResponseHelper::notFound('Offer not found');
            }

            return ResponseHelper::success('Offer retrieved successfully', $offer);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve offer: ' . $e->getMessage());
        }
    }

    /**
     * Update the specified offer
     *
     * @param UpdateOfferRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(UpdateOfferRequest $request, int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $offer = $this->offerService->updateOfferForBranch($id, $request->validated(), $branchId);

            if (!$offer) {
                return ResponseHelper::notFound('Offer not found');
            }

            return ResponseHelper::success('Offer updated successfully', $offer);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to update offer: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified offer
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $deleted = $this->offerService->deleteOfferForBranch($id, $branchId);

            if (!$deleted) {
                return ResponseHelper::notFound('Offer not found');
            }

            return ResponseHelper::success('Offer deleted successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to delete offer: ' . $e->getMessage());
        }
    }

    /**
     * Get featured offers
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function featured(Request $request): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $limit = $request->get('limit', 10);
            
            $offers = $this->offerService->getFeaturedOffersForBranch($branchId, $limit);

            return ResponseHelper::success('Featured offers retrieved successfully', $offers);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve featured offers: ' . $e->getMessage());
        }
    }

    /**
     * Validate an offer code
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function validate(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'offer_code' => 'required|string',
                'order_total' => 'required|numeric|min:0',
                'customer_id' => 'nullable|integer',
                'menu_items' => 'nullable|array'
            ]);

            $branchId = BranchHelper::getCurrentBranchId();
            $validation = $this->offerService->validateOfferCode(
                $request->get('offer_code'),
                $request->get('order_total'),
                $branchId,
                $request->get('customer_id'),
                $request->get('menu_items', [])
            );

            if ($validation['valid']) {
                return ResponseHelper::success($validation, 'Offer code is valid');
            } else {
                return ResponseHelper::error($validation['message'], null, 422);
            }
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to validate offer: ' . $e->getMessage());
        }
    }

    /**
     * Apply an offer to calculate discount
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function apply(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'offer_id' => 'required|integer',
                'order_total' => 'required|numeric|min:0',
                'menu_items' => 'nullable|array'
            ]);

            $branchId = BranchHelper::getCurrentBranchId();
            $discount = $this->offerService->calculateDiscount(
                $request->get('offer_id'),
                $request->get('order_total'),
                $branchId,
                $request->get('menu_items', [])
            );

            return ResponseHelper::success('Discount calculated successfully', $discount);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to apply offer: ' . $e->getMessage());
        }
    }

    /**
     * Get active offers for current time
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function active(Request $request): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $limit = $request->get('limit', 20);
            
            $offers = $this->offerService->getActiveOffersForBranch($branchId, $limit);

            return ResponseHelper::success('Active offers retrieved successfully', $offers);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve active offers: ' . $e->getMessage());
        }
    }

    /**
     * Get featured offers for public access (no authentication required)
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function publicFeatured(Request $request): JsonResponse
    {
        try {
            $limit = $request->get('limit', 10);
            $branchId = $request->get('branch_id'); // Optional branch filter for public access
            
            // Use the general getFeaturedOffers method which doesn't require specific branch
            $offers = $this->offerService->getFeaturedOffers($branchId, $limit);

            return ResponseHelper::success($offers, 'Featured offers retrieved successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve featured offers: ' . $e->getMessage());
        }
    }
}