# وحدة التقارير - Reports Module

وحدة التقارير هي نظام شامل لإنتاج وإدارة التقارير المختلفة في نظام نقاط البيع للمطاعم. توفر هذه الوحدة تقارير مفصلة ومتنوعة لمساعدة أصحاب المطاعم في اتخاذ قرارات مدروسة.

## 📊 الميزات الرئيسية

### 🔧 الوظائف الأساسية
- **إنتاج التقارير التلقائي**: إنشاء تقارير مجدولة ومخصصة
- **تصدير متعدد الصيغ**: PDF, Excel, CSV
- **تقارير في الوقت الفعلي**: بيانات محدثة لحظياً
- **تقارير مخصصة**: إمكانية تخصيص التقارير حسب الحاجة
- **أرشفة التقارير**: حفظ وإدارة التقارير السابقة
- **تقارير متعددة الفروع**: دعم المطاعم متعددة الفروع

## 📋 أنواع التقارير

### 1. التقارير اليومية (Daily Reports)
```php
// مثال على تقرير المبيعات اليومية
$dailyReports = [
    'daily_sales_summary' => 'ملخص المبيعات اليومية',
    'daily_stock_drawer' => 'تقرير المخزون اليومي',
    'daily_payments' => 'تقرير المدفوعات اليومية',
    'daily_discounts' => 'تقرير الخصومات اليومية',
    'daily_returns' => 'تقرير المرتجعات اليومية',
    'hourly_sales' => 'تقرير المبيعات بالساعة',
    'tax_report' => 'تقرير الضرائب'
];
```

**الميزات:**
- ملخص شامل للمبيعات اليومية
- تتبع حركة المخزون
- تقارير المدفوعات بجميع الطرق
- تحليل الخصومات والعروض
- إحصائيات المرتجعات والاستبدالات

### 2. التقارير الدورية (Periodic Reports)
```php
// مثال على التقارير الدورية
$periodicReports = [
    'product_performance' => 'أداء المنتجات',
    'staff_performance' => 'أداء الموظفين',
    'profit_report' => 'تقرير الأرباح',
    'customer_report' => 'تقرير العملاء'
];
```

**الميزات:**
- تحليل أداء المنتجات والأصناف
- تقييم أداء الموظفين
- حساب الأرباح والخسائر
- تحليل سلوك العملاء

### 3. التقارير المتقدمة (Advanced Reports)
```php
// مثال على التقارير المتقدمة
$advancedReports = [
    'financial_analysis' => 'التحليل المالي',
    'inventory_valuation' => 'تقييم المخزون',
    'cost_analysis' => 'تحليل التكاليف',
    'trend_analysis' => 'تحليل الاتجاهات',
    'comparative_analysis' => 'التحليل المقارن'
];
```

**الميزات:**
- تحليل مالي شامل
- تقييم دقيق للمخزون
- تحليل التكاليف والمصروفات
- تحليل الاتجاهات والتوقعات
- مقارنات بين الفترات المختلفة

### 4. تقارير المطعم المتخصصة (Restaurant-Specific Reports)
```php
// مثال على تقارير المطعم
$restaurantReports = [
    'menu_items_report' => 'تقرير عناصر القائمة',
    'meal_time_sales' => 'مبيعات أوقات الوجبات',
    'table_turnover' => 'معدل دوران الطاولات',
    'waste_tracking' => 'تتبع الهدر',
    'supplier_performance' => 'أداء الموردين'
];
```

**الميزات:**
- تحليل أداء عناصر القائمة
- إحصائيات أوقات الوجبات
- كفاءة استخدام الطاولات
- تتبع المواد المهدرة
- تقييم أداء الموردين

## 🛠️ الاستخدام والأمثلة

### إنشاء تقرير جديد
```php
use Modules\Reports\Services\ReportsService;

$reportsService = app(ReportsService::class);

// إنشاء تقرير مبيعات يومي
$report = $reportsService->generateReport([
    'type' => 'daily_sales_summary',
    'tenant_id' => $tenantId,
    'branch_id' => $branchId,
    'start_date' => '2024-01-01',
    'end_date' => '2024-01-31',
    'format' => 'pdf'
]);
```

### الحصول على تقرير أداء المنتجات
```php
// تقرير أداء المنتجات
$productPerformance = $reportsService->generateProductPerformanceReport(
    $tenantId, 
    $branchId, 
    'monthly', 
    '2024-01-01', 
    '2024-01-31'
);

// النتيجة تتضمن:
$result = [
    'summary' => [
        'total_products' => 150,
        'total_revenue' => 25000.00,
        'total_profit' => 8500.00,
        'total_items_sold' => 1200
    ],
    'best_sellers' => [...],
    'worst_performers' => [...],
    'all_products' => [...]
];
```

### تقرير أداء الموظفين
```php
// تقرير أداء الموظفين
$staffPerformance = $reportsService->generateStaffPerformanceReport(
    $tenantId,
    $branchId,
    'weekly',
    '2024-01-01',
    '2024-01-07'
);
```

### تقرير الأرباح المفصل
```php
// تقرير الأرباح
$profitReport = $reportsService->generateProfitReport(
    $tenantId,
    $branchId,
    'monthly',
    '2024-01-01',
    '2024-01-31'
);
```

## 🔌 واجهات برمجة التطبيقات (API Endpoints)

### تقارير المخزون
```http
GET /api/reports/inventory/stock-movement
GET /api/reports/inventory/reorder-point
```

### تقارير المطعم
```http
GET /api/reports/restaurant/menu-items
GET /api/reports/restaurant/meal-time-sales
```

### إدارة التقارير
```http
GET /api/reports/                    # قائمة التقارير
POST /api/reports/generate           # إنشاء تقرير جديد
GET /api/reports/{report}            # عرض تقرير محدد
DELETE /api/reports/{report}         # حذف تقرير
```

## 🎯 أمثلة عملية

### مثال 1: تقرير مبيعات شهري
```php
// إنشاء تقرير مبيعات شهري
$request = [
    'type' => 'monthly_sales',
    'tenant_id' => 1,
    'branch_id' => 2,
    'month' => '2024-01',
    'include_charts' => true,
    'format' => 'pdf'
];

$report = $reportsService->generateReport($request);

// النتيجة
echo "تم إنشاء التقرير بنجاح: " . $report->file_path;
```

### مثال 2: تقرير أفضل المنتجات مبيعاً
```php
// الحصول على أفضل 10 منتجات مبيعاً
$bestSellers = $reportsService->getBestSellingProducts(
    $tenantId,
    $branchId,
    10, // عدد المنتجات
    '2024-01-01',
    '2024-01-31'
);

foreach ($bestSellers as $product) {
    echo "المنتج: {$product->name} - المبيعات: {$product->total_sold}";
}
```

### مثال 3: تقرير الخسائر والهدر
```php
// تقرير تتبع الهدر
$wasteReport = $reportsService->generateWasteTrackingReport(
    $tenantId,
    $branchId,
    'daily',
    '2024-01-01',
    '2024-01-31'
);

// تحليل أسباب الهدر
$wasteAnalysis = [
    'expired_items' => $wasteReport['expired_percentage'],
    'damaged_items' => $wasteReport['damaged_percentage'],
    'overproduction' => $wasteReport['overproduction_percentage']
];
```

## 📈 التقارير التحليلية المتقدمة

### تحليل الاتجاهات
```php
// تحليل اتجاهات المبيعات
$trendAnalysis = $reportsService->generateTrendAnalysis([
    'metric' => 'sales',
    'period' => 'last_6_months',
    'comparison' => 'year_over_year'
]);
```

### التحليل المقارن
```php
// مقارنة أداء الفروع
$branchComparison = $reportsService->generateBranchComparison([
    'branches' => [1, 2, 3],
    'metrics' => ['sales', 'profit', 'customer_count'],
    'period' => 'last_quarter'
]);
```

## ⚙️ الإعدادات والتخصيص

### إعدادات التقارير
```php
// إعدادات التقارير في config/reports.php
return [
    'default_format' => 'pdf',
    'auto_archive_days' => 90,
    'max_report_size' => '50MB',
    'allowed_formats' => ['pdf', 'excel', 'csv'],
    'chart_library' => 'chartjs'
];
```

### تخصيص قوالب التقارير
```php
// تخصيص قالب تقرير
$customTemplate = [
    'header' => 'شعار المطعم والعنوان',
    'footer' => 'معلومات الاتصال',
    'colors' => ['primary' => '#007bff', 'secondary' => '#6c757d'],
    'fonts' => ['arabic' => 'NotoSansArabic', 'english' => 'Arial']
];
```

## 🔒 الأمان والصلاحيات

### صلاحيات الوصول
```php
// التحقق من صلاحيات التقارير
$permissions = [
    'reports.view' => 'عرض التقارير',
    'reports.generate' => 'إنشاء التقارير',
    'reports.export' => 'تصدير التقارير',
    'reports.delete' => 'حذف التقارير',
    'reports.advanced' => 'التقارير المتقدمة'
];
```

## 📱 التكامل مع الواجهات

### واجهة الويب
```blade
{{-- عرض قائمة التقارير --}}
@extends('layouts.app')

@section('content')
<div class="reports-dashboard">
    <h2>لوحة تحكم التقارير</h2>
    
    <div class="report-categories">
        <div class="category-card">
            <h3>التقارير اليومية</h3>
            <a href="{{ route('reports.daily') }}" class="btn btn-primary">
                عرض التقارير اليومية
            </a>
        </div>
    </div>
</div>
@endsection
```

### واجهة الجوال
```javascript
// API call للحصول على التقارير
const fetchReports = async () => {
    try {
        const response = await fetch('/api/reports', {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        const reports = await response.json();
        displayReports(reports);
    } catch (error) {
        console.error('خطأ في جلب التقارير:', error);
    }
};
```

## 🚀 الأداء والتحسين

### تحسين الأداء
- **التخزين المؤقت**: حفظ التقارير المتكررة في الذاكرة المؤقتة
- **المعالجة غير المتزامنة**: إنشاء التقارير الكبيرة في الخلفية
- **الفهرسة**: فهرسة قاعدة البيانات لتسريع الاستعلامات
- **الضغط**: ضغط ملفات التقارير لتوفير المساحة

### مراقبة الأداء
```php
// مراقبة أداء التقارير
$performanceMetrics = [
    'generation_time' => '2.5 seconds',
    'memory_usage' => '128 MB',
    'database_queries' => 15,
    'cache_hit_rate' => '85%'
];
```

## 🔧 استكشاف الأخطاء

### الأخطاء الشائعة وحلولها

1. **خطأ في إنشاء التقرير**
```php
// التحقق من صحة البيانات
if (!$this->validateReportData($data)) {
    throw new InvalidReportDataException('بيانات التقرير غير صحيحة');
}
```

2. **نفاد الذاكرة**
```php
// معالجة التقارير الكبيرة
ini_set('memory_limit', '512M');
set_time_limit(300); // 5 دقائق
```

3. **خطأ في التصدير**
```php
// التحقق من صيغة التصدير
$allowedFormats = ['pdf', 'excel', 'csv'];
if (!in_array($format, $allowedFormats)) {
    throw new UnsupportedFormatException('صيغة غير مدعومة');
}
```

## 📚 المراجع والموارد

### الوثائق التقنية
- [Laravel Reporting](https://laravel.com/docs/reporting)
- [Chart.js Documentation](https://www.chartjs.org/docs/)
- [TCPDF Documentation](https://tcpdf.org/docs/)

### أمثلة إضافية
- [تقارير المطاعم المتقدمة](./examples/advanced-restaurant-reports.md)
- [تخصيص قوالب التقارير](./examples/custom-report-templates.md)
- [تكامل التقارير مع API](./examples/reports-api-integration.md)

---

**ملاحظة**: هذه الوحدة تتطلب Laravel 8+ و PHP 8.0+ للعمل بشكل صحيح. تأكد من تحديث التبعيات قبل الاستخدام.