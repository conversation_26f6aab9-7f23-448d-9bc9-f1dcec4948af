<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
class DashboardController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */


    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
       

        return view('index');
    }

    /**
     * Toggle theme preference
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function toggleTheme(Request $request)
    {
        $theme = $request->input('theme', 'light');
        
        // You can store theme preference in user settings or session
        session(['theme' => $theme]);
        
        return response()->json([
            'success' => true,
            'theme' => $theme,
            'message' => __('messages.theme_updated')
        ]);
    }

    /**
     * Mark notification as read
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function markNotificationRead(Request $request)
    {
        $notificationId = $request->input('notification_id');
        
        // Here you would typically update the notification in the database
        // For now, we'll just return a success response
        
        return response()->json([
            'success' => true,
            'message' => __('messages.notification_marked_read')
        ]);
    }

    /**
     * Get dashboard statistics via AJAX
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStats()
    {
        // This would typically fetch real data from your models
        $stats = [
            'todaySales' => number_format(15420.50, 2),
            'totalOrders' => 156,
            'totalCustomers' => 1234,
            'staffCount' => 25,
            'todayExpenses' => number_format(2340.75, 2),
            'pendingOrders' => 12,
            'salesGrowth' => '+12.5%',
            'ordersGrowth' => '+8.3%',
            'customersGrowth' => '+15.2%',
            'expensesGrowth' => '-5.1%'
        ];

        return response()->json($stats);
    }

    /**
     * Get chart data via AJAX
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getChartData(Request $request)
    {
        $chartType = $request->input('type', 'sales');
        
        switch ($chartType) {
            case 'sales':
                return response()->json([
                    'labels' => ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                    'datasets' => [[
                        'label' => __('messages.sales'),
                        'data' => [1200, 1900, 3000, 5000, 2000, 3000, 4500],
                        'borderColor' => '#3b82f6',
                        'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                        'tension' => 0.4
                    ]]
                ]);
                
            case 'category':
                return response()->json([
                    'labels' => [
                        __('messages.main_dishes'),
                        __('messages.appetizers'),
                        __('messages.desserts'),
                        __('messages.beverages'),
                        __('messages.specials')
                    ],
                    'datasets' => [[
                        'data' => [35, 25, 15, 20, 5],
                        'backgroundColor' => [
                            '#ef4444',
                            '#f97316',
                            '#eab308',
                            '#22c55e',
                            '#3b82f6'
                        ]
                    ]]
                ]);
                
            default:
                return response()->json(['error' => 'Invalid chart type'], 400);
        }
    }

    /**
     * Load content for different dashboard sections
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function loadContent(Request $request)
    {
        $section = $request->input('section', 'dashboard');
        
        switch ($section) {
            case 'dashboard':
                return view('dashboard.content');
            case 'menu-items':
                // Redirect to Menu module
                return redirect()->route('menu-items.index');
            case 'item-categories':
                // Redirect to Menu module
                return redirect()->route('categories.index');
            case 'menus':
                // Redirect to Menu module
                return redirect()->route('menus.index');
            case 'variants':
                // Redirect to Menu module
                return redirect()->route('variants.index');
            case 'addons':
                // Redirect to Menu module
                return redirect()->route('addons.index');
            default:
                return view('dashboard.content');
        }
    }

    /**
     * Load orders content
     *
     * @return \Illuminate\Http\Response
     */
    public function loadOrders()
    {
        return view('dashboard.orders');
    }

    /**
     * Load inventory content
     *
     * @return \Illuminate\Http\Response
     */
    public function loadInventory()
    {
        return view('dashboard.inventory');
    }

    /**
     * Load customers content
     *
     * @return \Illuminate\Http\Response
     */
    public function loadCustomers()
    {
        return view('dashboard.customers');
    }

    /**
     * Load reports content
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function loadReports(Request $request)
    {
        $type = $request->input('type', 'sales');
        return view('dashboard.reports', compact('type'));
    }

    /**
     * Load settings content
     *
     * @return \Illuminate\Http\Response
     */
    public function loadSettings()
    {
        return view('dashboard.settings');
    }
}