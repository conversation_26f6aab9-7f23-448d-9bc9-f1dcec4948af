<?php

namespace Modules\Delivery\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\Delivery\Services\DeliveryPersonnelService;
use Modules\Delivery\Http\Requests\CreateDeliveryPersonnelRequest;
use Modules\Delivery\Http\Requests\UpdateDeliveryPersonnelRequest;
use Modules\Delivery\Http\Requests\UpdatePersonnelLocationRequest;
use Modules\Delivery\Http\Resources\DeliveryPersonnelResource;
use Modules\Delivery\Http\Resources\DeliveryPersonnelCollection;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class DeliveryPersonnelController extends Controller
{
    public function __construct(
        private DeliveryPersonnelService $personnelService
    ) {}

    /**
     * Get tenant ID from authenticated user
     */
    private function getTenantId(): int
    {
        return Auth::user()->tenant_id;
    }

    /**
     * Get delivery personnel with filters
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $filters = $request->only([
                'status', 'branch_id', 'vehicle_type', 'is_available'
            ]);
            
            $perPage = $request->get('per_page', 15);
            
            $personnel = $this->personnelService->getPersonnel($filters, $perPage);

            return response()->json([
                'success' => true,
                'data' => new DeliveryPersonnelCollection($personnel),
                'meta' => [
                    'current_page' => $personnel->currentPage(),
                    'last_page' => $personnel->lastPage(),
                    'per_page' => $personnel->perPage(),
                    'total' => $personnel->total(),
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve delivery personnel',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Create new delivery personnel
     */
    public function store(CreateDeliveryPersonnelRequest $request): JsonResponse
    {
        try {
            $personnel = $this->personnelService->createPersonnel(
                $request->validated()
            );

            return response()->json([
                'success' => true,
                'message' => 'Delivery personnel created successfully',
                'data' => new DeliveryPersonnelResource($personnel),
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create delivery personnel',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get specific delivery personnel
     */
    public function show(int $id): JsonResponse
    {
        try {
            $personnel = $this->personnelService->getPersonnelById($id);
            
            if (!$personnel) {
                return response()->json([
                    'success' => false,
                    'message' => 'Delivery personnel not found',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => new DeliveryPersonnelResource($personnel),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve delivery personnel',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update delivery personnel
     */
    public function update(UpdateDeliveryPersonnelRequest $request, int $id): JsonResponse
    {
        try {
            // First get the personnel model
            $personnel = $this->personnelService->getPersonnelById($id);
            
            if (!$personnel) {
                return response()->json([
                    'success' => false,
                    'message' => 'Delivery personnel not found',
                ], 404);
            }
            
            $personnel = $this->personnelService->updatePersonnel(
                $personnel,
                $request->validated()
            );

            return response()->json([
                'success' => true,
                'message' => 'Delivery personnel updated successfully',
                'data' => new DeliveryPersonnelResource($personnel),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update delivery personnel',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete delivery personnel
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $this->personnelService->deletePersonnel($id);

            return response()->json([
                'success' => true,
                'message' => 'Delivery personnel deleted successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete delivery personnel',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update personnel location
     */
    public function updateLocation(UpdatePersonnelLocationRequest $request, int $id): JsonResponse
    {
        try {
            $personnel = $this->personnelService->updateLocation(
                $id,
                $request->latitude,
                $request->longitude
            );

            return response()->json([
                'success' => true,
                'message' => 'Location updated successfully',
                'data' => new DeliveryPersonnelResource($personnel),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update location',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update personnel status
     */
    public function updateStatus(Request $request, int $id): JsonResponse
    {
        $request->validate([
            'status' => 'required|in:active,inactive,on_break,off_duty',
        ]);

        try {
            $personnel = $this->personnelService->updateStatus(
                $id,
                $request->status
            );

            return response()->json([
                'success' => true,
                'message' => 'Status updated successfully',
                'data' => new DeliveryPersonnelResource($personnel),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update status',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get available personnel for assignment
     */
    public function available(Request $request): JsonResponse
    {
        $request->validate([
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'branch_id' => 'nullable|exists:branches,id',
        ]);

        try {
            $personnel = $this->personnelService->getAvailablePersonnel(
                $request->latitude,
                $request->longitude,
                $request->branch_id
            );

            return response()->json([
                'success' => true,
                'data' => DeliveryPersonnelResource::collection($personnel),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve available personnel',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get personnel performance metrics
     */
    public function performance(Request $request, int $id): JsonResponse
    {
        $request->validate([
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
        ]);

        try {
            $metrics = $this->personnelService->getPerformanceMetrics(
                $id,
                $request->date_from,
                $request->date_to
            );

            return response()->json([
                'success' => true,
                'data' => $metrics,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve performance metrics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Verify personnel credentials
     */
    public function verify(Request $request, int $id): JsonResponse
    {
        $request->validate([
            'verification_status' => 'required|in:pending,verified,rejected',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            $personnel = $this->personnelService->verifyPersonnel(
                $id,
                $request->verification_status,
                $request->notes
            );

            return response()->json([
                'success' => true,
                'message' => 'Personnel verification updated successfully',
                'data' => new DeliveryPersonnelResource($personnel),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update verification status',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get personnel working hours
     */
    public function workingHours(int $id): JsonResponse
    {
        try {
            $workingHours = $this->personnelService->getWorkingHours($id);

            return response()->json([
                'success' => true,
                'data' => $workingHours,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve working hours',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update personnel working hours
     */
    public function updateWorkingHours(Request $request, int $id): JsonResponse
    {
        $request->validate([
            'working_hours' => 'required|array',
            'working_hours.*.day' => 'required|in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'working_hours.*.start_time' => 'required|date_format:H:i',
            'working_hours.*.end_time' => 'required|date_format:H:i|after:working_hours.*.start_time',
            'working_hours.*.is_working' => 'required|boolean',
        ]);

        try {
            $this->personnelService->updateWorkingHours(
                $id,
                $request->working_hours
            );

            return response()->json([
                'success' => true,
                'message' => 'Working hours updated successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update working hours',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Suspend personnel
     */
    public function suspendPersonnel(Request $request, int $id): JsonResponse
    {
        $request->validate([
            'reason' => 'required|string|max:500',
            'suspension_until' => 'nullable|date|after:today',
        ]);

        try {
            $personnel = $this->personnelService->suspendPersonnel(
                $id,
                $request->reason,
                $request->suspension_until
            );

            return response()->json([
                'success' => true,
                'message' => 'Personnel suspended successfully',
                'data' => new DeliveryPersonnelResource($personnel),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to suspend personnel',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Activate personnel
     */
    public function activatePersonnel(Request $request, int $id): JsonResponse
    {
        try {
            $personnel = $this->personnelService->activatePersonnel($id);

            return response()->json([
                'success' => true,
                'message' => 'Personnel activated successfully',
                'data' => new DeliveryPersonnelResource($personnel),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to activate personnel',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}