<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;

class Area extends Model
{
    protected $fillable = [
        'branch_id',
        'name',
        'description',
    ];

    /**
     * The attributes that should be appended to arrays.
     *
     * @var array
     */
    protected $appends = [
        'tables_count',
        'total_capacity',
        'available_tables_count',
        'occupied_tables_count',
    ];

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    public function tables(): HasMany
    {
        return $this->hasMany(Table::class);
    }

    public function reservations(): HasMany
    {
        return $this->hasMany(Reservation::class);
    }

    // Scopes
    public function scopeForBranch(Builder $query, $branchId): Builder
    {
        return $query->where('branch_id', $branchId);
    }

    public function scopeWithTables(Builder $query): Builder
    {
        return $query->with(['tables' => function ($q) {
            $q->where('is_active', true);
        }]);
    }

    public function scopeWithActiveReservations(Builder $query): Builder
    {
        return $query->with(['reservations' => function ($q) {
            $q->whereHas('reservationStatus', function ($status) {
                $status->whereIn('name', ['pending', 'confirmed', 'seated']);
            });
        }]);
    }

    // Computed Attributes
    public function getTablesCountAttribute(): int
    {
        return $this->tables()->where('is_active', true)->count();
    }

    public function getTotalCapacityAttribute(): int
    {
        return $this->tables()->where('is_active', true)->sum('seating_capacity');
    }

    public function getAvailableTablesCountAttribute(): int
    {
        return $this->tables()
            ->where('is_active', true)
            ->where('status', 'available')
            ->count();
    }

    public function getOccupiedTablesCountAttribute(): int
    {
        return $this->tables()
            ->where('is_active', true)
            ->whereIn('status', ['occupied', 'reserved'])
            ->count();
    }

    // Helper Methods
    public function getAvailableTables()
    {
        return $this->tables()
            ->where('is_active', true)
            ->where('status', 'available')
            ->get();
    }

    public function getTablesForCapacity(int $partySize)
    {
        return $this->tables()
            ->where('is_active', true)
            ->where('status', 'available')
            ->where('seating_capacity', '>=', $partySize)
            ->orderBy('seating_capacity')
            ->get();
    }

    public function hasAvailableCapacity(int $partySize): bool
    {
        return $this->tables()
            ->where('is_active', true)
            ->where('status', 'available')
            ->where('seating_capacity', '>=', $partySize)
            ->exists();
    }

    public function getStatistics(): array
    {
        $tables = $this->tables()->where('is_active', true);
        
        return [
            'total_tables' => $tables->count(),
            'total_capacity' => $tables->sum('seating_capacity'),
            'available_tables' => $tables->where('status', 'available')->count(),
            'occupied_tables' => $tables->where('status', 'occupied')->count(),
            'reserved_tables' => $tables->where('status', 'reserved')->count(),
            'cleaning_tables' => $tables->where('status', 'cleaning')->count(),
            'out_of_order_tables' => $tables->where('status', 'out_of_order')->count(),
        ];
    }
}