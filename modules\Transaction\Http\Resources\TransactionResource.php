<?php

namespace Modules\Transaction\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TransactionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'transaction_number' => $this->transaction_number,
            'total_amount' => $this->total_amount,
            'paid_amount' => $this->paid_amount,
            'due_amount' => $this->due_amount,
            'status' => $this->status,
            'status_label' => $this->getStatusLabel(),
            'tax_amount' => $this->tax_amount,
            'discount_amount' => $this->discount_amount,
            'service_charge' => $this->service_charge,
            'notes' => $this->notes,
            'payment_progress' => $this->getPaymentProgress(),
            'can_accept_payments' => $this->canAcceptPayments(),
            'is_paid' => $this->isPaid(),
            'is_partially_paid' => $this->isPartiallyPaid(),
            'is_due' => $this->isDue(),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,

            // Relationships
            'order' => $this->whenLoaded('order', function () {
                return [
                    'id' => $this->order->id,
                    'order_number' => $this->order->order_number,
                    'order_type' => $this->order->order_type,
                    'status' => $this->order->status,
                    'total_amount' => $this->order->total_amount,
                    'customer' => $this->when($this->order->relationLoaded('customer') && $this->order->customer, [
                        'id' => $this->order->customer->id,
                        'name' => $this->order->customer->name,
                        'phone' => $this->order->customer->phone,
                    ]),
                    'table' => $this->when($this->order->relationLoaded('table') && $this->order->table, [
                        'id' => $this->order->table->id,
                        'table_number' => $this->order->table->table_number,
                        'table_name' => $this->order->table->table_name,
                    ]),
                ];
            }),

            'payments' => PaymentResource::collection($this->whenLoaded('payments')),

            'payments_summary' => $this->whenLoaded('payments', function () {
                $payments = $this->payments;
                return [
                    'total_payments' => $payments->count(),
                    'completed_payments' => $payments->where('status', 'completed')->count(),
                    'pending_payments' => $payments->where('status', 'pending')->count(),
                    'failed_payments' => $payments->where('status', 'failed')->count(),
                    'by_payment_method' => $payments->groupBy('payment_method_id')->map(function ($group) {
                        $first = $group->first();
                        return [
                            'payment_method' => $first->paymentMethod->name ?? 'Unknown',
                            'count' => $group->count(),
                            'total_amount' => $group->where('status', 'completed')->sum('amount'),
                        ];
                    })->values(),
                ];
            }),

            'created_by' => $this->whenLoaded('createdBy', function () {
                return [
                    'id' => $this->createdBy->id,
                    'name' => $this->createdBy->name,
                ];
            }),

            'updated_by' => $this->whenLoaded('updatedBy', function () {
                return [
                    'id' => $this->updatedBy->id,
                    'name' => $this->updatedBy->name,
                ];
            }),
        ];
    }

    /**
     * Get status label for display.
     */
    protected function getStatusLabel(): string
    {
        $labels = [
            'due' => 'Due',
            'partially_paid' => 'Partially Paid',
            'paid' => 'Paid',
            'overpaid' => 'Overpaid',
            'refunded' => 'Refunded',
            'cancelled' => 'Cancelled',
        ];

        return $labels[$this->status] ?? ucfirst($this->status);
    }
}
