<?php $__env->startSection('title', 'Transaction Details'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .transaction-status {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
    }
    .status-paid { background-color: #d1fae5; color: #065f46; }
    .status-due { background-color: #fee2e2; color: #991b1b; }
    .status-partially_paid { background-color: #fef3c7; color: #92400e; }
    .status-cancelled { background-color: #f3f4f6; color: #374151; }
    .status-overpaid { background-color: #dbeafe; color: #1e40af; }
    
    .payment-status {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
    }
    .payment-completed { background-color: #d1fae5; color: #065f46; }
    .payment-pending { background-color: #fef3c7; color: #92400e; }
    .payment-failed { background-color: #fee2e2; color: #991b1b; }
    .payment-cancelled { background-color: #f3f4f6; color: #374151; }
    
    .modal {
        display: none;
    }
    .modal.show {
        display: flex;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<!-- Page Header -->
<div class="mb-6">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="mb-4 sm:mb-0">
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-receipt mr-3"></i>
                Transaction Details
            </h1>
            <p class="text-sm text-gray-600 mt-1">Viewing transaction #<?php echo e($transaction->transaction_number); ?></p>
        </div>
        <div class="flex gap-2">
            <a href="<?php echo e(route('transactions.index')); ?>" class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-md transition-colors duration-200">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to List
            </a>
            <?php if($transaction->canAcceptPayments()): ?>
            <a href="<?php echo e(route('payments.create')); ?>?transaction_id=<?php echo e($transaction->id); ?>" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-md transition-colors duration-200">
                <i class="fas fa-money-bill-wave mr-2"></i>
                Add Payment
            </a>
            <?php endif; ?>
            <a href="<?php echo e(route('transactions.edit', $transaction->id)); ?>" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors duration-200">
                <i class="fas fa-edit mr-2"></i>
                Edit
            </a>
            <button type="button" id="void-transaction-btn" class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-md transition-colors duration-200">
                <i class="fas fa-ban mr-2"></i>
                Void
            </button>
        </div>
    </div>
</div>

<!-- Transaction Details Card -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
    <!-- Main Transaction Info -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 lg:col-span-2">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">Transaction Information</h3>
                <span class="transaction-status status-<?php echo e($transaction->status); ?>">
                    <?php echo e(ucfirst(str_replace('_', ' ', $transaction->status))); ?>

                </span>
            </div>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="font-semibold text-gray-900 mb-3">Transaction Details</h4>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Transaction Number:</span>
                            <span class="font-medium"><?php echo e($transaction->transaction_number); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Date:</span>
                            <span class="font-medium"><?php echo e($transaction->created_at->format('M d, Y H:i')); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Total Amount:</span>
                            <span class="font-medium">$<?php echo e(number_format($transaction->total_amount, 2)); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Paid Amount:</span>
                            <span class="font-medium">$<?php echo e(number_format($transaction->paid_amount, 2)); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Due Amount:</span>
                            <span class="font-medium">$<?php echo e(number_format($transaction->due_amount, 2)); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Payment Progress:</span>
                            <span class="font-medium"><?php echo e(number_format($transaction->getPaymentProgress(), 0)); ?>%</span>
                        </div>
                    </div>
                </div>
                <div>
                    <h4 class="font-semibold text-gray-900 mb-3">Additional Information</h4>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Tax Amount:</span>
                            <span class="font-medium">$<?php echo e(number_format($transaction->tax_amount, 2)); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Discount Amount:</span>
                            <span class="font-medium">$<?php echo e(number_format($transaction->discount_amount, 2)); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Service Charge:</span>
                            <span class="font-medium">$<?php echo e(number_format($transaction->service_charge, 2)); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Created By:</span>
                            <span class="font-medium"><?php echo e($transaction->createdBy->name ?? 'N/A'); ?></span>
                        </div>
                        <?php if($transaction->updated_by): ?>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Last Updated By:</span>
                            <span class="font-medium"><?php echo e($transaction->updatedBy->name ?? 'N/A'); ?></span>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <?php if($transaction->notes): ?>
            <div class="mt-6 pt-6 border-t border-gray-200">
                <h4 class="font-semibold text-gray-900 mb-2">Notes</h4>
                <p class="text-gray-700 whitespace-pre-line"><?php echo e($transaction->notes); ?></p>
            </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Order Information -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Related Information</h3>
        </div>
        <div class="p-6">
            <?php if($transaction->order): ?>
            <h4 class="font-semibold text-gray-900 mb-3">Order Details</h4>
            <div class="space-y-3 mb-6">
                <div class="flex justify-between">
                    <span class="text-gray-600">Order Number:</span>
                    <span class="font-medium"><?php echo e($transaction->order->order_number); ?></span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Order Date:</span>
                    <span class="font-medium"><?php echo e($transaction->order->created_at->format('M d, Y H:i')); ?></span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Order Status:</span>
                    <span class="font-medium"><?php echo e(ucfirst($transaction->order->status)); ?></span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Items Count:</span>
                    <span class="font-medium"><?php echo e($transaction->order->items_count ?? 'N/A'); ?></span>
                </div>
            </div>
            
            <?php if($transaction->order->customer): ?>
            <h4 class="font-semibold text-gray-900 mb-3">Customer Information</h4>
            <div class="space-y-3 mb-6">
                <div class="flex justify-between">
                    <span class="text-gray-600">Name:</span>
                    <span class="font-medium"><?php echo e($transaction->order->customer->name); ?></span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Phone:</span>
                    <span class="font-medium"><?php echo e($transaction->order->customer->phone ?? 'N/A'); ?></span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Email:</span>
                    <span class="font-medium"><?php echo e($transaction->order->customer->email ?? 'N/A'); ?></span>
                </div>
            </div>
            <?php endif; ?>
            
            <?php if($transaction->order->table): ?>
            <h4 class="font-semibold text-gray-900 mb-3">Table Information</h4>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-gray-600">Table Number:</span>
                    <span class="font-medium"><?php echo e($transaction->order->table->table_number); ?></span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Table Name:</span>
                    <span class="font-medium"><?php echo e($transaction->order->table->table_name ?? 'N/A'); ?></span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Capacity:</span>
                    <span class="font-medium"><?php echo e($transaction->order->table->capacity ?? 'N/A'); ?></span>
                </div>
            </div>
            <?php endif; ?>
            <?php else: ?>
            <div class="text-gray-600 italic">This is a standalone transaction with no associated order.</div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Payments Card -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">Payment History</h3>
            <?php if($transaction->canAcceptPayments()): ?>
            <a href="<?php echo e(route('payments.create')); ?>?transaction_id=<?php echo e($transaction->id); ?>" class="inline-flex items-center px-3 py-1.5 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-md transition-colors duration-200">
                <i class="fas fa-plus mr-1"></i>
                Add Payment
            </a>
            <?php endif; ?>
        </div>
    </div>
    <div class="overflow-x-auto">
        <table class="w-full">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment ID</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Method</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Processed By</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <?php $__empty_1 = true; $__currentLoopData = $transaction->payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"><?php echo e($payment->payment_number); ?></td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700"><?php echo e($payment->paymentMethod->name ?? 'N/A'); ?></td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">$<?php echo e(number_format($payment->amount, 2)); ?></td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="payment-status payment-<?php echo e($payment->status); ?>">
                            <?php echo e(ucfirst($payment->status)); ?>

                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700"><?php echo e($payment->created_at->format('M d, Y H:i')); ?></td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700"><?php echo e($payment->processedBy->name ?? 'N/A'); ?></td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                        <div class="flex gap-1">
                            <a href="<?php echo e(route('payments.show', $payment->id)); ?>" class="px-2 py-1 bg-blue-500 hover:bg-blue-600 text-white text-xs rounded">
                                <i class="fas fa-eye"></i>
                            </a>
                            <?php if($payment->status === 'completed'): ?>
                            <a href="<?php echo e(route('payments.refund', $payment->id)); ?>" class="px-2 py-1 bg-yellow-500 hover:bg-yellow-600 text-white text-xs rounded">
                                <i class="fas fa-undo"></i>
                            </a>
                            <?php endif; ?>
                            <?php if($payment->status === 'pending'): ?>
                            <form action="<?php echo e(route('payments.cancel', $payment->id)); ?>" method="POST" class="inline">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('PATCH'); ?>
                                <button type="submit" class="px-2 py-1 bg-red-500 hover:bg-red-600 text-white text-xs rounded">
                                    <i class="fas fa-times"></i>
                                </button>
                            </form>
                            <?php endif; ?>
                        </div>
                    </td>
                </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <tr>
                    <td colspan="7" class="px-6 py-4 text-center text-sm text-gray-500">No payments found for this transaction.</td>
                </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- Void Transaction Modal -->
<div id="void-modal" class="modal fixed inset-0 bg-black bg-opacity-50 z-50 items-center justify-center">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">Void Transaction</h3>
                <button type="button" id="close-void-modal" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <form id="void-form" action="<?php echo e(route('transactions.destroy', $transaction->id)); ?>" method="POST">
            <?php echo csrf_field(); ?>
            <?php echo method_field('DELETE'); ?>
            <div class="p-6">
                <p class="text-gray-700 mb-4">Are you sure you want to void this transaction? This action cannot be undone.</p>
                <div>
                    <label for="reason" class="block text-sm font-medium text-gray-700 mb-2">Reason for Voiding</label>
                    <textarea id="reason" name="reason" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" required></textarea>
                </div>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end gap-2">
                <button type="button" id="cancel-void-btn" class="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md transition-colors duration-200">
                    Cancel
                </button>
                <button type="submit" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors duration-200">
                    Void Transaction
                </button>
            </div>
        </form>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    // Void transaction modal
    $('#void-transaction-btn').on('click', function() {
        $('#void-modal').addClass('show');
    });
    
    $('#close-void-modal, #cancel-void-btn').on('click', function() {
        $('#void-modal').removeClass('show');
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Transaction\Providers/../resources/views/transactions/show.blade.php ENDPATH**/ ?>