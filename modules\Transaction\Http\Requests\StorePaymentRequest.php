<?php

namespace Modules\Transaction\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Modules\Transaction\Models\PaymentMethod;

class StorePaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Add authorization logic as needed
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rules = [
            'transaction_id' => 'required|exists:transactions,id',
            'payment_method_id' => 'required|exists:payment_methods,id',
            'amount' => 'required|numeric|min:0.01',
            'payment_date' => 'nullable|date',
            'reference_number' => 'nullable|string|max:100',
            'notes' => 'nullable|string|max:500',
            'payment_details' => 'nullable|array',
        ];

        // Add cash-specific validation
        if ($this->payment_method_id) {
            $paymentMethod = PaymentMethod::find($this->payment_method_id);
            if ($paymentMethod && $paymentMethod->isCash()) {
                $rules['cash_received'] = 'required|numeric|min:' . ($this->amount ?? 0);
            }
        }

        // Add card-specific validation
        if ($this->payment_method_id) {
            $paymentMethod = PaymentMethod::find($this->payment_method_id);
            if ($paymentMethod && $paymentMethod->isCard()) {
                $rules['payment_details.card_last_four'] = 'nullable|string|size:4';
                $rules['payment_details.card_type'] = 'nullable|string|max:50';
                $rules['payment_details.authorization_code'] = 'nullable|string|max:50';
            }
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'transaction_id.required' => 'Transaction ID is required',
            'transaction_id.exists' => 'The selected transaction does not exist',
            'payment_method_id.required' => 'Payment method is required',
            'payment_method_id.exists' => 'The selected payment method does not exist',
            'amount.required' => 'Payment amount is required',
            'amount.numeric' => 'Payment amount must be a number',
            'amount.min' => 'Payment amount must be greater than zero',
            'cash_received.required' => 'Cash received amount is required for cash payments',
            'cash_received.numeric' => 'Cash received must be a number',
            'cash_received.min' => 'Cash received must be at least the payment amount',
            'payment_date.date' => 'Payment date must be a valid date',
            'reference_number.string' => 'Reference number must be a string',
            'reference_number.max' => 'Reference number cannot exceed 100 characters',
            'notes.string' => 'Notes must be a string',
            'notes.max' => 'Notes cannot exceed 500 characters',
            'payment_details.array' => 'Payment details must be an array',
            'payment_details.card_last_four.size' => 'Card last four digits must be exactly 4 characters',
            'payment_details.card_type.max' => 'Card type cannot exceed 50 characters',
            'payment_details.authorization_code.max' => 'Authorization code cannot exceed 50 characters',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'transaction_id' => 'transaction',
            'payment_method_id' => 'payment method',
            'amount' => 'payment amount',
            'cash_received' => 'cash received',
            'payment_date' => 'payment date',
            'reference_number' => 'reference number',
            'notes' => 'payment notes',
            'payment_details' => 'payment details',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Check if payment method is active
            if ($this->payment_method_id) {
                $paymentMethod = PaymentMethod::find($this->payment_method_id);
                if ($paymentMethod && !$paymentMethod->is_active) {
                    $validator->errors()->add('payment_method_id', 'The selected payment method is not active');
                }
            }

            // Check if transaction can accept payments
            if ($this->transaction_id) {
                $transaction = \Modules\Transaction\Models\Transaction::find($this->transaction_id);
                if ($transaction && !$transaction->canAcceptPayments()) {
                    $validator->errors()->add('transaction_id', 'This transaction cannot accept payments in its current status');
                }
            }

            // Validate payment amount doesn't exceed due amount
            if ($this->transaction_id && $this->amount) {
                $transaction = \Modules\Transaction\Models\Transaction::find($this->transaction_id);
                if ($transaction && $this->amount > $transaction->due_amount) {
                    $validator->errors()->add('amount', 'Payment amount cannot exceed the due amount of ' . number_format($transaction->due_amount, 2));
                }
            }
        });
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation()
    {
        // Set default payment date if not provided
        if (!$this->has('payment_date')) {
            $this->merge([
                'payment_date' => now(),
            ]);
        }
    }
}
