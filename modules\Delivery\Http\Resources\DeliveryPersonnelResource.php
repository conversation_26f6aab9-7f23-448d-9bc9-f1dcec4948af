<?php

namespace Modules\Delivery\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DeliveryPersonnelResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'branch_id' => $this->branch_id,
            'vehicle_type' => $this->vehicle_type,
            'vehicle_model' => $this->vehicle_model,
            'vehicle_plate_number' => $this->vehicle_plate_number,
            'license_number' => $this->license_number,
            'phone_number' => $this->phone_number,
            'emergency_contact' => $this->emergency_contact,
            'emergency_phone' => $this->emergency_phone,
            'max_concurrent_deliveries' => $this->max_concurrent_deliveries,
            'delivery_radius' => $this->delivery_radius,
            'hourly_rate' => $this->hourly_rate,
            'commission_rate' => $this->commission_rate,
            'status' => $this->status,
            'current_latitude' => $this->current_latitude,
            'current_longitude' => $this->current_longitude,
            'last_location_update' => $this->last_location_update?->format('Y-m-d H:i:s'),
            'working_hours' => $this->working_hours,
            'is_verified' => $this->is_verified,
            'verification_date' => $this->verification_date?->format('Y-m-d H:i:s'),
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),

            // Relationships
            'user' => $this->whenLoaded('user', function () {
                return [
                    'id' => $this->user->id,
                    'name' => $this->user->name,
                    'email' => $this->user->email,
                    'avatar' => $this->user->avatar,
                    'created_at' => $this->user->created_at?->format('Y-m-d H:i:s'),
                ];
            }),

            'branch' => $this->whenLoaded('branch', function () {
                return [
                    'id' => $this->branch->id,
                    'name' => $this->branch->name,
                    'address' => $this->branch->address,
                    'phone' => $this->branch->phone,
                    'latitude' => $this->branch->latitude,
                    'longitude' => $this->branch->longitude,
                ];
            }),

            'active_assignments' => $this->whenLoaded('activeDeliveries', function () {
                return DeliveryAssignmentResource::collection($this->activeDeliveries);
            }),

            'recent_assignments' => $this->whenLoaded('recentAssignments', function () {
                return DeliveryAssignmentResource::collection($this->recentAssignments);
            }),

            // Computed fields
            'status_label' => $this->getStatusLabel(),
            'vehicle_info' => $this->getVehicleInfo(),
            'is_available' => $this->isAvailable(),
            'is_online' => $this->isOnline(),
            'current_load' => $this->getCurrentLoad(),
            'can_accept_delivery' => $this->canAcceptDelivery(),
            'distance_from_branch' => $this->getDistanceFromBranch(),
            'working_today' => $this->isWorkingToday(),
            'current_shift' => $this->getCurrentShift(),
            'performance_rating' => $this->getPerformanceRating(),
            'total_deliveries_today' => $this->getTotalDeliveriesToday(),
            'earnings_today' => $this->getEarningsToday(),
        ];
    }

    /**
     * Get status label
     */
    private function getStatusLabel(): string
    {
        return match ($this->status) {
            'active' => 'Active',
            'inactive' => 'Inactive',
            'busy' => 'Busy',
            'offline' => 'Offline',
            'on_break' => 'On Break',
            'suspended' => 'Suspended',
            default => ucfirst($this->status),
        };
    }

    /**
     * Get formatted vehicle information
     */
    private function getVehicleInfo(): string
    {
        $parts = array_filter([
            $this->vehicle_type,
            $this->vehicle_model,
            $this->vehicle_plate_number,
        ]);

        return implode(' - ', $parts);
    }

    /**
     * Check if personnel is available for new deliveries
     */
    private function isAvailable(): bool
    {
        if ($this->status !== 'active') {
            return false;
        }

        $currentAssignments = $this->activeDeliveries()->count();
        return $currentAssignments < $this->max_concurrent_deliveries;
    }

    /**
     * Check if personnel is online (recently updated location)
     */
    private function isOnline(): bool
    {
        if (!$this->last_location_update) {
            return false;
        }

        return $this->last_location_update->diffInMinutes(now()) <= 10;
    }

    /**
     * Get current delivery load
     */
    private function getCurrentLoad(): int
    {
        return $this->activeDeliveries()->count();
    }

    /**
     * Check if personnel can accept new delivery
     */
    private function canAcceptDelivery(): bool
    {
        return $this->isAvailable() && $this->isOnline() && $this->isWorkingToday();
    }

    /**
     * Get distance from branch in kilometers
     */
    private function getDistanceFromBranch(): ?float
    {
        if (!$this->current_latitude || !$this->current_longitude || !$this->relationLoaded('branch')) {
            return null;
        }

        if (!$this->branch->latitude || !$this->branch->longitude) {
            return null;
        }

        return $this->calculateDistance(
            $this->current_latitude,
            $this->current_longitude,
            $this->branch->latitude,
            $this->branch->longitude
        );
    }

    /**
     * Check if personnel is working today
     */
    private function isWorkingToday(): bool
    {
        if (!$this->working_hours) {
            return true; // Assume available if no working hours set
        }

        $today = strtolower(now()->format('l'));
        $workingHours = is_string($this->working_hours) 
            ? json_decode($this->working_hours, true) 
            : $this->working_hours;

        if (!isset($workingHours[$today])) {
            return false;
        }

        $todaySchedule = $workingHours[$today];
        if (!$todaySchedule['enabled']) {
            return false;
        }

        $currentTime = now()->format('H:i');
        return $currentTime >= $todaySchedule['start'] && $currentTime <= $todaySchedule['end'];
    }

    /**
     * Get current shift information
     */
    private function getCurrentShift(): ?array
    {
        if (!$this->working_hours) {
            return null;
        }

        $today = strtolower(now()->format('l'));
        $workingHours = is_string($this->working_hours) 
            ? json_decode($this->working_hours, true) 
            : $this->working_hours;

        if (!isset($workingHours[$today]) || !$workingHours[$today]['enabled']) {
            return null;
        }

        return $workingHours[$today];
    }

    /**
     * Get performance rating (mock implementation)
     */
    private function getPerformanceRating(): float
    {
        // This would typically be calculated from delivery reviews and performance metrics
        return 4.5; // Mock rating
    }

    /**
     * Get total deliveries completed today
     */
    private function getTotalDeliveriesToday(): int
    {
        return $this->deliveryAssignments()
            ->whereDate('delivered_at', today())
            ->where('status', 'delivered')
            ->count();
    }

    /**
     * Get earnings for today
     */
    private function getEarningsToday(): float
    {
        $deliveries = $this->deliveryAssignments()
            ->whereDate('delivered_at', today())
            ->where('status', 'delivered')
            ->get();

        $earnings = 0;
        foreach ($deliveries as $delivery) {
            // Calculate earnings based on commission rate
            $earnings += $delivery->delivery_fee * ($this->commission_rate / 100);
            
            // Add tips if any
            if ($delivery->deliveryTip) {
                $earnings += $delivery->deliveryTip->amount;
            }
        }

        return round($earnings, 2);
    }

    /**
     * Calculate distance between two coordinates
     */
    private function calculateDistance(float $lat1, float $lon1, float $lat2, float $lon2): float
    {
        $earthRadius = 6371; // Earth's radius in kilometers

        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);

        $a = sin($dLat / 2) * sin($dLat / 2) +
            cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
            sin($dLon / 2) * sin($dLon / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return round($earthRadius * $c, 2);
    }
}