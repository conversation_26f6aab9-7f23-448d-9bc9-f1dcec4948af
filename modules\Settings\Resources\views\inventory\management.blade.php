@extends('layouts.master')

@section('title', 'Inventory Management Settings')

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
@endpush

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-teal-600 to-cyan-600 rounded-lg shadow-lg p-6 mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                            <i class="fas fa-boxes text-white text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <h1 class="text-2xl font-bold text-white">Inventory Management Settings</h1>
                        <p class="text-teal-100">Configure stock tracking, alerts, and inventory policies</p>
                    </div>
                </div>
            </div>
            <div class="mt-4 md:mt-0">
                <button class="px-4 py-2 bg-white text-teal-600 rounded-lg hover:bg-teal-50 transition-colors duration-200 font-medium">
                    <i class="fas fa-sync mr-2"></i>
                    Sync Inventory
                </button>
            </div>
        </div>
    </div>

    <!-- Inventory Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-check-circle text-green-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">In Stock Items</p>
                    <p class="text-2xl font-bold text-gray-900">247</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-exclamation-triangle text-yellow-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Low Stock Items</p>
                    <p class="text-2xl font-bold text-gray-900">12</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-times-circle text-red-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Out of Stock</p>
                    <p class="text-2xl font-bold text-gray-900">3</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-dollar-sign text-blue-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Value</p>
                    <p class="text-2xl font-bold text-gray-900">$45,230</p>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Stock Tracking Settings -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-chart-line mr-2 text-teal-600"></i>
                Stock Tracking Settings
            </h3>
            
            <form class="space-y-4">
                <div>
                    <label for="tracking_method" class="block text-sm font-medium text-gray-700 mb-2">
                        Tracking Method
                    </label>
                    <select id="tracking_method" name="tracking_method" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500">
                        <option value="automatic" selected>Automatic (Real-time)</option>
                        <option value="manual">Manual Updates</option>
                        <option value="hybrid">Hybrid (Auto + Manual)</option>
                    </select>
                </div>

                <div>
                    <label for="stock_unit" class="block text-sm font-medium text-gray-700 mb-2">
                        Default Stock Unit
                    </label>
                    <select id="stock_unit" name="stock_unit" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500">
                        <option value="pieces" selected>Pieces</option>
                        <option value="kg">Kilograms</option>
                        <option value="liters">Liters</option>
                        <option value="boxes">Boxes</option>
                        <option value="portions">Portions</option>
                    </select>
                </div>

                <div>
                    <label for="cost_method" class="block text-sm font-medium text-gray-700 mb-2">
                        Cost Calculation Method
                    </label>
                    <select id="cost_method" name="cost_method" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500">
                        <option value="fifo" selected>FIFO (First In, First Out)</option>
                        <option value="lifo">LIFO (Last In, First Out)</option>
                        <option value="average">Weighted Average</option>
                        <option value="standard">Standard Cost</option>
                    </select>
                </div>

                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div>
                            <label for="auto_deduct" class="text-sm font-medium text-gray-700">Auto Deduct Stock</label>
                            <p class="text-sm text-gray-500">Automatically reduce stock on order completion</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="auto_deduct" name="auto_deduct" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-teal-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="track_expiry" class="text-sm font-medium text-gray-700">Track Expiry Dates</label>
                            <p class="text-sm text-gray-500">Monitor product expiration dates</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="track_expiry" name="track_expiry" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-teal-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="batch_tracking" class="text-sm font-medium text-gray-700">Batch/Lot Tracking</label>
                            <p class="text-sm text-gray-500">Track items by batch or lot numbers</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="batch_tracking" name="batch_tracking" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-teal-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="negative_stock" class="text-sm font-medium text-gray-700">Allow Negative Stock</label>
                            <p class="text-sm text-gray-500">Allow orders when stock is insufficient</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="negative_stock" name="negative_stock" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-teal-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-600"></div>
                        </label>
                    </div>
                </div>

                <div class="pt-4">
                    <button type="submit" class="w-full px-4 py-2 text-sm font-medium text-white bg-teal-600 border border-transparent rounded-lg hover:bg-teal-700">
                        <i class="fas fa-save mr-2"></i>
                        Save Tracking Settings
                    </button>
                </div>
            </form>
        </div>

        <!-- Alert & Notification Settings -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-bell mr-2 text-orange-600"></i>
                Alert & Notification Settings
            </h3>
            
            <form class="space-y-4">
                <div>
                    <label for="low_stock_threshold" class="block text-sm font-medium text-gray-700 mb-2">
                        Low Stock Threshold (%)
                    </label>
                    <input type="number" id="low_stock_threshold" name="low_stock_threshold" value="20" min="1" max="50"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                    <p class="text-xs text-gray-500 mt-1">Alert when stock falls below this percentage</p>
                </div>

                <div>
                    <label for="critical_stock_threshold" class="block text-sm font-medium text-gray-700 mb-2">
                        Critical Stock Threshold (%)
                    </label>
                    <input type="number" id="critical_stock_threshold" name="critical_stock_threshold" value="5" min="1" max="20"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                    <p class="text-xs text-gray-500 mt-1">Critical alert when stock is extremely low</p>
                </div>

                <div>
                    <label for="expiry_warning_days" class="block text-sm font-medium text-gray-700 mb-2">
                        Expiry Warning (days before)
                    </label>
                    <input type="number" id="expiry_warning_days" name="expiry_warning_days" value="7" min="1" max="30"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                </div>

                <div>
                    <label for="alert_recipients" class="block text-sm font-medium text-gray-700 mb-2">
                        Alert Recipients
                    </label>
                    <textarea id="alert_recipients" name="alert_recipients" rows="3" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                              placeholder="<EMAIL>, <EMAIL>"><EMAIL>
<EMAIL></textarea>
                </div>

                <div class="space-y-3">
                    <h4 class="text-sm font-medium text-gray-900">Alert Types:</h4>
                    
                    <div class="flex items-center justify-between">
                        <div>
                            <label for="email_alerts" class="text-sm font-medium text-gray-700">Email Alerts</label>
                            <p class="text-sm text-gray-500">Send alerts via email</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="email_alerts" name="email_alerts" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="sms_alerts" class="text-sm font-medium text-gray-700">SMS Alerts</label>
                            <p class="text-sm text-gray-500">Send alerts via SMS</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="sms_alerts" name="sms_alerts" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="dashboard_alerts" class="text-sm font-medium text-gray-700">Dashboard Alerts</label>
                            <p class="text-sm text-gray-500">Show alerts on dashboard</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="dashboard_alerts" name="dashboard_alerts" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                        </label>
                    </div>
                </div>

                <div class="pt-4">
                    <button type="submit" class="w-full px-4 py-2 text-sm font-medium text-white bg-orange-600 border border-transparent rounded-lg hover:bg-orange-700">
                        <i class="fas fa-save mr-2"></i>
                        Save Alert Settings
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Supplier & Purchasing Settings -->
    <div class="mt-6 bg-white rounded-lg shadow-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fas fa-truck mr-2 text-blue-600"></i>
            Supplier & Purchasing Settings
        </h3>

        <form class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div class="space-y-4">
                <div>
                    <label for="auto_reorder" class="block text-sm font-medium text-gray-700 mb-2">
                        Auto Reorder Level
                    </label>
                    <select id="auto_reorder" name="auto_reorder" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="disabled">Disabled</option>
                        <option value="low_stock" selected>When Low Stock</option>
                        <option value="critical_stock">When Critical Stock</option>
                        <option value="out_of_stock">When Out of Stock</option>
                    </select>
                </div>

                <div>
                    <label for="default_supplier" class="block text-sm font-medium text-gray-700 mb-2">
                        Default Supplier
                    </label>
                    <select id="default_supplier" name="default_supplier" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Select Supplier</option>
                        <option value="supplier_1" selected>Fresh Foods Co.</option>
                        <option value="supplier_2">Metro Wholesale</option>
                        <option value="supplier_3">Quality Ingredients Ltd.</option>
                    </select>
                </div>

                <div>
                    <label for="purchase_order_prefix" class="block text-sm font-medium text-gray-700 mb-2">
                        Purchase Order Prefix
                    </label>
                    <input type="text" id="purchase_order_prefix" name="purchase_order_prefix" value="PO-" maxlength="10"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label for="lead_time_days" class="block text-sm font-medium text-gray-700 mb-2">
                        Default Lead Time (days)
                    </label>
                    <input type="number" id="lead_time_days" name="lead_time_days" value="3" min="1" max="30"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
            </div>

            <div class="space-y-4">
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div>
                            <label for="auto_approve_orders" class="text-sm font-medium text-gray-700">Auto Approve Orders</label>
                            <p class="text-sm text-gray-500">Automatically approve purchase orders</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="auto_approve_orders" name="auto_approve_orders" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="require_approval" class="text-sm font-medium text-gray-700">Require Manager Approval</label>
                            <p class="text-sm text-gray-500">Orders above threshold need approval</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="require_approval" name="require_approval" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="email_suppliers" class="text-sm font-medium text-gray-700">Email Orders to Suppliers</label>
                            <p class="text-sm text-gray-500">Automatically send orders via email</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="email_suppliers" name="email_suppliers" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                </div>

                <div>
                    <label for="approval_threshold" class="block text-sm font-medium text-gray-700 mb-2">
                        Approval Threshold ($)
                    </label>
                    <input type="number" id="approval_threshold" name="approval_threshold" value="500" min="0" step="0.01"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 class="text-sm font-medium text-blue-900 mb-2">Supplier Performance</h4>
                    <div class="space-y-2 text-sm text-blue-800">
                        <div class="flex justify-between">
                            <span>Fresh Foods Co.:</span>
                            <span class="font-medium">98% On-time</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Metro Wholesale:</span>
                            <span class="font-medium">95% On-time</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Quality Ingredients:</span>
                            <span class="font-medium">92% On-time</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2 pt-4">
                <button type="submit" class="px-6 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700">
                    <i class="fas fa-save mr-2"></i>
                    Save Supplier Settings
                </button>
            </div>
        </form>
    </div>

    <!-- Waste Management & Loss Prevention -->
    <div class="mt-6 bg-white rounded-lg shadow-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fas fa-trash-alt mr-2 text-red-600"></i>
            Waste Management & Loss Prevention
        </h3>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div class="space-y-4">
                <h4 class="font-medium text-gray-900">Waste Tracking</h4>
                
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div>
                            <label for="track_waste" class="text-sm font-medium text-gray-700">Track Waste</label>
                            <p class="text-sm text-gray-500">Monitor food waste and spoilage</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="track_waste" name="track_waste" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="require_waste_reason" class="text-sm font-medium text-gray-700">Require Waste Reason</label>
                            <p class="text-sm text-gray-500">Mandatory reason for waste entries</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="require_waste_reason" name="require_waste_reason" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                        </label>
                    </div>
                </div>

                <div>
                    <label for="waste_threshold" class="block text-sm font-medium text-gray-700 mb-2">
                        Waste Alert Threshold (%)
                    </label>
                    <input type="number" id="waste_threshold" name="waste_threshold" value="5" min="1" max="20"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500">
                </div>
            </div>

            <div class="space-y-4">
                <h4 class="font-medium text-gray-900">Loss Prevention</h4>
                
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div>
                            <label for="variance_tracking" class="text-sm font-medium text-gray-700">Variance Tracking</label>
                            <p class="text-sm text-gray-500">Track inventory discrepancies</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="variance_tracking" name="variance_tracking" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="require_manager_approval" class="text-sm font-medium text-gray-700">Manager Approval for Adjustments</label>
                            <p class="text-sm text-gray-500">Large adjustments need approval</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="require_manager_approval" name="require_manager_approval" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                        </label>
                    </div>
                </div>

                <div>
                    <label for="variance_threshold" class="block text-sm font-medium text-gray-700 mb-2">
                        Variance Alert Threshold ($)
                    </label>
                    <input type="number" id="variance_threshold" name="variance_threshold" value="50" min="1" step="0.01"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500">
                </div>
            </div>

            <div class="space-y-4">
                <h4 class="font-medium text-gray-900">Current Month Stats</h4>
                
                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="space-y-3 text-sm">
                        <div class="flex justify-between">
                            <span class="text-red-700">Total Waste:</span>
                            <span class="font-medium text-red-900">$247.50</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-red-700">Waste Percentage:</span>
                            <span class="font-medium text-red-900">2.8%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-red-700">Inventory Variance:</span>
                            <span class="font-medium text-red-900">$89.20</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-red-700">Top Waste Item:</span>
                            <span class="font-medium text-red-900">Lettuce</span>
                        </div>
                    </div>
                </div>

                <button class="w-full px-3 py-2 text-sm font-medium text-red-700 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100">
                    <i class="fas fa-chart-bar mr-2"></i>
                    View Detailed Report
                </button>
            </div>
        </div>

        <div class="mt-6 flex justify-end">
            <button class="px-6 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-lg hover:bg-red-700">
                <i class="fas fa-save mr-2"></i>
                Save Waste Management Settings
            </button>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Form submission handlers
    $('form').on('submit', function(e) {
        e.preventDefault();
        
        Swal.fire({
            icon: 'success',
            title: 'Success!',
            text: 'Inventory settings updated successfully.',
            timer: 2000,
            showConfirmButton: false
        });
    });

    // Sync inventory handler
    $('button:contains("Sync Inventory")').on('click', function() {
        Swal.fire({
            title: 'Sync Inventory',
            text: 'This will synchronize inventory data across all systems.',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Sync Now',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                Swal.fire({
                    title: 'Syncing Inventory...',
                    text: 'Please wait while we sync your inventory data.',
                    icon: 'info',
                    showConfirmButton: false,
                    timer: 3000
                }).then(() => {
                    Swal.fire({
                        icon: 'success',
                        title: 'Sync Complete',
                        text: 'Inventory data synchronized successfully.',
                        timer: 2000,
                        showConfirmButton: false
                    });
                });
            }
        });
    });

    // Threshold validation
    $('#low_stock_threshold, #critical_stock_threshold').on('input', function() {
        const lowThreshold = parseInt($('#low_stock_threshold').val()) || 0;
        const criticalThreshold = parseInt($('#critical_stock_threshold').val()) || 0;
        
        if (criticalThreshold >= lowThreshold) {
            Swal.fire({
                icon: 'warning',
                title: 'Invalid Threshold',
                text: 'Critical stock threshold must be lower than low stock threshold.',
                timer: 3000,
                showConfirmButton: false
            });
        }
    });

    // Cost method change handler
    $('#cost_method').on('change', function() {
        const method = $(this).val();
        let description = '';
        
        switch(method) {
            case 'fifo':
                description = 'First items purchased are the first to be sold.';
                break;
            case 'lifo':
                description = 'Last items purchased are the first to be sold.';
                break;
            case 'average':
                description = 'Cost is calculated using weighted average of all purchases.';
                break;
            case 'standard':
                description = 'Uses predetermined standard costs for all items.';
                break;
        }
        
        if (description) {
            Swal.fire({
                title: 'Cost Method: ' + method.toUpperCase(),
                text: description,
                icon: 'info',
                timer: 3000,
                showConfirmButton: false
            });
        }
    });

    // View detailed report handler
    $('button:contains("View Detailed Report")').on('click', function() {
        Swal.fire({
            title: 'Waste Management Report',
            text: 'This will open the detailed waste management and loss prevention report.',
            icon: 'info',
            confirmButtonText: 'Open Report'
        });
    });

    // Auto reorder level change handler
    $('#auto_reorder').on('change', function() {
        const level = $(this).val();
        if (level !== 'disabled') {
            Swal.fire({
                title: 'Auto Reorder Enabled',
                text: `Purchase orders will be automatically created when stock reaches ${level.replace('_', ' ')} level.`,
                icon: 'info',
                timer: 3000,
                showConfirmButton: false
            });
        }
    });
});
</script>
@endpush