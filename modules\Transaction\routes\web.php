<?php

use Illuminate\Support\Facades\Route;
use Modules\Transaction\Http\Controllers\Web\TransactionWebController;
use Modules\Transaction\Http\Controllers\Web\PaymentWebController;

/*
|--------------------------------------------------------------------------
| Transaction Module Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for the Transaction module.
| These routes are loaded by the TransactionServiceProvider within a group
| which is assigned the "web" middleware group.
|
*/

Route::prefix('transactions')->name('transactions.')->group(function () {
    // Transaction management routes
    Route::get('/', [TransactionWebController::class, 'index'])->name('index');
    Route::get('/create', [TransactionWebController::class, 'create'])->name('create');
    Route::post('/', [TransactionWebController::class, 'store'])->name('store');
    Route::get('/statistics', [TransactionWebController::class, 'statistics'])->name('statistics');
    Route::get('/due', [TransactionWebController::class, 'due'])->name('due');
    Route::get('/{id}', [TransactionWebController::class, 'show'])->name('show');
    Route::get('/{id}/edit', [TransactionWebController::class, 'edit'])->name('edit');
    Route::put('/{id}', [TransactionWebController::class, 'update'])->name('update');
    Route::delete('/{id}', [TransactionWebController::class, 'destroy'])->name('destroy');
    Route::patch('/{id}/status', [TransactionWebController::class, 'updateStatus'])->name('update-status');
});

Route::prefix('payments')->middleware(['auth', 'web'])->name('payments.')->group(function () {
    // Payment management routes
    Route::get('/', [PaymentWebController::class, 'index'])->name('index');
    Route::get('/create', [PaymentWebController::class, 'create'])->name('create');
    Route::post('/', [PaymentWebController::class, 'store'])->name('store');
    Route::get('/methods', [PaymentWebController::class, 'methods'])->name('methods');
    Route::get('/refunds', [PaymentWebController::class, 'refunds'])->name('refunds');
    Route::get('/statistics', [PaymentWebController::class, 'statistics'])->name('statistics');
    Route::get('/transaction/{transactionId}', [PaymentWebController::class, 'byTransaction'])->name('by-transaction');
    Route::get('/{id}', [PaymentWebController::class, 'show'])->name('show');
    Route::patch('/{id}/cancel', [PaymentWebController::class, 'cancel'])->name('cancel');
    Route::patch('/{id}/refund', [PaymentWebController::class, 'refund'])->name('refund');
    
    // Payment methods CRUD routes
    Route::post('/methods', [PaymentWebController::class, 'storePaymentMethod'])->name('methods.store');
    Route::get('/methods/{id}', [PaymentWebController::class, 'showPaymentMethod'])->name('methods.show');
    Route::put('/methods/{id}', [PaymentWebController::class, 'updatePaymentMethod'])->name('methods.update');
    Route::patch('/methods/{id}/toggle-status', [PaymentWebController::class, 'togglePaymentMethodStatus'])->name('methods.toggle-status');
    Route::delete('/methods/{id}', [PaymentWebController::class, 'destroyPaymentMethod'])->name('methods.destroy');
});
