<?php

namespace Modules\HR\Services;

use App\Models\User;
use App\Models\StaffAttendance;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Exception;

class AttendanceService
{
    /**
     * Check in staff member
     */
    public function checkIn(int $tenantId, int $userId, array $data = []): StaffAttendance
    {
        $today = Carbon::today();
        
        $attendance = StaffAttendance::firstOrCreate(
            [
                'tenant_id' => $tenantId,
                'user_id' => $userId,
                'date' => $today,
            ],
            [
                'branch_id' => $data['branch_id'] ?? null,
                'status' => 'absent',
            ]
        );

        $attendance->checkIn(
            $data['location'] ?? null,
            $data['ip_address'] ?? request()->ip()
        );

        return $attendance;
    }

    /**
     * Check out staff member
     */
    public function checkOut(int $tenantId, int $userId, array $data = []): ?StaffAttendance
    {
        $today = Carbon::today();
        
        $attendance = StaffAttendance::where('tenant_id', $tenantId)
                                   ->where('user_id', $userId)
                                   ->where('date', $today)
                                   ->first();

        if (!$attendance) {
            return null;
        }

        $attendance->checkOut(
            $data['location'] ?? null,
            $data['ip_address'] ?? request()->ip()
        );

        return $attendance;
    }

    /**
     * Get attendance records with filters
     */
    public function getAttendance(int $tenantId, array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = StaffAttendance::where('tenant_id', $tenantId)
                               ->with(['user', 'branch']);

        $this->applyFilters($query, $filters);

        return $query->orderBy('date', 'desc')
                    ->orderBy('check_in_time', 'desc')
                    ->paginate($perPage);
    }

    /**
     * Get attendance statistics - Optimized to use single query
     */
    public function getAttendanceStats(int $tenantId, string $startDate, string $endDate, ?int $branchId = null): array
    {
        $query = StaffAttendance::where('tenant_id', $tenantId)
                               ->whereBetween('date', [$startDate, $endDate]);

        if ($branchId) {
            $query->where('branch_id', $branchId);
        }

        // Use single query with aggregation to avoid multiple database calls
        $stats = $query->selectRaw('
            COUNT(*) as total_records,
            SUM(CASE WHEN status = "present" THEN 1 ELSE 0 END) as present_count,
            SUM(CASE WHEN status = "absent" THEN 1 ELSE 0 END) as absent_count,
            SUM(CASE WHEN status = "leave" THEN 1 ELSE 0 END) as leave_count,
            SUM(CASE WHEN late_minutes > 0 THEN 1 ELSE 0 END) as late_count,
            SUM(CASE WHEN overtime_minutes > 0 THEN 1 ELSE 0 END) as overtime_count
        ')->first();

        $totalRecords = $stats->total_records ?? 0;
        $presentCount = $stats->present_count ?? 0;

        return [
            'total_records' => $totalRecords,
            'present_count' => $presentCount,
            'absent_count' => $stats->absent_count ?? 0,
            'leave_count' => $stats->leave_count ?? 0,
            'late_count' => $stats->late_count ?? 0,
            'overtime_count' => $stats->overtime_count ?? 0,
            'attendance_rate' => $totalRecords > 0 ? round(($presentCount / $totalRecords) * 100, 2) : 0,
        ];
    }

    /**
     * Mark attendance for a staff member
     */
    public function markAttendance(int $userId, string $date, string $status, ?string $checkInTime = null, ?string $checkOutTime = null): StaffAttendance
    {
        $user = User::findOrFail($userId);
        $attendanceDate = Carbon::parse($date);

        return StaffAttendance::updateOrCreate(
            [
                'tenant_id' => $user->tenant_id,
                'user_id' => $userId,
                'date' => $attendanceDate,
            ],
            [
                'status' => $status,
                'check_in_time' => $checkInTime ? Carbon::parse($checkInTime) : null,
                'check_out_time' => $checkOutTime ? Carbon::parse($checkOutTime) : null,
                'branch_id' => $user->branch_id,
            ]
        );
    }

    /**
     * Process bulk attendance
     */
    public function processBulkAttendance(int $tenantId, array $attendanceData): array
    {
        $processed = [];
        $errors = [];

        DB::beginTransaction();
        try {
            foreach ($attendanceData as $data) {
                try {
                    $attendance = $this->markAttendance(
                        $data['user_id'],
                        $data['date'],
                        $data['status'],
                        $data['check_in_time'] ?? null,
                        $data['check_out_time'] ?? null
                    );
                    $processed[] = $attendance;
                } catch (Exception $e) {
                    $errors[] = [
                        'user_id' => $data['user_id'],
                        'error' => $e->getMessage()
                    ];
                }
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }

        return [
            'processed' => count($processed),
            'errors' => $errors,
            'success_rate' => count($attendanceData) > 0 ? (count($processed) / count($attendanceData)) * 100 : 0
        ];
    }

    /**
     * Apply filters to attendance query
     */
    private function applyFilters($query, array $filters): void
    {
        if (!empty($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        if (!empty($filters['branch_id'])) {
            $query->where('branch_id', $filters['branch_id']);
        }

        if (!empty($filters['date_from'])) {
            $query->where('date', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->where('date', '<=', $filters['date_to']);
        }

        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }
    }
}