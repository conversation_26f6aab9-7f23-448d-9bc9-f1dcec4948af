<?php

namespace Modules\Tenant\Services;

use App\Models\Tenant;
use App\Models\TenantSubscription;
use App\Models\SubscriptionPlan;
use App\Models\Order;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Modules\Transaction\Services\TransactionService;
use Modules\Transaction\Services\PaymentService;
use Modules\Transaction\Models\PaymentMethod;
use Modules\Transaction\Models\Transaction;
use Modules\Transaction\Models\Payment;

class SubscriptionService
{
    protected $transactionService;
    protected $paymentService;

    public function __construct(
        TransactionService $transactionService,
        PaymentService $paymentService
    ) {
        $this->transactionService = $transactionService;
        $this->paymentService = $paymentService;
    }

    /**
     * Create a new subscription
     */
    public function createSubscription(array $data): TenantSubscription
    {
        $plan = SubscriptionPlan::findOrFail($data['plan_id']);
        $tenant = Tenant::findOrFail($data['tenant_id']);

        // Cancel any existing active subscriptions
        $this->cancelExistingSubscriptions($tenant);

        // Set default values
        $subscriptionData = array_merge([
            'status' => 'trial',
            'start_date' => Carbon::now()->format('Y-m-d'),
            'trial_days' => $data['trial_days'] ?? 14,
            'billing_cycle' => $data['billing_cycle'] ?? 'monthly',
            'auto_renew' => $data['auto_renew'] ?? true,
            'discount_percentage' => $data['discount_percentage'] ?? 0,
        ], $data);

        // Calculate end date and next billing date
        if ($subscriptionData['status'] === 'trial') {
            $subscriptionData['end_date'] = Carbon::now()->addDays($subscriptionData['trial_days'])->format('Y-m-d');
            $subscriptionData['next_billing_date'] = $subscriptionData['end_date'];
        } else {
            $subscriptionData['next_billing_date'] = $this->calculateNextBillingDate(
                $subscriptionData['start_date'],
                $subscriptionData['billing_cycle']
            )->format('Y-m-d');
        }

        // Calculate total amount based on the selected billing cycle
        if ($subscriptionData['billing_cycle'] === 'yearly') {
            $subscriptionData['total_amount'] = $plan->getYearlyPrice();
        } else {
            $subscriptionData['total_amount'] = $plan->getMonthlyPrice();
        }

        // Apply discount if any
        if ($subscriptionData['discount_percentage'] > 0) {
            $subscriptionData['total_amount'] = $subscriptionData['total_amount'] * (1 - $subscriptionData['discount_percentage'] / 100);
        }

        $subscription = TenantSubscription::create($subscriptionData);

        // Create order, transaction, and payment if payment method is provided
        if (isset($data['payment_method']) && $subscriptionData['status'] !== 'trial') {
            $this->createSubscriptionPayment($subscription, $data);
        }

        return $subscription;
    }

    /**
     * Create transaction and payment for subscription
     */
    protected function createSubscriptionPayment(TenantSubscription $subscription, array $data): void
    {
        $plan = $subscription->plan;
        $amount = $subscription->total_amount;

        try {
            // Get payment method (prefer manual, fallback to cash, then any active)
            $paymentMethod = PaymentMethod::where('code', 'manual')->where('is_active', true)->first()
                ?? PaymentMethod::where('code', 'cash')->where('is_active', true)->first()
                ?? PaymentMethod::where('is_active', true)->first();

            if (!$paymentMethod) {
                throw new \Exception('No active payment method found');
            }

            // Create transaction directly using the new standalone method or fallback to manual creation
            if ($this->transactionService) {
                $transaction = $this->transactionService->createStandaloneTransaction([
                    'total_amount' => $amount,
                    'paid_amount' => $amount,
                    'status' => 'paid',
                    'notes' => "Subscription payment for subscription ID: {$subscription->id}",
                ]);
            } else {
                // Fallback: Create transaction manually
                $transactionService = app(TransactionService::class);
                $transaction = $transactionService->createStandaloneTransaction([
                    'total_amount' => $amount,
                    'paid_amount' => $amount,
                    'status' => 'paid',
                    'notes' => "Subscription payment for subscription ID: {$subscription->id}",
                ]);
            }

            // Create payment with payment number
            Payment::create([
                'transaction_id' => $transaction->id,
                'payment_method_id' => $paymentMethod->id,
                'payment_number' => $this->generatePaymentNumber(),
                'amount' => $amount,
                'status' => 'completed',
                'payment_date' => now(),
                'notes' => "Subscription payment for subscription ID: {$subscription->id}",
                'processed_by' => auth()->id(),
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to create subscription payment', [
                'subscription_id' => $subscription->id,
                'amount' => $amount,
                'error' => $e->getMessage()
            ]);
            throw new \Exception('Failed to create subscription payment: ' . $e->getMessage());
        }
    }

    /**
     * Update subscription
     */
    public function updateSubscription(TenantSubscription $subscription, array $data): TenantSubscription
    {
        // If billing cycle is changed, recalculate next billing date
        if (isset($data['billing_cycle']) && $data['billing_cycle'] !== $subscription->billing_cycle) {
            $data['next_billing_date'] = $this->calculateNextBillingDate(
                Carbon::now(),
                $data['billing_cycle']
            );
        }

        $subscription->update($data);
        return $subscription->fresh();
    }

    /**
     * Cancel subscription
     */
    public function cancelSubscription(TenantSubscription $subscription): TenantSubscription
    {
        $subscription->update([
            'status' => 'cancelled',
            'cancelled_at' => Carbon::now(),
            'cancellation_reason' => 'Manual cancellation'
        ]);

        return $subscription->fresh();
    }

    /**
     * Suspend subscription
     */
    public function suspendSubscription(TenantSubscription $subscription): TenantSubscription
    {
        $subscription->update([
            'status' => 'suspended',
            'suspended_at' => Carbon::now()
        ]);

        return $subscription->fresh();
    }

    /**
     * Reactivate subscription
     */
    public function reactivateSubscription(TenantSubscription $subscription): TenantSubscription
    {
        $subscription->update([
            'status' => 'active',
            'suspended_at' => null,
            'next_billing_date' => $this->calculateNextBillingDate(
                Carbon::now(),
                $subscription->billing_cycle
            )
        ]);

        return $subscription->fresh();
    }

    /**
     * Upgrade subscription to a new plan
     */
    public function upgradeSubscription(TenantSubscription $subscription, int $newPlanId): TenantSubscription
    {
        $newPlan = SubscriptionPlan::findOrFail($newPlanId);
        $oldPlan = $subscription->plan;

        // Calculate prorated amount if needed
        $proratedAmount = $this->calculateProratedAmount($subscription, $newPlan);

        $subscription->update([
            'plan_id' => $newPlanId,
            'upgraded_at' => Carbon::now(),
            'prorated_amount' => $proratedAmount,
            'previous_plan_id' => $oldPlan->id
        ]);

        return $subscription->fresh();
    }

    /**
     * Process subscription renewal
     */
    public function renewSubscription(TenantSubscription $subscription): TenantSubscription
    {
        $nextBillingDate = $this->calculateNextBillingDate(
            $subscription->next_billing_date,
            $subscription->billing_cycle
        );

        $subscription->update([
            'status' => 'active',
            'next_billing_date' => $nextBillingDate,
        ]);

        return $subscription->fresh();
    }

    /**
     * Convert trial to paid subscription
     */
    public function convertTrialToPaid(TenantSubscription $subscription): TenantSubscription
    {
        $nextBillingDate = $this->calculateNextBillingDate(
            Carbon::now(),
            $subscription->billing_cycle
        );

        $subscription->update([
            'status' => 'active',
            'end_date' => null, // Remove trial end date
            'next_billing_date' => $nextBillingDate,
        ]);

        return $subscription->fresh();
    }

    /**
     * Get subscription usage statistics
     */
    public function getSubscriptionUsage(TenantSubscription $subscription): array
    {
        $tenant = $subscription->tenant;
        $plan = $subscription->plan;

        $usage = [
            'plan_limits' => [
                'max_branches' => $plan->max_branches,
                'max_users' => $plan->max_users,
                'max_menu_items' => $plan->max_menu_items,
                'max_orders_per_month' => $plan->max_orders_per_month,
            ],
            'current_usage' => [
                'branches' => $tenant->branches()->count(),
                'users' => $tenant->users()->count(),
                'menu_items' => $tenant->menuItems()->count(),
                'monthly_orders' => $this->getMonthlyOrderCount($tenant),
            ],
            'usage_percentage' => [],
            'features' => $plan->features ?? [],
        ];

        // Calculate usage percentages
        foreach ($usage['plan_limits'] as $key => $limit) {
            $currentKey = str_replace('max_', '', $key);
            if ($currentKey === 'orders_per_month') {
                $currentKey = 'monthly_orders';
            }
            
            if (isset($usage['current_usage'][$currentKey]) && $limit > 0) {
                $usage['usage_percentage'][$currentKey] = round(
                    ($usage['current_usage'][$currentKey] / $limit) * 100,
                    2
                );
            }
        }

        return $usage;
    }

    /**
     * Check if subscription is expired
     */
    public function isExpired(TenantSubscription $subscription): bool
    {
        if ($subscription->status === 'trial') {
            return $subscription->end_date && Carbon::now()->isAfter($subscription->end_date);
        }

        return $subscription->next_billing_date && Carbon::now()->isAfter($subscription->next_billing_date);
    }

    /**
     * Get expiring subscriptions
     */
    public function getExpiringSubscriptions(int $daysAhead = 7): \Illuminate\Database\Eloquent\Collection
    {
        $expirationDate = Carbon::now()->addDays($daysAhead);

        return TenantSubscription::where('status', 'active')
            ->where('next_billing_date', '<=', $expirationDate)
            ->with(['tenant', 'plan'])
            ->get();
    }

    /**
     * Calculate next billing date
     */
    protected function calculateNextBillingDate($currentDate, string $billingCycle): Carbon
    {
        $date = is_string($currentDate) ? Carbon::parse($currentDate) : $currentDate;

        switch ($billingCycle) {
            case 'monthly':
                return $date->addMonth();
            case 'yearly':
                return $date->addYear();
            case 'quarterly':
                return $date->addMonths(3);
            default:
                return $date->addMonth();
        }
    }

    /**
     * Calculate prorated amount for plan upgrade
     */
    protected function calculateProratedAmount(TenantSubscription $subscription, SubscriptionPlan $newPlan): float
    {
        $oldPlan = $subscription->plan;
        $currentPeriodStart = Carbon::parse($subscription->start_date);
        $currentPeriodEnd = Carbon::parse($subscription->next_billing_date);
        $now = Carbon::now();

        // Calculate remaining days in current period
        $totalDays = $currentPeriodStart->diffInDays($currentPeriodEnd);
        $remainingDays = $now->diffInDays($currentPeriodEnd);

        if ($totalDays <= 0) {
            return 0;
        }

        // Calculate prorated amounts using the correct price methods
        $oldPlanPrice = $subscription->billing_cycle === 'yearly' ? $oldPlan->getYearlyPrice() : $oldPlan->getMonthlyPrice();
        $newPlanPrice = $subscription->billing_cycle === 'yearly' ? $newPlan->getYearlyPrice() : $newPlan->getMonthlyPrice();

        $oldPlanProrated = ($oldPlanPrice / $totalDays) * $remainingDays;
        $newPlanProrated = ($newPlanPrice / $totalDays) * $remainingDays;

        return max(0, $newPlanProrated - $oldPlanProrated);
    }

    /**
     * Cancel existing active subscriptions for tenant
     */
    protected function cancelExistingSubscriptions(Tenant $tenant): void
    {
        $tenant->subscriptions()
            ->whereIn('status', ['active', 'trial'])
            ->update([
                'status' => 'cancelled',
                'auto_renew' => false
            ]);
    }

    /**
     * Get monthly order count for tenant
     */
    protected function getMonthlyOrderCount(Tenant $tenant): int
    {
        $currentMonth = Carbon::now()->startOfMonth();
        
        return $tenant->orders()
            ->where('created_at', '>=', $currentMonth)
            ->count();
    }

    /**
     * Get subscription billing history
     */
    public function getBillingHistory(TenantSubscription $subscription): array
    {
        // This would typically integrate with a billing system
        // For now, return basic subscription events
        return [
            'subscription_created' => $subscription->created_at,
            'trial_started' => $subscription->start_date,
            'trial_ends' => $subscription->end_date,
            'last_billing' => $subscription->start_date,
            'next_billing' => $subscription->next_billing_date,
            'status' => $subscription->status,
            'billing_cycle' => $subscription->billing_cycle,
            'total_amount' => $subscription->total_amount,
        ];
    }

    /**
     * Generate unique payment number.
     */
    protected function generatePaymentNumber(): string
    {
        $prefix = 'PAY';
        $timestamp = Carbon::now()->format('YmdHis');
        $random = str_pad(mt_rand(1, 999), 3, '0', STR_PAD_LEFT);
        
        return $prefix . $timestamp . $random;
    }
}