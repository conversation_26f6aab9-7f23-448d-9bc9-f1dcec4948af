<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TenantSubscription extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'plan_id',
        'status',
        'start_date',
        'end_date',
        'trial_days',
        'billing_cycle',
        'next_billing_date',
        'auto_renew',
        'discount_percentage',
        'total_amount',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'start_date' => 'date',
            'end_date' => 'date',
            'next_billing_date' => 'date',
            'auto_renew' => 'boolean',
            'discount_percentage' => 'decimal:2',
            'total_amount' => 'decimal:2',
        ];
    }

    // Relationships
    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    public function subscriptionPlan()
    {
        return $this->belongsTo(SubscriptionPlan::class, 'plan_id');
    }

    public function plan()
    {
        return $this->belongsTo(SubscriptionPlan::class, 'plan_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->where('end_date', '>', now());
    }

    public function scopeExpired($query)
    {
        return $query->where('end_date', '<=', now());
    }

    public function scopeTrialing($query)
    {
        return $query->where('status', 'trial');
    }

    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeAutoRenewing($query)
    {
        return $query->where('auto_renew', true);
    }

    public function scopeExpiringIn($query, $days)
    {
        return $query->where('end_date', '<=', now()->addDays($days))
                    ->where('end_date', '>', now());
    }

    // Methods
    public function isActive()
    {
        return $this->status === 'active' && $this->end_date > now();
    }

    public function isTrialing()
    {
        return $this->status === 'trial';
    }

    public function isExpired()
    {
        return $this->end_date <= now();
    }

    public function isCancelled()
    {
        return $this->status === 'cancelled';
    }

    public function isGracePeriod()
    {
        return $this->status === 'past_due' && $this->end_date > now();
    }

    public function daysUntilExpiry()
    {
        if ($this->end_date <= now()) {
            return 0;
        }

        return now()->diffInDays($this->end_date);
    }

    public function daysUntilTrialExpiry()
    {
        if ($this->status !== 'trial' || $this->end_date <= now()) {
            return 0;
        }

        return now()->diffInDays($this->end_date);
    }

    public function cancel($reason = null)
    {
        $this->update([
            'status' => 'cancelled',
            'auto_renew' => false,
        ]);
    }

    public function renew($duration = null)
    {
        $duration = $duration ?? $this->getBillingCycleDuration();
        
        $this->update([
            'status' => 'active',
            'end_date' => now()->add($duration),
            'next_billing_date' => now()->add($duration),
        ]);
    }

    public function suspend()
    {
        $this->update([
            'status' => 'suspended',
        ]);
    }

    public function reactivate()
    {
        $this->update([
            'status' => 'active',
        ]);
    }

    private function getBillingCycleDuration()
    {
        switch ($this->billing_cycle) {
            case 'monthly':
                return '1 month';
            case 'quarterly':
                return '3 months';
            case 'yearly':
                return '1 year';
            default:
                return '1 month';
        }
    }

    public function hasFeature($feature)
    {
        return $this->subscriptionPlan->hasFeature($feature);
    }

    public function withinLimit($limitation, $currentUsage)
    {
        $limit = $this->subscriptionPlan->getLimitValue($limitation);
        
        if ($limit === null) {
            return true; // No limit
        }
        
        return $currentUsage < $limit;
    }
}