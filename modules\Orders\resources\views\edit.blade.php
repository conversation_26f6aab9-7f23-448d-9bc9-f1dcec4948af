@extends('layouts.master')

@section('css')
<link href="{{URL::asset('assets/plugins/select2/css/select2.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/sweet-alert/sweetalert.css')}}" rel="stylesheet">
@endsection

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">تعديل الطلب #{{ $order->order_number ?? 'ORD-001' }}</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ الطلبات / تعديل</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <a href="{{ route('orders.show', $order->id ?? 1) }}" class="btn btn-secondary btn-icon ml-2">
                <i class="mdi mdi-arrow-left"></i>
            </a>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">تعديل تفاصيل الطلب</h3>
                </div>
                <div class="card-body">
                    <form id="orderForm" method="POST" action="{{ route('orders.update', $order->id ?? 1) }}">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <!-- Customer Selection -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">العميل</label>
                                    <select class="form-control select2" name="customer_id" id="customer_id">
                                        <option value="">اختر العميل</option>
                                        @foreach($customers ?? [] as $customer)
                                            <option value="{{ $customer->id }}" {{ ($order->customer_id ?? '') == $customer->id ? 'selected' : '' }}>
                                                {{ $customer->name }} - {{ $customer->phone }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <!-- Table Selection -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">الطاولة</label>
                                    <select class="form-control select2" name="table_id" id="table_id">
                                        <option value="">اختر الطاولة</option>
                                        @foreach($tables ?? [] as $table)
                                            <option value="{{ $table->id }}" {{ ($order->table_id ?? '') == $table->id ? 'selected' : '' }}>
                                                {{ $table->name }} - {{ $table->capacity }} أشخاص
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Order Type -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">نوع الطلب <span class="text-danger">*</span></label>
                                    <select class="form-control select2" name="order_type" id="order_type" required>
                                        <option value="">اختر نوع الطلب</option>
                                        <option value="dine_in" {{ ($order->order_type ?? '') == 'dine_in' ? 'selected' : '' }}>تناول في المطعم</option>
                                        <option value="takeaway" {{ ($order->order_type ?? '') == 'takeaway' ? 'selected' : '' }}>طلب خارجي</option>
                                        <option value="delivery" {{ ($order->order_type ?? '') == 'delivery' ? 'selected' : '' }}>توصيل</option>
                                        <option value="online" {{ ($order->order_type ?? '') == 'online' ? 'selected' : '' }}>طلب أونلاين</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Status -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">حالة الطلب</label>
                                    <select class="form-control select2" name="status" id="status">
                                        <option value="pending" {{ ($order->status ?? '') == 'pending' ? 'selected' : '' }}>معلق</option>
                                        <option value="confirmed" {{ ($order->status ?? '') == 'confirmed' ? 'selected' : '' }}>مؤكد</option>
                                        <option value="preparing" {{ ($order->status ?? '') == 'preparing' ? 'selected' : '' }}>قيد التحضير</option>
                                        <option value="ready" {{ ($order->status ?? '') == 'ready' ? 'selected' : '' }}>جاهز</option>
                                        <option value="served" {{ ($order->status ?? '') == 'served' ? 'selected' : '' }}>تم التقديم</option>
                                        <option value="completed" {{ ($order->status ?? '') == 'completed' ? 'selected' : '' }}>مكتمل</option>
                                        <option value="cancelled" {{ ($order->status ?? '') == 'cancelled' ? 'selected' : '' }}>ملغي</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Special Instructions -->
                            <div class="col-12">
                                <div class="form-group">
                                    <label class="form-label">ملاحظات خاصة</label>
                                    <textarea class="form-control" name="special_instructions" id="special_instructions" rows="3" placeholder="أي ملاحظات أو تعليمات خاصة للطلب">{{ $order->special_instructions ?? '' }}</textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Order Items Section -->
                        <div class="card mt-4">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">عناصر الطلب</h5>
                                <button type="button" class="btn btn-primary btn-sm" id="add-item-btn">
                                    <i class="mdi mdi-plus"></i> إضافة عنصر
                                </button>
                            </div>
                            <div class="card-body">
                                <div id="order-items-container">
                                    <!-- Order items will be loaded here -->
                                </div>
                                
                                <!-- Order Summary -->
                                <div class="row mt-4">
                                    <div class="col-md-6 ml-auto">
                                        <table class="table table-sm">
                                            <tr>
                                                <td><strong>المجموع الفرعي:</strong></td>
                                                <td class="text-right"><span id="subtotal">$0.00</span></td>
                                            </tr>
                                            <tr>
                                                <td><strong>الضريبة:</strong></td>
                                                <td class="text-right"><span id="tax">$0.00</span></td>
                                            </tr>
                                            <tr>
                                                <td><strong>الخصم:</strong></td>
                                                <td class="text-right">
                                                    <input type="number" class="form-control form-control-sm" name="discount_amount" id="discount_amount" value="{{ $order->discount_amount ?? 0 }}" min="0" step="0.01">
                                                </td>
                                            </tr>
                                            <tr class="table-active">
                                                <td><strong>المجموع الكلي:</strong></td>
                                                <td class="text-right"><strong><span id="total">$0.00</span></strong></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('orders.show', $order->id ?? 1) }}" class="btn btn-secondary">
                                        <i class="mdi mdi-arrow-left"></i> إلغاء
                                    </a>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="mdi mdi-content-save"></i> حفظ التغييرات
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Item Modal -->
<div class="modal fade" id="addItemModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة عنصر للطلب</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">عنصر القائمة</label>
                            <select class="form-control select2" id="menu_item_id">
                                <option value="">اختر عنصر القائمة</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">الكمية</label>
                            <input type="number" class="form-control" id="item_quantity" value="1" min="1">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div class="form-group">
                            <label class="form-label">ملاحظات العنصر</label>
                            <textarea class="form-control" id="item_notes" rows="2"></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="add-item-confirm">إضافة</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('js')
<script src="{{URL::asset('assets/plugins/select2/js/select2.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/sweet-alert/sweetalert.min.js')}}"></script>

<script>
$(document).ready(function() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Initialize Select2
    $('.select2').select2();

    let orderItems = [];
    let itemCounter = 0;

    // Load existing order items
    function loadExistingItems() {
        // Sample data - replace with actual order items
        const existingItems = [
            {id: 1, menu_item_id: 1, name: 'برجر كلاسيك', price: 15.99, quantity: 2, notes: 'بدون بصل'},
            {id: 2, menu_item_id: 2, name: 'بيتزا مارجريتا', price: 22.50, quantity: 1, notes: ''}
        ];
        
        orderItems = existingItems;
        itemCounter = Math.max(...existingItems.map(item => item.id), 0);
        renderOrderItems();
        calculateTotals();
    }

    // Load menu items for the modal
    function loadMenuItems() {
        $.ajax({
            url: '/api/menu-items',
            method: 'GET',
            success: function(response) {
                const select = $('#menu_item_id');
                select.empty().append('<option value="">اختر عنصر القائمة</option>');
                
                if (response.data) {
                    response.data.forEach(item => {
                        select.append(`<option value="${item.id}" data-price="${item.price}">${item.name} - $${item.price}</option>`);
                    });
                }
            },
            error: function(xhr) {
                console.error('Error loading menu items:', xhr);
            }
        });
    }

    // Show add item modal
    $('#add-item-btn').click(function() {
        loadMenuItems();
        $('#addItemModal').modal('show');
    });

    // Add item to order
    $('#add-item-confirm').click(function() {
        const menuItemId = $('#menu_item_id').val();
        const quantity = parseInt($('#item_quantity').val());
        const notes = $('#item_notes').val();
        
        if (!menuItemId || quantity < 1) {
            swal('خطأ!', 'يرجى اختيار عنصر القائمة وكمية صحيحة', 'error');
            return;
        }

        const menuItemOption = $('#menu_item_id option:selected');
        const itemName = menuItemOption.text().split(' - $')[0];
        const itemPrice = parseFloat(menuItemOption.data('price'));
        
        const orderItem = {
            id: ++itemCounter,
            menu_item_id: menuItemId,
            name: itemName,
            price: itemPrice,
            quantity: quantity,
            notes: notes,
            total: itemPrice * quantity
        };

        orderItems.push(orderItem);
        renderOrderItems();
        calculateTotals();
        
        // Reset modal
        $('#menu_item_id').val('').trigger('change');
        $('#item_quantity').val(1);
        $('#item_notes').val('');
        $('#addItemModal').modal('hide');
    });

    // Render order items
    function renderOrderItems() {
        const container = $('#order-items-container');
        container.empty();

        if (orderItems.length === 0) {
            container.html('<p class="text-muted text-center">لم يتم إضافة أي عناصر بعد</p>');
            return;
        }

        orderItems.forEach(item => {
            const itemHtml = `
                <div class="card mb-2" data-item-id="${item.id}">
                    <div class="card-body py-2">
                        <div class="row align-items-center">
                            <div class="col-md-4">
                                <strong>${item.name}</strong>
                                ${item.notes ? `<br><small class="text-muted">${item.notes}</small>` : ''}
                            </div>
                            <div class="col-md-2 text-center">
                                $${item.price.toFixed(2)}
                            </div>
                            <div class="col-md-2 text-center">
                                <input type="number" class="form-control form-control-sm item-quantity" value="${item.quantity}" min="1" data-item-id="${item.id}">
                            </div>
                            <div class="col-md-2 text-center">
                                <strong>$${item.total.toFixed(2)}</strong>
                            </div>
                            <div class="col-md-2 text-center">
                                <button type="button" class="btn btn-sm btn-danger remove-item" data-item-id="${item.id}">
                                    <i class="mdi mdi-delete"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            container.append(itemHtml);
        });
    }

    // Update item quantity
    $(document).on('change', '.item-quantity', function() {
        const itemId = parseInt($(this).data('item-id'));
        const newQuantity = parseInt($(this).val());
        
        const item = orderItems.find(i => i.id === itemId);
        if (item) {
            item.quantity = newQuantity;
            item.total = item.price * newQuantity;
            renderOrderItems();
            calculateTotals();
        }
    });

    // Remove item
    $(document).on('click', '.remove-item', function() {
        const itemId = parseInt($(this).data('item-id'));
        orderItems = orderItems.filter(i => i.id !== itemId);
        renderOrderItems();
        calculateTotals();
    });

    // Calculate totals
    function calculateTotals() {
        const subtotal = orderItems.reduce((sum, item) => sum + item.total, 0);
        const taxRate = 0.15; // 15% tax
        const tax = subtotal * taxRate;
        const discount = parseFloat($('#discount_amount').val()) || 0;
        const total = subtotal + tax - discount;

        $('#subtotal').text('$' + subtotal.toFixed(2));
        $('#tax').text('$' + tax.toFixed(2));
        $('#total').text('$' + total.toFixed(2));
    }

    // Recalculate on discount change
    $('#discount_amount').on('input', calculateTotals);

    // Form submission
    $('#orderForm').submit(function(e) {
        e.preventDefault();
        
        if (orderItems.length === 0) {
            swal('خطأ!', 'يجب إضافة عنصر واحد على الأقل للطلب', 'error');
            return;
        }

        const formData = new FormData(this);
        formData.append('items', JSON.stringify(orderItems));

        $.ajax({
            url: $(this).attr('action'),
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                swal("نجح!", "تم تحديث الطلب بنجاح", "success", {
                    button: "موافق",
                }).then(() => {
                    window.location.href = "{{ route('orders.show', $order->id ?? 1) }}";
                });
            },
            error: function(xhr) {
                swal("خطأ!", "حدث خطأ أثناء تحديث الطلب", "error");
            }
        });
    });

    // Load existing items on page load
    loadExistingItems();
});
</script>
@endsection
