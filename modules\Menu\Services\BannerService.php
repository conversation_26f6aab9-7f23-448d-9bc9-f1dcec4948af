<?php

namespace Modules\Menu\Services;

use App\Models\Banner;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * Banner Service
 * 
 * Handles business logic for banner management including creation,
 * updates, display logic, analytics, and performance tracking.
 * 
 * @package Modules\Menu\Services
 * <AUTHOR> POS Team
 * @version 1.0.0
 */
class BannerService
{
    /**
     * Get all banners with optional filtering
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAllBanners(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = Banner::query();

        // Apply filters
        if (isset($filters['branch_id'])) {
            $query->forBranch($filters['branch_id']);
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        if (isset($filters['is_featured'])) {
            $query->featured();
        }

        if (isset($filters['banner_type'])) {
            $query->byType($filters['banner_type']);
        }

        if (isset($filters['position'])) {
            $query->byPosition($filters['position']);
        }

        if (isset($filters['display_location'])) {
            $query->byDisplayLocation($filters['display_location']);
        }

        if (isset($filters['status'])) {
            switch ($filters['status']) {
                case 'current':
                    $query->current();
                    break;
                case 'scheduled':
                    $query->where('start_date', '>', now());
                    break;
                case 'expired':
                    $query->where('end_date', '<', now());
                    break;
            }
        }

        if (isset($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('title', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('subtitle', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('description', 'like', '%' . $filters['search'] . '%');
            });
        }

        return $query->with(['tenant', 'branch'])
                    ->orderBy('priority', 'desc')
                    ->orderBy('sort_order', 'asc')
                    ->paginate($perPage);
    }

    /**
     * Get banner by ID
     *
     * @param int $id
     * @return Banner|null
     */
    public function getBannerById(int $id): ?Banner
    {
        return Banner::with(['tenant', 'branch'])->find($id);
    }

    /**
     * Create a new banner
     *
     * @param array $data
     * @return Banner
     * @throws Exception
     */
    public function createBanner(array $data): Banner
    {
        try {
            DB::beginTransaction();

            // Set default values
            $data['current_impressions'] = 0;
            $data['current_clicks'] = 0;
            $data['click_through_rate'] = 0;
            $data['sort_order'] = $data['sort_order'] ?? 0;
            $data['priority'] = $data['priority'] ?? 'medium';

            $banner = Banner::create($data);

            DB::commit();

            Log::info('Banner created successfully', ['banner_id' => $banner->id]);

            return $banner;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Failed to create banner', ['error' => $e->getMessage(), 'data' => $data]);
            throw $e;
        }
    }

    /**
     * Update an existing banner
     *
     * @param int $id
     * @param array $data
     * @return Banner
     * @throws Exception
     */
    public function updateBanner(int $id, array $data): Banner
    {
        try {
            DB::beginTransaction();

            $banner = Banner::findOrFail($id);
            $banner->update($data);

            DB::commit();

            Log::info('Banner updated successfully', ['banner_id' => $banner->id]);

            return $banner->fresh();
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Failed to update banner', ['error' => $e->getMessage(), 'banner_id' => $id]);
            throw $e;
        }
    }

    /**
     * Delete a banner
     *
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function deleteBanner(int $id): bool
    {
        try {
            DB::beginTransaction();

            $banner = Banner::findOrFail($id);
            $banner->delete();

            DB::commit();

            Log::info('Banner deleted successfully', ['banner_id' => $id]);

            return true;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Failed to delete banner', ['error' => $e->getMessage(), 'banner_id' => $id]);
            throw $e;
        }
    }

    /**
     * Get banners for display
     *
     * @param string $location
     * @param string|null $position
     * @param int|null $branchId
     * @param int $limit
     * @return Collection
     */
    public function getBannersForDisplay(string $location, ?string $position = null, ?int $branchId = null, int $limit = 5): Collection
    {
        $query = Banner::current()->active()->byDisplayLocation($location);

        if ($position) {
            $query->byPosition($position);
        }

        if ($branchId) {
            $query->forBranch($branchId);
        }

        $banners = $query->orderBy('priority', 'desc')
                        ->orderBy('sort_order', 'asc')
                        ->limit($limit)
                        ->get();

        // Filter banners that should be displayed and haven't reached limits
        return $banners->filter(function ($banner) {
            return $banner->shouldDisplay() && !$banner->hasReachedLimits();
        });
    }

    /**
     * Get featured banners
     *
     * @param int|null $branchId
     * @param int $limit
     * @return Collection
     */
    public function getFeaturedBanners(?int $branchId = null, int $limit = 3): Collection
    {
        $query = Banner::featured()->active()->current();

        if ($branchId) {
            $query->forBranch($branchId);
        }

        return $query->orderBy('priority', 'desc')
                    ->orderBy('sort_order', 'asc')
                    ->limit($limit)
                    ->get();
    }

    /**
     * Record banner impression
     *
     * @param int $bannerId
     * @param array $context
     * @return bool
     */
    public function recordImpression(int $bannerId, array $context = []): bool
    {
        try {
            $banner = Banner::find($bannerId);
            
            if (!$banner || !$banner->shouldDisplay()) {
                return false;
            }

            $banner->recordImpression();

            // Log impression for analytics
            Log::info('Banner impression recorded', [
                'banner_id' => $bannerId,
                'context' => $context
            ]);

            return true;
        } catch (Exception $e) {
            Log::error('Failed to record banner impression', [
                'error' => $e->getMessage(),
                'banner_id' => $bannerId
            ]);
            return false;
        }
    }

    /**
     * Record banner click
     *
     * @param int $bannerId
     * @param array $context
     * @return bool
     */
    public function recordClick(int $bannerId, array $context = []): bool
    {
        try {
            $banner = Banner::find($bannerId);
            
            if (!$banner || !$banner->shouldDisplay()) {
                return false;
            }

            $banner->recordClick();

            // Log click for analytics
            Log::info('Banner click recorded', [
                'banner_id' => $bannerId,
                'context' => $context
            ]);

            return true;
        } catch (Exception $e) {
            Log::error('Failed to record banner click', [
                'error' => $e->getMessage(),
                'banner_id' => $bannerId
            ]);
            return false;
        }
    }

    /**
     * Get banner analytics
     *
     * @param int $bannerId
     * @return array
     */
    public function getBannerAnalytics(int $bannerId): array
    {
        $banner = Banner::findOrFail($bannerId);

        return [
            'banner_id' => $banner->id,
            'title' => $banner->title,
            'status' => $banner->getStatus(),
            'performance' => $banner->getPerformanceMetrics(),
            'limits' => [
                'impressions' => [
                    'current' => $banner->current_impressions,
                    'max' => $banner->max_impressions,
                    'percentage' => $banner->max_impressions ? 
                        round(($banner->current_impressions / $banner->max_impressions) * 100, 2) : 0,
                ],
                'clicks' => [
                    'current' => $banner->current_clicks,
                    'max' => $banner->max_clicks,
                    'percentage' => $banner->max_clicks ? 
                        round(($banner->current_clicks / $banner->max_clicks) * 100, 2) : 0,
                ],
            ],
            'analytics_data' => $banner->analytics_data ?? [],
        ];
    }

    /**
     * Get banner performance report
     *
     * @param array $filters
     * @return array
     */
    public function getBannerPerformanceReport(array $filters = []): array
    {
        $query = Banner::query();

        // Apply filters
        if (isset($filters['branch_id'])) {
            $query->forBranch($filters['branch_id']);
        }

        if (isset($filters['banner_type'])) {
            $query->byType($filters['banner_type']);
        }

        if (isset($filters['date_from'])) {
            $query->where('start_date', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('end_date', '<=', $filters['date_to']);
        }

        $banners = $query->get();

        $totalImpressions = $banners->sum('current_impressions');
        $totalClicks = $banners->sum('current_clicks');
        $averageCTR = $totalImpressions > 0 ? round(($totalClicks / $totalImpressions) * 100, 2) : 0;

        return [
            'summary' => [
                'total_banners' => $banners->count(),
                'active_banners' => $banners->where('is_active', true)->count(),
                'total_impressions' => $totalImpressions,
                'total_clicks' => $totalClicks,
                'average_ctr' => $averageCTR,
            ],
            'top_performers' => $banners->sortByDesc('click_through_rate')->take(5)->values(),
            'by_type' => $banners->groupBy('banner_type')->map(function ($group) {
                return [
                    'count' => $group->count(),
                    'impressions' => $group->sum('current_impressions'),
                    'clicks' => $group->sum('current_clicks'),
                    'ctr' => $group->avg('click_through_rate'),
                ];
            }),
        ];
    }

    /**
     * Toggle banner status
     *
     * @param int $id
     * @return Banner
     * @throws Exception
     */
    public function toggleBannerStatus(int $id): Banner
    {
        try {
            $banner = Banner::findOrFail($id);
            $banner->update(['is_active' => !$banner->is_active]);

            Log::info('Banner status toggled', [
                'banner_id' => $id,
                'new_status' => $banner->is_active ? 'active' : 'inactive'
            ]);

            return $banner;
        } catch (Exception $e) {
            Log::error('Failed to toggle banner status', ['error' => $e->getMessage(), 'banner_id' => $id]);
            throw $e;
        }
    }

    /**
     * Duplicate a banner
     *
     * @param int $id
     * @param array $overrides
     * @return Banner
     * @throws Exception
     */
    public function duplicateBanner(int $id, array $overrides = []): Banner
    {
        try {
            DB::beginTransaction();

            $originalBanner = Banner::findOrFail($id);
            $bannerData = $originalBanner->toArray();

            // Remove fields that shouldn't be duplicated
            unset($bannerData['id'], $bannerData['created_at'], $bannerData['updated_at']);
            
            // Reset metrics
            $bannerData['current_impressions'] = 0;
            $bannerData['current_clicks'] = 0;
            $bannerData['click_through_rate'] = 0;
            
            // Update title
            $bannerData['title'] = $bannerData['title'] . ' (Copy)';
            
            // Apply overrides
            $bannerData = array_merge($bannerData, $overrides);

            $newBanner = Banner::create($bannerData);

            DB::commit();

            Log::info('Banner duplicated successfully', [
                'original_id' => $id,
                'new_id' => $newBanner->id
            ]);

            return $newBanner;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Failed to duplicate banner', ['error' => $e->getMessage(), 'banner_id' => $id]);
            throw $e;
        }
    }

    /**
     * Reset banner metrics
     *
     * @param int $id
     * @return Banner
     * @throws Exception
     */
    public function resetBannerMetrics(int $id): Banner
    {
        try {
            $banner = Banner::findOrFail($id);
            $banner->update([
                'current_impressions' => 0,
                'current_clicks' => 0,
                'click_through_rate' => 0,
                'analytics_data' => null,
            ]);

            Log::info('Banner metrics reset', ['banner_id' => $id]);

            return $banner;
        } catch (Exception $e) {
            Log::error('Failed to reset banner metrics', ['error' => $e->getMessage(), 'banner_id' => $id]);
            throw $e;
        }
    }
}