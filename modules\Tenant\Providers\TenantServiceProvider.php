<?php

namespace Modules\Tenant\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;
use Illuminate\Routing\Router;
use Modules\Tenant\Http\Middleware\TenantMiddleware;
use Modules\Tenant\Http\Middleware\SubscriptionMiddleware;
use Modules\Tenant\Http\Middleware\UsageLimitMiddleware;
use Modules\Tenant\Services\TenantService;
use Modules\Tenant\Services\SubscriptionService;
use Modules\Tenant\Services\BillingService;
use Modules\Tenant\Services\MultiStoreService;

class TenantServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register services
        $this->app->singleton(TenantService::class, function ($app) {
            return new TenantService();
        });
        
        $this->app->singleton(SubscriptionService::class, function ($app) {
            return new SubscriptionService(
                $app->make(\Modules\Transaction\Services\TransactionService::class),
                $app->make(\Modules\Transaction\Services\PaymentService::class)
            );
        });
        
        $this->app->singleton(BillingService::class, function ($app) {
            return new BillingService();
        });
        
        $this->app->singleton(MultiStoreService::class, function ($app) {
            return new MultiStoreService();
        });
        
        // Register config
        $this->mergeConfigFrom(
            __DIR__.'/../config/tenant.php',
            'tenant'
        );
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Load routes
        $this->loadRoutesFrom(__DIR__.'/../routes/web.php');
        $this->loadRoutesFrom(__DIR__.'/../routes/api.php');
        
        // Load views
        $this->loadViewsFrom(__DIR__.'/../resources/views', 'tenant');
        
        // Register middleware
        $this->registerMiddleware();
        
        // Publish config
        $this->publishes([
            __DIR__.'/../config/tenant.php' => config_path('tenant.php'),
        ], 'tenant-config');
        
        // Publish views
        $this->publishes([
            __DIR__.'/../resources/views' => resource_path('views/vendor/tenant'),
        ], 'tenant-views');
    }
    
    /**
     * Register middleware
     */
    protected function registerMiddleware(): void
    {
        $router = $this->app->make(Router::class);
        
        // Register middleware aliases
        $router->aliasMiddleware('tenant', TenantMiddleware::class);
        $router->aliasMiddleware('subscription', SubscriptionMiddleware::class);
        $router->aliasMiddleware('usage.limit', UsageLimitMiddleware::class);
        
        // Register middleware groups
        $router->middlewareGroup('tenant.api', [
            'api',
            'auth:sanctum',
            'tenant',
            'subscription',
        ]);
        
        $router->middlewareGroup('tenant.web', [
            'web',
            'auth',
            'tenant',
        ]);
    }
}
