<?php

namespace Modules\Menu\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * Store Banner Request
 * 
 * Handles validation for creating new banners in the restaurant system.
 * Validates banner content, display settings, targeting, scheduling,
 * and performance limits.
 * 
 * @package Modules\Menu\Http\Requests
 * <AUTHOR> POS Team
 * @version 1.0.0
 */
class StoreBannerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'tenant_id' => 'required|integer|exists:tenants,id',
            'branch_id' => 'required|integer|exists:branches,id',
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'banner_type' => 'required|string|in:promotional,informational,seasonal,event,offer,announcement,advertisement',
            'position' => 'required|string|in:top,middle,bottom,sidebar,popup,overlay,inline',
            'display_location' => 'required|string|in:homepage,menu,category,item_detail,checkout,order_confirmation,dashboard,all_pages',
            'image_url' => 'required|string|url',
            'mobile_image_url' => 'nullable|string|url',
            'thumbnail_url' => 'nullable|string|url',
            'link_url' => 'nullable|string|url',
            'link_text' => 'nullable|string|max:100',
            'link_target' => 'nullable|string|in:_self,_blank,_parent,_top',
            'call_to_action' => 'nullable|array',
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'required|date|after_or_equal:start_date',
            'start_time' => 'nullable|date_format:H:i',
            'end_time' => 'nullable|date_format:H:i',
            'target_audience' => 'nullable|array',
            'display_rules' => 'nullable|array',
            'priority' => 'required|string|in:low,medium,high,urgent',
            'max_impressions' => 'nullable|integer|min:1',
            'max_clicks' => 'nullable|integer|min:1',
            'is_animated' => 'boolean',
            'animation_type' => 'nullable|string|in:fade,slide,bounce,zoom,rotate,pulse,none',
            'animation_duration' => 'nullable|integer|min:100|max:10000',
            'auto_hide' => 'boolean',
            'auto_hide_delay' => 'nullable|integer|min:1000|max:30000',
            'is_dismissible' => 'boolean',
            'is_responsive' => 'boolean',
            'responsive_settings' => 'nullable|array',
            'background_color' => 'nullable|string|regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/',
            'text_color' => 'nullable|string|regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/',
            'custom_css' => 'nullable|string',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'analytics_data' => 'nullable|array',
            'metadata' => 'nullable|array',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'tenant_id.required' => 'Tenant ID is required.',
            'tenant_id.exists' => 'Selected tenant does not exist.',
            'branch_id.required' => 'Branch ID is required.',
            'branch_id.exists' => 'Selected branch does not exist.',
            'title.required' => 'Banner title is required.',
            'title.max' => 'Banner title cannot exceed 255 characters.',
            'subtitle.max' => 'Banner subtitle cannot exceed 255 characters.',
            'banner_type.required' => 'Banner type is required.',
            'banner_type.in' => 'Invalid banner type selected.',
            'position.required' => 'Banner position is required.',
            'position.in' => 'Invalid banner position selected.',
            'display_location.required' => 'Display location is required.',
            'display_location.in' => 'Invalid display location selected.',
            'image_url.required' => 'Banner image URL is required.',
            'image_url.url' => 'Banner image URL must be a valid URL.',
            'mobile_image_url.url' => 'Mobile image URL must be a valid URL.',
            'thumbnail_url.url' => 'Thumbnail URL must be a valid URL.',
            'link_url.url' => 'Link URL must be a valid URL.',
            'link_text.max' => 'Link text cannot exceed 100 characters.',
            'link_target.in' => 'Invalid link target selected.',
            'start_date.required' => 'Start date is required.',
            'start_date.after_or_equal' => 'Start date cannot be in the past.',
            'end_date.required' => 'End date is required.',
            'end_date.after_or_equal' => 'End date must be after or equal to start date.',
            'start_time.date_format' => 'Start time must be in HH:MM format.',
            'end_time.date_format' => 'End time must be in HH:MM format.',
            'priority.required' => 'Priority is required.',
            'priority.in' => 'Invalid priority level selected.',
            'max_impressions.integer' => 'Maximum impressions must be a valid number.',
            'max_impressions.min' => 'Maximum impressions must be at least 1.',
            'max_clicks.integer' => 'Maximum clicks must be a valid number.',
            'max_clicks.min' => 'Maximum clicks must be at least 1.',
            'animation_type.in' => 'Invalid animation type selected.',
            'animation_duration.integer' => 'Animation duration must be a valid number.',
            'animation_duration.min' => 'Animation duration must be at least 100ms.',
            'animation_duration.max' => 'Animation duration cannot exceed 10 seconds.',
            'auto_hide_delay.integer' => 'Auto hide delay must be a valid number.',
            'auto_hide_delay.min' => 'Auto hide delay must be at least 1 second.',
            'auto_hide_delay.max' => 'Auto hide delay cannot exceed 30 seconds.',
            'background_color.regex' => 'Background color must be a valid hex color code.',
            'text_color.regex' => 'Text color must be a valid hex color code.',
            'sort_order.min' => 'Sort order cannot be negative.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'tenant_id' => 'tenant',
            'branch_id' => 'branch',
            'banner_type' => 'banner type',
            'display_location' => 'display location',
            'image_url' => 'image URL',
            'mobile_image_url' => 'mobile image URL',
            'thumbnail_url' => 'thumbnail URL',
            'link_url' => 'link URL',
            'link_text' => 'link text',
            'link_target' => 'link target',
            'call_to_action' => 'call to action',
            'start_date' => 'start date',
            'end_date' => 'end date',
            'start_time' => 'start time',
            'end_time' => 'end time',
            'target_audience' => 'target audience',
            'display_rules' => 'display rules',
            'max_impressions' => 'maximum impressions',
            'max_clicks' => 'maximum clicks',
            'is_animated' => 'animated',
            'animation_type' => 'animation type',
            'animation_duration' => 'animation duration',
            'auto_hide' => 'auto hide',
            'auto_hide_delay' => 'auto hide delay',
            'is_dismissible' => 'dismissible',
            'is_responsive' => 'responsive',
            'responsive_settings' => 'responsive settings',
            'background_color' => 'background color',
            'text_color' => 'text color',
            'custom_css' => 'custom CSS',
            'is_active' => 'active status',
            'is_featured' => 'featured status',
            'sort_order' => 'sort order',
            'analytics_data' => 'analytics data',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'is_animated' => $this->boolean('is_animated'),
            'auto_hide' => $this->boolean('auto_hide'),
            'is_dismissible' => $this->boolean('is_dismissible'),
            'is_responsive' => $this->boolean('is_responsive'),
            'is_active' => $this->boolean('is_active'),
            'is_featured' => $this->boolean('is_featured'),
        ]);
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Validate animation settings
            if ($this->is_animated && !$this->animation_type) {
                $validator->errors()->add('animation_type', 'Animation type is required when banner is animated.');
            }

            if ($this->is_animated && $this->animation_type !== 'none' && !$this->animation_duration) {
                $validator->errors()->add('animation_duration', 'Animation duration is required for animated banners.');
            }

            // Validate auto hide settings
            if ($this->auto_hide && !$this->auto_hide_delay) {
                $validator->errors()->add('auto_hide_delay', 'Auto hide delay is required when auto hide is enabled.');
            }

            // Validate time consistency
            if ($this->start_time && $this->end_time) {
                try {
                    $startTime = \Carbon\Carbon::createFromFormat('H:i', $this->start_time);
                    $endTime = \Carbon\Carbon::createFromFormat('H:i', $this->end_time);
                    
                    if ($endTime->lte($startTime)) {
                        $validator->errors()->add('end_time', 'End time must be after start time.');
                    }
                } catch (\Exception $e) {
                    // Time format validation will catch this
                }
            }

            // Validate responsive settings
            if ($this->is_responsive && empty($this->responsive_settings)) {
                $validator->errors()->add('responsive_settings', 'Responsive settings are required when responsive mode is enabled.');
            }

            // Validate call to action
            if ($this->call_to_action && !empty($this->call_to_action)) {
                if (!isset($this->call_to_action['text']) || empty($this->call_to_action['text'])) {
                    $validator->errors()->add('call_to_action.text', 'Call to action text is required.');
                }
            }

            // Validate link consistency
            if ($this->link_text && !$this->link_url) {
                $validator->errors()->add('link_url', 'Link URL is required when link text is provided.');
            }

            // Validate position and display location compatibility
            $incompatibleCombinations = [
                'popup' => ['menu', 'category', 'item_detail'],
                'overlay' => ['checkout', 'order_confirmation'],
            ];

            foreach ($incompatibleCombinations as $position => $locations) {
                if ($this->position === $position && in_array($this->display_location, $locations)) {
                    $validator->errors()->add('position', "Position '{$position}' is not compatible with display location '{$this->display_location}'.");
                }
            }
        });
    }
}