<?php

namespace Modules\Settings\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Settings\PaymentSetting;
use App\Services\SettingsService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class PaymentSettingController extends Controller
{
    protected $settingsService;

    public function __construct(SettingsService $settingsService)
    {
        $this->settingsService = $settingsService;
    }

    public function index(Request $request): JsonResponse
    {
        $tenantId = $request->user()->tenant_id;
        $branchId = $request->input('branch_id');

        $paymentSettings = $this->settingsService->getPaymentSettings($tenantId, $branchId);

        return response()->json([
            'success' => true,
            'data' => $paymentSettings
        ]);
    }

    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'payment_method' => 'required|string|max:50',
            'is_enabled' => 'boolean',
            'gateway_config' => 'nullable|array',
            'min_amount' => 'nullable|numeric|min:0',
            'max_amount' => 'nullable|numeric|min:0',
            'transaction_fee' => 'numeric|min:0',
            'fee_type' => 'in:fixed,percentage',
            'display_order' => 'integer|min:0',
            'branch_id' => 'nullable|exists:branches,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $tenantId = $request->user()->tenant_id;
            $branchId = $request->input('branch_id');

            // Check if payment method already exists
            $existing = PaymentSetting::where('tenant_id', $tenantId)
                ->where('branch_id', $branchId)
                ->where('payment_method', $request->input('payment_method'))
                ->first();

            if ($existing) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment method already exists for this branch'
                ], 409);
            }

            $paymentSetting = PaymentSetting::create([
                'tenant_id' => $tenantId,
                'branch_id' => $branchId,
                'payment_method' => $request->input('payment_method'),
                'is_enabled' => $request->input('is_enabled', true),
                'gateway_config' => $request->input('gateway_config'),
                'min_amount' => $request->input('min_amount'),
                'max_amount' => $request->input('max_amount'),
                'transaction_fee' => $request->input('transaction_fee', 0),
                'fee_type' => $request->input('fee_type', 'fixed'),
                'display_order' => $request->input('display_order', 0),
            ]);

            return response()->json([
                'success' => true,
                'data' => $paymentSetting,
                'message' => 'Payment setting created successfully'
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create payment setting: ' . $e->getMessage()
            ], 500);
        }
    }

    public function show(Request $request, $id): JsonResponse
    {
        $tenantId = $request->user()->tenant_id;
        
        $paymentSetting = PaymentSetting::where('tenant_id', $tenantId)
            ->where('id', $id)
            ->first();

        if (!$paymentSetting) {
            return response()->json([
                'success' => false,
                'message' => 'Payment setting not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $paymentSetting
        ]);
    }

    public function update(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'is_enabled' => 'boolean',
            'gateway_config' => 'nullable|array',
            'min_amount' => 'nullable|numeric|min:0',
            'max_amount' => 'nullable|numeric|min:0',
            'transaction_fee' => 'numeric|min:0',
            'fee_type' => 'in:fixed,percentage',
            'display_order' => 'integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $tenantId = $request->user()->tenant_id;
            
            $paymentSetting = PaymentSetting::where('tenant_id', $tenantId)
                ->where('id', $id)
                ->first();

            if (!$paymentSetting) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment setting not found'
                ], 404);
            }

            $paymentSetting->update($request->only([
                'is_enabled', 'gateway_config', 'min_amount', 'max_amount',
                'transaction_fee', 'fee_type', 'display_order'
            ]));

            return response()->json([
                'success' => true,
                'data' => $paymentSetting,
                'message' => 'Payment setting updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update payment setting: ' . $e->getMessage()
            ], 500);
        }
    }

    public function destroy(Request $request, $id): JsonResponse
    {
        try {
            $tenantId = $request->user()->tenant_id;
            
            $paymentSetting = PaymentSetting::where('tenant_id', $tenantId)
                ->where('id', $id)
                ->first();

            if (!$paymentSetting) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment setting not found'
                ], 404);
            }

            $paymentSetting->delete();

            return response()->json([
                'success' => true,
                'message' => 'Payment setting deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete payment setting: ' . $e->getMessage()
            ], 500);
        }
    }

    public function calculateFee(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $tenantId = $request->user()->tenant_id;
            
            $paymentSetting = PaymentSetting::where('tenant_id', $tenantId)
                ->where('id', $id)
                ->first();

            if (!$paymentSetting) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment setting not found'
                ], 404);
            }

            $amount = $request->input('amount');
            $fee = $paymentSetting->calculateFee($amount);
            $isValid = $paymentSetting->isAmountValid($amount);

            return response()->json([
                'success' => true,
                'data' => [
                    'amount' => $amount,
                    'fee' => $fee,
                    'total' => $amount + $fee,
                    'is_valid' => $isValid
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to calculate fee: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getAvailablePaymentMethods(): JsonResponse
    {
        $methods = PaymentSetting::getAvailablePaymentMethods();

        return response()->json([
            'success' => true,
            'data' => $methods
        ]);
    }

    public function bulkUpdate(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'settings' => 'required|array',
            'settings.*.id' => 'required|integer',
            'settings.*.is_enabled' => 'boolean',
            'settings.*.display_order' => 'integer|min:0',
            'branch_id' => 'nullable|exists:branches,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $tenantId = $request->user()->tenant_id;
            $settings = $request->input('settings');
            $updated = [];

            foreach ($settings as $settingData) {
                $paymentSetting = PaymentSetting::where('tenant_id', $tenantId)
                    ->where('id', $settingData['id'])
                    ->first();

                if ($paymentSetting) {
                    $paymentSetting->update([
                        'is_enabled' => $settingData['is_enabled'] ?? $paymentSetting->is_enabled,
                        'display_order' => $settingData['display_order'] ?? $paymentSetting->display_order,
                    ]);
                    $updated[] = $paymentSetting;
                }
            }

            return response()->json([
                'success' => true,
                'data' => $updated,
                'message' => 'Payment settings updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update payment settings: ' . $e->getMessage()
            ], 500);
        }
    }

    public function initializeDefaults(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'branch_id' => 'nullable|exists:branches,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $tenantId = $request->user()->tenant_id;
            $branchId = $request->input('branch_id');

            $defaults = PaymentSetting::getDefaultSettings($tenantId, $branchId);
            $created = [];

            foreach ($defaults as $default) {
                $existing = PaymentSetting::where('tenant_id', $tenantId)
                    ->where('branch_id', $branchId)
                    ->where('payment_method', $default['payment_method'])
                    ->first();

                if (!$existing) {
                    $created[] = PaymentSetting::create($default);
                }
            }

            return response()->json([
                'success' => true,
                'data' => $created,
                'message' => 'Default payment settings initialized successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to initialize defaults: ' . $e->getMessage()
            ], 500);
        }
    }
}
