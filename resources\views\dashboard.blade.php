@extends('layouts.master')

@section('title', 'Dashboard')

@section('content')
<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Dashboard</h1>
            <p class="mt-1 text-sm text-gray-500">Welcome back! Here's what's happening at your restaurant today.</p>
        </div>
        <div class="mt-4 sm:mt-0">
            <button class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <i class="fas fa-plus mr-2"></i>
                New Order
            </button>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Today's Sales -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
                            <i class="fas fa-dollar-sign text-green-600"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Today's Sales</dt>
                            <dd class="text-lg font-medium text-gray-900">$2,847.50</dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                    <span class="text-green-600 font-medium">+12.5%</span>
                    <span class="text-gray-500">from yesterday</span>
                </div>
            </div>
        </div>

        <!-- Orders Today -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
                            <i class="fas fa-shopping-cart text-blue-600"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Orders Today</dt>
                            <dd class="text-lg font-medium text-gray-900">127</dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                    <span class="text-blue-600 font-medium">+8.2%</span>
                    <span class="text-gray-500">from yesterday</span>
                </div>
            </div>
        </div>

        <!-- Active Tables -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center">
                            <i class="fas fa-chair text-yellow-600"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Active Tables</dt>
                            <dd class="text-lg font-medium text-gray-900">18/25</dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                    <span class="text-yellow-600 font-medium">72%</span>
                    <span class="text-gray-500">occupancy rate</span>
                </div>
            </div>
        </div>

        <!-- Kitchen Queue -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-100 rounded-md flex items-center justify-center">
                            <i class="fas fa-fire text-red-600"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Kitchen Queue</dt>
                            <dd class="text-lg font-medium text-gray-900">7</dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                    <span class="text-red-600 font-medium">3 urgent</span>
                    <span class="text-gray-500">orders pending</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Tab Navigation -->
    <div class="bg-white shadow rounded-lg">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                <button class="tab-button active border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="overview">
                    <i class="fas fa-chart-line mr-2"></i>
                    Overview
                </button>
                <button class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="orders">
                    <i class="fas fa-shopping-cart mr-2"></i>
                    Recent Orders
                </button>
                <button class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="tables">
                    <i class="fas fa-chair mr-2"></i>
                    Table Status
                </button>
                <button class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="analytics">
                    <i class="fas fa-chart-bar mr-2"></i>
                    Analytics
                </button>
            </nav>
        </div>

        <!-- Tab Content -->
        <div class="p-6">
            <!-- Overview Tab -->
            <div id="overview" class="tab-content active">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Sales Chart -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Sales Trend (Last 7 Days)</h3>
                        <div class="h-64 flex items-center justify-center bg-white rounded border-2 border-dashed border-gray-300">
                            <div class="text-center">
                                <i class="fas fa-chart-line text-4xl text-gray-400 mb-2"></i>
                                <p class="text-gray-500">Sales chart would go here</p>
                            </div>
                        </div>
                    </div>

                    <!-- Top Items -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Top Selling Items</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-3 bg-white rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-hamburger text-blue-600"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900">Classic Burger</p>
                                        <p class="text-sm text-gray-500">47 orders today</p>
                                    </div>
                                </div>
                                <span class="text-lg font-semibold text-gray-900">$564</span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-white rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-pizza-slice text-green-600"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900">Margherita Pizza</p>
                                        <p class="text-sm text-gray-500">32 orders today</p>
                                    </div>
                                </div>
                                <span class="text-lg font-semibold text-gray-900">$448</span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-white rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-coffee text-yellow-600"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900">Cappuccino</p>
                                        <p class="text-sm text-gray-500">89 orders today</p>
                                    </div>
                                </div>
                                <span class="text-lg font-semibold text-gray-900">$267</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Orders Tab -->
            <div id="orders" class="tab-content">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Table</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Items</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#1247</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Table 12</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2x Burger, 1x Pizza</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$45.50</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Completed</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2 min ago</td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#1246</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Table 8</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">3x Coffee, 2x Cake</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$28.75</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Preparing</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">5 min ago</td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#1245</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Table 15</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1x Steak, 1x Wine</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$67.00</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Served</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">8 min ago</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Tables Tab -->
            <div id="tables" class="tab-content">
                <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                    <!-- Table cards -->
                    <div class="bg-green-50 border-2 border-green-200 rounded-lg p-4 text-center">
                        <div class="w-8 h-8 bg-green-500 rounded-full mx-auto mb-2 flex items-center justify-center">
                            <span class="text-white font-bold text-sm">1</span>
                        </div>
                        <p class="text-sm font-medium text-green-800">Occupied</p>
                        <p class="text-xs text-green-600">4 guests</p>
                    </div>
                    <div class="bg-gray-50 border-2 border-gray-200 rounded-lg p-4 text-center">
                        <div class="w-8 h-8 bg-gray-400 rounded-full mx-auto mb-2 flex items-center justify-center">
                            <span class="text-white font-bold text-sm">2</span>
                        </div>
                        <p class="text-sm font-medium text-gray-600">Available</p>
                        <p class="text-xs text-gray-500">Clean</p>
                    </div>
                    <div class="bg-yellow-50 border-2 border-yellow-200 rounded-lg p-4 text-center">
                        <div class="w-8 h-8 bg-yellow-500 rounded-full mx-auto mb-2 flex items-center justify-center">
                            <span class="text-white font-bold text-sm">3</span>
                        </div>
                        <p class="text-sm font-medium text-yellow-800">Reserved</p>
                        <p class="text-xs text-yellow-600">7:30 PM</p>
                    </div>
                    <div class="bg-green-50 border-2 border-green-200 rounded-lg p-4 text-center">
                        <div class="w-8 h-8 bg-green-500 rounded-full mx-auto mb-2 flex items-center justify-center">
                            <span class="text-white font-bold text-sm">4</span>
                        </div>
                        <p class="text-sm font-medium text-green-800">Occupied</p>
                        <p class="text-xs text-green-600">2 guests</p>
                    </div>
                    <div class="bg-red-50 border-2 border-red-200 rounded-lg p-4 text-center">
                        <div class="w-8 h-8 bg-red-500 rounded-full mx-auto mb-2 flex items-center justify-center">
                            <span class="text-white font-bold text-sm">5</span>
                        </div>
                        <p class="text-sm font-medium text-red-800">Cleaning</p>
                        <p class="text-xs text-red-600">5 min</p>
                    </div>
                    <div class="bg-gray-50 border-2 border-gray-200 rounded-lg p-4 text-center">
                        <div class="w-8 h-8 bg-gray-400 rounded-full mx-auto mb-2 flex items-center justify-center">
                            <span class="text-white font-bold text-sm">6</span>
                        </div>
                        <p class="text-sm font-medium text-gray-600">Available</p>
                        <p class="text-xs text-gray-500">Clean</p>
                    </div>
                </div>
            </div>

            <!-- Analytics Tab -->
            <div id="analytics" class="tab-content">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Revenue Analytics</h3>
                        <div class="h-64 flex items-center justify-center bg-white rounded border-2 border-dashed border-gray-300">
                            <div class="text-center">
                                <i class="fas fa-chart-pie text-4xl text-gray-400 mb-2"></i>
                                <p class="text-gray-500">Revenue chart would go here</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Customer Analytics</h3>
                        <div class="h-64 flex items-center justify-center bg-white rounded border-2 border-dashed border-gray-300">
                            <div class="text-center">
                                <i class="fas fa-users text-4xl text-gray-400 mb-2"></i>
                                <p class="text-gray-500">Customer analytics would go here</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Tab functionality is already handled in master.blade.php
    
    // Auto-refresh data every 30 seconds
    setInterval(function() {
        // Here you would typically make AJAX calls to refresh data
        console.log('Refreshing dashboard data...');
    }, 30000);
    
    // Add click handlers for interactive elements
    $('.stats-card').on('click', function() {
        // Handle stats card clicks
    });
});
</script>
@endpush