<?php

use Illuminate\Support\Facades\Route;

use Modules\Delivery\Http\Controllers\DeliveryPersonnelWebController;
use Modules\Delivery\Http\Controllers\DeliveryController;
use Modules\Delivery\Http\Controllers\DeliveryZoneWebController;
use Mo<PERSON>les\Delivery\Http\Controllers\DeliveryTrackingWebController;
use Modules\Delivery\Http\Controllers\DeliveryReviewWebController;
use Modules\Delivery\Http\Controllers\DeliveryAssignmentWebController;

/*
|--------------------------------------------------------------------------
| Delivery Web Routes
|--------------------------------------------------------------------------
|
| Here are the web routes for the Delivery module management interface.
|
*/

Route::middleware(['auth'])->group(function () {
    // Delivery Personnel Management Routes
    Route::prefix('delivery/personnel')->name('delivery.personnel.')->group(function () {
        Route::get('/', [DeliveryPersonnelWebController::class, 'index'])->name('index');
        Route::get('/create', [DeliveryPersonnelWebController::class, 'create'])->name('create');
        Route::post('/', [DeliveryPersonnelWebController::class, 'store'])->name('store');
        Route::get('/{personnel}', [DeliveryPersonnelWebController::class, 'show'])->name('show');
        Route::get('/{personnel}/edit', [DeliveryPersonnelWebController::class, 'edit'])->name('edit');
        Route::put('/{personnel}', [DeliveryPersonnelWebController::class, 'update'])->name('update');
        Route::delete('/{personnel}', [DeliveryPersonnelWebController::class, 'destroy'])->name('destroy');

        // DataTable AJAX endpoint
        Route::get('/data/personnel', [DeliveryPersonnelWebController::class, 'getPersonnelData'])->name('data');

        // Personnel Actions
        Route::post('/{personnel}/verify', [DeliveryPersonnelWebController::class, 'verify'])->name('verify');
        Route::post('/{personnel}/suspend', [DeliveryPersonnelWebController::class, 'suspend'])->name('suspend');
        Route::post('/{personnel}/activate', [DeliveryPersonnelWebController::class, 'activate'])->name('activate');
        Route::get('/{personnel}/performance', [DeliveryPersonnelWebController::class, 'performance'])->name('performance');
    });

    // Delivery Management Routes
    Route::prefix('delivery/orders')->name('delivery.orders.')->group(function () {
        Route::get('/', [DeliveryController::class, 'index'])->name('index');
        Route::get('/{assignment}', [DeliveryController::class, 'show'])->name('show');
        Route::put('/{assignment}/assign', [DeliveryController::class, 'assign'])->name('assign');
        Route::put('/{assignment}/status', [DeliveryController::class, 'updateStatus'])->name('update-status');

        // AJAX routes for DataTables
        Route::get('/data/assignments', [DeliveryController::class, 'getAssignmentsData'])->name('data');
    });

    // Delivery Zones Management Routes
    Route::prefix('delivery/zones')->name('delivery.zones.')->group(function () {
        Route::get('/', [DeliveryZoneWebController::class, 'index'])->name('index');
        Route::get('/create', [DeliveryZoneWebController::class, 'create'])->name('create');
        Route::post('/', [DeliveryZoneWebController::class, 'store'])->name('store');
        Route::get('/{zone}', [DeliveryZoneWebController::class, 'show'])->name('show');
        Route::get('/{zone}/edit', [DeliveryZoneWebController::class, 'edit'])->name('edit');
        Route::put('/{zone}', [DeliveryZoneWebController::class, 'update'])->name('update');
        Route::delete('/{zone}', [DeliveryZoneWebController::class, 'destroy'])->name('destroy');

        // DataTable AJAX endpoint
        Route::get('/data/zones', [DeliveryZoneWebController::class, 'getZonesData'])->name('data');

        // Zone Actions
        Route::post('/{zone}/toggle-status', [DeliveryZoneWebController::class, 'toggleStatus'])->name('toggle-status');
    });

    // Delivery Tracking Routes
    Route::prefix('delivery/tracking')->name('delivery.tracking.')->group(function () {
        Route::get('/', [DeliveryTrackingWebController::class, 'index'])->name('index');
        Route::get('/{tracking}', [DeliveryTrackingWebController::class, 'show'])->name('show');

        // DataTable AJAX endpoint
        Route::get('/data/tracking', [DeliveryTrackingWebController::class, 'getTrackingData'])->name('data');

        // Real-time tracking
        Route::get('/order/{orderId}', [DeliveryTrackingWebController::class, 'getOrderTracking'])->name('order');
    });

    // Delivery Reviews Routes
    Route::prefix('delivery/reviews')->name('delivery.reviews.')->group(function () {
        Route::get('/', [DeliveryReviewWebController::class, 'index'])->name('index');
        Route::get('/{orderId}', [DeliveryReviewWebController::class, 'show'])->name('show');

        // DataTable AJAX endpoint
        Route::get('/data/reviews', [DeliveryReviewWebController::class, 'getReviewsData'])->name('data');

        // Statistics and reports
        Route::get('/statistics/data', [DeliveryReviewWebController::class, 'getStatistics'])->name('statistics');
        Route::get('/personnel/{personnelId}/performance', [DeliveryReviewWebController::class, 'getPersonnelPerformance'])->name('personnel.performance');
    });

    // Delivery Assignments
    Route::prefix('delivery/assignments')->name('delivery.assignments.')->group(function () {
        Route::get('/', [DeliveryAssignmentWebController::class, 'index'])->name('index');
        Route::get('/data', [DeliveryAssignmentWebController::class, 'getAssignmentsData'])->name('data');
        Route::post('/', [DeliveryAssignmentWebController::class, 'store'])->name('store');
        Route::get('/{assignment}', [DeliveryAssignmentWebController::class, 'show'])->name('show');
        Route::get('/{assignment}/edit', [DeliveryAssignmentWebController::class, 'edit'])->name('edit');
        Route::put('/{assignment}', [DeliveryAssignmentWebController::class, 'update'])->name('update');
        Route::delete('/{assignment}', [DeliveryAssignmentWebController::class, 'destroy'])->name('destroy');
        Route::post('/{assignment}/assign-personnel', [DeliveryAssignmentWebController::class, 'assignPersonnel'])->name('assign-personnel');
        Route::post('/{assignment}/update-status', [DeliveryAssignmentWebController::class, 'updateStatus'])->name('update-status');
        Route::get('/statistics/summary', [DeliveryAssignmentWebController::class, 'getStatistics'])->name('statistics');
        Route::get('/personnel/available', [DeliveryAssignmentWebController::class, 'getAvailablePersonnel'])->name('available-personnel');
        Route::get('/orders/available', [DeliveryAssignmentWebController::class, 'getAvailableOrders'])->name('available-orders');
        Route::get('/{assignment}/details', [DeliveryAssignmentWebController::class, 'getDetails'])->name('details');
        Route::post('/bulk-action', [DeliveryAssignmentWebController::class, 'bulkAction'])->name('bulk-action');
    });
});
