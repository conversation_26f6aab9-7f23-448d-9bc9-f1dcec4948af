@extends('layouts.master')

@section('title', 'حركات المخزون')

@section('breadcrumb')
<div class="bg-white rounded-lg shadow-sm border border-gray-200 px-6 py-4 mb-6">
    <nav class="flex" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3 rtl:space-x-reverse">
            <li class="inline-flex items-center">
                <a href="{{ route('dashboard') }}" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                    <i class="fas fa-home ml-2"></i>
                    الرئيسية
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <i class="fas fa-chevron-left text-gray-400 mx-2"></i>
                    <a href="{{ route('inventory.dashboard') }}" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">المخزون</a>
                </div>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <i class="fas fa-chevron-left text-gray-400 mx-2"></i>
                    <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">حركات المخزون</span>
                </div>
            </li>
        </ol>
    </nav>
</div>
@endsection

@section('content')
<div class="space-y-6">
    <!-- Page Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 flex items-center gap-3">
                        <div class="w-10 h-10 bg-gradient-to-r from-purple-600 to-purple-700 rounded-lg flex items-center justify-center">
                            <i class="fas fa-exchange-alt text-white"></i>
                        </div>
                        حركات المخزون
                    </h1>
                    <p class="mt-1 text-sm text-gray-600">تتبع جميع حركات المخزون والتغييرات</p>
                </div>
                <div class="flex gap-2">
                    <button type="button" onclick="exportMovements()" class="px-4 py-2 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center gap-2">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                    <button type="button" onclick="generateMovementReport()" class="px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2">
                        <i class="fas fa-chart-line"></i>
                        تقرير الحركات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <i class="fas fa-filter text-purple-600"></i>
            فلاتر البحث
        </h3>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">المادة</label>
                <select id="item-filter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
                    <option value="">جميع المواد</option>
                </select>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">نوع الحركة</label>
                <select id="type-filter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
                    <option value="">جميع الأنواع</option>
                    <option value="in">إدخال</option>
                    <option value="out">إخراج</option>
                    <option value="adjustment">تسوية</option>
                    <option value="transfer">نقل</option>
                </select>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">من تاريخ</label>
                <input type="date" id="date-from" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">إلى تاريخ</label>
                <input type="date" id="date-to" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
            </div>
        </div>

        <div class="flex gap-2 mt-4">
            <button type="button" onclick="applyMovementFilters()" class="px-4 py-2 bg-purple-600 text-white font-medium rounded-lg hover:bg-purple-700 transition-colors duration-200 flex items-center gap-2">
                <i class="fas fa-filter"></i>
                تطبيق
            </button>
            <button type="button" onclick="clearMovementFilters()" class="px-4 py-2 bg-gray-600 text-white font-medium rounded-lg hover:bg-gray-700 transition-colors duration-200 flex items-center gap-2">
                <i class="fas fa-times"></i>
                مسح
            </button>
        </div>
    </div>

    <!-- Movement Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total In Card -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-arrow-down text-green-600 text-xl"></i>
                    </div>
                </div>
                <div class="mr-4 flex-1 text-right">
                    <p class="text-sm font-medium text-gray-600">إجمالي الإدخال</p>
                    <p class="text-2xl font-bold text-green-600">{{ $stats['total_in'] ?? 0 }}</p>
                </div>
            </div>
        </div>

        <!-- Total Out Card -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-arrow-up text-red-600 text-xl"></i>
                    </div>
                </div>
                <div class="mr-4 flex-1 text-right">
                    <p class="text-sm font-medium text-gray-600">إجمالي الإخراج</p>
                    <p class="text-2xl font-bold text-red-600">{{ $stats['total_out'] ?? 0 }}</p>
                </div>
            </div>
        </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
        <div class="card movement-card">
            <div class="card-body">
                <div class="d-flex">
                    <div class="stats-icon bg-warning">
                        <i class="mdi mdi-adjust"></i>
                    </div>
                    <div class="mr-auto">
                        <div class="text-right">
                            <h2 class="mb-1 tx-20 font-weight-bold text-warning">{{ $stats['total_adjustments'] ?? 0 }}</h2>
                            <p class="text-muted mb-0 tx-11">التسويات</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
        <div class="card movement-card">
            <div class="card-body">
                <div class="d-flex">
                    <div class="stats-icon bg-info">
                        <i class="mdi mdi-swap-horizontal"></i>
                    </div>
                    <div class="mr-auto">
                        <div class="text-right">
                            <h2 class="mb-1 tx-20 font-weight-bold text-info">{{ $stats['total_transfers'] ?? 0 }}</h2>
                            <p class="text-muted mb-0 tx-11">النقل</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Movements Table -->
<div class="row">
    <div class="col-xl-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">سجل حركات المخزون</h4>
                    <div class="card-options">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="refreshMovementsTable()">
                                <i class="mdi mdi-refresh"></i> تحديث
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-info" onclick="toggleTimelineView()">
                                <i class="mdi mdi-timeline"></i> عرض زمني
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Table View -->
                <div id="table-view">
                    <div class="table-responsive">
                        <table id="movements-table" class="table table-striped table-bordered text-md-nowrap">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>التاريخ والوقت</th>
                                    <th>المادة</th>
                                    <th>نوع الحركة</th>
                                    <th>الكمية</th>
                                    <th>المخزون قبل</th>
                                    <th>المخزون بعد</th>
                                    <th>السبب</th>
                                    <th>المستخدم</th>
                                    <th>ملاحظات</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Timeline View -->
                <div id="timeline-view" style="display: none;">
                    <div id="movements-timeline">
                        <!-- Timeline items will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@push('inventory-scripts')
<script>
let movementsTable;
let timelineView = false;

$(document).ready(function() {
    initializeMovementsTable();
    loadItemsForFilter();
});

function initializeMovementsTable() {
    movementsTable = $('#movements-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("inventory.api.stock.movements.datatable") }}',
            data: function(d) {
                d.item_id = $('#item-filter').val();
                d.type = $('#type-filter').val();
                d.date_from = $('#date-from').val();
                d.date_to = $('#date-to').val();
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { 
                data: 'created_at', 
                name: 'created_at',
                render: function(data) {
                    return new Date(data).toLocaleString('ar-SA');
                }
            },
            { 
                data: 'item.product.name', 
                name: 'item.product.name',
                render: function(data, type, row) {
                    return `${data}<br><small class="text-muted">${row.item.product.sku}</small>`;
                }
            },
            { 
                data: 'type', 
                name: 'type',
                render: function(data, type, row) {
                    return getMovementTypeBadge(data, row.quantity);
                }
            },
            { 
                data: 'quantity', 
                name: 'quantity',
                render: function(data, type, row) {
                    const sign = data > 0 ? '+' : '';
                    const color = data > 0 ? 'text-success' : 'text-danger';
                    return `<span class="${color}">${sign}${data} ${row.item.product.unit?.symbol || ''}</span>`;
                }
            },
            { 
                data: 'stock_before', 
                name: 'stock_before',
                render: function(data, type, row) {
                    return `${data} ${row.item.product.unit?.symbol || ''}`;
                }
            },
            { 
                data: 'stock_after', 
                name: 'stock_after',
                render: function(data, type, row) {
                    return `${data} ${row.item.product.unit?.symbol || ''}`;
                }
            },
            { data: 'reason', name: 'reason' },
            { data: 'user.name', name: 'user.name' },
            { 
                data: 'notes', 
                name: 'notes',
                render: function(data) {
                    return data || '-';
                }
            }
        ],
        order: [[1, 'desc']],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
        },
        responsive: true,
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'excel',
                text: 'تصدير Excel',
                className: 'btn btn-success btn-sm'
            },
            {
                extend: 'pdf',
                text: 'تصدير PDF',
                className: 'btn btn-danger btn-sm'
            }
        ]
    });
}

function loadItemsForFilter() {
    $.get('{{ route("inventory.api.items") }}')
        .done(function(items) {
            const options = items.map(item => 
                `<option value="${item.id}">${item.product.name} (${item.product.sku})</option>`
            ).join('');
            
            $('#item-filter').html('<option value="">جميع المواد</option>' + options);
        })
        .fail(function() {
            console.error('Failed to load items for filter');
        });
}

function getMovementTypeBadge(type, quantity) {
    let badge = '';
    let text = '';
    
    if (quantity > 0) {
        badge = 'movement-in';
        text = 'إدخال';
    } else if (quantity < 0) {
        badge = 'movement-out';
        text = 'إخراج';
    } else {
        badge = 'movement-adjustment';
        text = 'تسوية';
    }
    
    switch(type) {
        case 'purchase':
            text = 'شراء';
            break;
        case 'sale':
            text = 'بيع';
            break;
        case 'adjustment':
            text = 'تسوية';
            break;
        case 'transfer':
            text = 'نقل';
            break;
        case 'damage':
            text = 'تلف';
            break;
        case 'expired':
            text = 'انتهاء صلاحية';
            break;
        case 'count':
            text = 'جرد';
            break;
    }
    
    return `<span class="movement-type-badge ${badge}">${text}</span>`;
}

function applyMovementFilters() {
    movementsTable.ajax.reload();
}

function clearMovementFilters() {
    $('#item-filter').val('').trigger('change');
    $('#type-filter').val('').trigger('change');
    $('#date-from').val('');
    $('#date-to').val('');
    movementsTable.ajax.reload();
}

function refreshMovementsTable() {
    movementsTable.ajax.reload();
}

function toggleTimelineView() {
    timelineView = !timelineView;
    
    if (timelineView) {
        $('#table-view').hide();
        $('#timeline-view').show();
        loadTimelineData();
    } else {
        $('#timeline-view').hide();
        $('#table-view').show();
    }
}

function loadTimelineData() {
    const filters = {
        item_id: $('#item-filter').val(),
        type: $('#type-filter').val(),
        date_from: $('#date-from').val(),
        date_to: $('#date-to').val()
    };
    
    $.get('{{ route("inventory.api.stock.movements.timeline") }}', filters)
        .done(function(movements) {
            renderTimeline(movements);
        })
        .fail(function() {
            InventoryModule.showError('حدث خطأ أثناء جلب بيانات الخط الزمني');
        });
}

function renderTimeline(movements) {
    let timelineHtml = '';
    
    movements.forEach(function(movement) {
        const typeClass = movement.quantity > 0 ? 'in' : (movement.quantity < 0 ? 'out' : 'adjustment');
        const quantityColor = movement.quantity > 0 ? 'text-success' : 'text-danger';
        const sign = movement.quantity > 0 ? '+' : '';
        
        timelineHtml += `
            <div class="timeline-item ${typeClass}">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="mb-1">${movement.item.product.name}</h6>
                        <p class="mb-1">
                            <span class="${quantityColor} font-weight-bold">${sign}${movement.quantity} ${movement.item.product.unit?.symbol || ''}</span>
                            - ${getMovementTypeText(movement.type)}
                        </p>
                        <small class="text-muted">
                            ${movement.reason ? movement.reason + ' - ' : ''}
                            بواسطة ${movement.user.name}
                        </small>
                        ${movement.notes ? `<p class="mt-2 mb-0"><small class="text-muted">${movement.notes}</small></p>` : ''}
                    </div>
                    <div class="text-right">
                        <small class="text-muted">${new Date(movement.created_at).toLocaleString('ar-SA')}</small>
                        <div class="mt-1">
                            <small class="text-muted">
                                ${movement.stock_before} → ${movement.stock_after} ${movement.item.product.unit?.symbol || ''}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    $('#movements-timeline').html(timelineHtml || '<div class="text-center text-muted py-4">لا توجد حركات مخزون</div>');
}

function getMovementTypeText(type) {
    const types = {
        'purchase': 'شراء',
        'sale': 'بيع',
        'adjustment': 'تسوية',
        'transfer': 'نقل',
        'damage': 'تلف',
        'expired': 'انتهاء صلاحية',
        'count': 'جرد',
        'other': 'أخرى'
    };
    
    return types[type] || type;
}

function exportMovements() {
    const filters = {
        item_id: $('#item-filter').val(),
        type: $('#type-filter').val(),
        date_from: $('#date-from').val(),
        date_to: $('#date-to').val(),
        format: 'excel'
    };
    
    const queryString = new URLSearchParams(filters).toString();
    window.location.href = `{{ route("inventory.api.stock.movements.export") }}?${queryString}`;
}

function generateMovementReport() {
    const filters = {
        item_id: $('#item-filter').val(),
        type: $('#type-filter').val(),
        date_from: $('#date-from').val(),
        date_to: $('#date-to').val()
    };
    
    const queryString = new URLSearchParams(filters).toString();
    window.location.href = `{{ route("inventory.api.stock.movements.report") }}?${queryString}`;
}
</script>
@endpush
