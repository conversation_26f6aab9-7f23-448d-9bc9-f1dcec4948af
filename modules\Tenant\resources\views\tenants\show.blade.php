@extends('layouts.master')

@section('title', 'تفاصيل المستأجر')

@section('content')
<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h1 class="text-2xl font-bold text-gray-900 flex items-center gap-3">
                    <i class="fas fa-user text-blue-600"></i>
                    تفاصيل المستأجر: {{ $tenant->name }}
                </h1>
                <div class="flex gap-2">
                    <a href="{{ route('tenants.edit', $tenant->id) }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                        <i class="fas fa-edit text-sm"></i>
                        تعديل
                    </a>
                    <a href="{{ route('tenants.index') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                        <i class="fas fa-arrow-left text-sm"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="fas fa-info-circle text-blue-600"></i>
                        المعلومات الأساسية
                    </h2>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">اسم المستأجر</label>
                            <p class="text-gray-900 font-semibold">{{ $tenant->name }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">الكود</label>
                            <p class="text-gray-900">{{ $tenant->code ?? 'غير محدد' }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">نوع النشاط</label>
                            <p class="text-gray-900">
                                @switch($tenant->business_type)
                                    @case('restaurant')
                                        مطعم
                                        @break
                                    @case('cafe')
                                        مقهى
                                        @break
                                    @case('bakery')
                                        مخبز
                                        @break
                                    @case('fast_food')
                                        وجبات سريعة
                                        @break
                                    @case('catering')
                                        تموين
                                        @break
                                    @default
                                        {{ $tenant->business_type }}
                                @endswitch
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">الدولة</label>
                            <p class="text-gray-900">{{ $tenant->country->name ?? 'غير محدد' }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="fas fa-address-book text-blue-600"></i>
                        معلومات الاتصال
                    </h2>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">اسم جهة الاتصال</label>
                            <p class="text-gray-900">{{ $tenant->primary_contact_name }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">البريد الإلكتروني</label>
                            <p class="text-gray-900">
                                <a href="mailto:{{ $tenant->contact_email }}" class="text-blue-600 hover:text-blue-800">
                                    {{ $tenant->contact_email }}
                                </a>
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">رقم الهاتف</label>
                            <p class="text-gray-900">
                                <a href="tel:{{ $tenant->contact_phone }}" class="text-blue-600 hover:text-blue-800">
                                    {{ $tenant->contact_phone }}
                                </a>
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">الموقع الإلكتروني</label>
                            <p class="text-gray-900">
                                @if($tenant->website_url)
                                    <a href="{{ $tenant->website_url }}" target="_blank" class="text-blue-600 hover:text-blue-800">
                                        {{ $tenant->website_url }}
                                        <i class="fas fa-external-link-alt text-xs ml-1"></i>
                                    </a>
                                @else
                                    غير محدد
                                @endif
                            </p>
                        </div>
                    </div>
                    <div class="mt-6">
                        <label class="block text-sm font-medium text-gray-600 mb-1">عنوان النشاط</label>
                        <p class="text-gray-900">{{ $tenant->business_address }}</p>
                    </div>
                </div>
            </div>

            <!-- Business Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="fas fa-building text-blue-600"></i>
                        معلومات النشاط
                    </h2>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">الرقم الضريبي</label>
                            <p class="text-gray-900">{{ $tenant->tax_number ?? 'غير محدد' }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">رخصة النشاط</label>
                            <p class="text-gray-900">{{ $tenant->business_license ?? 'غير محدد' }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Settings -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="fas fa-cog text-blue-600"></i>
                        إعدادات النظام
                    </h2>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">المنطقة الزمنية</label>
                            <p class="text-gray-900">{{ $tenant->timezone }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">العملة</label>
                            <p class="text-gray-900">{{ $tenant->currency_code }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">اللغة</label>
                            <p class="text-gray-900">
                                {{ $tenant->language_code == 'ar' ? 'العربية' : 'English' }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Status Card -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="fas fa-chart-line text-blue-600"></i>
                        الحالة والإحصائيات
                    </h2>
                </div>
                <div class="p-6 space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-600 mb-1">الحالة</label>
                        <div class="flex items-center gap-2">
                            @if($tenant->status == 'active')
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i>
                                    نشط
                                </span>
                            @elseif($tenant->status == 'inactive')
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <i class="fas fa-times-circle mr-1"></i>
                                    غير نشط
                                </span>
                            @elseif($tenant->status == 'suspended')
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    <i class="fas fa-pause-circle mr-1"></i>
                                    معلق
                                </span>
                            @elseif($tenant->status == 'trial')
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <i class="fas fa-clock mr-1"></i>
                                    تجريبي
                                </span>
                            @endif
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-600 mb-1">عدد الفروع</label>
                        <p class="text-2xl font-bold text-blue-600">{{ $tenant->branches_count ?? 0 }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-600 mb-1">عدد المستخدمين</label>
                        <p class="text-2xl font-bold text-green-600">{{ $tenant->users_count ?? 0 }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-600 mb-1">تاريخ الإنشاء</label>
                        <p class="text-gray-900">{{ $tenant->created_at->format('Y-m-d') }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-600 mb-1">آخر تحديث</label>
                        <p class="text-gray-900">{{ $tenant->updated_at->format('Y-m-d H:i') }}</p>
                    </div>
                </div>
            </div>

            <!-- Actions Card -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="fas fa-tools text-blue-600"></i>
                        الإجراءات
                    </h2>
                </div>
                <div class="p-6 space-y-3">
                    @if($tenant->status == 'active')
                        <button onclick="changeStatus({{ $tenant->id }}, 'deactivate')" 
                                class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                            <i class="fas fa-times-circle text-sm"></i>
                            إلغاء التفعيل
                        </button>
                        <button onclick="changeStatus({{ $tenant->id }}, 'suspend')" 
                                class="w-full bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                            <i class="fas fa-pause-circle text-sm"></i>
                            تعليق
                        </button>
                    @elseif($tenant->status == 'inactive')
                        <button onclick="changeStatus({{ $tenant->id }}, 'activate')" 
                                class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                            <i class="fas fa-check-circle text-sm"></i>
                            تفعيل
                        </button>
                    @elseif($tenant->status == 'suspended')
                        <button onclick="changeStatus({{ $tenant->id }}, 'activate')" 
                                class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                            <i class="fas fa-play-circle text-sm"></i>
                            إعادة التفعيل
                        </button>
                    @endif
                    
                    <a href="{{ route('tenants.edit', $tenant->id) }}" 
                       class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                        <i class="fas fa-edit text-sm"></i>
                        تعديل المستأجر
                    </a>
                    
                    <button onclick="deleteTenant({{ $tenant->id }})" 
                            class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                        <i class="fas fa-trash text-sm"></i>
                        حذف المستأجر
                    </button>
                </div>
            </div>

            <!-- Quick Links -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="fas fa-link text-blue-600"></i>
                        روابط سريعة
                    </h2>
                </div>
                <div class="p-6 space-y-3">
                    <a href="{{ route('branches.index', ['tenant_id' => $tenant->id]) }}" 
                       class="w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                        <i class="fas fa-store text-sm"></i>
                        إدارة الفروع
                    </a>
                    
                    <a href="{{ route('subscriptions.index', ['tenant_id' => $tenant->id]) }}" 
                       class="w-full bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                        <i class="fas fa-credit-card text-sm"></i>
                        إدارة الاشتراكات
                    </a>
                    
                    <a href="{{ route('users.index', ['tenant_id' => $tenant->id]) }}" 
                       class="w-full bg-teal-600 hover:bg-teal-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                        <i class="fas fa-users text-sm"></i>
                        إدارة المستخدمين
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function changeStatus(tenantId, action) {
    const actions = {
        'activate': 'تفعيل',
        'deactivate': 'إلغاء تفعيل',
        'suspend': 'تعليق'
    };
    
    Swal.fire({
        title: 'تأكيد العملية',
        text: `هل أنت متأكد من ${actions[action]} هذا المستأجر؟`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'نعم، تأكيد',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`/admin/tenants/${tenantId}/${action}`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'تم بنجاح!',
                        text: `تم ${actions[action]} المستأجر بنجاح`,
                        confirmButtonText: 'موافق'
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ!',
                        text: data.message || `حدث خطأ أثناء ${actions[action]} المستأجر`,
                        confirmButtonText: 'موافق'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ!',
                    text: `حدث خطأ أثناء ${actions[action]} المستأجر`,
                    confirmButtonText: 'موافق'
                });
            });
        }
    });
}

function deleteTenant(tenantId) {
    Swal.fire({
        title: 'هل أنت متأكد؟',
        text: 'هل أنت متأكد من حذف هذا المستأجر؟ هذا الإجراء لا يمكن التراجع عنه.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'نعم، احذف!',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`/admin/tenants/${tenantId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'تم الحذف!',
                        text: 'تم حذف المستأجر بنجاح',
                        confirmButtonText: 'موافق'
                    }).then(() => {
                        window.location.href = '{{ route("tenants.index") }}';
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ!',
                        text: data.message || 'حدث خطأ أثناء حذف المستأجر',
                        confirmButtonText: 'موافق'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ!',
                    text: 'حدث خطأ أثناء حذف المستأجر',
                    confirmButtonText: 'موافق'
                });
            });
        }
    });
}
</script>

@if(session('success'))
    <script>
        Swal.fire({
            icon: 'success',
            title: 'تم بنجاح!',
            text: '{{ session('success') }}',
            confirmButtonText: 'موافق'
        });
    </script>
@endif

@if(session('error'))
    <script>
        Swal.fire({
            icon: 'error',
            title: 'خطأ!',
            text: '{{ session('error') }}',
            confirmButtonText: 'موافق'
        });
    </script>
@endif
@endsection