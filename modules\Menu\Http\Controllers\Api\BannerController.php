<?php

namespace Modules\Menu\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Modules\Menu\Services\BannerService;
use Modules\Menu\Http\Requests\StoreBannerRequest;
use Modules\Menu\Http\Requests\UpdateBannerRequest;
use App\Helpers\BranchHelper;
use App\Helpers\ResponseHelper;

/**
 * Banner API Controller
 * 
 * Handles API requests for banner management in the restaurant system.
 * Provides endpoints for CRUD operations, banner display, and analytics.
 * 
 * @package Modules\Menu\Http\Controllers\Api
 * <AUTHOR> POS Team
 * @version 1.0.0
 */
class BannerController extends Controller
{
    protected BannerService $bannerService;

    public function __construct(BannerService $bannerService)
    {
        $this->bannerService = $bannerService;
    }

    /**
     * Display a listing of banners
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            
            $filters = $request->only([
                'banner_type', 'position', 'display_location', 
                'is_active', 'is_featured', 'priority', 'search', 'sort_by', 'sort_order'
            ]);

            $perPage = $request->get('per_page', 15);
            $banners = $this->bannerService->getBannersForBranch($branchId, $filters, $perPage);

            return ResponseHelper::success('Banners retrieved successfully', $banners);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve banners: ' . $e->getMessage());
        }
    }

    /**
     * Store a newly created banner
     *
     * @param StoreBannerRequest $request
     * @return JsonResponse
     */
    public function store(StoreBannerRequest $request): JsonResponse
    {
        try {
            $data = BranchHelper::addBranchAndTenantIds($request->validated());
            $banner = $this->bannerService->createBanner($data);

            return ResponseHelper::success('Banner created successfully', $banner, 201);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to create banner: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified banner
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $banner = $this->bannerService->getBannerByIdForBranch($id, $branchId);

            if (!$banner) {
                return ResponseHelper::notFound('Banner not found');
            }

            return ResponseHelper::success('Banner retrieved successfully', $banner);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve banner: ' . $e->getMessage());
        }
    }

    /**
     * Update the specified banner
     *
     * @param UpdateBannerRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(UpdateBannerRequest $request, int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $banner = $this->bannerService->updateBannerForBranch($id, $request->validated(), $branchId);

            if (!$banner) {
                return ResponseHelper::notFound('Banner not found');
            }

            return ResponseHelper::success('Banner updated successfully', $banner);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to update banner: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified banner
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $deleted = $this->bannerService->deleteBannerForBranch($id, $branchId);

            if (!$deleted) {
                return ResponseHelper::notFound('Banner not found');
            }

            return ResponseHelper::success('Banner deleted successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to delete banner: ' . $e->getMessage());
        }
    }

    /**
     * Get banners for display
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function display(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'display_location' => 'required|string',
                'position' => 'nullable|string',
                'limit' => 'nullable|integer|min:1|max:50'
            ]);

            $branchId = BranchHelper::getCurrentBranchId();
            $banners = $this->bannerService->getBannersForDisplay(
                $branchId,
                $request->get('display_location'),
                $request->get('position'),
                $request->get('limit', 10)
            );

            return ResponseHelper::success('Display banners retrieved successfully', $banners);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve display banners: ' . $e->getMessage());
        }
    }

    /**
     * Get featured banners
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function featured(Request $request): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $limit = $request->get('limit', 10);
            
            $banners = $this->bannerService->getFeaturedBannersForBranch($branchId, $limit);

            return ResponseHelper::success('Featured banners retrieved successfully', $banners);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve featured banners: ' . $e->getMessage());
        }
    }
}