<?php

namespace Modules\Inventory\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Unit;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class UnitController extends Controller
{
    public function index()
    {
        return view('Inventory::units.index');
    }

    public function datatable(Request $request)
    {
        $units = Unit::with(['baseUnit']);

        return DataTables::of($units)
            ->addColumn('base_unit_name', function ($unit) {
                return $unit->baseUnit ? $unit->baseUnit->name : '-';
            })
            ->addColumn('status', function ($unit) {
                return $unit->is_active 
                    ? '<span class="badge bg-success">نشط</span>' 
                    : '<span class="badge bg-danger">غير نشط</span>';
            })
            ->addColumn('actions', function ($unit) {
                return '
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-primary edit-unit" 
                                data-id="' . $unit->id . '">
                            <i class="fe fe-edit"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-danger delete-unit" 
                                data-id="' . $unit->id . '">
                            <i class="fe fe-trash"></i>
                        </button>
                    </div>
                ';
            })
            ->rawColumns(['status', 'actions'])
            ->make(true);
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'abbreviation' => 'required|string|max:10|unique:units,abbreviation',
            'type' => 'required|string|in:weight,volume,length,count,time',
            'conversion_factor' => 'nullable|numeric|min:0',
            'base_unit_id' => 'nullable|exists:units,id',
        ]);

        $unit = Unit::create([
            'name' => $request->name,
            'abbreviation' => $request->abbreviation,
            'type' => $request->type,
            'conversion_factor' => $request->conversion_factor ?? 1,
            'base_unit_id' => $request->base_unit_id,
            'is_active' => $request->has('is_active'),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم إنشاء الوحدة بنجاح',
            'data' => $unit
        ]);
    }

    public function show($id)
    {
        $unit = Unit::with(['baseUnit', 'derivedUnits'])->findOrFail($id);
        return response()->json($unit);
    }

    public function update(Request $request, $id)
    {
        $unit = Unit::findOrFail($id);
        
        $request->validate([
            'name' => 'required|string|max:255',
            'abbreviation' => 'required|string|max:10|unique:units,abbreviation,' . $unit->id,
            'type' => 'required|string|in:weight,volume,length,count,time',
            'conversion_factor' => 'nullable|numeric|min:0',
            'base_unit_id' => 'nullable|exists:units,id',
        ]);

        $unit->update([
            'name' => $request->name,
            'abbreviation' => $request->abbreviation,
            'type' => $request->type,
            'conversion_factor' => $request->conversion_factor ?? 1,
            'base_unit_id' => $request->base_unit_id,
            'is_active' => $request->has('is_active'),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث الوحدة بنجاح',
            'data' => $unit
        ]);
    }

    public function destroy($id)
    {
        $unit = Unit::findOrFail($id);
        
        // Check if unit is being used by products
        if ($unit->products()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن حذف الوحدة لأنها مستخدمة في منتجات'
            ], 422);
        }

        // Check if unit has derived units
        if ($unit->derivedUnits()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن حذف الوحدة لأنها وحدة أساسية لوحدات أخرى'
            ], 422);
        }

        $unit->delete();

        return response()->json([
            'success' => true,
            'message' => 'تم حذف الوحدة بنجاح'
        ]);
    }

    public function getUnits()
    {
        $units = Unit::where('is_active', true)
            ->orderBy('name')
            ->get(['id', 'name', 'abbreviation', 'type']);

        return response()->json($units);
    }
}