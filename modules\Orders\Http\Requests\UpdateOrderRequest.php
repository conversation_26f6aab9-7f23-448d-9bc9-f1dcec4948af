<?php

namespace Modules\Orders\Http\Requests;

use App\Models\MenuItemAddon;
use Illuminate\Foundation\Http\FormRequest;

class UpdateOrderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $this->validateAddonsForMenuItems($validator);
        });
    }

    /**
     * Validate that addons belong to their respective menu items.
     */
    private function validateAddonsForMenuItems($validator)
    {
        $items = $this->input('items', []);
        
        foreach ($items as $itemIndex => $item) {
            if (!isset($item['menu_item_id']) || !isset($item['addons'])) {
                continue;
            }
            
            $menuItemId = $item['menu_item_id'];
            $addons = $item['addons'];
            
            foreach ($addons as $addonIndex => $addon) {
                if (!isset($addon['addon_id'])) {
                    continue;
                }
                
                $addonId = $addon['addon_id'];
                
                // Check if the addon belongs to the menu item
                $addonExists = MenuItemAddon::where('id', $addonId)
                    ->where('menu_item_id', $menuItemId)
                    ->exists();
                
                if (!$addonExists) {
                    $validator->errors()->add(
                        "items.{$itemIndex}.addons.{$addonIndex}.addon_id",
                        "The selected addon does not belong to the specified menu item."
                    );
                }
            }
        }
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rules = [
            'customer_id' => 'nullable|exists:customers,id',
            'table_id' => 'nullable|exists:tables,id',
            'order_type' => 'nullable|in:dine_in,takeaway,delivery,online',
            'status' => 'nullable|in:pending,confirmed,preparing,ready,served,completed,cancelled',
            'pax' => 'nullable|integer|min:1|max:20',
            'notes' => 'nullable|string|max:500',
            'delivery_man_id' => 'nullable|exists:users,id',
            'delivery_address' => 'nullable|string|max:500',
            'delivery_coordinates' => 'nullable|array',
            'delivery_coordinates.lat' => 'required_with:delivery_coordinates|numeric',
            'delivery_coordinates.lng' => 'required_with:delivery_coordinates|numeric',
            'items' => 'nullable|array',
            'items.*.menu_item_id' => 'required_with:items|exists:menu_items,id',
            'items.*.menu_item_name' => 'required_with:items|string|max:255',
            'items.*.quantity' => 'required_with:items|integer|min:1',
            'items.*.unit_price' => 'required_with:items|numeric|min:0',
            'items.*.notes' => 'nullable|string|max:255',
            'items.*.variant_id' => 'nullable|exists:menu_item_variants,id',
            'items.*.addons' => 'nullable|array',
            'items.*.addons.*.addon_id' => 'required_with:items.*.addons|exists:menu_item_addons,id',
            'items.*.addons.*.addon_name' => 'required_with:items.*.addons|string|max:255',
            'items.*.addons.*.quantity' => 'required_with:items.*.addons|integer|min:1',
            'items.*.addons.*.unit_price' => 'required_with:items.*.addons|numeric|min:0',
            'items.*.addons.*.total_price' => 'required_with:items.*.addons|numeric|min:0',
        ];

        // Order type specific validation rules for updates
        if ($this->input('order_type') === 'dine_in') {
            $rules['table_id'] = 'required|exists:tables,id';
            $rules['pax'] = 'required|integer|min:1|max:20';
        } elseif ($this->input('order_type') === 'delivery') {
            $rules['customer_id'] = 'required|exists:customers,id';
            $rules['delivery_man_id'] = 'required|exists:users,id';
            $rules['delivery_address'] = 'required|string|max:500';
        } elseif ($this->input('order_type') === 'online') {
            $rules['customer_id'] = 'required|exists:customers,id';
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'customer_id.required' => 'Customer is required for this order type.',
            'customer_id.exists' => 'Selected customer does not exist.',
            'table_id.required' => 'Table selection is required for dine-in orders.',
            'table_id.exists' => 'Selected table does not exist.',
            'order_type.in' => 'Invalid order type selected.',
            'status.in' => 'Invalid order status selected.',
            'pax.required' => 'Number of people (PAX) is required for dine-in orders.',
            'pax.min' => 'Number of people must be at least 1.',
            'pax.max' => 'Number of people cannot exceed 20.',
            'delivery_man_id.required' => 'Delivery person is required for delivery orders.',
            'delivery_man_id.exists' => 'Selected delivery person does not exist.',
            'delivery_address.required' => 'Delivery address is required for delivery orders.',
            'delivery_coordinates.lat.required_with' => 'Latitude is required when coordinates are provided.',
            'delivery_coordinates.lng.required_with' => 'Longitude is required when coordinates are provided.',
            'items.*.menu_item_id.required_with' => 'Menu item is required for each order item.',
            'items.*.menu_item_id.exists' => 'Selected menu item does not exist.',
            'items.*.menu_item_name.required_with' => 'Menu item name is required for each order item.',
            'items.*.quantity.required_with' => 'Quantity is required for each item.',
            'items.*.quantity.min' => 'Quantity must be at least 1.',
            'items.*.unit_price.required_with' => 'Unit price is required for each item.',
            'items.*.unit_price.min' => 'Unit price must be greater than or equal to 0.',
            'items.*.variant_id.exists' => 'Selected menu item variant does not exist.',
            'items.*.addons.*.addon_id.required_with' => 'Addon ID is required for each addon.',
            'items.*.addons.*.addon_id.exists' => 'The selected addon does not exist.',
            'items.*.addons.*.addon_name.required_with' => 'Addon name is required for each addon.',
            'items.*.addons.*.quantity.required_with' => 'Addon quantity is required.',
            'items.*.addons.*.quantity.min' => 'Addon quantity must be at least 1.',
            'items.*.addons.*.unit_price.required_with' => 'Addon unit price is required.',
            'items.*.addons.*.unit_price.min' => 'Addon unit price must be greater than or equal to 0.',
            'items.*.addons.*.total_price.required_with' => 'Addon total price is required.',
            'items.*.addons.*.total_price.min' => 'Addon total price must be greater than or equal to 0.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'branch_id' => 'branch',
            'customer_id' => 'customer',
            'table_id' => 'table',
            'order_type' => 'order type',
            'pax' => 'number of people',
            'delivery_man_id' => 'delivery person',
            'delivery_address' => 'delivery address',
            'delivery_coordinates.lat' => 'latitude',
            'delivery_coordinates.lng' => 'longitude',
            'items.*.menu_item_id' => 'menu item',
            'items.*.menu_item_name' => 'menu item name',
            'items.*.quantity' => 'quantity',
            'items.*.unit_price' => 'unit price',
            'items.*.variant_id' => 'menu item variant',
            'items.*.addons.*.addon_id' => 'addon',
            'items.*.addons.*.addon_name' => 'addon name',
            'items.*.addons.*.quantity' => 'addon quantity',
            'items.*.addons.*.unit_price' => 'addon unit price',
            'items.*.addons.*.total_price' => 'addon total price',
        ];
    }
}