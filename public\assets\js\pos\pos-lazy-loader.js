/**
 * POS Lazy Loader - Handles lazy loading and caching for large datasets
 * Optimizes performance for 300+ menu items with smart loading strategies
 */

class POSLazyLoader {
    constructor() {
        this.cache = new Map();
        this.loadingQueue = new Set();
        this.batchSize = 50;
        this.preloadDistance = 100; // pixels before element comes into view
        this.observer = null;
        this.imageCache = new Map();
        
        this.init();
    }

    /**
     * Initialize lazy loader
     */
    init() {
        this.setupIntersectionObserver();
        this.setupImageLazyLoading();
    }

    /**
     * Setup intersection observer for lazy loading
     */
    setupIntersectionObserver() {
        if ('IntersectionObserver' in window) {
            this.observer = new IntersectionObserver(
                (entries) => this.handleIntersection(entries),
                {
                    root: null,
                    rootMargin: `${this.preloadDistance}px`,
                    threshold: 0.1
                }
            );
        }
    }

    /**
     * Setup image lazy loading
     */
    setupImageLazyLoading() {
        this.imageObserver = new IntersectionObserver(
            (entries) => this.handleImageIntersection(entries),
            {
                root: null,
                rootMargin: '50px',
                threshold: 0.1
            }
        );
    }

    /**
     * Handle intersection for lazy loading
     */
    handleIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;
                const itemId = element.dataset.itemId;
                
                if (itemId && !this.cache.has(itemId)) {
                    this.loadMenuItemDetails(itemId);
                }
                
                this.observer.unobserve(element);
            }
        });
    }

    /**
     * Handle image intersection for lazy loading
     */
    handleImageIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                const src = img.dataset.src;
                
                if (src) {
                    this.loadImage(img, src);
                }
                
                this.imageObserver.unobserve(img);
            }
        });
    }

    /**
     * Load menu item details lazily
     */
    async loadMenuItemDetails(itemId) {
        if (this.loadingQueue.has(itemId)) return;
        
        this.loadingQueue.add(itemId);
        
        try {
            const response = await fetch(`/api/pos/menu-items/${itemId}/details`, {
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            if (response.ok) {
                const itemDetails = await response.json();
                this.cache.set(itemId, itemDetails);
                
                // Update UI if item is visible
                this.updateMenuItemUI(itemId, itemDetails);
            }
        } catch (error) {
            console.error('Failed to load menu item details:', error);
        } finally {
            this.loadingQueue.delete(itemId);
        }
    }

    /**
     * Load image with caching
     */
    loadImage(imgElement, src) {
        if (this.imageCache.has(src)) {
            imgElement.src = src;
            imgElement.classList.add('loaded');
            return;
        }

        const img = new Image();
        img.onload = () => {
            this.imageCache.set(src, true);
            imgElement.src = src;
            imgElement.classList.add('loaded');
            
            // Add fade-in effect
            imgElement.style.opacity = '0';
            imgElement.style.transition = 'opacity 0.3s ease';
            setTimeout(() => {
                imgElement.style.opacity = '1';
            }, 10);
        };
        
        img.onerror = () => {
            imgElement.style.display = 'none';
            const placeholder = imgElement.nextElementSibling;
            if (placeholder) {
                placeholder.style.display = 'flex';
            }
        };
        
        img.src = src;
    }

    /**
     * Update menu item UI with loaded details
     */
    updateMenuItemUI(itemId, details) {
        const element = document.querySelector(`[data-item-id="${itemId}"]`);
        if (!element) return;

        // Update variants and addons indicators
        if (details.variants && details.variants.length > 0) {
            this.addBadge(element, 'variant', 'V', 'Has Variants');
        }
        
        if (details.addons && details.addons.length > 0) {
            this.addBadge(element, 'addon', 'A', 'Has Add-ons');
        }

        // Update description if available
        if (details.long_description && !element.querySelector('.menu-item-description')) {
            const content = element.querySelector('.menu-item-content');
            const title = content.querySelector('.menu-item-title');
            
            const description = document.createElement('p');
            description.className = 'menu-item-description line-clamp-2';
            description.textContent = details.long_description;
            
            title.insertAdjacentElement('afterend', description);
        }
    }

    /**
     * Add badge to menu item
     */
    addBadge(element, type, text, title) {
        let badgesContainer = element.querySelector('.menu-item-badges');
        
        if (!badgesContainer) {
            badgesContainer = document.createElement('div');
            badgesContainer.className = 'menu-item-badges';
            element.appendChild(badgesContainer);
        }

        if (!badgesContainer.querySelector(`.menu-badge.${type}`)) {
            const badge = document.createElement('span');
            badge.className = `menu-badge ${type}`;
            badge.title = title;
            badge.textContent = text;
            badgesContainer.appendChild(badge);
        }
    }

    /**
     * Observe menu item for lazy loading
     */
    observeMenuItem(element) {
        if (this.observer) {
            this.observer.observe(element);
        }
    }

    /**
     * Observe image for lazy loading
     */
    observeImage(imgElement) {
        if (this.imageObserver) {
            this.imageObserver.observe(imgElement);
        }
    }

    /**
     * Preload critical menu items
     */
    async preloadCriticalItems(itemIds) {
        const batchPromises = [];
        
        for (let i = 0; i < itemIds.length; i += this.batchSize) {
            const batch = itemIds.slice(i, i + this.batchSize);
            batchPromises.push(this.loadBatch(batch));
        }
        
        await Promise.allSettled(batchPromises);
    }

    /**
     * Load a batch of menu items
     */
    async loadBatch(itemIds) {
        try {
            const response = await fetch('/api/pos/menu-items/batch', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({ item_ids: itemIds })
            });
            
            if (response.ok) {
                const items = await response.json();
                items.forEach(item => {
                    this.cache.set(item.id.toString(), item);
                });
            }
        } catch (error) {
            console.error('Failed to load batch:', error);
        }
    }

    /**
     * Get cached item details
     */
    getCachedItem(itemId) {
        return this.cache.get(itemId);
    }

    /**
     * Clear cache
     */
    clearCache() {
        this.cache.clear();
        this.imageCache.clear();
    }

    /**
     * Get cache statistics
     */
    getCacheStats() {
        return {
            itemsCount: this.cache.size,
            imagesCount: this.imageCache.size,
            loadingQueue: this.loadingQueue.size
        };
    }

    /**
     * Preload images for visible items
     */
    preloadVisibleImages() {
        const visibleImages = document.querySelectorAll('.menu-item img[data-src]');
        
        visibleImages.forEach(img => {
            const rect = img.getBoundingClientRect();
            const isVisible = rect.top < window.innerHeight && rect.bottom > 0;
            
            if (isVisible) {
                const src = img.dataset.src;
                if (src) {
                    this.loadImage(img, src);
                }
            }
        });
    }

    /**
     * Setup progressive loading for menu items
     */
    setupProgressiveLoading(container) {
        const items = container.querySelectorAll('.menu-item');
        
        items.forEach((item, index) => {
            // Add loading delay based on position
            const delay = Math.floor(index / 6) * 100; // 100ms delay per row
            
            setTimeout(() => {
                item.classList.add('loaded');
                this.observeMenuItem(item);
                
                // Observe images
                const img = item.querySelector('img[data-src]');
                if (img) {
                    this.observeImage(img);
                }
            }, delay);
        });
    }

    /**
     * Optimize for large datasets
     */
    optimizeForLargeDataset() {
        // Reduce batch size for slower connections
        if (navigator.connection) {
            const connection = navigator.connection;
            
            if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
                this.batchSize = 20;
            } else if (connection.effectiveType === '3g') {
                this.batchSize = 35;
            }
        }

        // Adjust preload distance based on scroll speed
        let lastScrollY = window.scrollY;
        let scrollSpeed = 0;
        
        window.addEventListener('scroll', () => {
            const currentScrollY = window.scrollY;
            scrollSpeed = Math.abs(currentScrollY - lastScrollY);
            lastScrollY = currentScrollY;
            
            // Increase preload distance for fast scrolling
            if (scrollSpeed > 50) {
                this.preloadDistance = 200;
            } else {
                this.preloadDistance = 100;
            }
        });
    }

    /**
     * Cleanup observers
     */
    cleanup() {
        if (this.observer) {
            this.observer.disconnect();
        }
        
        if (this.imageObserver) {
            this.imageObserver.disconnect();
        }
        
        this.clearCache();
    }
}

// Initialize lazy loader
document.addEventListener('DOMContentLoaded', () => {
    window.posLazyLoader = new POSLazyLoader();
    window.posLazyLoader.optimizeForLargeDataset();
});
