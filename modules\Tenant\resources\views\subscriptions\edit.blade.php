@extends('layouts.master')

@section('title', 'تعديل الاشتراك')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h1 class="text-xl font-semibold text-gray-900">تعديل الاشتراك</h1>
                <a href="{{ route('subscriptions-web.index') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                    <i class="fas fa-arrow-right text-sm"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>
        
        <div class="p-6">
            <form method="POST" action="{{ route('subscriptions-web.update', $subscription) }}">
                @csrf
                @method('PUT')
                <div class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="tenant_id" class="block text-sm font-medium text-gray-700 mb-2">
                                المستأجر <span class="text-red-500">*</span>
                            </label>
                            <select name="tenant_id" id="tenant_id" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 @error('tenant_id') border-red-500 @enderror">
                                <option value="">اختر المستأجر</option>
                                @foreach($tenants as $tenant)
                                    <option value="{{ $tenant->id }}" {{ (old('tenant_id') ?? $subscription->tenant_id) == $tenant->id ? 'selected' : '' }}>
                                        {{ $tenant->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('tenant_id')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="plan_id" class="block text-sm font-medium text-gray-700 mb-2">
                                الباقة <span class="text-red-500">*</span>
                            </label>
                            <select name="plan_id" id="plan_id" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 @error('plan_id') border-red-500 @enderror">
                                <option value="">اختر الباقة</option>
                                @foreach($plans as $plan)
                                    <option value="{{ $plan->id }}" data-price="{{ $plan->price }}" {{ (old('plan_id') ?? $subscription->plan_id) == $plan->id ? 'selected' : '' }}>
                                        {{ $plan->name }} - {{ number_format($plan->price, 2) }} ر.س ({{ $plan->billing_cycle === 'monthly' ? 'شهري' : ($plan->billing_cycle === 'yearly' ? 'سنوي' : 'ربع سنوي') }})
                                    </option>
                                @endforeach
                            </select>
                            @error('plan_id')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="start_date" class="block text-sm font-medium text-gray-700 mb-2">
                                تاريخ البداية <span class="text-red-500">*</span>
                            </label>
                            <input type="date" name="start_date" id="start_date" value="{{ old('start_date') ?? $subscription->start_date }}" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 @error('start_date') border-red-500 @enderror">
                            @error('start_date')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="end_date" class="block text-sm font-medium text-gray-700 mb-2">
                                تاريخ النهاية
                            </label>
                            <input type="date" name="end_date" id="end_date" value="{{ old('end_date') ?? $subscription->end_date }}" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 @error('end_date') border-red-500 @enderror">
                            @error('end_date')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="amount" class="block text-sm font-medium text-gray-700 mb-2">
                                المبلغ <span class="text-red-500">*</span>
                            </label>
                            <input type="number" name="amount" id="amount" step="0.01" min="0" value="{{ old('amount') ?? $subscription->amount }}" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 @error('amount') border-red-500 @enderror">
                            @error('amount')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                                الحالة <span class="text-red-500">*</span>
                            </label>
                            <select name="status" id="status" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 @error('status') border-red-500 @enderror">
                                <option value="active" {{ (old('status') ?? $subscription->status) == 'active' ? 'selected' : '' }}>نشط</option>
                                <option value="pending" {{ (old('status') ?? $subscription->status) == 'pending' ? 'selected' : '' }}>معلق</option>
                                <option value="suspended" {{ (old('status') ?? $subscription->status) == 'suspended' ? 'selected' : '' }}>موقوف</option>
                                <option value="cancelled" {{ (old('status') ?? $subscription->status) == 'cancelled' ? 'selected' : '' }}>ملغي</option>
                            </select>
                            @error('status')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="payment_method" class="block text-sm font-medium text-gray-700 mb-2">
                                طريقة الدفع
                            </label>
                            <input type="text" name="payment_method" id="payment_method" value="{{ old('payment_method') ?? $subscription->payment_method }}" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 @error('payment_method') border-red-500 @enderror">
                            @error('payment_method')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div class="flex items-center pt-6">
                            <input type="checkbox" name="auto_renew" id="auto_renew" value="1" {{ (old('auto_renew') ?? $subscription->auto_renew) ? 'checked' : '' }} class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                            <label for="auto_renew" class="mr-2 block text-sm text-gray-900">
                                تجديد تلقائي
                            </label>
                        </div>
                    </div>
                    
                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">
                            ملاحظات
                        </label>
                        <textarea name="notes" id="notes" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 @error('notes') border-red-500 @enderror">{{ old('notes') ?? $subscription->notes }}</textarea>
                        @error('notes')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
                
                <div class="flex justify-end gap-3 mt-8 pt-6 border-t border-gray-200">
                    <a href="{{ route('subscriptions-web.index') }}" class="px-6 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                        إلغاء
                    </a>
                    <button type="submit" class="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                        تحديث الاشتراك
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-fill amount when plan is selected
    document.getElementById('plan_id').addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const price = selectedOption.getAttribute('data-price');
        
        if (price) {
            document.getElementById('amount').value = price;
        }
    });
});
</script>
@endsection