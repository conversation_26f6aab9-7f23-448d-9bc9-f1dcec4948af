/**
 * POS System CSS - Styles for offline functionality and enhanced UI
 */

/* Notification Styles */
.notification {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notification-success {
    border-left: 4px solid #10b981;
}

.notification-error {
    border-left: 4px solid #ef4444;
}

.notification-warning {
    border-left: 4px solid #f59e0b;
}

.notification-info {
    border-left: 4px solid #3b82f6;
}

/* Offline Banner Styles */
#offlineBanner {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Sync Status Indicator */
#syncStatusIndicator {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

.sync-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Connection Status Styles */
#connectionStatus {
    transition: all 0.3s ease;
    border-radius: 20px;
    font-weight: 600;
}

#connectionStatus.online {
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
    color: #166534;
    border: 1px solid #22c55e;
}

#connectionStatus.offline {
    background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
    color: #991b1b;
    border: 1px solid #ef4444;
    animation: pulse 2s infinite;
}

/* Loading Overlay Enhancements */
#loadingOverlay {
    backdrop-filter: blur(5px);
    background: rgba(255, 255, 255, 0.9);
}

/* Menu Item Enhancements for Offline Mode */
.menu-item.offline-available {
    position: relative;
}

.menu-item.offline-available::after {
    content: '';
    position: absolute;
    top: 8px;
    left: 8px;
    width: 12px;
    height: 12px;
    background: #10b981;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.menu-item.offline-unavailable {
    opacity: 0.6;
    pointer-events: none;
}

.menu-item.offline-unavailable::after {
    content: '';
    position: absolute;
    top: 8px;
    left: 8px;
    width: 12px;
    height: 12px;
    background: #ef4444;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Order Item Offline Status */
.order-item.pending-sync {
    border-left: 4px solid #f59e0b;
    background: linear-gradient(145deg, #fef3c7 0%, #fde68a 100%);
}

.order-item.pending-sync::before {
    content: 'Pending Sync';
    position: absolute;
    top: 8px;
    right: 8px;
    background: #f59e0b;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 600;
}

.order-item.sync-failed {
    border-left: 4px solid #ef4444;
    background: linear-gradient(145deg, #fef2f2 0%, #fecaca 100%);
}

.order-item.sync-failed::before {
    content: 'Sync Failed';
    position: absolute;
    top: 8px;
    right: 8px;
    background: #ef4444;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 600;
}

/* Pulse Animation */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

/* Offline Mode Indicators */
.offline-mode-indicator {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    padding: 12px 16px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
    z-index: 1000;
    animation: pulse 2s infinite;
}

.offline-mode-indicator i {
    margin-right: 8px;
}

/* Sync Queue Status */
.sync-queue-status {
    position: fixed;
    bottom: 80px;
    right: 20px;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 999;
    min-width: 200px;
}

.sync-queue-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px 0;
    font-size: 12px;
    color: #6b7280;
}

.sync-queue-item .status {
    padding: 2px 6px;
    border-radius: 8px;
    font-weight: 600;
}

.sync-queue-item .status.pending {
    background: #fef3c7;
    color: #92400e;
}

.sync-queue-item .status.syncing {
    background: #dbeafe;
    color: #1e40af;
}

.sync-queue-item .status.failed {
    background: #fef2f2;
    color: #991b1b;
}

/* Enhanced Button Styles for Offline Mode */
.btn-offline {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    font-weight: 600;
    cursor: not-allowed;
    opacity: 0.7;
}

.btn-offline:hover {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    transform: none;
}

/* Data Sync Indicators */
.data-sync-indicator {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: #6b7280;
}

.data-sync-indicator.synced {
    color: #10b981;
}

.data-sync-indicator.pending {
    color: #f59e0b;
}

.data-sync-indicator.failed {
    color: #ef4444;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .offline-mode-indicator {
        bottom: 10px;
        right: 10px;
        padding: 8px 12px;
        font-size: 12px;
    }
    
    .sync-queue-status {
        bottom: 60px;
        right: 10px;
        min-width: 180px;
    }
    
    #offlineBanner {
        font-size: 14px;
        padding: 8px 16px;
    }
}

/* Print Styles for Offline Receipts */
@media print {
    .offline-receipt {
        background: white;
        color: black;
    }
    
    .offline-receipt .offline-notice {
        border: 2px dashed #666;
        padding: 10px;
        margin: 10px 0;
        text-align: center;
        font-weight: bold;
    }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    .notification,
    #connectionStatus,
    .menu-item,
    .sync-spinner {
        animation: none;
        transition: none;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    #connectionStatus.online {
        background: #22c55e;
        color: white;
        border: 2px solid #166534;
    }

    #connectionStatus.offline {
        background: #ef4444;
        color: white;
        border: 2px solid #991b1b;
    }

    .notification {
        border-width: 2px;
        border-style: solid;
    }
}

/* Performance Optimizations for Large Datasets */
.menu-container {
    contain: layout style paint;
}

.menu-item {
    contain: layout style paint;
    will-change: transform;
}

.menu-item.loaded {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.menu-item:not(.loaded) {
    opacity: 0;
    transform: translateY(20px);
}

/* Virtual Scrolling Optimizations */
#menuVirtualContainer {
    contain: strict;
    overflow-anchor: none;
}

#menuVirtualContent {
    contain: layout style paint;
}

/* Lazy Loading Image Styles */
.menu-item img {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.menu-item img.loaded {
    opacity: 1;
}

.menu-item img[data-src] {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    background-size: 400% 400%;
    animation: shimmer 1.5s ease-in-out infinite;
}

@keyframes shimmer {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Search Results Optimization */
#searchResults {
    contain: layout style paint;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

/* Pagination Styles */
#menuPagination button:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

#menuPagination button:not(:disabled):hover {
    background: #3b82f6;
    color: white;
    transform: translateY(-1px);
}

/* Loading States */
.menu-item-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Memory Optimization */
.menu-item:not(.visible) {
    visibility: hidden;
    pointer-events: none;
}

/* Scroll Performance */
.menu-container {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* GPU Acceleration for Smooth Scrolling */
.menu-item,
.menu-item-image,
.menu-badge {
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Reduce Paint Operations */
.menu-item-content {
    contain: layout style;
}

/* Optimize Text Rendering */
.menu-item-title,
.menu-item-description {
    text-rendering: optimizeSpeed;
}

/* Large Dataset Specific Optimizations */
@media screen and (min-width: 1024px) {
    .menu-container {
        contain: strict;
    }

    .menu-item {
        contain: layout style paint;
    }
}

/* Mobile Optimizations for Large Datasets */
@media screen and (max-width: 768px) {
    .menu-item {
        contain: layout style;
        will-change: auto;
    }

    .menu-item img {
        image-rendering: optimizeSpeed;
    }

    /* Reduce animations on mobile for better performance */
    .menu-item:hover {
        transform: none;
    }

    .menu-badge {
        animation: none;
    }
}
