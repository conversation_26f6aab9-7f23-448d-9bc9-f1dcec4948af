<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

echo "Current offers table columns:\n";
$columns = Schema::getColumnListing('offers');
foreach ($columns as $column) {
    echo "- $column\n";
}

echo "\nTable structure:\n";
$result = DB::select('DESCRIBE offers');
foreach ($result as $row) {
    echo "- {$row->Field}: {$row->Type} " . ($row->Null === 'YES' ? 'NULL' : 'NOT NULL') . "\n";
}