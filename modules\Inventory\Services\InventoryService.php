<?php

namespace Modules\Inventory\Services;

use App\Models\Product;
use App\Models\BranchInventory;
use App\Models\InventoryMovement;
use App\Models\InventoryLog;
use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderItem;
use App\Models\Supplier;
use App\Models\Recipe;
use App\Models\RecipeIngredient;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Exception;

class InventoryService
{
    /**
     * Get all inventory items with filtering and pagination
     */
    public function getAllItems(array $filters = [])
    {
        $query = BranchInventory::with(['product', 'branch'])
            ->where('branch_id', $this->getCurrentBranchId());

        // Apply filters
        if (isset($filters['search'])) {
            $query->whereHas('product', function ($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('sku', 'like', '%' . $filters['search'] . '%');
            });
        }

        if (isset($filters['category'])) {
            $query->whereHas('product', function ($q) use ($filters) {
                $q->where('category', $filters['category']);
            });
        }

        if (isset($filters['low_stock']) && $filters['low_stock']) {
            $query->whereRaw('current_stock <= minimum_level');
        }

        return $query->paginate($filters['per_page'] ?? 15);
    }

    /**
     * Get inventory item by ID
     */
    public function getItemById(string $id)
    {
        return BranchInventory::with(['product', 'branch', 'movements' => function ($query) {
            $query->latest()->limit(10);
        }])->where('branch_id', $this->getCurrentBranchId())
          ->findOrFail($id);
    }

    /**
     * Create new inventory item
     */
    public function createItem(array $data)
    {
        DB::beginTransaction();
        try {
            // Create or get product
            $product = $this->createOrUpdateProduct($data);
            
            // Create branch inventory record
            $inventory = BranchInventory::create([
                'branch_id' => $this->getCurrentBranchId(),
                'product_id' => $product->id,
                'current_stock' => $data['initial_stock'] ?? 0,
                'minimum_level' => $data['minimum_stock'] ?? 0,
                'maximum_level' => $data['maximum_stock'] ?? null,
                'reorder_point' => $data['reorder_point'] ?? $data['minimum_stock'] ?? 0,
                'cost_per_unit' => $data['cost_per_unit'] ?? 0,
                'last_updated' => now(),
            ]);

            // Log initial stock if any
            if (($data['initial_stock'] ?? 0) > 0) {
                $this->logInventoryMovement($inventory->id, 'initial_stock', $data['initial_stock'], 'Initial stock entry');
            }

            DB::commit();
            return $inventory->load(['product', 'branch']);
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Update inventory item
     */
    public function updateItem(string $id, array $data)
    {
        DB::beginTransaction();
        try {
            $inventory = BranchInventory::where('branch_id', $this->getCurrentBranchId())
                ->findOrFail($id);

            // Update product if needed
            if (isset($data['name']) || isset($data['sku']) || isset($data['description'])) {
                $this->createOrUpdateProduct($data, $inventory->product);
            }

            // Update inventory record
            $inventory->update([
                'minimum_level' => $data['minimum_stock'] ?? $inventory->minimum_level,
                'maximum_level' => $data['maximum_stock'] ?? $inventory->maximum_level,
                'reorder_point' => $data['reorder_point'] ?? $inventory->reorder_point,
                'cost_per_unit' => $data['cost_per_unit'] ?? $inventory->cost_per_unit,
                'last_updated' => now(),
            ]);

            DB::commit();
            return $inventory->load(['product', 'branch']);
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Delete inventory item
     */
    public function deleteItem(string $id)
    {
        $inventory = BranchInventory::where('branch_id', $this->getCurrentBranchId())
            ->findOrFail($id);
        
        // Check if item has stock
        if ($inventory->current_stock > 0) {
            throw new Exception('Cannot delete item with existing stock. Please adjust stock to zero first.');
        }

        $inventory->delete();
    }

    /**
     * Update stock quantity
     */
    public function updateStock(string $id, int $quantity, string $type, string $reason = null)
    {
        DB::beginTransaction();
        try {
            $inventory = BranchInventory::where('branch_id', $this->getCurrentBranchId())
                ->findOrFail($id);

            $oldStock = $inventory->current_stock;
            $newStock = $this->calculateNewStock($oldStock, $quantity, $type);

            if ($newStock < 0) {
                throw new Exception('Insufficient stock. Current stock: ' . $oldStock);
            }

            $inventory->update([
                'current_stock' => $newStock,
                'last_updated' => now(),
            ]);

            // Log the movement
            $this->logInventoryMovement($inventory->id, $type, $quantity, $reason, $oldStock, $newStock);

            DB::commit();
            return $inventory->load(['product', 'branch']);
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get low stock items
     */
    public function getLowStockItems()
    {
        return BranchInventory::with(['product', 'branch'])
            ->where('branch_id', $this->getCurrentBranchId())
            ->whereRaw('current_stock <= minimum_level')
            ->orderBy('current_stock', 'asc')
            ->get();
    }

    /**
     * Get critical stock items (out of stock)
     */
    public function getCriticalStockItems()
    {
        return BranchInventory::with(['product.unit', 'branch'])
            ->where('branch_id', $this->getCurrentBranchId())
            ->where('current_stock', '<=', 0)
            ->orderBy('current_stock', 'asc')
            ->get();
    }

    /**
     * Get recent inventory movements
     */
    public function getRecentMovements()
    {
        return InventoryMovement::with(['branchInventory.product', 'user'])
            ->whereHas('branchInventory', function ($query) {
                $query->where('branch_id', $this->getCurrentBranchId());
            })
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();
    }

    /**
     * Get stock analytics for overview cards
     */
    public function getStockAnalytics()
    {
        $branchId = $this->getCurrentBranchId();
        
        $criticalStock = BranchInventory::where('branch_id', $branchId)
            ->where('current_stock', '<=', 0)
            ->count();
            
        $lowStock = BranchInventory::where('branch_id', $branchId)
            ->whereRaw('current_stock > 0 AND current_stock <= minimum_level')
            ->count();
            
        $normalStock = BranchInventory::where('branch_id', $branchId)
            ->whereRaw('current_stock > minimum_level AND current_stock <= maximum_level')
            ->count();
            
        $overstock = BranchInventory::where('branch_id', $branchId)
            ->whereRaw('current_stock > maximum_level')
            ->count();
        
        return [
            'critical_stock' => $criticalStock,
            'low_stock' => $lowStock,
            'normal_stock' => $normalStock,
            'overstock' => $overstock,
        ];
    }

    /**
     * Get inventory movements for an item
     */
    public function getItemMovements(string $id)
    {
        $inventory = BranchInventory::where('branch_id', $this->getCurrentBranchId())
            ->findOrFail($id);

        return InventoryMovement::where('branch_inventory_id', $inventory->id)
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->paginate(20);
    }

    /**
     * Deduct ingredients based on recipe
     */
    public function deductIngredientsFromRecipe(int $recipeId, int $quantity = 1)
    {
        DB::beginTransaction();
        try {
            $recipe = Recipe::with('ingredients.product')->findOrFail($recipeId);
            
            foreach ($recipe->ingredients as $ingredient) {
                $requiredQuantity = $ingredient->quantity * $quantity;
                
                $inventory = BranchInventory::where('branch_id', $this->getCurrentBranchId())
                    ->where('product_id', $ingredient->product_id)
                    ->first();

                if (!$inventory) {
                    throw new Exception("Ingredient {$ingredient->product->name} not found in inventory");
                }

                if ($inventory->current_stock < $requiredQuantity) {
                    throw new Exception("Insufficient stock for {$ingredient->product->name}. Required: {$requiredQuantity}, Available: {$inventory->current_stock}");
                }

                $this->updateStock($inventory->id, $requiredQuantity, 'subtract', "Used in recipe: {$recipe->name}");
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get inventory analytics
     */
    public function getInventoryAnalytics()
    {
        $branchId = $this->getCurrentBranchId();
        
        return [
            'total_items' => BranchInventory::where('branch_id', $branchId)->count(),
            'low_stock_items' => BranchInventory::where('branch_id', $branchId)
                ->whereRaw('current_stock <= minimum_level')->count(),
            'out_of_stock_items' => BranchInventory::where('branch_id', $branchId)
                ->where('current_stock', 0)->count(),
            'total_value' => BranchInventory::where('branch_id', $branchId)
                ->selectRaw('SUM(current_stock * cost_per_unit) as total')
                ->value('total') ?? 0,
            'recent_movements' => InventoryMovement::whereHas('branchInventory', function ($query) use ($branchId) {
                $query->where('branch_id', $branchId);
            })->where('created_at', '>=', now()->subDays(7))->count(),
        ];
    }

    /**
     * Generate reorder suggestions
     */
    public function getReorderSuggestions()
    {
        return BranchInventory::with(['product'])
            ->where('branch_id', $this->getCurrentBranchId())
            ->whereRaw('current_stock <= reorder_point')
            ->orderBy('current_stock', 'asc')
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'product' => $item->product->name,
                    'sku' => $item->product->sku,
                    'current_stock' => $item->current_stock,
                    'reorder_point' => $item->reorder_point,
                    'suggested_quantity' => max($item->maximum_level - $item->current_stock, $item->minimum_level),
                    'cost_per_unit' => $item->cost_per_unit,
                ];
            });
    }

    /**
     * Get movement statistics for overview cards
     */
    public function getMovementStats()
    {
        $branchId = $this->getCurrentBranchId();
        
        $totalIn = InventoryMovement::whereHas('branchInventory', function ($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })->where('type', 'in')->sum('quantity');
        
        $totalOut = InventoryMovement::whereHas('branchInventory', function ($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })->where('type', 'out')->sum('quantity');
        
        $totalAdjustments = InventoryMovement::whereHas('branchInventory', function ($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })->where('type', 'adjustment')->count();
        
        $totalTransfers = InventoryMovement::whereHas('branchInventory', function ($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })->where('type', 'transfer')->count();
        
        return [
            'total_in' => $totalIn,
            'total_out' => $totalOut,
            'total_adjustments' => $totalAdjustments,
            'total_transfers' => $totalTransfers,
        ];
    }

    /**
     * Get KPIs for analytics dashboard
     */
    public function getKPIs()
    {
        $branchId = $this->getCurrentBranchId();
        
        $totalItems = BranchInventory::where('branch_id', $branchId)->count();
        $inventoryValue = BranchInventory::where('branch_id', $branchId)
            ->selectRaw('SUM(current_stock * cost_per_unit) as total')
            ->value('total') ?? 0;
        $lowStockItems = BranchInventory::where('branch_id', $branchId)
            ->whereRaw('current_stock <= minimum_level')->count();
        
        // Calculate turnover rate (simplified calculation)
        $totalMovements = InventoryMovement::whereHas('branchInventory', function ($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })->where('created_at', '>=', now()->subDays(30))->count();
        
        $turnoverRate = $totalItems > 0 ? ($totalMovements / $totalItems) : 0;
        
        return [
            'total_items' => $totalItems,
            'inventory_value' => $inventoryValue,
            'low_stock_items' => $lowStockItems,
            'turnover_rate' => $turnoverRate,
        ];
    }

    /**
     * Get metrics for analytics dashboard
     */
    public function getMetrics()
    {
        $branchId = $this->getCurrentBranchId();
        
        // Calculate average storage days (simplified)
        $avgStorageDays = 30; // Placeholder calculation
        
        // Calculate accuracy rate based on recent movements
        $totalMovements = InventoryMovement::whereHas('branchInventory', function ($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })->where('created_at', '>=', now()->subDays(30))->count();
        
        $adjustmentMovements = InventoryMovement::whereHas('branchInventory', function ($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })->where('type', 'adjustment')
          ->where('created_at', '>=', now()->subDays(30))->count();
        
        $accuracyRate = $totalMovements > 0 ? (($totalMovements - $adjustmentMovements) / $totalMovements) * 100 : 100;
        
        // Calculate storage cost (simplified)
        $storageCost = BranchInventory::where('branch_id', $branchId)
            ->selectRaw('SUM(current_stock * cost_per_unit * 0.02) as cost') // 2% of inventory value as storage cost
            ->value('cost') ?? 0;
        
        // Calculate turnover rate
        $totalItems = BranchInventory::where('branch_id', $branchId)->count();
        $totalMovements = InventoryMovement::whereHas('branchInventory', function ($query) use ($branchId) {
            $query->where('branch_id', $branchId);
        })->where('created_at', '>=', now()->subDays(30))->count();
        
        $turnoverRate = $totalItems > 0 ? ($totalMovements / $totalItems) : 0;
        
        return [
            'turnover_rate' => $turnoverRate,
            'avg_storage_days' => $avgStorageDays,
            'accuracy_rate' => $accuracyRate,
            'storage_cost' => $storageCost,
        ];
    }

    /**
     * Private helper methods
     */
    private function getCurrentBranchId()
    {
        return Auth::user()->branch_id ?? 1; // Default to branch 1 if not set
    }

    private function createOrUpdateProduct(array $data, Product $product = null)
    {
        $productData = [
            'name' => $data['name'] ?? $product?->name,
            'sku' => $data['sku'] ?? $product?->sku ?? $this->generateSKU(),
            'description' => $data['description'] ?? $product?->description,
            'category' => $data['category'] ?? $product?->category ?? 'general',
            'unit_id' => $data['unit_id'] ?? $product?->unit_id ?? 1,
            'is_active' => $data['is_active'] ?? $product?->is_active ?? true,
        ];

        if ($product) {
            $product->update($productData);
            return $product;
        }

        return Product::create($productData);
    }

    private function calculateNewStock(int $currentStock, int $quantity, string $type)
    {
        return match ($type) {
            'add' => $currentStock + $quantity,
            'subtract' => $currentStock - $quantity,
            'set' => $quantity,
            default => throw new Exception('Invalid stock update type')
        };
    }

    private function logInventoryMovement(int $inventoryId, string $type, int $quantity, string $reason = null, int $oldStock = null, int $newStock = null)
    {
        InventoryMovement::create([
            'branch_inventory_id' => $inventoryId,
            'type' => $type,
            'quantity' => $quantity,
            'reason' => $reason,
            'old_stock' => $oldStock,
            'new_stock' => $newStock,
            'user_id' => Auth::id(),
            'created_at' => now(),
        ]);

        // Also log in inventory_logs for audit trail
        InventoryLog::create([
            'branch_inventory_id' => $inventoryId,
            'action' => $type,
            'quantity_before' => $oldStock,
            'quantity_after' => $newStock,
            'quantity_changed' => $quantity,
            'reason' => $reason,
            'user_id' => Auth::id(),
            'created_at' => now(),
        ]);
    }

    private function generateSKU()
    {
        return 'SKU-' . strtoupper(uniqid());
    }
}