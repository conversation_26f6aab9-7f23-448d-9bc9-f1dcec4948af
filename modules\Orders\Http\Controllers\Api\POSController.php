<?php

namespace Modules\Orders\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use App\Models\MenuItem;
use App\Models\Table;
use App\Models\User;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Modules\Orders\Http\Requests\StoreOrderRequest;
use Modules\Orders\Services\OrderService;
use Modules\Kitchen\Services\KitchenService;

class POSController extends Controller
{
    protected $orderService;
    protected $kitchenService;

    public function __construct(OrderService $orderService, KitchenService $kitchenService)
    {
        $this->orderService = $orderService;
        $this->kitchenService = $kitchenService;
    }

    /**
     * Get POS dashboard data
     */
    public function getDashboardData()
    {
        try {
            $user = Auth::user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }
            
            // Get recent orders for the current branch
            $recentOrders = Order::where('branch_id', $user->branch_id)
                ->with(['customer', 'table', 'orderItems'])
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'recent_orders' => $recentOrders
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get POS form data (customers, tables, menu items, etc.)
     */
    public function getFormData()
    {
        try {
            $user = Auth::user();
            
            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }
            
            // Get customers for the current branch/tenant
            $customers = Customer::where('tenant_id', $user->tenant_id)
                ->where('is_active', true)
                ->orderBy('first_name')
                ->get();

            // Get available tables for the current branch
            $tables = Table::where('branch_id', $user->branch_id)
                ->where('is_active', true)
                ->orderBy('table_number')
                ->get();

            // Get delivery personnel (users with delivery role)
            $deliveryPersonnel = User::where('tenant_id', $user->tenant_id)
                ->where('is_active', true)
                ->whereHas('roles', function($query) {
                    $query->where('name', 'delivery');
                })
                ->orderBy('name')
                ->get();

            // Get menu items for the current branch
            $menuItems = MenuItem::whereHas('menu', function($query) use ($user) {
                    $query->where('branch_id', $user->branch_id)
                          ->where('is_active', true);
                })
                ->with(['category', 'variants', 'addons', 'menu'])
                ->where('is_active', true)
                ->orderBy('sort_order')
                ->orderBy('name')
                ->get();

            // If no menu items found, try to get all active menu items (fallback)
            if ($menuItems->isEmpty()) {
                $menuItems = MenuItem::with(['category', 'variants', 'addons', 'menu'])
                    ->where('is_active', true)
                    ->orderBy('sort_order')
                    ->orderBy('name')
                    ->limit(20)
                    ->get();
            }

            // Group menu items by category name, handling null categories
            $menuCategories = $menuItems->groupBy(function($item) {
                return $item->category ? $item->category->name : 'Uncategorized';
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'customers' => $customers,
                    'tables' => $tables,
                    'delivery_personnel' => $deliveryPersonnel,
                    'menu_items' => $menuItems,
                    'menu_categories' => $menuCategories
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a new order from POS
     */
    public function store(StoreOrderRequest $request)
    {
        try {
          
            $user = Auth::user();
            
            // Add branch and tenant info
            $data = $request->validated();
            $data['branch_id'] = $user->branch_id;
            $data['tenant_id'] = $user->tenant_id;
            $data['cashier_id'] = $user->id;
            
            // Ensure order status is confirmed for KOT creation
            if (!isset($data['status']) || $data['status'] !== 'cancelled') {
                $data['status'] = 'confirmed';
            }

            // Create the order (KOT creation is handled automatically in OrderService)
            $order = $this->orderService->createOrder($data);

            // Check if KOT was created (for response message)
            $kotCreated = $order->hasActiveKot();

            return response()->json([
                'success' => true,
                'message' => 'Order created successfully' . ($kotCreated ? ' with KOT orders' : ''),
                'data' => $order,
                'kot_created' => $kotCreated
            ], 201);

        } catch (\Exception $e) {

            
            return response()->json([
                'success' => false,
                'message' => 'Failed to create order: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get menu item variants and addons for AJAX requests
     */
    public function getMenuItemAddons(MenuItem $menuItem)
    {
        try {
            $user = Auth::user();

            // Verify menu item belongs to user's branch
            if (!$menuItem->menu || $menuItem->menu->branch_id !== $user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Menu item not found'
                ], 404);
            }

            $menuItem->load(['variants', 'addons']);

            return response()->json([
                'success' => true,
                'data' => [
                    'variants' => $menuItem->variants,
                    'addons' => $menuItem->addons
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get menu item details with variants and addons
     */
    public function getMenuItemDetails(MenuItem $menuItem)
    {
        try {
            $user = Auth::user();

            // Verify menu item belongs to user's branch
            if (!$menuItem->menu || $menuItem->menu->branch_id !== $user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Menu item not found'
                ], 404);
            }

            $menuItem->load(['variants' => function($query) {
                $query->where('is_active', true)->orderBy('sort_order');
            }, 'addons' => function($query) {
                $query->orderBy('addon_group_name')->orderBy('sort_order');
            }]);

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $menuItem->id,
                    'name' => $menuItem->name,
                    'description' => $menuItem->description,
                    'base_price' => $menuItem->base_price,
                    'image' => $menuItem->image,
                    'variants' => $menuItem->variants->map(function($variant) {
                        return [
                            'id' => $variant->id,
                            'name' => $variant->name,
                            'price_modifier' => $variant->price_modifier,
                            'is_default' => $variant->is_default,
                        ];
                    }),
                    'addons' => $menuItem->addons->groupBy('addon_group_name')->map(function($group, $groupName) {
                        return [
                            'group_name' => $groupName,
                            'items' => $group->map(function($addon) {
                                return [
                                    'id' => $addon->id,
                                    'name' => $addon->name,
                                    'price' => $addon->price,
                                    'is_required' => $addon->is_required,
                                    'max_quantity' => $addon->max_quantity,
                                ];
                            })
                        ];
                    })->values()
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Calculate order totals for AJAX requests
     */
    public function calculateOrderTotals(Request $request)
    {
        try {
            $items = $request->input('items', []);
            $orderDiscount = $request->input('order_discount', []);
            $taxRate = $request->input('tax_rate', 0.1); // Default 10% tax

            $subtotal = 0;
            $totalDiscount = 0;

            foreach ($items as $item) {
                $itemSubtotal = ($item['unit_price'] ?? 0) * ($item['quantity'] ?? 1);
                
                // Add addon costs
                if (isset($item['addons']) && is_array($item['addons'])) {
                    foreach ($item['addons'] as $addon) {
                        $itemSubtotal += ($addon['unit_price'] ?? 0) * ($addon['quantity'] ?? 1);
                    }
                }

                $subtotal += $itemSubtotal;
            }

            // Apply order-level discount
            if (!empty($orderDiscount)) {
                if ($orderDiscount['type'] === 'percentage') {
                    $orderDiscountAmount = $subtotal * ($orderDiscount['value'] / 100);
                } else {
                    $orderDiscountAmount = $orderDiscount['value'];
                }
                $subtotal -= $orderDiscountAmount;
                $totalDiscount += $orderDiscountAmount;
            }

            $taxAmount = $subtotal * $taxRate;
            $total = $subtotal + $taxAmount;

            return response()->json([
                'success' => true,
                'data' => [
                    'subtotal' => round($subtotal, 2),
                    'tax_amount' => round($taxAmount, 2),
                    'total_discount' => round($totalDiscount, 2),
                    'total' => round($total, 2)
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }
}