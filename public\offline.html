<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - POS System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .offline-container {
            text-align: center;
            max-width: 500px;
            padding: 2rem;
        }
        
        .offline-icon {
            font-size: 5rem;
            margin-bottom: 2rem;
            opacity: 0.8;
        }
        
        .offline-title {
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .offline-message {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .offline-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: 2px solid white;
            background: transparent;
            color: white;
            border-radius: 25px;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .btn:hover {
            background: white;
            color: #667eea;
            transform: translateY(-2px);
        }
        
        .btn-primary {
            background: white;
            color: #667eea;
        }
        
        .btn-primary:hover {
            background: transparent;
            color: white;
        }
        
        .connection-status {
            margin-top: 2rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        
        .status-offline {
            background: #ff4757;
        }
        
        .status-online {
            background: #2ed573;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @media (max-width: 600px) {
            .offline-container {
                padding: 1rem;
            }
            
            .offline-title {
                font-size: 2rem;
            }
            
            .offline-message {
                font-size: 1rem;
            }
            
            .offline-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon pulse">📡</div>
        <h1 class="offline-title">You're Offline</h1>
        <p class="offline-message">
            It looks like you've lost your internet connection. Don't worry - the POS system can still work offline!
        </p>
        
        <div class="offline-actions">
            <button class="btn btn-primary" onclick="tryReconnect()">Try Again</button>
            <a href="/pos/create" class="btn">Go to POS</a>
        </div>
        
        <div class="connection-status">
            <span class="status-indicator status-offline" id="statusIndicator"></span>
            <span id="statusText">Offline</span>
        </div>
    </div>

    <script>
        // Check connection status
        function updateConnectionStatus() {
            const indicator = document.getElementById('statusIndicator');
            const text = document.getElementById('statusText');
            
            if (navigator.onLine) {
                indicator.className = 'status-indicator status-online';
                text.textContent = 'Online';
                
                // Auto-redirect when back online
                setTimeout(() => {
                    window.location.href = '/pos/create';
                }, 1000);
            } else {
                indicator.className = 'status-indicator status-offline';
                text.textContent = 'Offline';
            }
        }
        
        // Try to reconnect
        function tryReconnect() {
            updateConnectionStatus();
            
            if (navigator.onLine) {
                window.location.href = '/pos/create';
            } else {
                // Show feedback
                const btn = event.target;
                const originalText = btn.textContent;
                btn.textContent = 'Still Offline...';
                btn.disabled = true;
                
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.disabled = false;
                }, 2000);
            }
        }
        
        // Listen for connection changes
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);
        
        // Initial status check
        updateConnectionStatus();
        
        // Periodic status check
        setInterval(updateConnectionStatus, 5000);
    </script>
</body>
</html>
