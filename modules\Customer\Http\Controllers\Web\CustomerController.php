<?php

namespace Modules\Customer\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use Modules\Customer\Services\CustomerService;
use Modules\Customer\Http\Requests\StoreCustomerRequest;
use Modules\Customer\Http\Requests\UpdateCustomerRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Yajra\DataTables\Facades\DataTables;
use App\Helpers\ResponseHelper;

class CustomerController extends Controller
{
    protected CustomerService $customerService;

    public function __construct(CustomerService $customerService)
    {
        $this->customerService = $customerService;
    }

    /**
     * Display customers listing page
     */
    public function index(): View
    {
        return view('customer::index');
    }

    /**
     * Show the form for creating a new customer
     */
    public function create(): View
    {
        return view('customer::create');
    }

    /**
     * Store a newly created customer
     */
    public function store(StoreCustomerRequest $request): JsonResponse
    {
        try {
            $customer = $this->customerService->createCustomer($request->validated());
            
            return ResponseHelper::success(
                'Customer created successfully',
                $customer
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to create customer: ' . $e->getMessage()
            );
        }
    }

    /**
     * Display the specified customer
     */
    public function show(Customer $customer): View
    {
        return view('customer::show', compact('customer'));
    }

    /**
     * Show the form for editing the specified customer
     */
    public function edit(Customer $customer): View
    {
        return view('customer::edit', compact('customer'));
    }

    /**
     * Update the specified customer
     */
    public function update(UpdateCustomerRequest $request, Customer $customer): JsonResponse
    {
        try {
            $customer = $this->customerService->updateCustomer($customer, $request->validated());
            
            return ResponseHelper::success(
                'Customer updated successfully',
                $customer
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to update customer: ' . $e->getMessage()
            );
        }
    }

    /**
     * Remove the specified customer
     */
    public function destroy(Customer $customer): JsonResponse
    {
        try {
            $this->customerService->deleteCustomer($customer);
            
            return ResponseHelper::success('Customer deleted successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to delete customer: ' . $e->getMessage()
            );
        }
    }

    /**
     * Get customers data for DataTables
     */
    public function getCustomersData(Request $request): JsonResponse
    {
        try {
            $customers = Customer::with(['branch'])
                ->where('branch_id', auth()->user()->branch_id);

            return DataTables::of($customers)
                ->addColumn('full_name', function ($customer) {
                    return $customer->first_name . ' ' . $customer->last_name;
                })
                ->addColumn('status', function ($customer) {
                    return $customer->is_active 
                        ? '<span class="badge bg-success">Active</span>'
                        : '<span class="badge bg-danger">Inactive</span>';
                })
                ->addColumn('loyalty_points', function ($customer) {
                    return number_format($customer->loyalty_points, 2);
                })
                ->addColumn('last_visit', function ($customer) {
                    return $customer->last_visit_at 
                        ? $customer->last_visit_at->format('M d, Y')
                        : 'Never';
                })
                ->addColumn('actions', function ($customer) {
                    return view('customer::partials.actions', compact('customer'))->render();
                })
                ->rawColumns(['status', 'actions'])
                ->make(true);
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to load customers data: ' . $e->getMessage()
            );
        }
    }

    /**
     * Get customer statistics
     */
    public function getStatistics(): JsonResponse
    {
        try {
            $statistics = $this->customerService->getStatistics();
            
            return ResponseHelper::success('Statistics retrieved successfully', $statistics);
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to get statistics: ' . $e->getMessage()
            );
        }
    }

    /**
     * Activate customer
     */
    public function activate(Customer $customer): JsonResponse
    {
        try {
            $customer = $this->customerService->activateCustomer($customer);
            
            return ResponseHelper::success(
                'Customer activated successfully',
                $customer
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to activate customer: ' . $e->getMessage()
            );
        }
    }

    /**
     * Deactivate customer
     */
    public function deactivate(Customer $customer): JsonResponse
    {
        try {
            $customer = $this->customerService->deactivateCustomer($customer);
            
            return ResponseHelper::success(
                'Customer deactivated successfully',
                $customer
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to deactivate customer: ' . $e->getMessage()
            );
        }
    }

    /**
     * Update customer last visit
     */
    public function updateLastVisit(Customer $customer): JsonResponse
    {
        try {
            $customer = $this->customerService->updateLastVisit($customer);
            
            return ResponseHelper::success(
                'Last visit updated successfully',
                $customer
            );
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to update last visit: ' . $e->getMessage()
            );
        }
    }

    /**
     * Search customers
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $query = $request->get('q', '');
            $customers = $this->customerService->searchCustomers($query);
            
            return ResponseHelper::success('Customers found', $customers);
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to search customers: ' . $e->getMessage()
            );
        }
    }

    /**
     * Get active customers
     */
    public function activeCustomers(): JsonResponse
    {
        try {
            $customers = $this->customerService->getActiveCustomers();
            
            return ResponseHelper::success('Active customers retrieved', $customers);
        } catch (\Exception $e) {
            return ResponseHelper::error(
                'Failed to get active customers: ' . $e->getMessage()
            );
        }
    }
}