<?php

namespace Modules\Reservation\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WaiterRequestResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'table_id' => $this->table_id,
            'branch_id' => $this->branch_id,
            'status' => $this->status,
            'waiter_id' => $this->waiter_id,
            'request_type' => $this->request_type,
            'notes' => $this->notes,
            'response_time' => $this->response_time?->format('Y-m-d H:i:s'),
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
            
            // Status flags
            'is_pending' => $this->isPending(),
            'is_completed' => $this->isCompleted(),
            'is_cancelled' => $this->isCancelled(),
            
            // Human readable status
            'status_name' => $this->status_name,
            'request_type_name' => $this->request_type ? ucfirst($this->request_type) : 'Service',
            
            // Relationships
            'table' => $this->whenLoaded('table'),
            'waiter' => $this->whenLoaded('waiter'),
            'branch' => $this->whenLoaded('branch'),
        ];
    }
}