<?php

namespace Modules\Reservation\Contracts;

interface AreaServiceInterface
{
    /**
     * Get all areas for a branch.
     */
    public function getAllAreas(int $branchId = null);

    /**
     * Create a new area.
     */
    public function createArea(array $data);

    /**
     * Get area by ID.
     */
    public function getAreaById(int $id);

    /**
     * Update area.
     */
    public function updateArea(int $id, array $data);

    /**
     * Delete area.
     */
    public function deleteArea(int $id);

    /**
     * Get tables in an area.
     */
    public function getAreaTables(int $areaId);

    /**
     * Get area statistics.
     */
    public function getAreaStats(int $areaId);
}