@extends('layouts.master')

@section('title', 'Kitchen Display')

@push('styles')
<meta name="csrf-token" content="{{ csrf_token() }}">
<style>
.kitchen-display {
    background: #111827;
    color: #f9fafb;
    min-height: 100vh;
    padding: 1rem;
}

.kot-display-card {
    background: #1f2937;
    border: 2px solid #374151;
    border-radius: 0.75rem;
    margin-bottom: 1rem;
    padding: 1.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.kot-display-card.priority-urgent {
    border-color: #ef4444;
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
}

.kot-display-card.priority-high {
    border-color: #f59e0b;
    box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
}

.kot-display-card.priority-normal {
    border-color: #3b82f6;
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.2);
}

.kot-display-card.priority-low {
    border-color: #6b7280;
}

.kot-display-card.status-preparing {
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
}

.kot-display-card.status-ready {
    background: linear-gradient(135deg, #166534 0%, #15803d 100%);
    animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
    0% { box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7); }
    70% { box-shadow: 0 0 0 15px rgba(34, 197, 94, 0); }
    100% { box-shadow: 0 0 0 0 rgba(34, 197, 94, 0); }
}

.kot-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    border-bottom: 1px solid #374151;
    padding-bottom: 0.75rem;
}

.kot-number {
    font-size: 1.75rem;
    font-weight: 700;
    color: #fbbf24;
}

.kot-time {
    font-size: 1.5rem;
    font-weight: 700;
}

.kot-time.overdue {
    color: #ef4444;
    animation: blink-urgent 1s infinite;
}

.kot-time.warning {
    color: #f59e0b;
}

.kot-time.normal {
    color: #22c55e;
}

@keyframes blink-urgent {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.4; }
}

.kitchen-selector {
    background: #1f2937;
    border: 1px solid #374151;
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.auto-refresh-indicator {
    color: #22c55e;
    font-size: 0.875rem;
    font-weight: 500;
}

.auto-refresh-indicator.refreshing {
    color: #f59e0b;
}

.kot-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 1.5rem;
}

.empty-state {
    text-align: center;
    color: #6b7280;
    font-size: 1.25rem;
    margin-top: 4rem;
}

@media (max-width: 768px) {
    .kot-grid {
        grid-template-columns: 1fr;
    }

    .kitchen-display {
        padding: 0.5rem;
    }
}
</style>
@endpush

@section('content')
<div class="kitchen-display">
    <!-- Kitchen Selector -->
    <div class="kitchen-selector">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 items-center">
            <div>
                <label for="kitchen-select" class="block text-sm font-medium text-gray-200 mb-2">Select Kitchen:</label>
                <select class="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="kitchen-select">
                    <option value="">All Kitchens</option>
                    @foreach($kitchens as $kitchenOption)
                        <option value="{{ $kitchenOption->id }}"
                                {{ $kitchen && $kitchen->id == $kitchenOption->id ? 'selected' : '' }}>
                            {{ $kitchenOption->name }}
                        </option>
                    @endforeach
                </select>
            </div>

            <div class="flex justify-center">
                <div class="auto-refresh-indicator" id="refresh-indicator">
                    <i class="fas fa-sync-alt mr-2"></i> Auto-refresh: ON
                </div>
            </div>

            <div class="text-right">
                <div class="text-white">
                    <div id="current-time" class="text-2xl font-bold"></div>
                    <div class="text-sm text-gray-300">Last updated: <span id="last-updated">--</span></div>
                </div>
            </div>
        </div>
    </div>

    <!-- KOT Display Grid -->
    <div class="kot-grid" id="kot-grid">
        <!-- KOTs will be loaded here -->
    </div>

    <!-- Empty State -->
    <div class="empty-state hidden" id="empty-state">
        <i class="fas fa-utensils text-6xl mb-4 text-gray-500"></i>
        <div class="text-xl font-semibold">No active KOTs</div>
        <div class="text-sm text-gray-400 mt-2">All orders are completed or no orders assigned to this kitchen</div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    let refreshInterval;
    let isRefreshing = false;

    // Update current time
    function updateCurrentTime() {
        const now = new Date();
        $('#current-time').text(now.toLocaleTimeString());
    }

    // Load active KOTs
    function loadActiveKots() {
        if (isRefreshing) return;
        
        isRefreshing = true;
        $('#refresh-indicator').addClass('refreshing').html('<i class="fas fa-sync-alt fa-spin"></i> Refreshing...');

        const kitchenId = $('#kitchen-select').val();

        $.ajax({
            url: '{{ route("kot-orders.active") }}',
            method: 'GET',
            data: { kitchen_id: kitchenId },
            success: function(response) {
                if (response.success) {
                    displayKots(response.data);
                    $('#last-updated').text(new Date().toLocaleTimeString());
                }
            },
            error: function(xhr) {
                console.error('Error loading KOTs:', xhr);
            },
            complete: function() {
                isRefreshing = false;
                $('#refresh-indicator').removeClass('refreshing').html('<i class="fas fa-sync-alt"></i> Auto-refresh: ON');
            }
        });
    }

    // Display KOTs
    function displayKots(kots) {
        const kotGrid = $('#kot-grid');
        const emptyState = $('#empty-state');

        if (kots.length === 0) {
            kotGrid.empty();
            emptyState.removeClass('hidden');
            return;
        }

        emptyState.addClass('hidden');
        kotGrid.empty();

        kots.forEach(function(kot) {
            const kotCard = createKotCard(kot);
            kotGrid.append(kotCard);
        });
    }

    // Create KOT card HTML
    function createKotCard(kot) {
        const priorityClass = `priority-${kot.priority}`;
        const statusClass = `status-${kot.status}`;

        let timeClass = 'normal';
        if (kot.is_overdue) {
            timeClass = 'overdue';
        } else if (kot.remaining_time !== null && kot.remaining_time <= 5) {
            timeClass = 'warning';
        }

        return `
            <div class="kot-display-card ${priorityClass} ${statusClass}">
                <div class="kot-header">
                    <div class="kot-number">#${kot.kot_number}</div>
                    <div class="kot-time ${timeClass}">${kot.elapsed_time} min</div>
                </div>
                <div class="mb-4">
                    <div class="text-sm text-gray-300">Order: ${kot.order_number}</div>
                    <div class="text-sm text-gray-300">Created: ${kot.created_at}</div>
                    <div class="flex space-x-2 mt-2">
                        <span class="px-2 py-1 text-xs font-semibold rounded-full ${getPriorityBadgeClass(kot.priority)}">${kot.priority.toUpperCase()}</span>
                        <span class="px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadgeClass(kot.status)}">${kot.status.toUpperCase()}</span>
                    </div>
                </div>
                <div class="kot-items-info">
                    <div class="text-sm text-gray-300 font-medium">${kot.items_count} item(s)</div>
                </div>
            </div>
        `;
    }

    // Get priority badge class
    function getPriorityBadgeClass(priority) {
        const classes = {
            'low': 'bg-gray-500 text-white',
            'normal': 'bg-blue-500 text-white',
            'high': 'bg-yellow-500 text-black',
            'urgent': 'bg-red-500 text-white'
        };
        return classes[priority] || 'bg-gray-500 text-white';
    }

    // Get status badge class
    function getStatusBadgeClass(status) {
        const classes = {
            'pending': 'bg-yellow-500 text-black',
            'preparing': 'bg-blue-500 text-white',
            'ready': 'bg-green-500 text-white',
            'completed': 'bg-purple-500 text-white',
            'cancelled': 'bg-red-500 text-white'
        };
        return classes[status] || 'bg-gray-500 text-white';
    }

    // Handle kitchen selection change
    $('#kitchen-select').on('change', function() {
        loadActiveKots();
    });

    // Start auto-refresh
    function startAutoRefresh() {
        refreshInterval = setInterval(function() {
            loadActiveKots();
            updateCurrentTime();
        }, 10000); // Refresh every 10 seconds
    }

    // Initial load
    updateCurrentTime();
    loadActiveKots();
    startAutoRefresh();

    // Update time every second
    setInterval(updateCurrentTime, 1000);

    // Handle page visibility change to pause/resume refresh
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            clearInterval(refreshInterval);
        } else {
            startAutoRefresh();
            loadActiveKots();
        }
    });
});
</script>
@endpush
