<?php

namespace Modules\Delivery\Services;

use Modules\Delivery\Models\DeliveryTracking;
use Modules\Delivery\Models\DeliveryAssignment;
use Modules\Delivery\Models\DeliveryPersonnel;
use Illuminate\Database\Eloquent\Collection;

class DeliveryTrackingService
{
    /**
     * Record location update for delivery personnel
     */
    public function recordLocationUpdate(int $personnelId, array $locationData): DeliveryTracking
    {
        $personnel = DeliveryPersonnel::findOrFail($personnelId);
        
        // Update personnel current location
        $personnel->updateLocation(
            $locationData['latitude'],
            $locationData['longitude']
        );

        // Find active assignment
        $activeAssignment = $personnel->activeDeliveries()->first();
        
        if (!$activeAssignment) {
            throw new \Exception('No active delivery assignment found for personnel');
        }

        return DeliveryTracking::create([
            'delivery_assignment_id' => $activeAssignment->id,
            'delivery_personnel_id' => $personnelId,
            'latitude' => $locationData['latitude'],
            'longitude' => $locationData['longitude'],
            'accuracy' => $locationData['accuracy'] ?? null,
            'speed' => $locationData['speed'] ?? null,
            'heading' => $locationData['heading'] ?? null,
            'recorded_at' => now(),
        ]);
    }

    /**
     * Get tracking history for an assignment
     */
    public function getTrackingHistory(int $assignmentId): Collection
    {
        return DeliveryTracking::where('delivery_assignment_id', $assignmentId)
            ->orderBy('recorded_at')
            ->get();
    }

    /**
     * Get latest location for personnel
     */
    public function getLatestLocation(int $personnelId): ?DeliveryTracking
    {
        return DeliveryTracking::where('delivery_personnel_id', $personnelId)
            ->latest('recorded_at')
            ->first();
    }

    /**
     * Get real-time tracking data for assignment
     */
    public function getRealTimeTracking(int $assignmentId): array
    {
        $assignment = DeliveryAssignment::with([
            'deliveryPersonnel',
            'order',
            'latestTracking'
        ])->findOrFail($assignmentId);

        $latestTracking = $assignment->latestTracking;
        
        if (!$latestTracking) {
            return [
                'assignment' => $assignment,
                'current_location' => null,
                'estimated_arrival' => null,
                'distance_remaining' => null,
            ];
        }

        // Calculate estimated arrival time and distance
        $estimatedArrival = $this->calculateEstimatedArrival($assignment, $latestTracking);
        $distanceRemaining = $this->calculateDistanceToDestination($assignment, $latestTracking);

        return [
            'assignment' => $assignment,
            'current_location' => [
                'latitude' => $latestTracking->latitude,
                'longitude' => $latestTracking->longitude,
                'accuracy' => $latestTracking->accuracy,
                'speed' => $latestTracking->speed,
                'heading' => $latestTracking->heading,
                'last_update' => $latestTracking->recorded_at,
            ],
            'estimated_arrival' => $estimatedArrival,
            'distance_remaining' => $distanceRemaining,
        ];
    }

    /**
     * Get tracking data for customer
     */
    public function getCustomerTracking(int $orderId): array
    {
        $assignment = DeliveryAssignment::with([
            'deliveryPersonnel.user',
            'order',
            'latestTracking'
        ])->where('order_id', $orderId)->first();

        if (!$assignment) {
            return [
                'status' => 'not_assigned',
                'message' => 'Delivery not yet assigned',
            ];
        }

        $latestTracking = $assignment->latestTracking;
        
        return [
            'status' => $assignment->status,
            'personnel' => [
                'name' => $assignment->deliveryPersonnel->user->name,
                'phone' => $assignment->deliveryPersonnel->phone_number,
                'vehicle_type' => $assignment->deliveryPersonnel->vehicle_type,
                'rating' => $assignment->deliveryPersonnel->rating,
            ],
            'current_location' => $latestTracking ? [
                'latitude' => $latestTracking->latitude,
                'longitude' => $latestTracking->longitude,
                'last_update' => $latestTracking->recorded_at,
            ] : null,
            'estimated_arrival' => $latestTracking ? 
                $this->calculateEstimatedArrival($assignment, $latestTracking) : null,
            'assigned_at' => $assignment->assigned_at,
            'picked_up_at' => $assignment->picked_up_at,
        ];
    }

    /**
     * Calculate estimated arrival time
     */
    private function calculateEstimatedArrival(DeliveryAssignment $assignment, DeliveryTracking $tracking): ?string
    {
        if (!$assignment->order->delivery_coordinates || !$tracking->speed) {
            return null;
        }

        $destinationLat = $assignment->order->delivery_coordinates['lat'];
        $destinationLng = $assignment->order->delivery_coordinates['lng'];
        
        $distance = $this->calculateDistance(
            $tracking->latitude,
            $tracking->longitude,
            $destinationLat,
            $destinationLng
        );

        // Convert speed from m/s to km/h if needed, assume km/h for now
        $speedKmh = $tracking->speed;
        
        if ($speedKmh > 0) {
            $timeHours = $distance / $speedKmh;
            $timeMinutes = $timeHours * 60;
            
            return now()->addMinutes($timeMinutes)->format('Y-m-d H:i:s');
        }

        return null;
    }

    /**
     * Calculate distance to destination
     */
    private function calculateDistanceToDestination(DeliveryAssignment $assignment, DeliveryTracking $tracking): ?float
    {
        if (!$assignment->order->delivery_coordinates) {
            return null;
        }

        $destinationLat = $assignment->order->delivery_coordinates['lat'];
        $destinationLng = $assignment->order->delivery_coordinates['lng'];
        
        return $this->calculateDistance(
            $tracking->latitude,
            $tracking->longitude,
            $destinationLat,
            $destinationLng
        );
    }

    /**
     * Calculate distance between two coordinates
     */
    private function calculateDistance(float $lat1, float $lng1, float $lat2, float $lng2): float
    {
        $earthRadius = 6371; // km
        $latDiff = deg2rad($lat2 - $lat1);
        $lngDiff = deg2rad($lng2 - $lng1);
        
        $a = sin($latDiff / 2) * sin($latDiff / 2) +
             cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
             sin($lngDiff / 2) * sin($lngDiff / 2);
        
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        
        return $earthRadius * $c;
    }

    /**
     * Get tracking statistics for personnel
     */
    public function getPersonnelTrackingStats(int $personnelId, array $filters = []): array
    {
        $query = DeliveryTracking::where('delivery_personnel_id', $personnelId);

        if (isset($filters['date_from'])) {
            $query->whereDate('recorded_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('recorded_at', '<=', $filters['date_to']);
        }

        $trackingRecords = $query->orderBy('recorded_at')->get();
        
        $totalDistance = 0;
        $totalTime = 0;
        $avgSpeed = 0;
        
        if ($trackingRecords->count() > 1) {
            for ($i = 1; $i < $trackingRecords->count(); $i++) {
                $prev = $trackingRecords[$i - 1];
                $current = $trackingRecords[$i];
                
                $distance = $this->calculateDistance(
                    $prev->latitude,
                    $prev->longitude,
                    $current->latitude,
                    $current->longitude
                );
                
                $totalDistance += $distance;
                
                $timeDiff = $prev->recorded_at->diffInMinutes($current->recorded_at);
                $totalTime += $timeDiff;
            }
            
            $avgSpeed = $totalTime > 0 ? ($totalDistance / ($totalTime / 60)) : 0; // km/h
        }

        return [
            'total_tracking_points' => $trackingRecords->count(),
            'total_distance_km' => round($totalDistance, 2),
            'total_time_minutes' => $totalTime,
            'average_speed_kmh' => round($avgSpeed, 2),
            'first_location' => $trackingRecords->first(),
            'last_location' => $trackingRecords->last(),
        ];
    }
}