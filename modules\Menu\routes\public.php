<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Http\JsonResponse;
use Modules\Menu\Http\Controllers\Api\OfferController;

/*
|--------------------------------------------------------------------------
| Menu Module Public Routes
|--------------------------------------------------------------------------
|
| These routes are public and do not require authentication.
| They are intended for customer-facing applications.
|
*/

// Public menu endpoints (no authentication required)
Route::name('api.menu.public.')->group(function () {
    
    // Simple test endpoints that return success responses
    Route::get('menu/categories/public', function (): JsonResponse {
        return response()->json([
            'success' => true,
            'message' => 'Public categories endpoint working',
            'data' => []
        ]);
    })->name('categories.public');
    
    Route::get('menu/menus/public', function (): JsonResponse {
        return response()->json([
            'success' => true,
            'message' => 'Public menus endpoint working',
            'data' => []
        ]);
    })->name('menus.public');
    
    Route::get('menu/menu-items/public', function (): JsonResponse {
        return response()->json([
            'success' => true,
            'message' => 'Public menu items endpoint working',
            'data' => []
        ]);
    })->name('menu-items.public');
    
    Route::get('menu/banners/display', function (): JsonResponse {
        return response()->json([
            'success' => true,
            'message' => 'Public banners display endpoint working',
            'data' => []
        ]);
    })->name('banners.display');
    
    Route::get('menu/events/upcoming', function (): JsonResponse {
        return response()->json([
            'success' => true,
            'message' => 'Public events upcoming endpoint working',
            'data' => []
        ]);
    })->name('events.upcoming');
    
    Route::get('menu/offers/featured', [\Modules\Menu\Http\Controllers\Api\OfferController::class, 'publicFeatured'])->name('offers.featured');

});