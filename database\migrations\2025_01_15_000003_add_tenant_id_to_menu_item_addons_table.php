<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if tenant_id column already exists
        if (!Schema::hasColumn('menu_item_addons', 'tenant_id')) {
            Schema::table('menu_item_addons', function (Blueprint $table) {
                // Add tenant_id column as nullable first
                $table->foreignId('tenant_id')->nullable()->after('id');
            });
        }

        // Update existing records to set tenant_id based on their menu_item's tenant_id
        DB::statement('
            UPDATE menu_item_addons mia 
            JOIN menu_items mi ON mia.menu_item_id = mi.id 
            SET mia.tenant_id = mi.tenant_id 
            WHERE mia.tenant_id IS NULL
        ');

        Schema::table('menu_item_addons', function (Blueprint $table) {
            // Now make it non-nullable and add foreign key constraint
            $table->foreignId('tenant_id')->nullable(false)->change();
            
            // Check if foreign key doesn't exist before adding
            if (!$this->foreignKeyExists('menu_item_addons', 'menu_item_addons_tenant_id_foreign')) {
                $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
            }
            
            // Add indexes for better performance if they don't exist
            if (!$this->indexExists('menu_item_addons', 'menu_item_addons_tenant_id_branch_id_index')) {
                $table->index(['tenant_id', 'branch_id']);
            }
            if (!$this->indexExists('menu_item_addons', 'menu_item_addons_tenant_id_menu_item_id_index')) {
                $table->index(['tenant_id', 'menu_item_id']);
            }
        });
    }

    /**
     * Check if foreign key exists
     */
    private function foreignKeyExists($table, $keyName)
    {
        $keys = DB::select("
            SELECT CONSTRAINT_NAME 
            FROM information_schema.KEY_COLUMN_USAGE 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = ? 
            AND CONSTRAINT_NAME = ?
        ", [$table, $keyName]);
        
        return count($keys) > 0;
    }

    /**
     * Check if index exists
     */
    private function indexExists($table, $indexName)
    {
        $indexes = DB::select("
            SELECT INDEX_NAME 
            FROM information_schema.STATISTICS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = ? 
            AND INDEX_NAME = ?
        ", [$table, $indexName]);
        
        return count($indexes) > 0;
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('menu_item_addons', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['tenant_id', 'branch_id']);
            $table->dropIndex(['tenant_id', 'menu_item_id']);
            
            // Drop foreign key and column
            $table->dropForeign(['tenant_id']);
            $table->dropColumn('tenant_id');
        });
    }
};