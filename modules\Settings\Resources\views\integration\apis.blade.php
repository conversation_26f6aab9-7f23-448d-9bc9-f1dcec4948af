@extends('layouts.master')

@section('title', 'API & Integration Settings')

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
@endpush

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- <PERSON> Header -->
    <div class="bg-gradient-to-r from-blue-600 to-cyan-600 rounded-lg shadow-lg p-6 mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                            <i class="fas fa-plug text-white text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <h1 class="text-2xl font-bold text-white">API & Integration Settings</h1>
                        <p class="text-blue-100">Manage third-party integrations and API configurations</p>
                    </div>
                </div>
            </div>
            <div class="mt-4 md:mt-0 flex space-x-3">
                <button id="test-connection-btn" class="px-4 py-2 bg-white text-blue-600 rounded-lg hover:bg-blue-50 transition-colors duration-200 font-medium">
                    <i class="fas fa-wifi mr-2"></i>
                    Test Connections
                </button>
                <button id="generate-api-key-btn" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-400 transition-colors duration-200 font-medium">
                    <i class="fas fa-key mr-2"></i>
                    Generate API Key
                </button>
            </div>
        </div>
    </div>

    <!-- Integration Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-check-circle text-green-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Active Integrations</p>
                    <p class="text-2xl font-bold text-gray-900">8</p>
                    <p class="text-xs text-green-600">Connected</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-exchange-alt text-blue-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">API Calls Today</p>
                    <p class="text-2xl font-bold text-gray-900">1,247</p>
                    <p class="text-xs text-blue-600">Within limits</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-exclamation-triangle text-yellow-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Pending Setup</p>
                    <p class="text-2xl font-bold text-gray-900">3</p>
                    <p class="text-xs text-yellow-600">Needs attention</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-times-circle text-red-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Failed Requests</p>
                    <p class="text-2xl font-bold text-gray-900">12</p>
                    <p class="text-xs text-red-600">Last 24 hours</p>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Payment Gateway Integrations -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-credit-card mr-2 text-green-600"></i>
                Payment Gateway Integrations
            </h3>
            
            <div class="space-y-4">
                <!-- Stripe -->
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fab fa-stripe text-indigo-600"></i>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900">Stripe</h4>
                                <p class="text-sm text-gray-500">Credit card processing</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="px-2 py-1 text-xs font-medium text-green-700 bg-green-100 rounded">Connected</span>
                            <button class="px-3 py-1 text-xs font-medium text-indigo-700 bg-indigo-100 rounded hover:bg-indigo-200">
                                Configure
                            </button>
                        </div>
                    </div>
                    <form class="space-y-3">
                        <div>
                            <label for="stripe_public_key" class="block text-sm font-medium text-gray-700 mb-1">
                                Public Key
                            </label>
                            <input type="text" id="stripe_public_key" name="stripe_public_key" 
                                   value="pk_test_51H..." 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                        </div>
                        <div>
                            <label for="stripe_secret_key" class="block text-sm font-medium text-gray-700 mb-1">
                                Secret Key
                            </label>
                            <input type="password" id="stripe_secret_key" name="stripe_secret_key" 
                                   value="sk_test_51H..." 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                        </div>
                        <div class="flex items-center justify-between">
                            <div>
                                <label for="stripe_test_mode" class="text-sm font-medium text-gray-700">Test Mode</label>
                                <p class="text-sm text-gray-500">Use test environment</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" id="stripe_test_mode" name="stripe_test_mode" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                            </label>
                        </div>
                    </form>
                </div>

                <!-- PayPal -->
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fab fa-paypal text-blue-600"></i>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900">PayPal</h4>
                                <p class="text-sm text-gray-500">Digital wallet payments</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="px-2 py-1 text-xs font-medium text-yellow-700 bg-yellow-100 rounded">Setup Required</span>
                            <button class="px-3 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded hover:bg-blue-200">
                                Setup
                            </button>
                        </div>
                    </div>
                    <form class="space-y-3">
                        <div>
                            <label for="paypal_client_id" class="block text-sm font-medium text-gray-700 mb-1">
                                Client ID
                            </label>
                            <input type="text" id="paypal_client_id" name="paypal_client_id" 
                                   placeholder="Enter PayPal Client ID" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label for="paypal_client_secret" class="block text-sm font-medium text-gray-700 mb-1">
                                Client Secret
                            </label>
                            <input type="password" id="paypal_client_secret" name="paypal_client_secret" 
                                   placeholder="Enter PayPal Client Secret" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div class="flex items-center justify-between">
                            <div>
                                <label for="paypal_sandbox" class="text-sm font-medium text-gray-700">Sandbox Mode</label>
                                <p class="text-sm text-gray-500">Use sandbox environment</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" id="paypal_sandbox" name="paypal_sandbox" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                        </div>
                    </form>
                </div>

                <!-- Square -->
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-square text-gray-600"></i>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900">Square</h4>
                                <p class="text-sm text-gray-500">Point of sale integration</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="px-2 py-1 text-xs font-medium text-red-700 bg-red-100 rounded">Disconnected</span>
                            <button class="px-3 py-1 text-xs font-medium text-gray-700 bg-gray-100 rounded hover:bg-gray-200">
                                Connect
                            </button>
                        </div>
                    </div>
                    <div class="text-center py-4">
                        <p class="text-sm text-gray-500">Click "Connect" to set up Square integration</p>
                    </div>
                </div>

                <div class="pt-4">
                    <button type="submit" class="w-full px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-lg hover:bg-green-700">
                        <i class="fas fa-save mr-2"></i>
                        Save Payment Settings
                    </button>
                </div>
            </div>
        </div>

        <!-- Delivery & Third-Party Services -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-truck mr-2 text-orange-600"></i>
                Delivery & Third-Party Services
            </h3>
            
            <div class="space-y-4">
                <!-- Uber Eats -->
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-black rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-utensils text-white"></i>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900">Uber Eats</h4>
                                <p class="text-sm text-gray-500">Food delivery platform</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="px-2 py-1 text-xs font-medium text-green-700 bg-green-100 rounded">Connected</span>
                            <button class="px-3 py-1 text-xs font-medium text-black bg-gray-100 rounded hover:bg-gray-200">
                                Manage
                            </button>
                        </div>
                    </div>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Orders Today:</span>
                            <span class="font-medium">23</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Revenue:</span>
                            <span class="font-medium">$456.78</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Commission:</span>
                            <span class="font-medium text-red-600">-$68.52</span>
                        </div>
                    </div>
                </div>

                <!-- DoorDash -->
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-motorcycle text-red-600"></i>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900">DoorDash</h4>
                                <p class="text-sm text-gray-500">Food delivery service</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="px-2 py-1 text-xs font-medium text-green-700 bg-green-100 rounded">Connected</span>
                            <button class="px-3 py-1 text-xs font-medium text-red-700 bg-red-100 rounded hover:bg-red-200">
                                Manage
                            </button>
                        </div>
                    </div>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Orders Today:</span>
                            <span class="font-medium">18</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Revenue:</span>
                            <span class="font-medium">$342.15</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Commission:</span>
                            <span class="font-medium text-red-600">-$51.32</span>
                        </div>
                    </div>
                </div>

                <!-- Grubhub -->
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-hamburger text-orange-600"></i>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900">Grubhub</h4>
                                <p class="text-sm text-gray-500">Online food ordering</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="px-2 py-1 text-xs font-medium text-yellow-700 bg-yellow-100 rounded">Setup Required</span>
                            <button class="px-3 py-1 text-xs font-medium text-orange-700 bg-orange-100 rounded hover:bg-orange-200">
                                Setup
                            </button>
                        </div>
                    </div>
                    <div class="text-center py-4">
                        <p class="text-sm text-gray-500">Complete setup to start receiving orders</p>
                    </div>
                </div>

                <!-- Local Delivery Service -->
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-shipping-fast text-blue-600"></i>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900">Local Delivery</h4>
                                <p class="text-sm text-gray-500">Custom delivery service</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="px-2 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded">Active</span>
                            <button class="px-3 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded hover:bg-blue-200">
                                Configure
                            </button>
                        </div>
                    </div>
                    <form class="space-y-3">
                        <div>
                            <label for="delivery_radius" class="block text-sm font-medium text-gray-700 mb-1">
                                Delivery Radius (km)
                            </label>
                            <input type="number" id="delivery_radius" name="delivery_radius" value="5" min="1" max="50"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label for="delivery_fee" class="block text-sm font-medium text-gray-700 mb-1">
                                Delivery Fee ($)
                            </label>
                            <input type="number" id="delivery_fee" name="delivery_fee" value="3.99" step="0.01" min="0"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                    </form>
                </div>

                <div class="pt-4">
                    <button type="submit" class="w-full px-4 py-2 text-sm font-medium text-white bg-orange-600 border border-transparent rounded-lg hover:bg-orange-700">
                        <i class="fas fa-save mr-2"></i>
                        Save Delivery Settings
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- API Management & Webhooks -->
    <div class="mt-6 bg-white rounded-lg shadow-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fas fa-code mr-2 text-purple-600"></i>
            API Management & Webhooks
        </h3>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div class="space-y-4">
                <h4 class="font-medium text-gray-900">API Keys & Authentication</h4>
                
                <div class="space-y-4">
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <div>
                                <h5 class="font-medium text-gray-900">Production API Key</h5>
                                <p class="text-sm text-gray-500">For live environment</p>
                            </div>
                            <span class="px-2 py-1 text-xs font-medium text-green-700 bg-green-100 rounded">Active</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <input type="text" value="pk_live_51H7..." readonly 
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600">
                            <button class="px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200">
                                <i class="fas fa-copy"></i>
                            </button>
                            <button class="px-3 py-2 text-sm font-medium text-red-700 bg-red-100 border border-red-300 rounded-lg hover:bg-red-200">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                        <div class="mt-2 text-xs text-gray-500">
                            Created: 2024-01-15 | Last used: 2 hours ago
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <div>
                                <h5 class="font-medium text-gray-900">Test API Key</h5>
                                <p class="text-sm text-gray-500">For development environment</p>
                            </div>
                            <span class="px-2 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded">Test</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <input type="text" value="pk_test_51H7..." readonly 
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600">
                            <button class="px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200">
                                <i class="fas fa-copy"></i>
                            </button>
                            <button class="px-3 py-2 text-sm font-medium text-blue-700 bg-blue-100 border border-blue-300 rounded-lg hover:bg-blue-200">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                        <div class="mt-2 text-xs text-gray-500">
                            Created: 2024-01-10 | Last used: 5 minutes ago
                        </div>
                    </div>

                    <form class="space-y-3">
                        <div>
                            <label for="api_rate_limit" class="block text-sm font-medium text-gray-700 mb-2">
                                Rate Limit (requests per minute)
                            </label>
                            <input type="number" id="api_rate_limit" name="api_rate_limit" value="100" min="10" max="1000"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        </div>

                        <div class="space-y-3">
                            <div class="flex items-center justify-between">
                                <div>
                                    <label for="api_logging" class="text-sm font-medium text-gray-700">API Request Logging</label>
                                    <p class="text-sm text-gray-500">Log all API requests for debugging</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" id="api_logging" name="api_logging" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                                </label>
                            </div>

                            <div class="flex items-center justify-between">
                                <div>
                                    <label for="api_cors" class="text-sm font-medium text-gray-700">CORS Enabled</label>
                                    <p class="text-sm text-gray-500">Allow cross-origin requests</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" id="api_cors" name="api_cors" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div class="space-y-4">
                <h4 class="font-medium text-gray-900">Webhook Configuration</h4>
                
                <div class="space-y-4">
                    <form class="space-y-3">
                        <div>
                            <label for="webhook_url" class="block text-sm font-medium text-gray-700 mb-2">
                                Webhook URL
                            </label>
                            <input type="url" id="webhook_url" name="webhook_url" 
                                   value="https://yoursite.com/webhooks/pos" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        </div>

                        <div>
                            <label for="webhook_secret" class="block text-sm font-medium text-gray-700 mb-2">
                                Webhook Secret
                            </label>
                            <div class="flex items-center space-x-2">
                                <input type="password" id="webhook_secret" name="webhook_secret" 
                                       value="whsec_1234567890abcdef" 
                                       class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                                <button type="button" class="px-3 py-2 text-sm font-medium text-purple-700 bg-purple-100 border border-purple-300 rounded-lg hover:bg-purple-200">
                                    Generate
                                </button>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Webhook Events
                            </label>
                            <div class="space-y-2 max-h-32 overflow-y-auto">
                                <label class="flex items-center">
                                    <input type="checkbox" name="webhook_events[]" value="order.created" checked class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                                    <span class="ml-2 text-sm text-gray-700">Order Created</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="webhook_events[]" value="order.updated" checked class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                                    <span class="ml-2 text-sm text-gray-700">Order Updated</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="webhook_events[]" value="payment.completed" checked class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                                    <span class="ml-2 text-sm text-gray-700">Payment Completed</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="webhook_events[]" value="inventory.low" class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                                    <span class="ml-2 text-sm text-gray-700">Low Inventory</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="webhook_events[]" value="customer.created" class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                                    <span class="ml-2 text-sm text-gray-700">Customer Created</span>
                                </label>
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div>
                                <label for="webhook_enabled" class="text-sm font-medium text-gray-700">Enable Webhooks</label>
                                <p class="text-sm text-gray-500">Send webhook notifications</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" id="webhook_enabled" name="webhook_enabled" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                            </label>
                        </div>
                    </form>

                    <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                        <h5 class="font-medium text-purple-900 mb-2">Recent Webhook Deliveries</h5>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between items-center">
                                <span class="text-purple-800">order.created</span>
                                <div class="flex items-center space-x-2">
                                    <span class="text-green-600">200</span>
                                    <span class="text-purple-600">2 min ago</span>
                                </div>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-purple-800">payment.completed</span>
                                <div class="flex items-center space-x-2">
                                    <span class="text-green-600">200</span>
                                    <span class="text-purple-600">5 min ago</span>
                                </div>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-purple-800">order.updated</span>
                                <div class="flex items-center space-x-2">
                                    <span class="text-red-600">500</span>
                                    <span class="text-purple-600">8 min ago</span>
                                </div>
                            </div>
                        </div>
                        <button class="mt-3 w-full px-3 py-2 text-sm font-medium text-purple-700 bg-purple-100 border border-purple-300 rounded-lg hover:bg-purple-200">
                            View All Deliveries
                        </button>
                    </div>

                    <div class="flex space-x-2">
                        <button type="button" class="flex-1 px-4 py-2 text-sm font-medium text-purple-700 bg-purple-100 border border-purple-300 rounded-lg hover:bg-purple-200">
                            <i class="fas fa-vial mr-2"></i>
                            Test Webhook
                        </button>
                        <button type="submit" class="flex-1 px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-lg hover:bg-purple-700">
                            <i class="fas fa-save mr-2"></i>
                            Save Webhook Settings
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Analytics & Monitoring -->
    <div class="mt-6 bg-white rounded-lg shadow-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fas fa-chart-line mr-2 text-indigo-600"></i>
            Analytics & Monitoring
        </h3>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div class="space-y-4">
                <h4 class="font-medium text-gray-900">Google Analytics</h4>
                
                <form class="space-y-3">
                    <div>
                        <label for="ga_tracking_id" class="block text-sm font-medium text-gray-700 mb-2">
                            Tracking ID
                        </label>
                        <input type="text" id="ga_tracking_id" name="ga_tracking_id" 
                               value="GA-123456789-1" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="ga_enabled" class="text-sm font-medium text-gray-700">Enable Analytics</label>
                            <p class="text-sm text-gray-500">Track website analytics</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="ga_enabled" name="ga_enabled" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="ga_ecommerce" class="text-sm font-medium text-gray-700">Enhanced Ecommerce</label>
                            <p class="text-sm text-gray-500">Track purchase events</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="ga_ecommerce" name="ga_ecommerce" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                        </label>
                    </div>
                </form>
            </div>

            <div class="space-y-4">
                <h4 class="font-medium text-gray-900">Facebook Pixel</h4>
                
                <form class="space-y-3">
                    <div>
                        <label for="fb_pixel_id" class="block text-sm font-medium text-gray-700 mb-2">
                            Pixel ID
                        </label>
                        <input type="text" id="fb_pixel_id" name="fb_pixel_id" 
                               value="123456789012345" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="fb_enabled" class="text-sm font-medium text-gray-700">Enable Pixel</label>
                            <p class="text-sm text-gray-500">Track Facebook conversions</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="fb_enabled" name="fb_enabled" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="fb_conversions" class="text-sm font-medium text-gray-700">Conversion Tracking</label>
                            <p class="text-sm text-gray-500">Track purchase conversions</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="fb_conversions" name="fb_conversions" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                </form>
            </div>

            <div class="space-y-4">
                <h4 class="font-medium text-gray-900">Custom Tracking</h4>
                
                <form class="space-y-3">
                    <div>
                        <label for="custom_script" class="block text-sm font-medium text-gray-700 mb-2">
                            Custom Script
                        </label>
                        <textarea id="custom_script" name="custom_script" rows="4" 
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-gray-500"
                                  placeholder="<!-- Custom tracking code -->"></textarea>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="custom_enabled" class="text-sm font-medium text-gray-700">Enable Custom Script</label>
                            <p class="text-sm text-gray-500">Load custom tracking code</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="custom_enabled" name="custom_enabled" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-gray-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gray-600"></div>
                        </label>
                    </div>
                </form>
            </div>
        </div>

        <div class="mt-6 pt-6 border-t border-gray-200">
            <button type="submit" class="w-full px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-lg hover:bg-indigo-700">
                <i class="fas fa-save mr-2"></i>
                Save Analytics Settings
            </button>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Form submission handlers
    $('form').on('submit', function(e) {
        e.preventDefault();
        
        Swal.fire({
            icon: 'success',
            title: 'Success!',
            text: 'Integration settings updated successfully.',
            timer: 2000,
            showConfirmButton: false
        });
    });

    // Test connections handler
    $('#test-connection-btn').on('click', function() {
        Swal.fire({
            title: 'Testing Connections...',
            text: 'Please wait while we test all active integrations.',
            icon: 'info',
            showConfirmButton: false,
            timer: 3000
        }).then(() => {
            Swal.fire({
                title: 'Connection Test Results',
                html: `
                    <div class="text-left space-y-2">
                        <div class="flex justify-between items-center">
                            <span>Stripe:</span>
                            <span class="text-green-600"><i class="fas fa-check-circle"></i> Connected</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span>PayPal:</span>
                            <span class="text-red-600"><i class="fas fa-times-circle"></i> Failed</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span>Uber Eats:</span>
                            <span class="text-green-600"><i class="fas fa-check-circle"></i> Connected</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span>DoorDash:</span>
                            <span class="text-green-600"><i class="fas fa-check-circle"></i> Connected</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span>Webhooks:</span>
                            <span class="text-green-600"><i class="fas fa-check-circle"></i> Active</span>
                        </div>
                    </div>
                `,
                icon: 'info',
                confirmButtonText: 'OK'
            });
        });
    });

    // Generate API key handler
    $('#generate-api-key-btn').on('click', function() {
        Swal.fire({
            title: 'Generate New API Key',
            text: 'This will create a new API key. Are you sure you want to continue?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Generate',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                const newKey = 'pk_live_' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
                
                Swal.fire({
                    title: 'New API Key Generated',
                    html: `
                        <div class="text-left">
                            <p class="mb-3">Your new API key has been generated:</p>
                            <div class="bg-gray-100 p-3 rounded border">
                                <code class="text-sm">${newKey}</code>
                            </div>
                            <p class="mt-3 text-sm text-red-600">
                                <i class="fas fa-exclamation-triangle mr-1"></i>
                                Please save this key securely. It will not be shown again.
                            </p>
                        </div>
                    `,
                    icon: 'success',
                    confirmButtonText: 'I have saved the key'
                });
            }
        });
    });

    // Copy API key handlers
    $('button:contains("fa-copy")').on('click', function() {
        const apiKey = $(this).siblings('input').val();
        navigator.clipboard.writeText(apiKey).then(() => {
            Swal.fire({
                icon: 'success',
                title: 'Copied!',
                text: 'API key copied to clipboard.',
                timer: 1500,
                showConfirmButton: false
            });
        });
    });

    // Regenerate API key handlers
    $('button:contains("fa-sync-alt")').on('click', function() {
        const keyType = $(this).closest('.border').find('h5').text();
        
        Swal.fire({
            title: `Regenerate ${keyType}`,
            text: 'This will invalidate the current key. Are you sure?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Regenerate',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                const newKey = keyType.includes('Test') ? 'pk_test_' : 'pk_live_';
                const randomString = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
                
                $(this).siblings('input').val(newKey + randomString);
                
                Swal.fire({
                    icon: 'success',
                    title: 'Key Regenerated',
                    text: `${keyType} has been regenerated successfully.`,
                    timer: 2000,
                    showConfirmButton: false
                });
            }
        });
    });

    // Integration setup handlers
    $('button:contains("Setup")').on('click', function() {
        const serviceName = $(this).closest('.border').find('h4').text();
        
        Swal.fire({
            title: `Setup ${serviceName}`,
            text: `This will guide you through the ${serviceName} integration setup process.`,
            icon: 'info',
            confirmButtonText: 'Start Setup'
        });
    });

    // Integration management handlers
    $('button:contains("Manage")').on('click', function() {
        const serviceName = $(this).closest('.border').find('h4').text();
        
        Swal.fire({
            title: `Manage ${serviceName}`,
            html: `
                <div class="text-left space-y-3">
                    <div class="flex justify-between">
                        <span>Status:</span>
                        <span class="text-green-600">Active</span>
                    </div>
                    <div class="flex justify-between">
                        <span>Last Sync:</span>
                        <span>2 minutes ago</span>
                    </div>
                    <div class="flex justify-between">
                        <span>Orders Today:</span>
                        <span>23</span>
                    </div>
                    <div class="mt-4 space-y-2">
                        <button class="w-full px-3 py-2 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200">
                            Sync Now
                        </button>
                        <button class="w-full px-3 py-2 text-sm bg-yellow-100 text-yellow-700 rounded hover:bg-yellow-200">
                            Pause Integration
                        </button>
                        <button class="w-full px-3 py-2 text-sm bg-red-100 text-red-700 rounded hover:bg-red-200">
                            Disconnect
                        </button>
                    </div>
                </div>
            `,
            showConfirmButton: false,
            showCancelButton: true,
            cancelButtonText: 'Close'
        });
    });

    // Webhook test handler
    $('button:contains("Test Webhook")').on('click', function() {
        Swal.fire({
            title: 'Test Webhook',
            input: 'select',
            inputOptions: {
                'order.created': 'Order Created',
                'order.updated': 'Order Updated',
                'payment.completed': 'Payment Completed',
                'inventory.low': 'Low Inventory'
            },
            inputPlaceholder: 'Select event to test',
            showCancelButton: true,
            confirmButtonText: 'Send Test',
            inputValidator: (value) => {
                if (!value) {
                    return 'Please select an event to test!';
                }
            }
        }).then((result) => {
            if (result.isConfirmed) {
                Swal.fire({
                    title: 'Sending Test Webhook...',
                    text: 'Please wait while we send the test webhook.',
                    icon: 'info',
                    showConfirmButton: false,
                    timer: 2000
                }).then(() => {
                    Swal.fire({
                        icon: 'success',
                        title: 'Test Sent',
                        text: 'Test webhook sent successfully. Check your endpoint for the payload.',
                        confirmButtonText: 'OK'
                    });
                });
            }
        });
    });

    // Generate webhook secret
    $('button:contains("Generate")').on('click', function() {
        const newSecret = 'whsec_' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
        $('#webhook_secret').val(newSecret);
        
        Swal.fire({
            icon: 'success',
            title: 'Secret Generated',
            text: 'New webhook secret has been generated.',
            timer: 1500,
            showConfirmButton: false
        });
    });

    // View webhook deliveries
    $('button:contains("View All Deliveries")').on('click', function() {
        Swal.fire({
            title: 'Webhook Delivery Log',
            html: `
                <div class="text-left max-h-64 overflow-y-auto">
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
                            <span>order.created</span>
                            <div class="flex items-center space-x-2">
                                <span class="text-green-600">200</span>
                                <span class="text-gray-500">2 min ago</span>
                            </div>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
                            <span>payment.completed</span>
                            <div class="flex items-center space-x-2">
                                <span class="text-green-600">200</span>
                                <span class="text-gray-500">5 min ago</span>
                            </div>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-red-50 rounded">
                            <span>order.updated</span>
                            <div class="flex items-center space-x-2">
                                <span class="text-red-600">500</span>
                                <span class="text-gray-500">8 min ago</span>
                            </div>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
                            <span>inventory.low</span>
                            <div class="flex items-center space-x-2">
                                <span class="text-green-600">200</span>
                                <span class="text-gray-500">15 min ago</span>
                            </div>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
                            <span>customer.created</span>
                            <div class="flex items-center space-x-2">
                                <span class="text-green-600">200</span>
                                <span class="text-gray-500">22 min ago</span>
                            </div>
                        </div>
                    </div>
                </div>
            `,
            confirmButtonText: 'Close'
        });
    });

    // Test mode toggles
    $('#stripe_test_mode, #paypal_sandbox').on('change', function() {
        const service = $(this).attr('id').includes('stripe') ? 'Stripe' : 'PayPal';
        const mode = $(this).is(':checked') ? 'test' : 'live';
        
        if (mode === 'live') {
            Swal.fire({
                title: `Switch to Live Mode`,
                text: `Are you sure you want to switch ${service} to live mode? This will process real transactions.`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Switch to Live',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (!result.isConfirmed) {
                    $(this).prop('checked', true);
                }
            });
        }
    });
});
</script>
@endpush