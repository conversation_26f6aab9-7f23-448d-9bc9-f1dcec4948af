<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\MenuItemAddon;

class MenuItemAddonSeeder extends Seeder
{
    public function run()
    {
        $menuItem = \App\Models\MenuItem::first();

        if (! $menuItem) {
            $this->command->info('No MenuItem found, skipping MenuItemAddon seeding.');
            return;
        }

        MenuItemAddon::create([
            'menu_item_id' => $menuItem->id,
      
            'name' => 'Extra Cheese',
            'code' => 'EXTRA_CHEESE',
            'price' => 1.50,
            'cost' => 0.50,
        ]);
    }
}