# Reservation Module

## Overview
The Reservation Module provides comprehensive reservation management functionality for the restaurant POS system. It handles table reservations, area management, QR code generation, and reservation lifecycle management.

## Features

### Core Features
- **Reservation Management**: Create, update, cancel, and complete reservations
- **Table Management**: Manage tables, areas, and seating arrangements
- **QR Code Integration**: Generate and manage QR codes for tables
- **Real-time Availability**: Check table availability in real-time
- **Multi-status Tracking**: Track reservation status throughout the lifecycle

### Reservation Statuses
- `pending` - Initial reservation request
- `confirmed` - Reservation confirmed by staff
- `seated` - Customer has been seated
- `completed` - Reservation completed successfully
- `cancelled` - Reservation cancelled
- `no_show` - Customer didn't show up

### API Endpoints

#### Reservations
- `GET /api/reservation/reservations` - List all reservations
- `POST /api/reservation/reservations` - Create new reservation
- `GET /api/reservation/reservations/{id}` - Get reservation details
- `PUT /api/reservation/reservations/{id}` - Update reservation
- `DELETE /api/reservation/reservations/{id}` - Cancel reservation
- `POST /api/reservation/reservations/{id}/confirm` - Confirm reservation
- `POST /api/reservation/reservations/{id}/seat` - Mark as seated
- `POST /api/reservation/reservations/{id}/complete` - Complete reservation
- `POST /api/reservation/reservations/{id}/no-show` - Mark as no-show

#### Areas
- `GET /api/reservation/areas` - List all areas
- `POST /api/reservation/areas` - Create new area
- `GET /api/reservation/areas/{id}` - Get area details
- `PUT /api/reservation/areas/{id}` - Update area
- `DELETE /api/reservation/areas/{id}` - Delete area

#### Tables
- `GET /api/reservation/tables` - List all tables
- `POST /api/reservation/tables` - Create new table
- `GET /api/reservation/tables/{id}` - Get table details
- `PUT /api/reservation/tables/{id}` - Update table
- `DELETE /api/reservation/tables/{id}` - Delete table
- `GET /api/reservation/tables/availability` - Check table availability
- `POST /api/reservation/tables/{id}/generate-qr` - Generate QR code for table

#### QR Codes
- `GET /api/reservation/qr/{code}` - Get table info by QR code
- `POST /api/reservation/qr/generate` - Generate new QR code

## Services

### ReservationService
Main service for handling reservation operations including creation, updates, and status management.

### AreaService
Manages restaurant areas and their associated tables.

### TableService
Handles table management, availability checking, and QR code generation.

### QRCodeService
Generates and manages QR codes for tables.

## Models

### Reservation
- Manages reservation data and relationships
- Handles status transitions
- Tracks customer information and special requests

### Area
- Represents restaurant areas (dining room, patio, VIP, etc.)
- Links to tables within the area

### Table
- Represents individual tables
- Tracks capacity, status, and QR codes
- Links to reservations and orders

### ReservationStatus
- Defines available reservation statuses
- Used for status tracking and filtering

## Installation

The module is automatically registered through the ReservationServiceProvider.

## Usage

### Creating a Reservation
```php
$reservationService = app(ReservationService::class);
$reservation = $reservationService->createReservation([
    "branch_id" : 1,
    "customer_name" : "John Doe",
    "customer_phone" : "+**********",
    "party_size" : 4,
    "reservation_datetime" : "2024-01-15 19:00:00",
    "special_requests" : "Window table preferred"
]);
```

### Checking Table Availability
```php
$tableService = app(TableService::class);
$availableTables = $tableService->getAvailableTables(
    branchId: 1,
    datetime: '2024-01-15 19:00:00',
    partySize: 4,
    duration: 120
);
```

### Generating QR Code
```php
$qrService = app(QRCodeService::class);
$qrCode = $qrService->generateTableQR($tableId);
```

## Configuration

The module uses the following configuration options:
- Default reservation duration
- QR code settings
- Availability check parameters
- Status transition rules

## Dependencies

- Laravel Framework
- SimpleSoftwareIO/simple-qrcode (for QR code generation)
- Existing models: Branch, Customer, User