<?php

namespace Modules\Delivery\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DeliveryTrackingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'delivery_assignment_id' => $this->delivery_assignment_id,
            'delivery_personnel_id' => $this->delivery_personnel_id,
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
            'accuracy' => $this->accuracy,
            'speed' => $this->speed,
            'heading' => $this->heading,
            'altitude' => $this->altitude,
            'battery_level' => $this->battery_level,
            'is_manual' => $this->is_manual,
            'notes' => $this->notes,
            'recorded_at' => $this->recorded_at?->format('Y-m-d H:i:s'),
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),

            // Relationships
            'delivery_assignment' => $this->whenLoaded('deliveryAssignment', function () {
                return [
                    'id' => $this->deliveryAssignment->id,
                    'tracking_code' => $this->deliveryAssignment->tracking_code,
                    'status' => $this->deliveryAssignment->status,
                    'delivery_address' => $this->deliveryAssignment->delivery_address,
                    'estimated_delivery_time' => $this->deliveryAssignment->estimated_delivery_time,
                ];
            }),

            'delivery_personnel' => $this->whenLoaded('deliveryPersonnel', function () {
                return [
                    'id' => $this->deliveryPersonnel->id,
                    'user_id' => $this->deliveryPersonnel->user_id,
                    'vehicle_type' => $this->deliveryPersonnel->vehicle_type,
                    'vehicle_plate_number' => $this->deliveryPersonnel->vehicle_plate_number,
                    'phone_number' => $this->deliveryPersonnel->phone_number,
                ];
            }),

            // Computed fields
            'coordinates' => $this->getCoordinates(),
            'location_accuracy_label' => $this->getLocationAccuracyLabel(),
            'speed_kmh' => $this->getSpeedInKmh(),
            'heading_direction' => $this->getHeadingDirection(),
            'battery_status' => $this->getBatteryStatus(),
            'time_ago' => $this->getTimeAgo(),
            'is_recent' => $this->isRecent(),
            'location_source' => $this->getLocationSource(),
            'movement_status' => $this->getMovementStatus(),
        ];
    }

    /**
     * Get coordinates as array
     */
    private function getCoordinates(): array
    {
        return [
            'latitude' => (float) $this->latitude,
            'longitude' => (float) $this->longitude,
        ];
    }

    /**
     * Get location accuracy label
     */
    private function getLocationAccuracyLabel(): string
    {
        if (!$this->accuracy) {
            return 'Unknown';
        }

        return match (true) {
            $this->accuracy <= 5 => 'Excellent',
            $this->accuracy <= 10 => 'Good',
            $this->accuracy <= 20 => 'Fair',
            $this->accuracy <= 50 => 'Poor',
            default => 'Very Poor',
        };
    }

    /**
     * Get speed in km/h
     */
    private function getSpeedInKmh(): ?float
    {
        if (!$this->speed) {
            return null;
        }

        // Convert m/s to km/h
        return round($this->speed * 3.6, 1);
    }

    /**
     * Get heading direction
     */
    private function getHeadingDirection(): ?string
    {
        if (!$this->heading) {
            return null;
        }

        $directions = [
            'N', 'NNE', 'NE', 'ENE',
            'E', 'ESE', 'SE', 'SSE',
            'S', 'SSW', 'SW', 'WSW',
            'W', 'WNW', 'NW', 'NNW'
        ];

        $index = round($this->heading / 22.5) % 16;
        return $directions[$index];
    }

    /**
     * Get battery status
     */
    private function getBatteryStatus(): array
    {
        if (!$this->battery_level) {
            return [
                'level' => null,
                'status' => 'Unknown',
                'color' => 'gray',
            ];
        }

        $status = match (true) {
            $this->battery_level >= 80 => ['status' => 'Excellent', 'color' => 'green'],
            $this->battery_level >= 50 => ['status' => 'Good', 'color' => 'blue'],
            $this->battery_level >= 20 => ['status' => 'Low', 'color' => 'orange'],
            default => ['status' => 'Critical', 'color' => 'red'],
        };

        return array_merge(['level' => $this->battery_level], $status);
    }

    /**
     * Get time ago string
     */
    private function getTimeAgo(): string
    {
        if (!$this->recorded_at) {
            return 'Unknown';
        }

        $diffInMinutes = $this->recorded_at->diffInMinutes(now());

        return match (true) {
            $diffInMinutes < 1 => 'Just now',
            $diffInMinutes < 60 => $diffInMinutes . ' minute' . ($diffInMinutes > 1 ? 's' : '') . ' ago',
            $diffInMinutes < 1440 => $this->recorded_at->diffInHours(now()) . ' hour' . ($this->recorded_at->diffInHours(now()) > 1 ? 's' : '') . ' ago',
            default => $this->recorded_at->diffInDays(now()) . ' day' . ($this->recorded_at->diffInDays(now()) > 1 ? 's' : '') . ' ago',
        };
    }

    /**
     * Check if location is recent
     */
    private function isRecent(): bool
    {
        if (!$this->recorded_at) {
            return false;
        }

        return $this->recorded_at->diffInMinutes(now()) <= 5;
    }

    /**
     * Get location source
     */
    private function getLocationSource(): string
    {
        return $this->is_manual ? 'Manual Entry' : 'GPS Tracking';
    }

    /**
     * Get movement status
     */
    private function getMovementStatus(): string
    {
        if (!$this->speed) {
            return 'Stationary';
        }

        $speedKmh = $this->getSpeedInKmh();

        return match (true) {
            $speedKmh < 1 => 'Stationary',
            $speedKmh < 5 => 'Walking',
            $speedKmh < 25 => 'Cycling',
            $speedKmh < 60 => 'Driving (City)',
            default => 'Driving (Highway)',
        };
    }
}