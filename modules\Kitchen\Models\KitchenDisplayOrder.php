<?php

namespace Modules\Kitchen\Models;

use App\Models\Order;
use App\Models\Branch;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class KitchenDisplayOrder extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'order_id',
        'branch_id',
        'station',
        'priority',
        'status',
        'sent_to_kitchen_at',
        'started_at',
        'completed_at',
        'estimated_prep_time',
        'special_instructions',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'estimated_prep_time' => 'integer',
            'sent_to_kitchen_at' => 'datetime',
            'started_at' => 'datetime',
            'completed_at' => 'datetime',
        ];
    }

    /**
     * Get the order that owns the kitchen display order.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the branch that owns the kitchen display order.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Scope to get orders by status.
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get pending orders.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get preparing orders.
     */
    public function scopePreparing($query)
    {
        return $query->where('status', 'preparing');
    }

    /**
     * Scope to get ready orders.
     */
    public function scopeReady($query)
    {
        return $query->where('status', 'ready');
    }

    /**
     * Scope to get served orders.
     */
    public function scopeServed($query)
    {
        return $query->where('status', 'served');
    }

    /**
     * Scope to get orders by priority.
     */
    public function scopeByPriority($query, string $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope to get high priority orders.
     */
    public function scopeHighPriority($query)
    {
        return $query->whereIn('priority', ['high', 'urgent']);
    }

    /**
     * Scope to get orders by station.
     */
    public function scopeByStation($query, string $station)
    {
        return $query->where('station', $station);
    }

    /**
     * Scope to order by priority and creation time.
     */
    public function scopeOrdered($query)
    {
        return $query->orderByRaw("FIELD(priority, 'urgent', 'high', 'normal', 'low')")
            ->orderBy('created_at');
    }

    /**
     * Check if order is overdue.
     */
    public function isOverdue(): bool
    {
        if (!$this->estimated_prep_time || $this->status === 'served') {
            return false;
        }

        $expectedCompletionTime = $this->sent_to_kitchen_at->addMinutes($this->estimated_prep_time);
        return now()->gt($expectedCompletionTime);
    }

    /**
     * Get the time elapsed since order was sent to kitchen.
     */
    public function getElapsedTimeMinutes(): int
    {
        return $this->sent_to_kitchen_at->diffInMinutes(now());
    }

    /**
     * Start the order preparation.
     */
    public function start(): void
    {
        $this->update([
            'status' => 'preparing',
            'started_at' => now(),
        ]);
    }

    /**
     * Mark the order as ready.
     */
    public function markReady(): void
    {
        $this->update([
            'status' => 'ready',
        ]);
    }

    /**
     * Mark the order as served.
     */
    public function markServed(): void
    {
        $this->update([
            'status' => 'served',
            'completed_at' => now(),
        ]);
    }
}
