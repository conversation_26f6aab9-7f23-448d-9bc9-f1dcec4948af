@extends('layouts.master')

@section('title', 'Payment Methods')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-white">Payment Methods</h1>
                    <p class="text-blue-100">Manage payment methods for your restaurant</p>
                </div>
                <div class="flex space-x-3">
                    <button id="new-payment-method-btn" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-blue-600 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-plus mr-2"></i>
                        Add Payment Method
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-credit-card text-blue-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Methods</dt>
                                <dd class="text-lg font-medium text-gray-900" id="total-methods">-</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Active Methods</dt>
                                <dd class="text-lg font-medium text-gray-900" id="active-methods">-</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-times-circle text-red-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Inactive Methods</dt>
                                <dd class="text-lg font-medium text-gray-900" id="inactive-methods">-</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Filters</h3>
            </div>
            <div class="px-6 py-4">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Search -->
                    <div>
                        <label for="search-filter" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                        <input type="text" id="search-filter" placeholder="Search by name, code, or description" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>

                    <!-- Status Filter -->
                    <div>
                        <label for="status-filter" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <select id="status-filter" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">All Status</option>
                            <option value="1">Active</option>
                            <option value="0">Inactive</option>
                        </select>
                    </div>

                    <!-- Actions -->
                    <div class="flex items-end space-x-2">
                        <button id="apply-filters-btn" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-filter mr-2"></i>
                            Apply Filters
                        </button>
                        <button id="reset-filters-btn" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-undo mr-2"></i>
                            Reset
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Methods Table -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Payment Methods</h3>
            </div>
            <div class="overflow-x-auto">
                <table id="payment-methods-table" class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sort Order</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- Data will be loaded via DataTables -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Create/Edit Payment Method Modal -->
<div id="payment-method-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900" id="modal-title">Add Payment Method</h3>
                <button id="close-modal-btn" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <form id="payment-method-form">
                <input type="hidden" id="payment-method-id" name="id">
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                            Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="name" name="name" required class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <div id="name-error" class="text-red-500 text-sm mt-1 hidden"></div>
                    </div>

                    <!-- Code -->
                    <div>
                        <label for="code" class="block text-sm font-medium text-gray-700 mb-2">
                            Code <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="code" name="code" required class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="e.g., cash, credit_card">
                        <div id="code-error" class="text-red-500 text-sm mt-1 hidden"></div>
                        <p class="text-gray-500 text-xs mt-1">Only lowercase letters, numbers, and underscores allowed</p>
                    </div>

                    <!-- Description -->
                    <div class="md:col-span-2">
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <textarea id="description" name="description" rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"></textarea>
                        <div id="description-error" class="text-red-500 text-sm mt-1 hidden"></div>
                    </div>

                    <!-- Status -->
                    <div>
                        <label for="is_active" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <select id="is_active" name="is_active" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="1">Active</option>
                            <option value="0">Inactive</option>
                        </select>
                        <div id="is_active-error" class="text-red-500 text-sm mt-1 hidden"></div>
                    </div>

                    <!-- Sort Order -->
                    <div>
                        <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-2">Sort Order</label>
                        <input type="number" id="sort_order" name="sort_order" min="0" value="0" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <div id="sort_order-error" class="text-red-500 text-sm mt-1 hidden"></div>
                    </div>
                </div>

                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" id="cancel-btn" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Cancel
                    </button>
                    <button type="submit" id="save-btn" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-save mr-2"></i>
                        Save Payment Method
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
$(document).ready(function() {
    let paymentMethodsTable;
    let isEditMode = false;

    // Initialize DataTable
    function initializeDataTable() {
        paymentMethodsTable = $('#payment-methods-table').DataTable({
            processing: true,
            serverSide: true,
            ajax: {
            url: '{{ route("payments.methods") }}',
            data: function(d) {
                d.search_filter = $('#search-filter').val();
                d.status_filter = $('#status-filter').val();
            }
        },
            columns: [
                { data: 'name', name: 'name' },
                { data: 'code', name: 'code' },
                { data: 'description', name: 'description', orderable: false },
                { data: 'status_badge', name: 'is_active', orderable: false },
                { data: 'sort_order', name: 'sort_order' },
                { data: 'action', name: 'action', orderable: false, searchable: false }
            ],
            order: [[4, 'asc'], [0, 'asc']],
            pageLength: 25,
            responsive: true,
            language: {
                processing: '<div class="flex items-center justify-center"><i class="fas fa-spinner fa-spin mr-2"></i> Loading...</div>',
                emptyTable: 'No payment methods found',
                zeroRecords: 'No matching payment methods found'
            }
        });
    }

    // Load statistics
    function loadStatistics() {
        $.get('{{ route("payments.methods") }}', { ajax: true, stats: true })
            .done(function(response) {
                if (response.statistics) {
                    $('#total-methods').text(response.statistics.total);
                    $('#active-methods').text(response.statistics.active);
                    $('#inactive-methods').text(response.statistics.inactive);
                }
            })
            .fail(function() {
                console.error('Failed to load statistics');
            });
    }

    // Initialize page
    initializeDataTable();
    loadStatistics();

    // Filter handlers
    $('#apply-filters-btn').click(function() {
        paymentMethodsTable.ajax.reload();
    });

    $('#reset-filters-btn').click(function() {
        $('#search-filter').val('');
        $('#status-filter').val('');
        paymentMethodsTable.ajax.reload();
    });

    // Enter key on search
    $('#search-filter').keypress(function(e) {
        if (e.which === 13) {
            paymentMethodsTable.ajax.reload();
        }
    });

    // Modal handlers
    $('#new-payment-method-btn').click(function() {
        openModal(false);
    });

    $('#close-modal-btn, #cancel-btn').click(function() {
        closeModal();
    });

    // Form submission
    $('#payment-method-form').submit(function(e) {
        e.preventDefault();
        savePaymentMethod();
    });

    // Table action handlers
    $(document).on('click', '.edit-payment-method-btn', function() {
        const id = $(this).data('id');
        editPaymentMethod(id);
    });

    $(document).on('click', '.toggle-status-btn', function() {
        const id = $(this).data('id');
        togglePaymentMethodStatus(id);
    });

    $(document).on('click', '.delete-payment-method-btn', function() {
        const id = $(this).data('id');
        deletePaymentMethod(id);
    });

    // Functions
    function openModal(editMode = false, data = null) {
        isEditMode = editMode;
        
        if (editMode && data) {
            $('#modal-title').text('Edit Payment Method');
            $('#payment-method-id').val(data.id);
            $('#name').val(data.name);
            $('#code').val(data.code).prop('readonly', true);
            $('#description').val(data.description);
            $('#is_active').val(data.is_active ? '1' : '0');
            $('#sort_order').val(data.sort_order);
        } else {
            $('#modal-title').text('Add Payment Method');
            $('#payment-method-form')[0].reset();
            $('#payment-method-id').val('');
            $('#code').prop('readonly', false);
        }

        clearErrors();
        $('#payment-method-modal').removeClass('hidden');
    }

    function closeModal() {
        $('#payment-method-modal').addClass('hidden');
        $('#payment-method-form')[0].reset();
        clearErrors();
        isEditMode = false;
    }

    function clearErrors() {
        $('.text-red-500').addClass('hidden');
        $('.border-red-500').removeClass('border-red-500');
    }

    function showErrors(errors) {
        clearErrors();
        
        for (const field in errors) {
            const errorElement = $(`#${field}-error`);
            const inputElement = $(`#${field}`);
            
            if (errorElement.length) {
                errorElement.text(errors[field][0]).removeClass('hidden');
                inputElement.addClass('border-red-500');
            }
        }
    }

    function savePaymentMethod() {
        const formData = new FormData($('#payment-method-form')[0]);
        const url = isEditMode ? 
            `{{ route('payments.methods.update', '') }}/${$('#payment-method-id').val()}` : 
            '{{ route("payments.methods.store") }}';
        
        const method = isEditMode ? 'PUT' : 'POST';

        $.ajax({
            url: url,
            method: method,
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            beforeSend: function() {
                $('#save-btn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i> Saving...');
            },
            success: function(response) {
                closeModal();
                paymentMethodsTable.ajax.reload();
                loadStatistics();
                
                // Show success message
                showNotification('Payment method saved successfully!', 'success');
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    showErrors(xhr.responseJSON.errors);
                } else {
                    showNotification('Failed to save payment method. Please try again.', 'error');
                }
            },
            complete: function() {
                $('#save-btn').prop('disabled', false).html('<i class="fas fa-save mr-2"></i> Save Payment Method');
            }
        });
    }

    function editPaymentMethod(id) {
        $.get(`{{ route('payments.methods.show', '') }}/${id}`)
            .done(function(response) {
                openModal(true, response.data);
            })
            .fail(function() {
                showNotification('Failed to load payment method data.', 'error');
            });
    }

    function togglePaymentMethodStatus(id) {
        if (!confirm('Are you sure you want to toggle the status of this payment method?')) {
            return;
        }

        $.ajax({
            url: `{{ route('payments.methods.toggle-status', '') }}/${id}`,
            method: 'PATCH',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                paymentMethodsTable.ajax.reload();
                loadStatistics();
                showNotification('Payment method status updated successfully!', 'success');
            },
            error: function() {
                showNotification('Failed to update payment method status.', 'error');
            }
        });
    }

    function deletePaymentMethod(id) {
        if (!confirm('Are you sure you want to delete this payment method? This action cannot be undone.')) {
            return;
        }

        $.ajax({
            url: `{{ route('payments.methods.destroy', '') }}/${id}`,
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                paymentMethodsTable.ajax.reload();
                loadStatistics();
                showNotification('Payment method deleted successfully!', 'success');
            },
            error: function(xhr) {
                const message = xhr.responseJSON?.message || 'Failed to delete payment method.';
                showNotification(message, 'error');
            }
        });
    }

    function showNotification(message, type = 'info') {
        // Simple notification - you can replace this with a more sophisticated notification system
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const notification = $(`
            <div class="fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg ${alertClass === 'alert-success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                ${message}
            </div>
        `);
        
        $('body').append(notification);
        
        setTimeout(() => {
            notification.fadeOut(() => notification.remove());
        }, 3000);
    }
});
</script>
@endpush