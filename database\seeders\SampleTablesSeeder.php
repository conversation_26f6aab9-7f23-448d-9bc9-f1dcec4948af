<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Table;

class SampleTablesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample tables if they don't exist
        if (Table::count() == 0) {
            for ($i = 1; $i <= 10; $i++) {
                Table::create([
                    'branch_id' => 1,
                    'area_id' => 1,
                    'table_number' => 'T' . str_pad($i, 3, '0', STR_PAD_LEFT),
                    'table_name' => 'Table ' . $i,
                    'seating_capacity' => rand(2, 8),
                    'section' => $i <= 5 ? 'Main' : 'Patio',
                    'status' => 'available',
                    'is_active' => true,
                ]);
            }
            
            $this->command->info('Created 10 sample tables');
        } else {
            $this->command->info('Tables already exist: ' . Table::count() . ' tables found');
        }
    }
}