<?php

namespace Modules\Reservation\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TableResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'branch_id' => $this->branch_id,
            'area_id' => $this->area_id,
            'table_number' => $this->table_number,
            'table_name' => $this->table_name,
            'seating_capacity' => $this->seating_capacity,
            'section' => $this->section,
            'status' => $this->status,
            'qr_code' => $this->qr_code,
            'position_coordinates' => $this->position_coordinates,
            'notes' => $this->notes,
            'is_active' => $this->is_active,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            
            // Relationships
            'branch' => $this->whenLoaded('branch', function () {
                return [
                    'id' => $this->branch->id,
                    'name' => $this->branch->name,
                ];
            }),
            
            'area' => $this->whenLoaded('area', function () {
                return [
                    'id' => $this->area->id,
                    'name' => $this->area->name,
                    'description' => $this->area->description,
                ];
            }),
            
            'reservations' => $this->whenLoaded('reservations', function () {
                return $this->reservations->map(function ($reservation) {
                    return [
                        'id' => $reservation->id,
                        'reservation_number' => $reservation->reservation_number,
                        'customer_name' => $reservation->name,
                        'party_size' => $reservation->party_size,
                        'reservation_datetime' => $reservation->reservation_datetime,
                        'duration_minutes' => $reservation->duration_minutes,
                        'status_id' => $reservation->reservation_status_id,
                    ];
                });
            }),
            
            'active_reservations' => $this->whenLoaded('activeReservations', function () {
                return $this->activeReservations->map(function ($reservation) {
                    return [
                        'id' => $reservation->id,
                        'reservation_number' => $reservation->reservation_number,
                        'customer_name' => $reservation->name,
                        'party_size' => $reservation->party_size,
                        'reservation_datetime' => $reservation->reservation_datetime,
                        'duration_minutes' => $reservation->duration_minutes,
                        'status_id' => $reservation->reservation_status_id,
                    ];
                });
            }),
            
            'orders' => $this->whenLoaded('orders', function () {
                return $this->orders->map(function ($order) {
                    return [
                        'id' => $order->id,
                        'order_number' => $order->order_number ?? null,
                        'status' => $order->status ?? null,
                        'total_amount' => $order->total_amount ?? null,
                        'created_at' => $order->created_at,
                    ];
                });
            }),
            
            // Computed attributes
            'reservations_count' => $this->whenCounted('reservations'),
            'active_reservations_count' => $this->whenCounted('activeReservations'),
            'orders_count' => $this->whenCounted('orders'),
            
            // QR Code URLs (if available)
            'qr_urls' => $this->when($this->qr_code, function () {
                $baseUrl = config('app.url');
                return [
                    'table' => "{$baseUrl}/table/{$this->qr_code}",
                    'menu' => "{$baseUrl}/menu/{$this->qr_code}",
                    'order' => "{$baseUrl}/order/{$this->qr_code}",
                ];
            }),
        ];
    }
}