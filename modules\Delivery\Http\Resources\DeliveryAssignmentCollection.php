<?php

namespace Modules\Delivery\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class DeliveryAssignmentCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
            'meta' => [
                'total' => $this->total(),
                'count' => $this->count(),
                'per_page' => $this->perPage(),
                'current_page' => $this->currentPage(),
                'total_pages' => $this->lastPage(),
                'has_more_pages' => $this->hasMorePages(),
            ],
            'links' => [
                'first' => $this->url(1),
                'last' => $this->url($this->lastPage()),
                'prev' => $this->previousPageUrl(),
                'next' => $this->nextPageUrl(),
            ],
            'summary' => [
                'total_assignments' => $this->collection->count(),
                'pending_assignments' => $this->collection->where('status', 'pending')->count(),
                'assigned_assignments' => $this->collection->where('status', 'assigned')->count(),
                'picked_up_assignments' => $this->collection->where('status', 'picked_up')->count(),
                'in_transit_assignments' => $this->collection->where('status', 'in_transit')->count(),
                'delivered_assignments' => $this->collection->where('status', 'delivered')->count(),
                'cancelled_assignments' => $this->collection->where('status', 'cancelled')->count(),
                'failed_assignments' => $this->collection->where('status', 'failed')->count(),
                'overdue_assignments' => $this->collection->filter(function ($assignment) {
                    // Access the underlying model data
                    $model = $assignment->resource ?? $assignment;
                    if (!$model->delivery_time || in_array($model->status, ['delivered', 'cancelled', 'failed'])) {
                        return false;
                    }
                    return now()->isAfter($model->delivery_time);
                })->count(),
                'high_priority_assignments' => $this->collection->where('priority', 'high')->count(),
                'urgent_assignments' => $this->collection->where('priority', 'urgent')->count(),
            ],
        ];
    }
}