<?php

namespace Modules\Delivery\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class CreateDeliveryZoneRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:100',
            'branch_id' => 'required|exists:branches,id',
            'coordinates' => 'required_without:address|array|min:3',
            'coordinates.*.latitude' => 'required_with:coordinates|numeric|between:-90,90',
            'coordinates.*.longitude' => 'required_with:coordinates|numeric|between:-180,180',
            'address' => 'required_without:coordinates|string|max:255',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:100',
            'delivery_fee' => 'required|numeric|min:0',
            'minimum_order_amount' => 'nullable|numeric|min:0',
            'estimated_delivery_time' => 'required|integer|min:1|max:300',
            'is_active' => 'nullable|boolean',
            'priority' => 'nullable|integer|min:1|max:10',
            'description' => 'nullable|string|max:500',
            'color' => 'nullable|string|regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Zone name is required',
            'name.max' => 'Zone name cannot exceed 100 characters',
            'branch_id.required' => 'Branch is required',
            'branch_id.exists' => 'Selected branch does not exist',
            'coordinates.required_without' => 'Zone coordinates or address is required',
            'coordinates.min' => 'Zone must have at least 3 coordinate points',
            'coordinates.*.latitude.required_with' => 'Latitude is required for each coordinate',
            'coordinates.*.latitude.between' => 'Latitude must be between -90 and 90',
            'coordinates.*.longitude.required_with' => 'Longitude is required for each coordinate',
            'coordinates.*.longitude.between' => 'Longitude must be between -180 and 180',
            'address.required_without' => 'Address is required if coordinates are not provided',
            'address.max' => 'Address cannot exceed 255 characters',
            'city.max' => 'City cannot exceed 100 characters',
            'state.max' => 'State cannot exceed 100 characters',
            'postal_code.max' => 'Postal code cannot exceed 20 characters',
            'country.max' => 'Country cannot exceed 100 characters',
            'delivery_fee.required' => 'Delivery fee is required',
            'delivery_fee.min' => 'Delivery fee cannot be negative',
            'minimum_order_amount.min' => 'Minimum order amount cannot be negative',
            'estimated_delivery_time.required' => 'Estimated delivery time is required',
            'estimated_delivery_time.min' => 'Estimated delivery time must be at least 1 minute',
            'estimated_delivery_time.max' => 'Estimated delivery time cannot exceed 300 minutes',
            'priority.min' => 'Priority must be at least 1',
            'priority.max' => 'Priority cannot exceed 10',
            'description.max' => 'Description cannot exceed 500 characters',
            'color.regex' => 'Color must be a valid hex color code (e.g., #FF0000)',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default values
        if (!$this->has('is_active')) {
            $this->merge(['is_active' => true]);
        }

        if (!$this->has('priority')) {
            $this->merge(['priority' => 5]);
        }

        if (!$this->has('color')) {
            $this->merge(['color' => '#007bff']);
        }

        // Ensure coordinates is an array
        if ($this->has('coordinates') && is_string($this->coordinates)) {
            try {
                $coordinates = json_decode($this->coordinates, true);
                if (is_array($coordinates)) {
                    $this->merge(['coordinates' => $coordinates]);
                }
            } catch (\Exception $e) {
                // Keep original value for validation to fail
            }
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Check for duplicate zone name within the same branch
            if ($this->name && $this->branch_id) {
                $existingZone = \Modules\Delivery\Models\DeliveryZone::where('name', $this->name)
                    ->where('branch_id', $this->branch_id)
                    ->first();

                if ($existingZone) {
                    $validator->errors()->add('name', 'A zone with this name already exists for the selected branch');
                }
            }

            // Validate polygon coordinates (must form a closed polygon)
            if ($this->has('coordinates') && is_array($this->coordinates)) {
                $coordinates = $this->coordinates;
                
                if (count($coordinates) >= 3) {
                    // Check if polygon is closed (first and last points should be the same)
                    $firstPoint = $coordinates[0];
                    $lastPoint = end($coordinates);
                    
                    if ($firstPoint['latitude'] !== $lastPoint['latitude'] || 
                        $firstPoint['longitude'] !== $lastPoint['longitude']) {
                        // Auto-close the polygon by adding the first point at the end
                        $coordinates[] = $firstPoint;
                        $this->merge(['coordinates' => $coordinates]);
                    }
                }

                // Validate that coordinates form a valid polygon (no self-intersections)
                if (!$this->isValidPolygon($coordinates)) {
                    $validator->errors()->add('coordinates', 'Coordinates do not form a valid polygon');
                }
            }

            // Check if branch belongs to the same tenant
            if ($this->branch_id) {
                $branch = \App\Models\Branch::find($this->branch_id);
                if ($branch && $branch->tenant_id !== Auth::user()->tenant_id) {
                    $validator->errors()->add('branch_id', 'Branch does not belong to your organization');
                }
            }
        });
    }

    /**
     * Check if coordinates form a valid polygon
     */
    private function isValidPolygon(array $coordinates): bool
    {
        // Basic validation - at least 3 points and reasonable coordinate values
        if (count($coordinates) < 3) {
            return false;
        }

        foreach ($coordinates as $coord) {
            if (!isset($coord['latitude']) || !isset($coord['longitude'])) {
                return false;
            }

            $lat = $coord['latitude'];
            $lng = $coord['longitude'];

            if ($lat < -90 || $lat > 90 || $lng < -180 || $lng > 180) {
                return false;
            }
        }

        return true;
    }
}