<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('menu_item_variants', function (Blueprint $table) {
            $table->id();
            $table->foreignId('branch_id')->nullable()->constrained('branches')->onDelete('cascade');
            $table->foreignId('menu_item_id')->nullable()->constrained('menu_items')->onDelete('cascade');
            $table->string('name', 100)->nullable();
            $table->string('name_en', 100)->nullable();
            $table->string('code', 50)->nullable();
            $table->string('type', 50)->nullable();
            $table->decimal('price_modifier', 10, 2)->nullable()->default(0);
            $table->decimal('cost_modifier', 10, 2)->nullable()->default(0);
            $table->boolean('is_default')->nullable()->default(false);
            $table->boolean('is_active')->nullable()->default(true);
            $table->integer('sort_order')->nullable()->default(0);
            $table->string('color_code', 7)->nullable();
            $table->string('image')->nullable();
            $table->timestamps();
            
            $table->unique(['menu_item_id', 'code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('menu_item_variants');
    }
};