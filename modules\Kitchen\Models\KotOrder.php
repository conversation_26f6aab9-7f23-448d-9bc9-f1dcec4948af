<?php

namespace Modules\Kitchen\Models;

use App\Models\Order;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class KotOrder extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'branch_id',
        'kitchen_id',
        'order_id',
        'kot_number',
        'status',
        'priority',
        'estimated_prep_time_minutes',
        'actual_prep_time_minutes',
        'items_data',
        'special_instructions',
        'assigned_to',
        'started_at',
        'completed_at',
        'sent_to_kitchen_at',
        'notes',
        'created_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'items_data' => 'array',
            'estimated_prep_time_minutes' => 'integer',
            'actual_prep_time_minutes' => 'integer',
            'started_at' => 'datetime',
            'completed_at' => 'datetime',
            'sent_to_kitchen_at' => 'datetime',
        ];
    }

    /**
     * Get the kitchen that owns the KOT order.
     */
    public function kitchen(): BelongsTo
    {
        return $this->belongsTo(Kitchen::class);
    }

    /**
     * Get the order that this KOT belongs to.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the user assigned to this KOT.
     */
    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * Get the user who created this KOT.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the KOT order items.
     */
    public function kotOrderItems(): HasMany
    {
        return $this->hasMany(KotOrderItem::class);
    }

    /**
     * Scope to get KOTs by status.
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get pending KOTs.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get preparing KOTs.
     */
    public function scopePreparing($query)
    {
        return $query->where('status', 'preparing');
    }

    /**
     * Scope to get ready KOTs.
     */
    public function scopeReady($query)
    {
        return $query->where('status', 'ready');
    }

    /**
     * Scope to get completed KOTs.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope to get KOTs by priority.
     */
    public function scopeByPriority($query, string $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope to get high priority KOTs.
     */
    public function scopeHighPriority($query)
    {
        return $query->whereIn('priority', ['high', 'urgent']);
    }

    /**
     * Scope to get KOTs by kitchen.
     */
    public function scopeByKitchen($query, int $kitchenId)
    {
        return $query->where('kitchen_id', $kitchenId);
    }

    /**
     * Scope to order by priority and creation time.
     */
    public function scopeOrdered($query)
    {
        return $query->orderByRaw("FIELD(priority, 'urgent', 'high', 'normal', 'low')")
            ->orderBy('created_at');
    }

    /**
     * Check if KOT is overdue.
     */
    public function isOverdue(): bool
    {
        if (!$this->estimated_prep_time_minutes || $this->status === 'completed') {
            return false;
        }

        $expectedCompletionTime = $this->created_at->addMinutes($this->estimated_prep_time_minutes);
        return now()->gt($expectedCompletionTime);
    }

    /**
     * Get the time elapsed since KOT was created.
     */
    public function getElapsedTimeMinutes(): int
    {
        return $this->created_at->diffInMinutes(now());
    }

    /**
     * Get remaining time for completion.
     */
    public function getRemainingTimeMinutes(): ?int
    {
        if (!$this->estimated_prep_time_minutes || $this->status === 'completed') {
            return null;
        }

        $elapsedMinutes = $this->getElapsedTimeMinutes();
        $remainingMinutes = $this->estimated_prep_time_minutes - $elapsedMinutes;

        return max(0, $remainingMinutes);
    }

    /**
     * Start the KOT preparation.
     */
    public function start(?int $userId = null): void
    {
        $this->update([
            'status' => 'preparing',
            'started_at' => now(),
            'assigned_to' => $userId,
        ]);
    }

    /**
     * Mark the KOT as ready.
     */
    public function markReady(): void
    {
        $this->update([
            'status' => 'ready',
        ]);
    }

    /**
     * Complete the KOT.
     */
    public function complete(): void
    {
        $actualPrepTime = $this->started_at ? 
            $this->started_at->diffInMinutes(now()) : 
            $this->created_at->diffInMinutes(now());

        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
            'actual_prep_time_minutes' => $actualPrepTime,
        ]);
    }

    /**
     * Cancel the KOT.
     */
    public function cancel(string $reason = null): void
    {
        $this->update([
            'status' => 'cancelled',
            'notes' => $reason ? "Cancelled: {$reason}" : 'Cancelled',
        ]);
    }

    /**
     * Generate a unique KOT number.
     */
    public static function generateKotNumber(): string
    {
        $prefix = 'KOT';
        $date = now()->format('Ymd');
        $lastKot = static::whereDate('created_at', today())
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastKot ? (int) substr($lastKot->kot_number, -4) + 1 : 1;

        return $prefix . $date . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }
}
