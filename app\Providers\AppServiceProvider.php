<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * All module service providers
     */
    protected $moduleProviders = [
        \Modules\Menu\Providers\MenuServiceProvider::class,
        \Modules\Orders\Providers\OrderServiceProvider::class,
        \Modules\Inventory\Providers\InventoryServiceProvider::class,
        \Modules\Kitchen\Providers\KitchenServiceProvider::class,
        \Modules\Transaction\Providers\TransactionServiceProvider::class,

        \Modules\Customer\Providers\CustomerServiceProvider::class,
        \Modules\Reports\Providers\ReportsServiceProvider::class,
        \Modules\Settings\Providers\SettingsServiceProvider::class,

        \Modules\Tenant\Providers\TenantServiceProvider::class,
        \Modules\Reservation\Providers\ReservationServiceProvider::class,

        \Modules\Delivery\Providers\DeliveryServiceProvider::class,
        \Modules\Auth\Providers\AuthServiceProvider::class,
    ];

    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register module service providers
        foreach ($this->moduleProviders as $provider) {
            $this->app->register($provider);
        }
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}
