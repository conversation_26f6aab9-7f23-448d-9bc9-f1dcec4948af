<?php

namespace Modules\Orders\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;
use Modules\Orders\Services\OrderService;

class OrderServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register the OrderService
        $this->app->singleton(\Modules\Orders\Services\OrderService::class, function ($app) {
            // Try to resolve TransactionService, but don't fail if it's not available
            $transactionService = null;
            try {
                $transactionService = $app->make(\Modules\Transaction\Services\TransactionService::class);
            } catch (\Exception $e) {
                // TransactionService not available, continue without it
            }

            return new \Modules\Orders\Services\OrderService(
                $app->make(\Modules\Inventory\Services\InventoryService::class),
                $app->make(\Modules\Kitchen\Services\KitchenService::class),
                $transactionService
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Load routes
        $this->loadRoutes();
        
        // Load views
        $this->loadViewsFrom(__DIR__ . '/../resources/views', 'orders');
        
        // Register middleware
        $router = $this->app['router'];
        $router->aliasMiddleware('order.access', \Modules\Orders\Http\Middleware\OrderAccessMiddleware::class);
        
        // Load migrations if needed
        // $this->loadMigrationsFrom(__DIR__ . '/../database/migrations');
        
        // Publish config if needed
        // $this->publishes([
        //     __DIR__ . '/../config/orders.php' => config_path('orders.php'),
        // ], 'orders-config');
    }

    /**
     * Load module routes
     */
    protected function loadRoutes(): void
    {
        // Load API routes
        if (file_exists(__DIR__ . '/../routes/api.php')) {
            Route::group([
                'middleware' => 'api',
                'prefix' => 'api',
            ], function () {
                require __DIR__ . '/../routes/api.php';
            });
        }

        // Load Web routes
        if (file_exists(__DIR__ . '/../routes/web.php')) {
            Route::group([
                'middleware' => 'web',
            ], function () {
                require __DIR__ . '/../routes/web.php';
            });
        }
    }
}