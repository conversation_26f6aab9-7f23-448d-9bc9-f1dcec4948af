<?php

namespace App\Models\Settings;

use App\Models\Tenant;
use App\Models\Branch;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PrinterSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'tenant_id',
        'branch_id',
        'name',
        'type',
        'connection_type',
        'connection_config',
        'paper_size',
        'print_template',
        'auto_cut',
        'cash_drawer',
        'copies',
        'is_default',
        'is_active',
    ];

    protected $casts = [
        'connection_config' => 'array',
        'print_template' => 'array',
        'auto_cut' => 'boolean',
        'cash_drawer' => 'boolean',
        'is_default' => 'boolean',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    public function scopeForTenant($query, $tenantId)
    {
        return $query->where('tenant_id', $tenantId);
    }

    public function scopeForBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    // Methods
    public function testConnection(): bool
    {
        // Implementation for testing printer connection
        // This would vary based on connection_type
        return true; // Placeholder
    }

    public function getConnectionString(): string
    {
        $config = $this->connection_config;
        
        switch ($this->connection_type) {
            case 'network':
                return "tcp://{$config['ip']}:{$config['port']}";
            case 'usb':
                return $config['device_path'] ?? '/dev/usb/lp0';
            case 'serial':
                return $config['serial_port'] ?? '/dev/ttyUSB0';
            default:
                return '';
        }
    }

    public function getDefaultTemplate(): array
    {
        return [
            'header' => [
                'logo' => true,
                'restaurant_name' => true,
                'address' => true,
                'phone' => true,
            ],
            'order_details' => [
                'order_number' => true,
                'date_time' => true,
                'table_number' => true,
                'customer_name' => false,
            ],
            'items' => [
                'show_quantity' => true,
                'show_price' => true,
                'show_total' => true,
                'show_modifiers' => true,
            ],
            'totals' => [
                'subtotal' => true,
                'tax' => true,
                'discount' => true,
                'total' => true,
            ],
            'footer' => [
                'thank_you_message' => 'شكراً لزيارتكم',
                'qr_code' => false,
                'barcode' => false,
            ],
        ];
    }
}
