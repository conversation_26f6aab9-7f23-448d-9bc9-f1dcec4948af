<!-- Assign Personnel Modal -->
<div id="assignPersonnelModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center pb-3">
            <h3 class="text-lg font-medium text-gray-900">تعيين موظف توصيل</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeModal('assignPersonnelModal')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="mt-4">
            <form id="assignPersonnelForm">
                <input type="hidden" id="assignmentId" name="assignment_id">

                <div class="mb-4">
                    <label for="personnelSelect" class="block text-sm font-medium text-gray-700 mb-2">اختر موظف التوصيل</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="personnelSelect" name="personnel_id" required>
                        <option value="">جاري تحميل الموظفين...</option>
                    </select>
                </div>

                <div class="mb-4">
                    <label for="assignmentNotes" class="block text-sm font-medium text-gray-700 mb-2">ملاحظات (اختياري)</label>
                    <textarea class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" id="assignmentNotes" name="notes" rows="3" placeholder="أي تعليمات خاصة أو ملاحظات..."></textarea>
                </div>
            </form>
        </div>
        <div class="flex justify-end space-x-2 pt-4">
            <button type="button" class="px-4 py-2 bg-gray-500 text-white text-sm font-medium rounded-md hover:bg-gray-600" onclick="closeModal('assignPersonnelModal')">إلغاء</button>
            <button type="button" class="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700" id="confirmAssign">تعيين الموظف</button>
        </div>
    </div>
</div>

<!-- Update Status Modal -->
<div class="modal fade" id="updateStatusModal" tabindex="-1" role="dialog" aria-labelledby="updateStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="updateStatusModalLabel">Update Delivery Status</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="updateStatusForm">
                    <input type="hidden" id="statusAssignmentId" name="assignment_id">
                    
                    <div class="form-group">
                        <label for="statusSelect">New Status</label>
                        <select class="form-control" id="statusSelect" name="status" required>
                            <option value="">Select status...</option>
                            <option value="assigned">Assigned</option>
                            <option value="picked_up">Picked Up</option>
                            <option value="in_transit">In Transit</option>
                            <option value="delivered">Delivered</option>
                            <option value="failed">Failed</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="statusNotes">Notes (Optional)</label>
                        <textarea class="form-control" id="statusNotes" name="notes" rows="3" placeholder="Any additional notes about this status update..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmStatusUpdate">Update Status</button>
            </div>
        </div>
    </div>
</div>

<!-- Track Assignment Modal -->
<div class="modal fade" id="trackAssignmentModal" tabindex="-1" role="dialog" aria-labelledby="trackAssignmentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="trackAssignmentModalLabel">Track Delivery</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Current Status</h6>
                        <table class="table table-borderless table-sm">
                            <tr>
                                <td><strong>Status:</strong></td>
                                <td><span id="trackingStatus">-</span></td>
                            </tr>
                            <tr>
                                <td><strong>Personnel:</strong></td>
                                <td><span id="trackingPersonnel">-</span></td>
                            </tr>
                            <tr>
                                <td><strong>Last Update:</strong></td>
                                <td><span id="trackingLastUpdate">-</span></td>
                            </tr>
                            <tr>
                                <td><strong>Estimated Delivery:</strong></td>
                                <td><span id="trackingEstimated">-</span></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>Live Location</h6>
                        <div id="trackingMap" style="height: 200px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px;">
                            <div class="d-flex align-items-center justify-content-center h-100">
                                <div class="text-center">
                                    <i class="fas fa-map-marker-alt fa-2x text-muted mb-2"></i>
                                    <p class="text-muted">Live tracking map</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>Recent Locations</h6>
                        <div id="recentLocations">
                            <p class="text-muted">Loading recent locations...</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="refreshTracking">Refresh</button>
            </div>
        </div>
    </div>
</div>

<!-- Create Assignment Modal -->
<div class="modal fade" id="createAssignmentModal" tabindex="-1" role="dialog" aria-labelledby="createAssignmentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createAssignmentModalLabel">Create New Assignment</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="createAssignmentForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="createOrderId">Order <span class="text-danger">*</span></label>
                                <select class="form-control" id="createOrderId" name="order_id" required>
                                    <option value="">Select order...</option>
                                </select>
                                <small class="form-text text-muted">Only orders ready for delivery will be shown</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="createPersonnelId">Delivery Personnel</label>
                                <select class="form-control" id="createPersonnelId" name="delivery_personnel_id">
                                    <option value="">Assign later...</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="createDeliveryAddress">Delivery Address <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="createDeliveryAddress" name="delivery_address" rows="3" required placeholder="Enter delivery address..."></textarea>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="createDeliveryFee">Delivery Fee</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input type="number" class="form-control" id="createDeliveryFee" name="delivery_fee" step="0.01" min="0" placeholder="0.00">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="createEstimatedTime">Estimated Delivery Time</label>
                                <input type="datetime-local" class="form-control" id="createEstimatedTime" name="estimated_delivery_time">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="createPriority">Priority</label>
                                <select class="form-control" id="createPriority" name="priority">
                                    <option value="normal">Normal</option>
                                    <option value="high">High</option>
                                    <option value="urgent">Urgent</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="createLatitude">Latitude</label>
                                <input type="number" class="form-control" id="createLatitude" name="delivery_latitude" step="any" placeholder="e.g., 40.7128">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="createLongitude">Longitude</label>
                                <input type="number" class="form-control" id="createLongitude" name="delivery_longitude" step="any" placeholder="e.g., -74.0060">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="createNotes">Special Instructions</label>
                        <textarea class="form-control" id="createNotes" name="notes" rows="3" placeholder="Any special delivery instructions..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="confirmCreateAssignment">Create Assignment</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Assignment Modal -->
<div class="modal fade" id="editAssignmentModal" tabindex="-1" role="dialog" aria-labelledby="editAssignmentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editAssignmentModalLabel">Edit Assignment</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="editAssignmentForm">
                    <input type="hidden" id="editAssignmentId" name="assignment_id">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="editPersonnelId">Delivery Personnel</label>
                                <select class="form-control" id="editPersonnelId" name="delivery_personnel_id">
                                    <option value="">Unassigned</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="editStatus">Status</label>
                                <select class="form-control" id="editStatus" name="status">
                                    <option value="pending">Pending</option>
                                    <option value="assigned">Assigned</option>
                                    <option value="picked_up">Picked Up</option>
                                    <option value="in_transit">In Transit</option>
                                    <option value="delivered">Delivered</option>
                                    <option value="cancelled">Cancelled</option>
                                    <option value="failed">Failed</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="editDeliveryAddress">Delivery Address <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="editDeliveryAddress" name="delivery_address" rows="3" required></textarea>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="editDeliveryFee">Delivery Fee</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input type="number" class="form-control" id="editDeliveryFee" name="delivery_fee" step="0.01" min="0">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="editEstimatedTime">Estimated Delivery Time</label>
                                <input type="datetime-local" class="form-control" id="editEstimatedTime" name="estimated_delivery_time">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="editPriority">Priority</label>
                                <select class="form-control" id="editPriority" name="priority">
                                    <option value="normal">Normal</option>
                                    <option value="high">High</option>
                                    <option value="urgent">Urgent</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="editLatitude">Latitude</label>
                                <input type="number" class="form-control" id="editLatitude" name="delivery_latitude" step="any">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="editLongitude">Longitude</label>
                                <input type="number" class="form-control" id="editLongitude" name="delivery_longitude" step="any">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="editNotes">Special Instructions</label>
                        <textarea class="form-control" id="editNotes" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmEditAssignment">Update Assignment</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteAssignmentModal" tabindex="-1" role="dialog" aria-labelledby="deleteAssignmentModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteAssignmentModalLabel">Delete Assignment</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    Are you sure you want to delete this delivery assignment?
                </div>
                <p>This action cannot be undone. The assignment will be permanently removed from the system.</p>
                <input type="hidden" id="deleteAssignmentId">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteAssignment">Delete Assignment</button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Actions Modal -->
<div class="modal fade" id="bulkActionsModal" tabindex="-1" role="dialog" aria-labelledby="bulkActionsModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkActionsModalLabel">Bulk Actions</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="bulkActionsForm">
                    <div class="form-group">
                        <label for="bulkAction">Select Action</label>
                        <select class="form-control" id="bulkAction" name="action" required>
                            <option value="">Choose action...</option>
                            <option value="assign_personnel">Assign Personnel</option>
                            <option value="update_status">Update Status</option>
                            <option value="delete">Delete Selected</option>
                            <option value="export">Export Selected</option>
                        </select>
                    </div>
                    
                    <div class="form-group" id="bulkPersonnelGroup" style="display: none;">
                        <label for="bulkPersonnelSelect">Select Personnel</label>
                        <select class="form-control" id="bulkPersonnelSelect" name="personnel_id">
                            <option value="">Choose personnel...</option>
                        </select>
                    </div>
                    
                    <div class="form-group" id="bulkStatusGroup" style="display: none;">
                        <label for="bulkStatusSelect">Select Status</label>
                        <select class="form-control" id="bulkStatusSelect" name="status">
                            <option value="">Choose status...</option>
                            <option value="assigned">Assigned</option>
                            <option value="picked_up">Picked Up</option>
                            <option value="in_transit">In Transit</option>
                            <option value="delivered">Delivered</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="bulkNotes">Notes (Optional)</label>
                        <textarea class="form-control" id="bulkNotes" name="notes" rows="3" placeholder="Any notes for this bulk action..."></textarea>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <span id="bulkSelectedCount">0</span> assignments selected for this action.
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmBulkAction">Apply Action</button>
            </div>
        </div>
    </div>
</div>

<!-- Assignment Details Modal -->
<div class="modal fade" id="assignmentDetailsModal" tabindex="-1" role="dialog" aria-labelledby="assignmentDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="assignmentDetailsModalLabel">Assignment Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="assignmentDetailsContent">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                        <p>Loading assignment details...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="editFromDetails">Edit Assignment</button>
            </div>
        </div>
    </div>
</div>