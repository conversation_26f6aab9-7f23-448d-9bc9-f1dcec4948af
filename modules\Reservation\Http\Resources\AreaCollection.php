<?php

namespace Modules\Reservation\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class AreaCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
            'meta' => [
                'total' => $this->count(),
                'total_tables' => $this->collection->sum(function ($area) {
                    return $area->tables_count ?? 0;
                }),
                'total_capacity' => $this->collection->sum(function ($area) {
                    return $area->total_capacity ?? 0;
                }),
            ],
        ];
    }
}