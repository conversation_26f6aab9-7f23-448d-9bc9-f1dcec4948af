<?php

namespace Modules\Delivery\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DeliveryAssignmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'order_id' => $this->order_id,
            'delivery_personnel_id' => $this->delivery_personnel_id,
            'delivery_zone_id' => $this->delivery_zone_id,
            'status' => $this->status,
            'assigned_at' => $this->assigned_at?->format('Y-m-d H:i:s'),
            'picked_up_at' => $this->picked_up_at?->format('Y-m-d H:i:s'),
            'delivered_at' => $this->delivered_at?->format('Y-m-d H:i:s'),
            'delivery_notes' => $this->delivery_notes,
            'failure_reason' => $this->failure_reason,
            'delivery_proof' => $this->delivery_proof,
            'delivery_fee_earned' => $this->delivery_fee_earned,
            'estimated_duration_minutes' => $this->estimated_duration_minutes,
            'actual_duration_minutes' => $this->actual_duration_minutes,
            'distance_km' => $this->distance_km,
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),

            // Relationships
            'order' => $this->whenLoaded('order', function () {
                return [
                    'id' => $this->order->id,
                    'order_number' => $this->order->order_number,
                    'total_amount' => $this->order->total_amount,
                    'status' => $this->order->status,
                    'customer_name' => $this->order->customer_name,
                    'customer_phone' => $this->order->customer_phone,
                    'customer_email' => $this->order->customer_email,
                    'created_at' => $this->order->created_at?->format('Y-m-d H:i:s'),
                ];
            }),

            'delivery_personnel' => $this->whenLoaded('deliveryPersonnel', function () {
                return new DeliveryPersonnelResource($this->deliveryPersonnel);
            }),

            'delivery_zone' => $this->whenLoaded('deliveryZone', function () {
                return new DeliveryZoneResource($this->deliveryZone);
            }),

            'tracking' => $this->whenLoaded('deliveryTracking', function () {
                return DeliveryTrackingResource::collection($this->deliveryTracking);
            }),

            'latest_tracking' => $this->whenLoaded('latestTracking', function () {
                return new DeliveryTrackingResource($this->latestTracking);
            }),

            'review' => $this->whenLoaded('deliveryReview', function () {
                return [
                    'id' => $this->deliveryReview->id,
                    'rating' => $this->deliveryReview->rating,
                    'comment' => $this->deliveryReview->comment,
                    'created_at' => $this->deliveryReview->created_at?->format('Y-m-d H:i:s'),
                ];
            }),

            'tip' => $this->whenLoaded('deliveryTip', function () {
                return [
                    'id' => $this->deliveryTip->id,
                    'amount' => $this->deliveryTip->amount,
                    'payment_method' => $this->deliveryTip->payment_method,
                    'created_at' => $this->deliveryTip->created_at?->format('Y-m-d H:i:s'),
                ];
            }),

            // Computed fields
            'status_label' => $this->getStatusLabel(),
            'is_overdue' => $this->isOverdue(),
            'estimated_arrival' => $this->getEstimatedArrival(),
            'delivery_duration_minutes' => $this->getDeliveryDurationMinutes(),
            'is_delayed' => $this->isDelayed(),
            'can_be_cancelled' => $this->canBeCancelled(),
            'can_be_reassigned' => $this->canBeReassigned(),
        ];
    }

    /**
     * Get status label
     */
    private function getStatusLabel(): string
    {
        return match ($this->status) {
            'pending' => 'Pending Assignment',
            'assigned' => 'Assigned',
            'picked_up' => 'Picked Up',
            'in_transit' => 'In Transit',
            'delivered' => 'Delivered',
            'cancelled' => 'Cancelled',
            'failed' => 'Failed',
            default => ucfirst($this->status),
        };
    }

    /**
     * Check if delivery is overdue
     */
    private function isOverdue(): bool
    {
        if (!$this->estimated_duration_minutes || in_array($this->status, ['delivered', 'cancelled', 'failed'])) {
            return false;
        }

        if ($this->picked_up_at) {
            $estimatedDeliveryTime = $this->picked_up_at->addMinutes($this->estimated_duration_minutes);
            return now()->isAfter($estimatedDeliveryTime);
        }

        return false;
    }

    /**
     * Get estimated arrival time
     */
    private function getEstimatedArrival(): ?string
    {
        if (in_array($this->status, ['delivered', 'cancelled', 'failed'])) {
            return null;
        }

        if ($this->estimated_duration_minutes && $this->picked_up_at) {
            return $this->picked_up_at
                ->addMinutes($this->estimated_duration_minutes)
                ->format('Y-m-d H:i:s');
        }

        return null;
    }

    /**
     * Get delivery duration in minutes
     */
    private function getDeliveryDurationMinutes(): ?int
    {
        if (!$this->picked_up_at || !$this->delivered_at) {
            return null;
        }

        return $this->picked_up_at->diffInMinutes($this->delivered_at);
    }

    /**
     * Check if delivery is delayed
     */
    private function isDelayed(): bool
    {
        if (!$this->estimated_duration_minutes || !$this->picked_up_at) {
            return false;
        }

        $estimatedDeliveryTime = $this->picked_up_at->addMinutes($this->estimated_duration_minutes);
        
        if ($this->delivered_at) {
            return $this->delivered_at->isAfter($estimatedDeliveryTime);
        }

        return now()->isAfter($estimatedDeliveryTime) && !in_array($this->status, ['delivered', 'cancelled', 'failed']);
    }

    /**
     * Check if assignment can be cancelled
     */
    private function canBeCancelled(): bool
    {
        return in_array($this->status, ['pending', 'assigned', 'picked_up']);
    }

    /**
     * Check if assignment can be reassigned
     */
    private function canBeReassigned(): bool
    {
        return in_array($this->status, ['pending', 'failed']);
    }
}