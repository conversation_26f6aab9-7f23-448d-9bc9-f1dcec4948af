<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\MenuItemVariant;

class MenuItemVariantSeeder extends Seeder
{
    public function run()
    {
        MenuItemVariant::create([
            'menu_item_id' => 1,
            'name' => 'Large',
            'code' => 'VARIANT_LARGE',
            'price_modifier' => 2.00,
            'cost_modifier' => 0.50,
            'is_default' => true,
            'sort_order' => 1,
        ]);
    }
}