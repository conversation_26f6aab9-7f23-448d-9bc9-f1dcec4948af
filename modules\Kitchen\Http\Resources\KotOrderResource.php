<?php

namespace Modules\Kitchen\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class KotOrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'tenant_id' => $this->tenant_id,
            'branch_id' => $this->branch_id,
            'kitchen_id' => $this->kitchen_id,
            'order_id' => $this->order_id,
            'kot_number' => $this->kot_number,
            'status' => $this->status,
            'status_label' => ucfirst($this->status),
            'priority' => $this->priority,
            'priority_label' => ucfirst($this->priority),
            'estimated_prep_time_minutes' => $this->estimated_prep_time_minutes,
            'actual_prep_time_minutes' => $this->actual_prep_time_minutes,
            'items_data' => $this->items_data,
            'special_instructions' => $this->special_instructions,
            'assigned_to' => $this->assigned_to,
            'started_at' => $this->started_at?->toISOString(),
            'completed_at' => $this->completed_at?->toISOString(),
            'sent_to_kitchen_at' => $this->sent_to_kitchen_at?->toISOString(),
            'notes' => $this->notes,
            'created_by' => $this->created_by,
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),

            // Relationships
            'kitchen' => $this->whenLoaded('kitchen', function () {
                return [
                    'id' => $this->kitchen->id,
                    'name' => $this->kitchen->name,
                    'code' => $this->kitchen->code,
                    'station_type' => $this->kitchen->station_type,
                ];
            }),

            'order' => $this->whenLoaded('order', function () {
                return [
                    'id' => $this->order->id,
                    'order_number' => $this->order->order_number,
                    'status' => $this->order->status,
                    'total_amount' => $this->order->total_amount,
                    'customer' => $this->whenLoaded('order.customer', function () {
                        return [
                            'id' => $this->order->customer->id,
                            'name' => $this->order->customer->name,
                            'phone' => $this->order->customer->phone,
                        ];
                    }),
                ];
            }),

            'assigned_to_user' => $this->whenLoaded('assignedTo', function () {
                return [
                    'id' => $this->assignedTo->id,
                    'name' => $this->assignedTo->name,
                    'email' => $this->assignedTo->email,
                ];
            }),

            'creator' => $this->whenLoaded('creator', function () {
                return [
                    'id' => $this->creator->id,
                    'name' => $this->creator->name,
                ];
            }),

            'kot_order_items' => $this->whenLoaded('kotOrderItems', function () {
                return KotOrderItemResource::collection($this->kotOrderItems);
            }),

            // Computed attributes
            'elapsed_time_minutes' => $this->getElapsedTimeMinutes(),
            'remaining_time_minutes' => $this->getRemainingTimeMinutes(),
            'is_overdue' => $this->isOverdue(),
            'items_count' => $this->kotOrderItems()->count(),

            // Status badges for UI
            'status_badge' => [
                'class' => $this->getStatusBadgeClass(),
                'text' => ucfirst($this->status),
            ],

            'priority_badge' => [
                'class' => $this->getPriorityBadgeClass(),
                'text' => ucfirst($this->priority),
            ],

            // Time display
            'time_display' => [
                'elapsed' => $this->getElapsedTimeMinutes() . ' min',
                'remaining' => $this->getRemainingTimeMinutes() !== null ? 
                    $this->getRemainingTimeMinutes() . ' min' : 'N/A',
                'estimated' => $this->estimated_prep_time_minutes ? 
                    $this->estimated_prep_time_minutes . ' min' : 'N/A',
                'actual' => $this->actual_prep_time_minutes ? 
                    $this->actual_prep_time_minutes . ' min' : 'N/A',
            ],

            // Timestamps formatted for display
            'formatted_timestamps' => [
                'created_at' => $this->created_at?->format('M d, Y H:i'),
                'started_at' => $this->started_at?->format('M d, Y H:i'),
                'completed_at' => $this->completed_at?->format('M d, Y H:i'),
                'sent_to_kitchen_at' => $this->sent_to_kitchen_at?->format('M d, Y H:i'),
            ],
        ];
    }

    /**
     * Get status badge CSS class.
     */
    private function getStatusBadgeClass(): string
    {
        $classes = [
            'pending' => 'badge-warning',
            'preparing' => 'badge-info',
            'ready' => 'badge-success',
            'completed' => 'badge-primary',
            'cancelled' => 'badge-danger',
        ];

        return $classes[$this->status] ?? 'badge-secondary';
    }

    /**
     * Get priority badge CSS class.
     */
    private function getPriorityBadgeClass(): string
    {
        $classes = [
            'low' => 'badge-secondary',
            'normal' => 'badge-primary',
            'high' => 'badge-warning',
            'urgent' => 'badge-danger',
        ];

        return $classes[$this->priority] ?? 'badge-secondary';
    }
}
