<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Branch;
use App\Models\Customer;
use App\Models\Table;
use App\Models\MenuItem;
use App\Models\User;
use Modules\Orders\Helpers\OrderHelper;
use Modules\Kitchen\Services\KitchenService;
use Modules\Kitchen\Models\Kitchen;
use Modules\Kitchen\Models\KitchenMenuItem;
use Modules\Kitchen\Models\KotOrder;
use Modules\Kitchen\Models\KotOrderItem;

class KotWorkflowDemoSeeder extends Seeder
{
    protected $kitchenService;

    public function __construct()
    {
        $this->kitchenService = new KitchenService();
    }

    public function run()
    {
        $this->command->info('🍽️  Starting KOT Workflow Demonstration...');
        $this->command->info('');

        // Step 1: Verify prerequisites
        if (!$this->verifyPrerequisites()) {
            return;
        }

        // Step 2: Create sample orders with different scenarios
        $this->createKotDemonstrationOrders();

        // Step 3: Demonstrate order status changes and KOT creation
        $this->demonstrateOrderStatusChanges();

        // Step 4: Show KOT management operations
        $this->demonstrateKotManagement();

        $this->command->info('');
        $this->command->info('✅ KOT Workflow Demonstration completed successfully!');
        $this->command->info('');
        $this->showSummary();
    }

    private function verifyPrerequisites(): bool
    {
        $this->command->info('🔍 Verifying prerequisites...');

        $branches = Branch::count();
        $kitchens = Kitchen::count();
        $menuItems = MenuItem::where('is_active', true)->count();
        $kitchenMenuItems = KitchenMenuItem::where('is_active', true)->count();
        $customers = Customer::count();
        $users = User::where('is_active', true)->count();

        $this->command->info("   - Branches: {$branches}");
        $this->command->info("   - Kitchens: {$kitchens}");
        $this->command->info("   - Menu Items: {$menuItems}");
        $this->command->info("   - Kitchen-Menu Assignments: {$kitchenMenuItems}");
        $this->command->info("   - Customers: {$customers}");
        $this->command->info("   - Users: {$users}");

        if ($branches === 0 || $kitchens === 0 || $menuItems === 0 || $kitchenMenuItems === 0) {
            $this->command->error('❌ Missing prerequisites. Please run the following seeders first:');
            $this->command->error('   - BranchSeeder');
            $this->command->error('   - MenuItemSeeder');
            $this->command->error('   - KitchenModuleSeeder');
            $this->command->error('   - CustomerSeeder');
            $this->command->error('   - UserSeeder');
            return false;
        }

        $this->command->info('✅ All prerequisites met!');
        $this->command->info('');
        return true;
    }

    private function createKotDemonstrationOrders()
    {
        $this->command->info('📝 Creating demonstration orders...');

        $branch = Branch::first();
        $customer = Customer::first();
        $table = Table::where('branch_id', $branch->id)->first();
        $cashier = User::where('branch_id', $branch->id)->first() ?? User::first();

        // Get menu items assigned to different kitchens
        $kitchenMenuItems = KitchenMenuItem::with(['kitchen', 'menuItem'])
            ->where('is_active', true)
            ->get()
            ->groupBy('kitchen_id');

        $scenarios = [
            [
                'name' => 'Multi-Kitchen Order',
                'description' => 'Order with items from multiple kitchens',
                'status' => 'confirmed',
                'type' => 'dine_in',
                'kitchen_count' => 3,
            ],
            [
                'name' => 'Single Kitchen Order',
                'description' => 'Order with items from one kitchen only',
                'status' => 'confirmed',
                'type' => 'takeaway',
                'kitchen_count' => 1,
            ],
            [
                'name' => 'Pending Order',
                'description' => 'Order that will be confirmed later',
                'status' => 'pending',
                'type' => 'dine_in',
                'kitchen_count' => 2,
            ],
        ];

        foreach ($scenarios as $index => $scenario) {
            $this->command->info("   Creating: {$scenario['name']}");

            // Create order
            $order = Order::create([
                'branch_id' => $branch->id,
                'customer_id' => $customer->id,
                'table_id' => $scenario['type'] === 'dine_in' ? $table?->id : null,
                'order_number' => OrderHelper::generateOrderNumber(),
                'status' => $scenario['status'],
                'order_type' => $scenario['type'],
                'pax' => $scenario['type'] === 'dine_in' ? rand(2, 6) : null,
                'subtotal' => 0,
                'tax_amount' => 0,
                'total_amount' => 0,
                'notes' => "Demo: {$scenario['description']}",
                'cashier_id' => $cashier->id,
                'created_at' => now()->subMinutes(rand(5, 30)),
            ]);

            // Add items from specified number of kitchens
            $selectedKitchens = $kitchenMenuItems->keys()->take($scenario['kitchen_count']);
            $subtotal = 0;

            foreach ($selectedKitchens as $kitchenId) {
                $kitchenItems = $kitchenMenuItems[$kitchenId];
                $selectedItem = $kitchenItems->random();

                $quantity = rand(1, 3);
                $unitPrice = $selectedItem->menuItem->price ?? rand(15, 45);
                $totalPrice = $quantity * $unitPrice;

                OrderItem::create([
                    'order_id' => $order->id,
                    'menu_item_id' => $selectedItem->menu_item_id,
                    'menu_item_name' => $selectedItem->menuItem->name,
                    'quantity' => $quantity,
                    'unit_price' => $unitPrice,
                    'total_price' => $totalPrice,
                    'special_instructions' => rand(1, 3) === 1 ? 'Special preparation needed' : null,
                ]);

                $subtotal += $totalPrice;
            }

            // Update totals
            $taxAmount = $subtotal * 0.1;
            $order->update([
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'total_amount' => $subtotal + $taxAmount,
            ]);

            // Create KOT if order is confirmed
            if ($scenario['status'] === 'confirmed') {
                $this->createKotForOrder($order);
            }

            $this->command->info("     ✅ Order {$order->order_number} created");
        }

        $this->command->info('');
    }

    private function demonstrateOrderStatusChanges()
    {
        $this->command->info('🔄 Demonstrating order status changes and KOT creation...');

        // Find a pending order to confirm
        $pendingOrder = Order::where('status', 'pending')->first();

        if ($pendingOrder) {
            $this->command->info("   📋 Found pending order: {$pendingOrder->order_number}");
            $this->command->info("   🔄 Changing status from 'pending' to 'confirmed'...");

            // Update status to confirmed (this should trigger KOT creation)
            $pendingOrder->update(['status' => 'confirmed']);
            $pendingOrder->load(['orderItems.menuItem']);

            // Manually create KOT to demonstrate the logic
            if (!$pendingOrder->hasActiveKot()) {
                $this->createKotForOrder($pendingOrder);
            } else {
                $this->command->info("     ⚠️  Order already has active KOT");
            }
        }

        $this->command->info('');
    }

    private function demonstrateKotManagement()
    {
        $this->command->info('👨‍🍳 Demonstrating KOT management operations...');

        $kotOrders = KotOrder::with(['kitchen', 'order', 'kotOrderItems'])
            ->where('status', 'pending')
            ->take(2)
            ->get();

        foreach ($kotOrders as $kotOrder) {
            $this->command->info("   🎫 KOT #{$kotOrder->kot_number} - {$kotOrder->kitchen->name}");
            $this->command->info("      Status: {$kotOrder->status}");
            $this->command->info("      Items: {$kotOrder->kotOrderItems->count()}");
            $this->command->info("      Estimated prep time: {$kotOrder->estimated_prep_time_minutes} minutes");

            // Simulate KOT status progression
            if (rand(1, 2) === 1) {
                $kotOrder->update(['status' => 'preparing', 'started_at' => now()]);
                $this->command->info("      🔄 Status updated to 'preparing'");

                if (rand(1, 2) === 1) {
                    $kotOrder->update(['status' => 'ready', 'completed_at' => now()]);
                    $this->command->info("      ✅ Status updated to 'ready'");
                }
            }
        }

        $this->command->info('');
    }

    private function createKotForOrder($order)
    {
        try {
            $order->load(['orderItems.menuItem']);

            if (!$order->hasActiveKot()) {
                $kotOrders = $this->kitchenService->createKotFromOrder($order);
                
                $this->command->info("     🎫 Created " . count($kotOrders) . " KOT(s) for order {$order->order_number}:");
                
                foreach ($kotOrders as $kotOrder) {
                    $itemCount = $kotOrder->kotOrderItems()->count();
                    $this->command->info("        - KOT #{$kotOrder->kot_number} → {$kotOrder->kitchen->name} ({$itemCount} items)");
                }
            } else {
                $this->command->info("     ⚠️  Order {$order->order_number} already has active KOT");
            }
        } catch (\Exception $e) {
            $this->command->error("     ❌ Failed to create KOT for order {$order->order_number}: " . $e->getMessage());
        }
    }

    private function showSummary()
    {
        $this->command->info('📊 KOT Workflow Summary:');
        $this->command->info('');

        // Count orders by status
        $orderStats = Order::selectRaw('status, count(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        $this->command->info('📋 Orders by Status:');
        foreach ($orderStats as $status => $count) {
            $this->command->info("   - {$status}: {$count}");
        }

        // Count KOTs by status
        $kotStats = KotOrder::selectRaw('status, count(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        $this->command->info('');
        $this->command->info('🎫 KOTs by Status:');
        foreach ($kotStats as $status => $count) {
            $this->command->info("   - {$status}: {$count}");
        }

        // Show KOT distribution by kitchen
        $kitchenStats = KotOrder::with('kitchen')
            ->get()
            ->groupBy('kitchen.name')
            ->map(function ($kots) {
                return $kots->count();
            });

        $this->command->info('');
        $this->command->info('👨‍🍳 KOTs by Kitchen:');
        foreach ($kitchenStats as $kitchenName => $count) {
            $this->command->info("   - {$kitchenName}: {$count}");
        }

        $this->command->info('');
        $this->command->info('🔍 Key Logic Demonstrated:');
        $this->command->info('   ✅ Orders with status "confirmed" automatically create KOTs');
        $this->command->info('   ✅ KOTs are grouped by kitchen based on menu item assignments');
        $this->command->info('   ✅ Each kitchen gets a separate KOT for their items');
        $this->command->info('   ✅ Duplicate KOT creation is prevented by hasActiveKot() check');
        $this->command->info('   ✅ KOT status can be managed independently (pending → preparing → ready)');
    }
}