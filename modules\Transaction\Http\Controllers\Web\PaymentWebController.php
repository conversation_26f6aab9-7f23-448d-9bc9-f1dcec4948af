<?php

namespace Modules\Transaction\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use Modules\Transaction\Models\Payment;
use Modules\Transaction\Services\PaymentService;
use Modules\Transaction\Services\TransactionService;
use Modules\Transaction\Http\Requests\StorePaymentRequest;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Yajra\DataTables\Facades\DataTables;

class PaymentWebController extends Controller
{
    protected PaymentService $paymentService;
    protected TransactionService $transactionService;

    public function __construct(PaymentService $paymentService, TransactionService $transactionService)
    {
        $this->paymentService = $paymentService;
        $this->transactionService = $transactionService;
    }

    /**
     * Display a listing of payments.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->getDataTablesData($request);
        }

        $paymentMethods = $this->paymentService->getPaymentMethods();
        $dueTransactions = $this->transactionService->getDueTransactions(['limit' => 50]);

        return view('transaction::payments.index', compact('paymentMethods', 'dueTransactions'));
    }

    /**
     * Get DataTables data for payments.
     */
    private function getDataTablesData(Request $request)
    {
        $user = auth()->user();
        
        // Check if user has tenant_id
        if (!$user || !$user->tenant_id) {
            return response()->json([
                'error' => 'User must be associated with a tenant to view payments.'
            ], 403);
        }

        $query = Payment::with(['transaction.order', 'paymentMethod', 'processedBy'])
            ->where('tenant_id', $user->tenant_id);

        return DataTables::of($query)
            ->addColumn('id', function ($payment) {
                return $payment->id;
            })
            ->addColumn('payment_number', function ($payment) {
                return $payment->payment_number;
            })
            ->addColumn('transaction_number', function ($payment) {
                return $payment->transaction ? $payment->transaction->transaction_number : 'N/A';
            })
            ->addColumn('order_number', function ($payment) {
                return $payment->transaction && $payment->transaction->order 
                    ? $payment->transaction->order->order_number 
                    : 'N/A';
            })
            ->addColumn('payment_method', function ($payment) {
                return $payment->paymentMethod ? $payment->paymentMethod->name : 'Unknown';
            })
            ->addColumn('payment_method_badge', function ($payment) {
                $method = $payment->paymentMethod;
                $methodName = $method ? $method->name : 'Unknown';
                $methodCode = $method ? $method->code : 'unknown';
                
                return '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">' 
                    . $methodName . '</span>';
            })
            ->addColumn('status', function ($payment) {
                return $payment->status;
            })
            ->addColumn('status_badge', function ($payment) {
                $statusColors = [
                    'completed' => 'bg-green-100 text-green-800',
                    'pending' => 'bg-yellow-100 text-yellow-800',
                    'failed' => 'bg-red-100 text-red-800',
                    'cancelled' => 'bg-gray-100 text-gray-800',
                    'refunded' => 'bg-purple-100 text-purple-800',
                ];
                
                $colorClass = $statusColors[$payment->status] ?? 'bg-gray-100 text-gray-800';
                
                return '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ' 
                    . $colorClass . '">' . ucfirst(str_replace('_', ' ', $payment->status)) . '</span>';
            })
            ->addColumn('amount', function ($payment) {
                return $payment->amount;
            })
            ->addColumn('formatted_amount', function ($payment) {
                return '$' . number_format($payment->amount, 2);
            })
            ->addColumn('reference_number', function ($payment) {
                return $payment->reference_number ?: '-';
            })
            ->addColumn('formatted_date', function ($payment) {
                return $payment->payment_date ? $payment->payment_date->format('M d, Y H:i') : '-';
            })
            ->addColumn('processed_by', function ($payment) {
                return $payment->processedBy ? $payment->processedBy->name : 'System';
            })
            ->addColumn('action', function ($payment) {
                $actions = '<div class="flex space-x-2">';
                
                // View button
                $actions .= '<button type="button" class="view-payment-btn inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" data-id="' . $payment->id . '">';
                $actions .= '<i class="fas fa-eye mr-1"></i> View';
                $actions .= '</button>';
                
                // Cancel button (if can be cancelled)
                if ($payment->canBeCancelled()) {
                    $actions .= '<button type="button" class="cancel-payment-btn inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-yellow-700 bg-yellow-100 hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 ml-2" data-id="' . $payment->id . '">';
                    $actions .= '<i class="fas fa-ban mr-1"></i> Cancel';
                    $actions .= '</button>';
                }
                
                // Refund button (if can be refunded)
                if ($payment->canBeRefunded()) {
                    $actions .= '<button type="button" class="refund-payment-btn inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 ml-2" data-id="' . $payment->id . '">';
                    $actions .= '<i class="fas fa-undo mr-1"></i> Refund';
                    $actions .= '</button>';
                }
                
                $actions .= '</div>';
                
                return $actions;
            })
            ->filter(function ($query) use ($request) {
                // Status filter
                if ($request->has('status') && $request->status != '') {
                    $query->where('status', $request->status);
                }
                
                // Payment method filter
                if ($request->has('payment_method') && $request->payment_method != '') {
                    $query->where('payment_method_id', $request->payment_method);
                }
                
                // Date range filter
                if ($request->has('date_from') && $request->date_from != '') {
                    $query->whereDate('payment_date', '>=', $request->date_from);
                }
                
                if ($request->has('date_to') && $request->date_to != '') {
                    $query->whereDate('payment_date', '<=', $request->date_to);
                }
                
                // Amount range filter
                if ($request->has('amount_from') && $request->amount_from != '') {
                    $query->where('amount', '>=', $request->amount_from);
                }
                
                if ($request->has('amount_to') && $request->amount_to != '') {
                    $query->where('amount', '<=', $request->amount_to);
                }
            })
            ->rawColumns(['payment_method_badge', 'status_badge', 'action'])
            ->make(true);
    }

    /**
     * Show the form for creating a new payment.
     */
    public function create(Request $request): View
    {
        $transactionId = $request->get('transaction_id');
        $transaction = null;

        if ($transactionId) {
            $transaction = $this->transactionService->getTransactionById($transactionId);
        }

        $paymentMethods = $this->paymentService->getPaymentMethods();
        $dueTransactions = $this->transactionService->getDueTransactions(['limit' => 50]);

        return view('transaction::payments.create', compact('transaction', 'paymentMethods', 'dueTransactions'));
    }

    /**
     * Store a newly created payment.
     */
    public function store(StorePaymentRequest $request): RedirectResponse
    {
        try {
            $transaction = $this->transactionService->getTransactionById($request->transaction_id);

            if (!$transaction) {
                return redirect()
                    ->back()
                    ->withInput()
                    ->with('error', 'Transaction not found.');
            }

            $payment = $this->paymentService->processPayment($transaction, $request->validated());

            return redirect()
                ->route('payments.show', $payment->id)
                ->with('success', 'Payment processed successfully.');
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Failed to process payment: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified payment.
     */
    public function show(int $id): View
    {
        $payment = $this->paymentService->getPaymentById($id);

        if (!$payment) {
            abort(404, 'Payment not found');
        }

        return view('transaction::payments.show', compact('payment'));
    }

    /**
     * Cancel the specified payment.
     */
    public function cancel(int $id, Request $request): RedirectResponse
    {
        try {
            $payment = $this->paymentService->getPaymentById($id);

            if (!$payment) {
                return redirect()
                    ->route('payments.index')
                    ->with('error', 'Payment not found.');
            }

            $reason = $request->input('reason', 'Payment cancelled by user');
            $this->paymentService->cancelPayment($payment, $reason);

            return redirect()
                ->route('payments.show', $payment->id)
                ->with('success', 'Payment cancelled successfully.');
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', 'Failed to cancel payment: ' . $e->getMessage());
        }
    }

    /**
     * Refund the specified payment.
     */
    public function refund(int $id, Request $request): RedirectResponse
    {
        try {
            $payment = $this->paymentService->getPaymentById($id);

            if (!$payment) {
                return redirect()
                    ->route('payments.index')
                    ->with('error', 'Payment not found.');
            }

            $refundAmount = $request->input('refund_amount');
            $reason = $request->input('reason', 'Payment refunded by user');
            
            $this->paymentService->refundPayment($payment, $refundAmount, $reason);

            return redirect()
                ->route('payments.show', $payment->id)
                ->with('success', 'Payment refunded successfully.');
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', 'Failed to refund payment: ' . $e->getMessage());
        }
    }

    /**
     * Show payment statistics page.
     */
    public function statistics(): View
    {
        return view('transaction::payments.statistics');
    }

    /**
     * Show payment methods page.
     */
    public function methods(): View
    {
        return view('transaction::payments.methods');
    }

    /**
     * Show refunds page.
     */
    public function refunds(): View
    {
        return view('transaction::payments.refunds');
    }

    /**
     * Show payments for a specific transaction.
     */
    public function byTransaction(int $transactionId): View
    {
        $transaction = $this->transactionService->getTransactionById($transactionId);

        if (!$transaction) {
            abort(404, 'Transaction not found');
        }

        return view('transaction::payments.by-transaction', compact('transaction'));
    }
}