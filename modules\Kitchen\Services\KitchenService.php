<?php

namespace Modules\Kitchen\Services;

use App\Models\Order;
use App\Models\MenuItem;
use Modules\Kitchen\Models\Kitchen;
use Modules\Kitchen\Models\KotOrder;
use Modules\Kitchen\Models\KotOrderItem;
use Modules\Kitchen\Models\KitchenMenuItem;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class KitchenService
{
    /**
     * Create KOT from order.
     */
    public function createKotFromOrder(Order $order): array
    {
        try {
            DB::beginTransaction();

            $kotOrders = [];

            // Group order items by kitchen
            $itemsByKitchen = $this->groupOrderItemsByKitchen($order);

            foreach ($itemsByKitchen as $kitchenId => $items) {
                $kitchen = Kitchen::find($kitchenId);

                if (!$kitchen || !$kitchen->canAcceptOrders()) {
                    continue;
                }

                // Create KOT order
                $kotOrder = KotOrder::create([
                    'tenant_id' => $order->tenant_id ?? null,
                    'branch_id' => $order->branch_id,
                    'kitchen_id' => $kitchenId,
                    'order_id' => $order->id,
                    'kot_number' => KotOrder::generateKotNumber(),
                    'status' => 'pending',
                    'priority' => $this->determinePriority($order),
                    'estimated_prep_time_minutes' => $this->calculateEstimatedPrepTime($items),
                    'items_data' => $this->prepareItemsData($items),
                    'special_instructions' => $order->special_instructions,
                    'sent_to_kitchen_at' => now(),
                    'created_by' => auth()->id(),
                ]);

                // Create KOT order items
                foreach ($items as $orderItem) {
                    KotOrderItem::create([
                        'kot_order_id' => $kotOrder->id,
                        'order_item_id' => $orderItem->id,
                        'menu_item_id' => $orderItem->menu_item_id,
                        'quantity' => $orderItem->quantity,
                        'status' => 'pending',
                        'special_instructions' => $orderItem->special_instructions,
                        'modifications' => $orderItem->modifications ?? [],
                        'prep_time_minutes' => $this->getMenuItemPrepTime($orderItem->menu_item_id, $kitchenId),
                    ]);
                }

                $kotOrders[] = $kotOrder;
            }

            DB::commit();

            Log::info('KOT orders created successfully', [
                'order_id' => $order->id,
                'kot_count' => count($kotOrders)
            ]);

            return $kotOrders;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create KOT from order', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Get all kitchens.
     */
    public function getAllKitchens(): Collection
    {
        return Kitchen::with(['branch', 'manager'])
            ->active()
            ->ordered()
            ->get();
    }

    /**
     * Get kitchens by branch.
     */
    public function getKitchensByBranch(int $branchId): Collection
    {
        return Kitchen::where('branch_id', $branchId)
            ->active()
            ->ordered()
            ->get();
    }

    /**
     * Get kitchen by ID with relationships.
     */
    public function getKitchenById(int $id): ?Kitchen
    {
        return Kitchen::with(['branch', 'manager', 'menuItems', 'kotOrders'])
            ->find($id);
    }

    /**
     * Create a new kitchen.
     */
    public function createKitchen(array $data): Kitchen
    {
        $data['created_by'] = auth()->id();
        $data['code'] = $data['code'] ?? $this->generateKitchenCode($data['name']);

        return Kitchen::create($data);
    }

    /**
     * Update kitchen.
     */
    public function updateKitchen(Kitchen $kitchen, array $data): Kitchen
    {
        $kitchen->update($data);
        return $kitchen->fresh();
    }

    /**
     * Delete kitchen.
     */
    public function deleteKitchen(Kitchen $kitchen): bool
    {
        // Check if kitchen has active KOTs
        $activeKots = $kitchen->kotOrders()
            ->whereIn('status', ['pending', 'preparing'])
            ->count();

        if ($activeKots > 0) {
            throw new \Exception('Cannot delete kitchen with active KOT orders');
        }

        return $kitchen->delete();
    }

    /**
     * Assign menu item to kitchen.
     */
    public function assignMenuItem(int $kitchenId, int $menuItemId, array $data = []): KitchenMenuItem
    {
        return KitchenMenuItem::updateOrCreate(
            [
                'kitchen_id' => $kitchenId,
                'menu_item_id' => $menuItemId,
            ],
            array_merge($data, [
                'tenant_id' => auth()->user()->tenant_id ?? null,
                'assigned_by' => auth()->id(),
                'assigned_at' => now(),
                'is_active' => true,
            ])
        );
    }

    /**
     * Remove menu item from kitchen.
     */
    public function removeMenuItem(int $kitchenId, int $menuItemId): bool
    {
        return KitchenMenuItem::where('kitchen_id', $kitchenId)
            ->where('menu_item_id', $menuItemId)
            ->delete();
    }

    /**
     * Group order items by kitchen.
     */
    private function groupOrderItemsByKitchen(Order $order): array
    {
        $itemsByKitchen = [];
        $unassignedItems = [];

        foreach ($order->orderItems as $orderItem) {
            // Find which kitchen this menu item is assigned to
            $kitchenMenuItem = KitchenMenuItem::where('menu_item_id', $orderItem->menu_item_id)
                ->where('is_active', true)
                ->first();

            if ($kitchenMenuItem) {
                // Check if the assigned kitchen is active
                $kitchen = Kitchen::find($kitchenMenuItem->kitchen_id);
                if ($kitchen && $kitchen->is_active) {
                    $kitchenId = $kitchenMenuItem->kitchen_id;
                    if (!isset($itemsByKitchen[$kitchenId])) {
                        $itemsByKitchen[$kitchenId] = [];
                    }
                    $itemsByKitchen[$kitchenId][] = $orderItem;
                } else {
                    // Kitchen is not active, add to unassigned items
                    $unassignedItems[] = $orderItem;
                }
            } else {
                // No kitchen assignment found, add to unassigned items
                $unassignedItems[] = $orderItem;
            }
        }

        // If there are unassigned items, assign them to the default kitchen
        if (!empty($unassignedItems)) {
            $defaultKitchen = $order->branch->defaultKitchen;
            
            if ($defaultKitchen && $defaultKitchen->is_active) {
                $defaultKitchenId = $defaultKitchen->id;
                if (!isset($itemsByKitchen[$defaultKitchenId])) {
                    $itemsByKitchen[$defaultKitchenId] = [];
                }
                $itemsByKitchen[$defaultKitchenId] = array_merge(
                    $itemsByKitchen[$defaultKitchenId] ?? [],
                    $unassignedItems
                );
            } else {
                // If no default kitchen or it's not active, try to find any active kitchen in the branch
                $fallbackKitchen = Kitchen::where('branch_id', $order->branch_id)
                    ->where('is_active', true)
                    ->first();
                
                if ($fallbackKitchen) {
                    $fallbackKitchenId = $fallbackKitchen->id;
                    if (!isset($itemsByKitchen[$fallbackKitchenId])) {
                        $itemsByKitchen[$fallbackKitchenId] = [];
                    }
                    $itemsByKitchen[$fallbackKitchenId] = array_merge(
                        $itemsByKitchen[$fallbackKitchenId] ?? [],
                        $unassignedItems
                    );
                }
            }
        }

        return $itemsByKitchen;
    }

    /**
     * Determine priority based on order.
     */
    private function determinePriority(Order $order): string
    {
        // You can implement custom logic here
        // For now, return normal priority
        return 'normal';
    }

    /**
     * Calculate estimated prep time for items.
     */
    private function calculateEstimatedPrepTime(array $items): int
    {
        $totalTime = 0;

        foreach ($items as $orderItem) {
            $kitchenMenuItem = KitchenMenuItem::where('menu_item_id', $orderItem->menu_item_id)
                ->where('is_active', true)
                ->first();

            if ($kitchenMenuItem && $kitchenMenuItem->prep_time_minutes) {
                $totalTime += $kitchenMenuItem->prep_time_minutes * $orderItem->quantity;
            }
        }

        return max($totalTime, 5); // Minimum 5 minutes
    }

    /**
     * Prepare items data for storage.
     */
    private function prepareItemsData(array $items): array
    {
        $itemsData = [];

        foreach ($items as $orderItem) {
            $itemsData[] = [
                'order_item_id' => $orderItem->id,
                'menu_item_id' => $orderItem->menu_item_id,
                'menu_item_name' => $orderItem->menuItem->name ?? 'Unknown Item',
                'quantity' => $orderItem->quantity,
                'unit_price' => $orderItem->unit_price,
                'special_instructions' => $orderItem->special_instructions,
                'modifications' => $orderItem->modifications ?? [],
            ];
        }

        return $itemsData;
    }

    /**
     * Get menu item prep time for specific kitchen.
     */
    private function getMenuItemPrepTime(int $menuItemId, int $kitchenId): ?int
    {
        $kitchenMenuItem = KitchenMenuItem::where('menu_item_id', $menuItemId)
            ->where('kitchen_id', $kitchenId)
            ->where('is_active', true)
            ->first();

        return $kitchenMenuItem?->prep_time_minutes;
    }

    /**
     * Generate kitchen code from name.
     */
    private function generateKitchenCode(string $name): string
    {
        $code = strtoupper(substr(preg_replace('/[^A-Za-z0-9]/', '', $name), 0, 6));

        // Ensure uniqueness
        $counter = 1;
        $originalCode = $code;

        while (Kitchen::where('code', $code)->exists()) {
            $code = $originalCode . $counter;
            $counter++;
        }

        return $code;
    }
}
