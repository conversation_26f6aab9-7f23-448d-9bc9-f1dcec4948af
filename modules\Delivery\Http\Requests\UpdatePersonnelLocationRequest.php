<?php

namespace Modules\Delivery\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class UpdatePersonnelLocationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'accuracy' => 'nullable|numeric|min:0',
            'speed' => 'nullable|numeric|min:0',
            'heading' => 'nullable|numeric|between:0,360',
            'altitude' => 'nullable|numeric',
            'battery_level' => 'nullable|integer|between:0,100',
            'is_manual' => 'nullable|boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'latitude.required' => 'Latitude is required',
            'latitude.between' => 'Latitude must be between -90 and 90',
            'longitude.required' => 'Longitude is required',
            'longitude.between' => 'Longitude must be between -180 and 180',
            'accuracy.min' => 'Accuracy cannot be negative',
            'speed.min' => 'Speed cannot be negative',
            'heading.between' => 'Heading must be between 0 and 360 degrees',
            'battery_level.between' => 'Battery level must be between 0 and 100',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default values
        if (!$this->has('is_manual')) {
            $this->merge(['is_manual' => false]);
        }

        // Round coordinates to reasonable precision (6 decimal places)
        if ($this->has('latitude')) {
            $this->merge(['latitude' => round($this->latitude, 6)]);
        }

        if ($this->has('longitude')) {
            $this->merge(['longitude' => round($this->longitude, 6)]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $personnelId = $this->route('id');
            
            // Check if personnel exists and belongs to user
            if ($personnelId) {
                $personnel = \Modules\Delivery\Models\DeliveryPersonnel::find($personnelId);
                
                if (!$personnel) {
                    $validator->errors()->add('personnel_id', 'Personnel not found');
                    return;
                }

                $user = Auth::user();
                
                // Check if user is authorized to update this personnel's location
                if ($personnel->user_id !== $user->id && !$user->hasRole(['admin', 'manager'])) {
                    $validator->errors()->add('personnel_id', 'You are not authorized to update location for this personnel');
                }

                // Check if personnel belongs to the same tenant
                if ($personnel->user && $personnel->user->tenant_id !== $user->tenant_id) {
                    $validator->errors()->add('personnel_id', 'Personnel does not belong to your organization');
                }

                // Check if personnel is active
                if ($personnel->status === 'inactive') {
                    $validator->errors()->add('personnel_id', 'Cannot update location for inactive personnel');
                }
            }

            // Validate coordinates are reasonable
            if ($this->has('latitude') && $this->has('longitude')) {
                $lat = $this->latitude;
                $lng = $this->longitude;

                // Basic sanity check - coordinates should not be 0,0
                if ($lat == 0 && $lng == 0) {
                    $validator->errors()->add('latitude', 'Invalid coordinates detected');
                }

                // Check for obviously invalid coordinates
                if (abs($lat) < 0.001 && abs($lng) < 0.001) {
                    $validator->errors()->add('latitude', 'Coordinates appear to be invalid or too precise');
                }
            }

            // Validate speed is reasonable
            if ($this->has('speed') && $this->speed > 200) {
                $validator->errors()->add('speed', 'Speed seems unreasonably high for delivery');
            }
        });
    }
}