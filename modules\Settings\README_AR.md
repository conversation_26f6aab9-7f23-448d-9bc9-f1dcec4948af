# وحدة الإعدادات - Settings Module

وحدة الإعدادات هي نظام شامل لإدارة جميع إعدادات نظام نقاط البيع للمطاعم. توفر هذه الوحدة واجهة موحدة لتخصيص وإدارة جميع جوانب النظام.

## ⚙️ الميزات الرئيسية

### 🔧 الوظائف الأساسية
- **إدارة الإعدادات العامة**: إعدادات النظام الأساسية
- **إعدادات الفروع**: تخصيص إعدادات كل فرع
- **إعدادات الأمان**: حماية وتشفير البيانات
- **إعدادات الطابعات**: تكوين طابعات الإيصالات والمطبخ
- **إعدادات الدفع**: طرق الدفع المختلفة
- **إعدادات المطعم**: إعدادات خاصة بالمطعم
- **إعدادات التقارير**: تخصيص التقارير والإشعارات

## 🏗️ هيكل الوحدة

### المتحكمات (Controllers)
```php
// المتحكمات الرئيسية
Controllers/
├── SettingController.php              // المتحكم الرئيسي
├── BranchSettingController.php        // إعدادات الفروع
├── SecuritySettingController.php      // إعدادات الأمان
├── PrinterSettingController.php       // إعدادات الطابعات
├── PaymentSettingController.php       // إعدادات الدفع
└── Web/
    └── SettingsWebController.php      // واجهة الويب
```

### الخدمات (Services)
```php
// خدمات الإعدادات
Services/
├── SettingService.php                 // الخدمة الرئيسية
└── SettingHelper.php                  // مساعدات الإعدادات
```

## 📋 أنواع الإعدادات

### 1. الإعدادات العامة (General Settings)
```php
// مثال على الإعدادات العامة
$generalSettings = [
    'app_name' => 'اسم التطبيق',
    'app_logo' => 'شعار التطبيق',
    'timezone' => 'المنطقة الزمنية',
    'language' => 'اللغة الافتراضية',
    'currency' => 'العملة',
    'date_format' => 'تنسيق التاريخ'
];
```

**الميزات:**
- تخصيص اسم وشعار التطبيق
- إعداد المنطقة الزمنية واللغة
- تحديد العملة وتنسيق التاريخ
- إعدادات الواجهة العامة

### 2. إعدادات الأعمال (Business Settings)
```php
// إعدادات الأعمال
$businessSettings = [
    'business_name' => 'اسم المطعم',
    'business_address' => 'عنوان المطعم',
    'business_phone' => 'رقم الهاتف',
    'business_email' => 'البريد الإلكتروني',
    'tax_number' => 'الرقم الضريبي'
];
```

### 3. إعدادات نقطة البيع (POS Settings)
```php
// إعدادات نقطة البيع
$posSettings = [
    'default_tax_rate' => 'معدل الضريبة الافتراضي',
    'auto_print_receipt' => 'طباعة الإيصال تلقائياً',
    'sound_enabled' => 'تفعيل الأصوات',
    'order_prefix' => 'بادئة رقم الطلب'
];
```

### 4. إعدادات الطابعات (Printer Settings)
```php
// إعدادات الطابعات
$printerSettings = [
    'receipt_printer' => [
        'name' => 'طابعة الإيصالات',
        'ip_address' => '*************',
        'port' => 9100,
        'paper_size' => '80mm',
        'auto_cut' => true
    ],
    'kitchen_printer' => [
        'name' => 'طابعة المطبخ',
        'ip_address' => '*************',
        'port' => 9100,
        'paper_size' => '80mm',
        'auto_cut' => false
    ]
];
```

### 5. إعدادات الدفع (Payment Settings)
```php
// إعدادات طرق الدفع
$paymentSettings = [
    'cash' => [
        'enabled' => true,
        'name' => 'نقداً',
        'icon' => 'fas fa-money-bill'
    ],
    'credit_card' => [
        'enabled' => true,
        'name' => 'بطاقة ائتمان',
        'gateway' => 'stripe',
        'merchant_id' => 'MERCHANT_123'
    ],
    'digital_wallet' => [
        'enabled' => true,
        'name' => 'محفظة رقمية',
        'providers' => ['paypal', 'apple_pay', 'google_pay']
    ]
];
```

### 6. إعدادات الأمان (Security Settings)
```php
// إعدادات الأمان
$securitySettings = [
    'password_policy' => [
        'min_length' => 8,
        'require_uppercase' => true,
        'require_numbers' => true,
        'require_symbols' => false
    ],
    'session_timeout' => 30, // دقيقة
    'max_login_attempts' => 5,
    'two_factor_auth' => false,
    'ip_whitelist' => ['***********/24']
];
```

## 🛠️ الاستخدام والأمثلة

### إدارة الإعدادات الأساسية
```php
use Modules\Settings\Services\SettingService;

$settingService = app(SettingService::class);

// الحصول على إعداد
$appName = $settingService->get('general', 'app_name', 'إبيسيس');

// تحديث إعداد
$settingService->set('general', 'app_name', 'مطعم الأصالة');

// حفظ عدة إعدادات
$settingService->setMultiple('business', [
    'business_name' => 'مطعم الأصالة',
    'business_phone' => '+970591234567',
    'business_email' => '<EMAIL>'
]);
```

### إعدادات الفروع
```php
// إعدادات خاصة بفرع معين
$branchSettings = $settingService->getBranchSettings($branchId);

// تحديث إعدادات الفرع
$settingService->setBranchSetting($branchId, 'operating_hours', [
    'open_time' => '08:00',
    'close_time' => '23:00',
    'days' => ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday']
]);
```

### إعدادات الطابعات
```php
use Modules\Settings\Http\Controllers\PrinterSettingController;

// إضافة طابعة جديدة
$printerData = [
    'name' => 'طابعة الإيصالات الرئيسية',
    'type' => 'receipt',
    'connection_type' => 'network',
    'ip_address' => '*************',
    'port' => 9100,
    'paper_size' => '80mm',
    'auto_cut' => true,
    'is_enabled' => true
];

$printer = $printerController->store($printerData);
```

### إعدادات الدفع
```php
// تفعيل طريقة دفع جديدة
$paymentMethod = [
    'name' => 'فيزا',
    'type' => 'credit_card',
    'gateway' => 'stripe',
    'is_enabled' => true,
    'settings' => [
        'public_key' => 'pk_test_...',
        'secret_key' => 'sk_test_...',
        'webhook_secret' => 'whsec_...'
    ]
];

$settingService->setPaymentMethod($paymentMethod);
```

## 🔌 واجهات برمجة التطبيقات (API Endpoints)

### الإعدادات العامة
```http
GET /api/settings/general                    # الحصول على الإعدادات العامة
POST /api/settings/general                   # تحديث الإعدادات العامة
DELETE /api/settings/general/{key}           # حذف إعداد محدد
```

### إعدادات الفروع
```http
GET /api/settings/branch/{branchId}          # إعدادات فرع محدد
POST /api/settings/branch/{branchId}         # تحديث إعدادات الفرع
```

### إعدادات الطابعات
```http
GET /api/settings/printers                   # قائمة الطابعات
POST /api/settings/printers                  # إضافة طابعة جديدة
PUT /api/settings/printers/{id}              # تحديث طابعة
DELETE /api/settings/printers/{id}           # حذف طابعة
POST /api/settings/printers/{id}/test        # اختبار الطابعة
```

### إعدادات الدفع
```http
GET /api/settings/payment                    # طرق الدفع
POST /api/settings/payment                   # إضافة طريقة دفع
PUT /api/settings/payment/{id}               # تحديث طريقة دفع
DELETE /api/settings/payment/{id}            # حذف طريقة دفع
```

### إعدادات الأمان
```http
GET /api/settings/security                   # إعدادات الأمان
POST /api/settings/security                  # تحديث إعدادات الأمان
```

## 🎯 أمثلة عملية

### مثال 1: إعداد مطعم جديد
```php
// إعداد مطعم جديد بالكامل
$restaurantSetup = [
    // الإعدادات العامة
    'general' => [
        'app_name' => 'مطعم الأصالة',
        'timezone' => 'Asia/Gaza',
        'language' => 'ar',
        'currency' => 'USD'
    ],
    
    // إعدادات الأعمال
    'business' => [
        'business_name' => 'مطعم الأصالة للمأكولات الشعبية',
        'business_address' => 'رام الله، فلسطين',
        'business_phone' => '+970591234567',
        'tax_number' => '*********'
    ],
    
    // إعدادات المطعم
    'restaurant' => [
        'restaurant_type' => 'casual_dining',
        'table_count' => 20,
        'service_charge' => 10,
        'delivery_enabled' => true,
        'takeaway_enabled' => true
    ]
];

foreach ($restaurantSetup as $category => $settings) {
    $settingService->setMultiple($category, $settings);
}
```

### مثال 2: تكوين نظام الطباعة
```php
// إعداد نظام طباعة متكامل
$printingSystem = [
    'receipt_printer' => [
        'name' => 'طابعة الإيصالات',
        'type' => 'receipt',
        'ip_address' => '*************',
        'paper_size' => '80mm',
        'auto_cut' => true,
        'print_logo' => true,
        'print_footer' => true
    ],
    
    'kitchen_printer' => [
        'name' => 'طابعة المطبخ',
        'type' => 'kitchen',
        'ip_address' => '*************',
        'paper_size' => '80mm',
        'auto_cut' => false,
        'print_time' => true,
        'print_table_number' => true
    ],
    
    'bar_printer' => [
        'name' => 'طابعة البار',
        'type' => 'bar',
        'ip_address' => '*************',
        'paper_size' => '80mm',
        'filter_categories' => ['beverages', 'cocktails']
    ]
];

foreach ($printingSystem as $printerConfig) {
    $printerController->store($printerConfig);
}
```

### مثال 3: إعداد نظام الدفع المتعدد
```php
// إعداد طرق دفع متعددة
$paymentMethods = [
    [
        'name' => 'نقداً',
        'type' => 'cash',
        'is_enabled' => true,
        'sort_order' => 1
    ],
    [
        'name' => 'فيزا/ماستركارد',
        'type' => 'credit_card',
        'gateway' => 'stripe',
        'is_enabled' => true,
        'sort_order' => 2,
        'settings' => [
            'public_key' => env('STRIPE_PUBLIC_KEY'),
            'secret_key' => env('STRIPE_SECRET_KEY')
        ]
    ],
    [
        'name' => 'PayPal',
        'type' => 'digital_wallet',
        'gateway' => 'paypal',
        'is_enabled' => true,
        'sort_order' => 3,
        'settings' => [
            'client_id' => env('PAYPAL_CLIENT_ID'),
            'client_secret' => env('PAYPAL_CLIENT_SECRET'),
            'mode' => 'sandbox'
        ]
    ]
];

foreach ($paymentMethods as $method) {
    $settingService->setPaymentMethod($method);
}
```

## 📱 واجهات المستخدم

### واجهة الويب - لوحة الإعدادات
```blade
{{-- resources/views/settings/dashboard.blade.php --}}
@extends('layouts.app')

@section('content')
<div class="settings-dashboard">
    <div class="row">
        <div class="col-md-12">
            <h2>لوحة تحكم الإعدادات</h2>
        </div>
    </div>
    
    <div class="row">
        {{-- الإعدادات العامة --}}
        <div class="col-md-4 mb-3">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-cog me-2"></i>الإعدادات العامة
                    </h5>
                    <p class="card-text">إعدادات النظام الأساسية واللغة والعملة</p>
                    <a href="{{ route('settings.general') }}" class="btn btn-primary">
                        إدارة الإعدادات
                    </a>
                </div>
            </div>
        </div>
        
        {{-- إعدادات الطابعات --}}
        <div class="col-md-4 mb-3">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-print me-2"></i>إعدادات الطابعات
                    </h5>
                    <p class="card-text">تكوين طابعات الإيصالات والمطبخ</p>
                    <a href="{{ route('settings.printers') }}" class="btn btn-primary">
                        إدارة الطابعات
                    </a>
                </div>
            </div>
        </div>
        
        {{-- إعدادات الدفع --}}
        <div class="col-md-4 mb-3">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-credit-card me-2"></i>إعدادات الدفع
                    </h5>
                    <p class="card-text">طرق الدفع والبوابات المالية</p>
                    <a href="{{ route('settings.payment') }}" class="btn btn-primary">
                        إدارة الدفع
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
```

### نموذج إعدادات الطابعة
```blade
{{-- نموذج إضافة طابعة جديدة --}}
<form id="printerForm" method="POST" action="{{ route('settings.printers.store') }}">
    @csrf
    
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label for="printer_name">اسم الطابعة</label>
                <input type="text" class="form-control" id="printer_name" 
                       name="name" required>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="form-group">
                <label for="printer_type">نوع الطابعة</label>
                <select class="form-control" id="printer_type" name="type" required>
                    <option value="receipt">طابعة إيصالات</option>
                    <option value="kitchen">طابعة مطبخ</option>
                    <option value="bar">طابعة بار</option>
                </select>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label for="ip_address">عنوان IP</label>
                <input type="text" class="form-control" id="ip_address" 
                       name="ip_address" placeholder="*************">
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="form-group">
                <label for="port">المنفذ</label>
                <input type="number" class="form-control" id="port" 
                       name="port" value="9100">
            </div>
        </div>
    </div>
    
    <div class="form-group">
        <div class="form-check">
            <input type="checkbox" class="form-check-input" id="auto_cut" 
                   name="auto_cut" value="1">
            <label class="form-check-label" for="auto_cut">
                قطع الورق تلقائياً
            </label>
        </div>
    </div>
    
    <button type="submit" class="btn btn-success">
        <i class="fas fa-save me-2"></i>حفظ الطابعة
    </button>
    
    <button type="button" class="btn btn-info" onclick="testPrinter()">
        <i class="fas fa-print me-2"></i>اختبار الطباعة
    </button>
</form>
```

## 🔒 الأمان والصلاحيات

### نظام الصلاحيات
```php
// صلاحيات إدارة الإعدادات
$settingsPermissions = [
    'settings.view' => 'عرض الإعدادات',
    'settings.edit' => 'تعديل الإعدادات',
    'settings.general' => 'الإعدادات العامة',
    'settings.security' => 'إعدادات الأمان',
    'settings.printers' => 'إعدادات الطابعات',
    'settings.payment' => 'إعدادات الدفع',
    'settings.branch' => 'إعدادات الفروع',
    'settings.system' => 'إعدادات النظام'
];
```

### تشفير الإعدادات الحساسة
```php
// تشفير الإعدادات الحساسة
class EncryptedSetting
{
    public static function set($key, $value)
    {
        $encryptedValue = encrypt($value);
        return Setting::updateOrCreate(
            ['key' => $key],
            ['value' => $encryptedValue, 'is_encrypted' => true]
        );
    }
    
    public static function get($key, $default = null)
    {
        $setting = Setting::where('key', $key)->first();
        
        if (!$setting) {
            return $default;
        }
        
        return $setting->is_encrypted 
            ? decrypt($setting->value) 
            : $setting->value;
    }
}
```

## ⚡ الأداء والتحسين

### التخزين المؤقت للإعدادات
```php
// تخزين الإعدادات في الذاكرة المؤقتة
class CachedSettingService
{
    protected $cache;
    protected $ttl = 3600; // ساعة واحدة
    
    public function get($category, $key, $default = null)
    {
        $cacheKey = "settings.{$category}.{$key}";
        
        return Cache::remember($cacheKey, $this->ttl, function () use ($category, $key, $default) {
            return $this->settingService->get($category, $key, $default);
        });
    }
    
    public function set($category, $key, $value)
    {
        $result = $this->settingService->set($category, $key, $value);
        
        // مسح الذاكرة المؤقتة
        Cache::forget("settings.{$category}.{$key}");
        
        return $result;
    }
}
```

### تحسين قاعدة البيانات
```sql
-- فهارس لتحسين الأداء
CREATE INDEX idx_settings_category_key ON settings (category, key);
CREATE INDEX idx_settings_tenant_category ON settings (tenant_id, category);
CREATE INDEX idx_branch_settings_branch_key ON branch_settings (branch_id, key);
```

## 🔧 استكشاف الأخطاء

### الأخطاء الشائعة وحلولها

1. **خطأ في الاتصال بالطابعة**
```php
// اختبار الاتصال بالطابعة
public function testPrinterConnection($printerId)
{
    try {
        $printer = PrinterSetting::findOrFail($printerId);
        
        $socket = @fsockopen($printer->ip_address, $printer->port, $errno, $errstr, 5);
        
        if (!$socket) {
            throw new PrinterConnectionException("فشل الاتصال: {$errstr}");
        }
        
        fclose($socket);
        return ['status' => 'success', 'message' => 'الاتصال ناجح'];
        
    } catch (Exception $e) {
        return ['status' => 'error', 'message' => $e->getMessage()];
    }
}
```

2. **خطأ في إعدادات الدفع**
```php
// التحقق من صحة إعدادات بوابة الدفع
public function validatePaymentGateway($gateway, $settings)
{
    switch ($gateway) {
        case 'stripe':
            if (empty($settings['public_key']) || empty($settings['secret_key'])) {
                throw new InvalidPaymentSettingsException('مفاتيح Stripe مطلوبة');
            }
            break;
            
        case 'paypal':
            if (empty($settings['client_id']) || empty($settings['client_secret'])) {
                throw new InvalidPaymentSettingsException('معرف وسر PayPal مطلوبان');
            }
            break;
    }
}
```

3. **خطأ في صيغة الإعدادات**
```php
// التحقق من صحة صيغة الإعدادات
public function validateSettingFormat($key, $value)
{
    $validators = [
        'email' => 'email',
        'phone' => 'regex:/^[+]?[0-9\s\-\(\)]+$/',
        'url' => 'url',
        'ip_address' => 'ip',
        'port' => 'integer|between:1,65535'
    ];
    
    if (isset($validators[$key])) {
        $validator = Validator::make(
            [$key => $value],
            [$key => $validators[$key]]
        );
        
        if ($validator->fails()) {
            throw new InvalidSettingFormatException(
                "صيغة الإعداد {$key} غير صحيحة"
            );
        }
    }
}
```

## 📚 المراجع والموارد

### الوثائق التقنية
- [Laravel Configuration](https://laravel.com/docs/configuration)
- [Laravel Validation](https://laravel.com/docs/validation)
- [Laravel Caching](https://laravel.com/docs/cache)

### أمثلة إضافية
- [إعداد نظام متعدد الفروع](./examples/multi-branch-settings.md)
- [تكامل بوابات الدفع](./examples/payment-gateway-integration.md)
- [أمان الإعدادات المتقدم](./examples/advanced-settings-security.md)

---

**ملاحظة**: تأكد من إجراء نسخ احتياطية منتظمة للإعدادات، خاصة قبل إجراء تحديثات كبيرة على النظام.