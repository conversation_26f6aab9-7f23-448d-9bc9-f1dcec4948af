@extends('layouts.master')

@section('title', 'إضافة فرع جديد')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h1 class="text-2xl font-bold text-gray-900 flex items-center gap-3">
                    <i class="fas fa-building text-blue-600"></i>
                    إضافة فرع جديد
                </h1>
                <a href="{{ route('branches.index') }}" 
                   class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                    <i class="fas fa-arrow-right"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>

    <!-- Create Form -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">معلومات الفرع</h2>
        </div>
        
        <form action="{{ route('branches.store') }}" method="POST" class="p-6">
            @csrf
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Basic Information -->
                <div class="md:col-span-2">
                    <h3 class="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">
                        <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                        المعلومات الأساسية
                    </h3>
                </div>
                
                <div>
                    <label for="tenant_id" class="block text-sm font-medium text-gray-700 mb-2">
                        المستأجر <span class="text-red-500">*</span>
                    </label>
                    <select name="tenant_id" id="tenant_id" required 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('tenant_id') border-red-500 @enderror">
                        <option value="">اختر المستأجر</option>
                        @foreach($tenants as $tenant)
                            <option value="{{ $tenant->id }}" {{ old('tenant_id') == $tenant->id ? 'selected' : '' }}>
                                {{ $tenant->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('tenant_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                        اسم الفرع <span class="text-red-500">*</span>
                    </label>
                    <input type="text" name="name" id="name" value="{{ old('name') }}" required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('name') border-red-500 @enderror"
                           placeholder="أدخل اسم الفرع">
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="code" class="block text-sm font-medium text-gray-700 mb-2">
                        كود الفرع
                    </label>
                    <input type="text" name="code" id="code" value="{{ old('code') }}" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('code') border-red-500 @enderror"
                           placeholder="سيتم إنشاؤه تلقائياً إذا ترك فارغاً">
                    @error('code')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    <p class="mt-1 text-xs text-gray-500">إذا ترك فارغاً، سيتم إنشاء كود تلقائياً</p>
                </div>
                
                <div>
                    <label for="manager_name" class="block text-sm font-medium text-gray-700 mb-2">
                        اسم المدير <span class="text-red-500">*</span>
                    </label>
                    <input type="text" name="manager_name" id="manager_name" value="{{ old('manager_name') }}" required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('manager_name') border-red-500 @enderror"
                           placeholder="أدخل اسم مدير الفرع">
                    @error('manager_name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <!-- Contact Information -->
                <div class="md:col-span-2">
                    <h3 class="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200 mt-6">
                        <i class="fas fa-address-book text-green-600 mr-2"></i>
                        معلومات الاتصال
                    </h3>
                </div>
                
                <div class="md:col-span-2">
                    <label for="address" class="block text-sm font-medium text-gray-700 mb-2">
                        العنوان <span class="text-red-500">*</span>
                    </label>
                    <textarea name="address" id="address" required rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('address') border-red-500 @enderror"
                              placeholder="أدخل عنوان الفرع بالتفصيل">{{ old('address') }}</textarea>
                    @error('address')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                        رقم الهاتف <span class="text-red-500">*</span>
                    </label>
                    <input type="text" name="phone" id="phone" value="{{ old('phone') }}" required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('phone') border-red-500 @enderror"
                           placeholder="مثال: +966501234567">
                    @error('phone')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                        البريد الإلكتروني
                    </label>
                    <input type="email" name="email" id="email" value="{{ old('email') }}" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('email') border-red-500 @enderror"
                           placeholder="<EMAIL>">
                    @error('email')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <!-- Business Settings -->
                <div class="md:col-span-2">
                    <h3 class="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200 mt-6">
                        <i class="fas fa-cogs text-purple-600 mr-2"></i>
                        إعدادات العمل
                    </h3>
                </div>
                
                <div>
                    <label for="seating_capacity" class="block text-sm font-medium text-gray-700 mb-2">
                        سعة الجلوس
                    </label>
                    <input type="number" name="seating_capacity" id="seating_capacity" value="{{ old('seating_capacity') }}" min="1"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('seating_capacity') border-red-500 @enderror"
                           placeholder="عدد المقاعد المتاحة">
                    @error('seating_capacity')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="delivery_radius" class="block text-sm font-medium text-gray-700 mb-2">
                        نطاق التوصيل (كيلومتر)
                    </label>
                    <input type="number" name="delivery_radius" id="delivery_radius" value="{{ old('delivery_radius') }}" min="0" step="0.1"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('delivery_radius') border-red-500 @enderror"
                           placeholder="مثال: 5.5">
                    @error('delivery_radius')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="timezone" class="block text-sm font-medium text-gray-700 mb-2">
                        المنطقة الزمنية <span class="text-red-500">*</span>
                    </label>
                    <select name="timezone" id="timezone" required 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('timezone') border-red-500 @enderror">
                        <option value="">اختر المنطقة الزمنية</option>
                        <option value="Asia/Riyadh" {{ old('timezone') == 'Asia/Riyadh' ? 'selected' : '' }}>الرياض (Asia/Riyadh)</option>
                        <option value="Asia/Dubai" {{ old('timezone') == 'Asia/Dubai' ? 'selected' : '' }}>دبي (Asia/Dubai)</option>
                        <option value="Asia/Kuwait" {{ old('timezone') == 'Asia/Kuwait' ? 'selected' : '' }}>الكويت (Asia/Kuwait)</option>
                        <option value="Asia/Qatar" {{ old('timezone') == 'Asia/Qatar' ? 'selected' : '' }}>قطر (Asia/Qatar)</option>
                        <option value="Asia/Bahrain" {{ old('timezone') == 'Asia/Bahrain' ? 'selected' : '' }}>البحرين (Asia/Bahrain)</option>
                    </select>
                    @error('timezone')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <!-- Services -->
                <div class="md:col-span-2">
                    <h3 class="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200 mt-6">
                        <i class="fas fa-concierge-bell text-orange-600 mr-2"></i>
                        الخدمات المتاحة
                    </h3>
                </div>
                
                <div class="md:col-span-2">
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <input type="checkbox" name="is_dine_in_enabled" id="is_dine_in_enabled" value="1" 
                                   {{ old('is_dine_in_enabled') ? 'checked' : 'checked' }}
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="is_dine_in_enabled" class="mr-3 block text-sm font-medium text-gray-900">
                                <i class="fas fa-utensils text-blue-600 mr-2"></i>
                                تناول في المكان
                            </label>
                        </div>
                        
                        <div class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <input type="checkbox" name="is_takeaway_enabled" id="is_takeaway_enabled" value="1" 
                                   {{ old('is_takeaway_enabled') ? 'checked' : 'checked' }}
                                   class="h-4 w-4 text-cyan-600 focus:ring-cyan-500 border-gray-300 rounded">
                            <label for="is_takeaway_enabled" class="mr-3 block text-sm font-medium text-gray-900">
                                <i class="fas fa-shopping-bag text-cyan-600 mr-2"></i>
                                استلام
                            </label>
                        </div>
                        
                        <div class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <input type="checkbox" name="is_delivery_enabled" id="is_delivery_enabled" value="1" 
                                   {{ old('is_delivery_enabled') ? 'checked' : '' }}
                                   class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                            <label for="is_delivery_enabled" class="mr-3 block text-sm font-medium text-gray-900">
                                <i class="fas fa-truck text-green-600 mr-2"></i>
                                توصيل
                            </label>
                        </div>
                        
                        <div class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <input type="checkbox" name="is_online_ordering_enabled" id="is_online_ordering_enabled" value="1" 
                                   {{ old('is_online_ordering_enabled') ? 'checked' : '' }}
                                   class="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded">
                            <label for="is_online_ordering_enabled" class="mr-3 block text-sm font-medium text-gray-900">
                                <i class="fas fa-globe text-yellow-600 mr-2"></i>
                                طلب أونلاين
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Submit Buttons -->
            <div class="flex justify-end gap-3 mt-8 pt-6 border-t border-gray-200">
                <a href="{{ route('branches.index') }}" 
                   class="px-6 py-2 text-sm font-medium text-gray-700 bg-gray-200 border border-gray-300 rounded-lg hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                    <i class="fas fa-times mr-2"></i>
                    إلغاء
                </a>
                <button type="submit" 
                        class="px-6 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                    <i class="fas fa-save mr-2"></i>
                    حفظ الفرع
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
@endpush