@extends('layouts.app')

@section('title', 'Customer Management')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">Customer Management</h1>
                    <p class="text-muted">Manage your customers and loyalty programs</p>
                </div>
                <div>
                    <button type="button" class="btn btn-info me-2" data-bs-toggle="modal" data-bs-target="#customerStatsModal">
                            <i class="fas fa-chart-bar me-2"></i>Statistics
                        </button>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createCustomerModal">
                            <i class="fas fa-plus me-2"></i>Add Customer
                        </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title" id="totalCustomers">0</h4>
                            <p class="card-text">Total Customers</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title" id="activeCustomers">0</h4>
                            <p class="card-text">Active Customers</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title" id="newCustomersThisMonth">0</h4>
                            <p class="card-text">New This Month</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-plus fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title" id="totalLoyaltyPoints">0</h4>
                            <p class="card-text">Total Loyalty Points</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-star fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="statusFilter" class="form-label">Status</label>
                            <select id="statusFilter" class="form-select">
                                <option value="">All Status</option>
                                <option value="1">Active</option>
                                <option value="0">Inactive</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="tierFilter" class="form-label">Tier</label>
                            <select id="tierFilter" class="form-select">
                                <option value="">All Tiers</option>
                                <option value="Bronze">Bronze</option>
                                <option value="Silver">Silver</option>
                                <option value="Gold">Gold</option>
                                <option value="Platinum">Platinum</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="dateFilter" class="form-label">Registration Date</label>
                            <input type="date" id="dateFilter" class="form-control">
                        </div>
                        <div class="col-md-3">
                            <label for="searchFilter" class="form-label">Search</label>
                            <input type="text" id="searchFilter" class="form-control" placeholder="Search customers...">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Customers Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Customers List</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="customersTable" class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Phone</th>
                                    <th>Loyalty Points</th>
                                    <th>Tier</th>
                                    <th>Last Visit</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Customer Modal -->
@include('customer::modals.create-customer')

<!-- Edit Customer Modal -->
@include('customer::modals.edit-customer')

<!-- View Customer Modal -->
@include('customer::modals.view-customer')

<!-- Loyalty Points Modal -->
@include('customer::modals.loyalty-points')

<!-- Customer Statistics Modal -->
@include('customer::modals.customer-statistics')

@endsection

@push('styles')
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap5.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css">
@endpush

@push('scripts')
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>

<script>
$(document).ready(function() {
    // Load statistics
    loadStatistics();
    
    // Initialize DataTable
    const table = $('#customersTable').DataTable({
        processing: true,
        serverSide: true,
        responsive: true,
        ajax: {
            url: '{{ route("customers.data.table") }}',
            data: function(d) {
                d.status = $('#statusFilter').val();
                d.tier = $('#tierFilter').val();
                d.date = $('#dateFilter').val();
                d.search = $('#searchFilter').val();
            }
        },
        columns: [
            { data: 'id', name: 'id' },
            { 
                data: 'full_name', 
                name: 'full_name',
                render: function(data, type, row) {
                    return `<div class="d-flex align-items-center">
                        <div class="avatar-sm me-2">
                            <div class="avatar-title bg-primary rounded-circle">
                                ${row.first_name.charAt(0)}${row.last_name.charAt(0)}
                            </div>
                        </div>
                        <div>
                            <h6 class="mb-0">${data}</h6>
                            <small class="text-muted">ID: ${row.id}</small>
                        </div>
                    </div>`;
                }
            },
            { data: 'email', name: 'email' },
            { data: 'phone', name: 'phone' },
            { 
                data: 'loyalty_points', 
                name: 'loyalty_points',
                render: function(data) {
                    return `<span class="badge bg-warning">${data} pts</span>`;
                }
            },
            { 
                data: 'tier', 
                name: 'tier',
                render: function(data) {
                    const colors = {
                        'Bronze': 'bg-secondary',
                        'Silver': 'bg-light text-dark',
                        'Gold': 'bg-warning',
                        'Platinum': 'bg-dark'
                    };
                    return `<span class="badge ${colors[data] || 'bg-secondary'}">${data}</span>`;
                }
            },
            { 
                data: 'last_visit', 
                name: 'last_visit',
                render: function(data) {
                    return data ? moment(data).format('MMM DD, YYYY') : 'Never';
                }
            },
            { 
                data: 'is_active', 
                name: 'is_active',
                render: function(data) {
                    return data ? 
                        '<span class="badge bg-success">Active</span>' : 
                        '<span class="badge bg-danger">Inactive</span>';
                }
            },
            { 
                data: 'actions', 
                name: 'actions', 
                orderable: false, 
                searchable: false,
                render: function(data, type, row) {
                    return `
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewCustomer(${row.id})">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-warning" onclick="editCustomer(${row.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-info" onclick="manageLoyalty(${row.id})">
                                <i class="fas fa-star"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteCustomer(${row.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    `;
                }
            }
        ],
        dom: 'Bfrtip',
        buttons: [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ],
        order: [[0, 'desc']],
        pageLength: 25,
        language: {
            processing: '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>'
        }
    });

    // Filter event handlers
    $('#statusFilter, #tierFilter, #dateFilter').on('change', function() {
        table.draw();
    });

    $('#searchFilter').on('keyup', function() {
        table.draw();
    });
});

// Load statistics
function loadStatistics() {
    $.get('{{ route("customers.data.statistics") }}', function(data) {
        $('#totalCustomers').text(data.total_customers);
        $('#activeCustomers').text(data.active_customers);
        $('#newCustomersThisMonth').text(data.new_customers_this_month);
        $('#totalLoyaltyPoints').text(data.total_loyalty_points);
    });
}

// Customer actions
function viewCustomer(id) {
    // Implementation for viewing customer details
    console.log('View customer:', id);
}

function editCustomer(id) {
    // Implementation for editing customer
    console.log('Edit customer:', id);
}

function manageLoyalty(id) {
    // Implementation for managing loyalty points
    console.log('Manage loyalty for customer:', id);
}

function deleteCustomer(id) {
    if (confirm('Are you sure you want to delete this customer?')) {
        // Implementation for deleting customer
        console.log('Delete customer:', id);
    }
}
</script>
@endpush