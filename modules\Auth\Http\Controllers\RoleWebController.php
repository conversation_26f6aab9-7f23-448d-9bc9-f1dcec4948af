<?php

namespace Modules\Auth\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RoleWebController extends Controller
{
    /**
     * Display a listing of roles.
     */
    public function index()
    {
        $permissions = Permission::all()->groupBy(function ($permission) {
            return explode('.', $permission->name)[0] ?? 'other';
        });
        
        return view('Auth::roles.index', compact('permissions'));
    }

    /**
     * Show the form for creating a new role.
     */
    public function create()
    {
        $permissions = Permission::all()->groupBy(function ($permission) {
            return explode('.', $permission->name)[0] ?? 'other';
        });
        
        return view('Auth::roles.create', compact('permissions'));
    }

    /**
     * Store a newly created role.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|unique:roles,name|max:255',
            'guard_name' => 'required|string|max:255',
            'permissions' => 'nullable|array',
            'permissions.*' => 'exists:permissions,name',
        ]);

        try {
            DB::beginTransaction();

            $role = Role::create([
                'name' => $request->name,
                'guard_name' => $request->guard_name,
            ]);

            // Assign permissions if provided
            if ($request->filled('permissions')) {
                $role->syncPermissions($request->permissions);
            }

            DB::commit();
            
            return redirect()->route('roles.index')
                ->with('success', 'تم إنشاء الدور بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()
                ->with('error', 'حدث خطأ أثناء إنشاء الدور: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified role.
     */
    public function show(Role $role)
    {
        $role->load(['permissions', 'users']);
        
        return view('Auth::roles.show', compact('role'));
    }

    /**
     * Show the form for editing the specified role.
     */
    public function edit(Role $role)
    {
        $permissions = Permission::all()->groupBy(function ($permission) {
            return explode('.', $permission->name)[0] ?? 'other';
        });
        
        $rolePermissions = $role->permissions->pluck('name')->toArray();
        
        return view('Auth::roles.edit', compact('role', 'permissions', 'rolePermissions'));
    }

    /**
     * Update the specified role.
     */
    public function update(Request $request, Role $role)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:roles,name,' . $role->id,
            'guard_name' => 'required|string|max:255',
            'permissions' => 'nullable|array',
            'permissions.*' => 'exists:permissions,name',
        ]);

        try {
            DB::beginTransaction();

            $role->update([
                'name' => $request->name,
                'guard_name' => $request->guard_name,
            ]);

            // Sync permissions
            $role->syncPermissions($request->permissions ?? []);

            DB::commit();
            
            return redirect()->route('roles.show', $role)
                ->with('success', 'تم تحديث الدور بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()
                ->with('error', 'حدث خطأ أثناء تحديث الدور: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified role.
     */
    public function destroy(Role $role)
    {
        try {
            // Check if role has users assigned
            if ($role->users()->count() > 0) {
                return back()->with('error', 'لا يمكن حذف الدور لوجود مستخدمين مرتبطين به');
            }

            $role->delete();
            
            return redirect()->route('roles.index')
                ->with('success', 'تم حذف الدور بنجاح');
        } catch (\Exception $e) {
            return back()->with('error', 'حدث خطأ أثناء حذف الدور: ' . $e->getMessage());
        }
    }

    /**
     * Get roles data for DataTable.
     */
    public function getRolesData(Request $request)
    {
        $query = Role::withCount(['permissions', 'users'])
            ->select(['id', 'name', 'guard_name', 'created_at']);

        return DataTables::of($query)
            ->addIndexColumn()
            ->addColumn('permissions_count', function ($role) {
                return '<span class="badge badge-info">' . $role->permissions_count . ' صلاحية</span>';
            })
            ->addColumn('users_count', function ($role) {
                return '<span class="badge badge-success">' . $role->users_count . ' مستخدم</span>';
            })
            ->addColumn('guard_badge', function ($role) {
                $badges = [
                    'web' => '<span class="badge badge-primary">ويب</span>',
                    'api' => '<span class="badge badge-secondary">API</span>',
                    'sanctum' => '<span class="badge badge-warning">Sanctum</span>',
                ];
                return $badges[$role->guard_name] ?? '<span class="badge badge-light">' . $role->guard_name . '</span>';
            })
            ->addColumn('action', function ($role) {
                $actions = '<div class="btn-group" role="group">';
                $actions .= '<a href="' . route('roles.show', $role) . '" class="btn btn-sm btn-info" title="عرض"><i class="fa fa-eye"></i></a>';
                $actions .= '<a href="' . route('roles.edit', $role) . '" class="btn btn-sm btn-primary" title="تعديل"><i class="fa fa-edit"></i></a>';
                $actions .= '<a href="' . route('roles.permissions', $role) . '" class="btn btn-sm btn-warning" title="إدارة الصلاحيات"><i class="fa fa-key"></i></a>';
                $actions .= '<a href="' . route('roles.users', $role) . '" class="btn btn-sm btn-success" title="المستخدمين"><i class="fa fa-users"></i></a>';
                $actions .= '<button type="button" class="btn btn-sm btn-danger" onclick="deleteRole(' . $role->id . ')" title="حذف"><i class="fa fa-trash"></i></button>';
                $actions .= '</div>';
                
                return $actions;
            })
            ->rawColumns(['permissions_count', 'users_count', 'guard_badge', 'action'])
            ->make(true);
    }

    /**
     * Show role permissions page.
     */
    public function permissions(Role $role)
    {
        $permissions = Permission::all()->groupBy(function ($permission) {
            return explode('.', $permission->name)[0] ?? 'other';
        });
        
        $rolePermissions = $role->permissions->pluck('name')->toArray();
        
        return view('Auth::roles.permissions', compact('role', 'permissions', 'rolePermissions'));
    }

    /**
     * Assign permissions to role.
     */
    public function assignPermissions(Request $request, Role $role)
    {
        $request->validate([
            'permissions' => 'nullable|array',
            'permissions.*' => 'exists:permissions,name',
        ]);

        try {
            $role->syncPermissions($request->permissions ?? []);
            
            return back()->with('success', 'تم تحديث صلاحيات الدور بنجاح');
        } catch (\Exception $e) {
            return back()->with('error', 'حدث خطأ أثناء تحديث الصلاحيات: ' . $e->getMessage());
        }
    }

    /**
     * Get role permissions data for DataTable.
     */
    public function getRolePermissionsData(Request $request, Role $role)
    {
        $query = $role->permissions()
            ->select(['id', 'name', 'guard_name', 'created_at']);

        return DataTables::of($query)
            ->addIndexColumn()
            ->addColumn('group', function ($permission) {
                return explode('.', $permission->name)[0] ?? 'other';
            })
            ->addColumn('guard_badge', function ($permission) {
                $badges = [
                    'web' => '<span class="badge badge-primary">ويب</span>',
                    'api' => '<span class="badge badge-secondary">API</span>',
                    'sanctum' => '<span class="badge badge-warning">Sanctum</span>',
                ];
                return $badges[$permission->guard_name] ?? '<span class="badge badge-light">' . $permission->guard_name . '</span>';
            })
            ->addColumn('action', function ($permission) use ($role) {
                return '<button type="button" class="btn btn-sm btn-danger" onclick="removePermission(' . $role->id . ', ' . $permission->id . ')" title="إزالة"><i class="fa fa-times"></i></button>';
            })
            ->rawColumns(['guard_badge', 'action'])
            ->make(true);
    }

    /**
     * Show role users page.
     */
    public function users(Role $role)
    {
        return view('Auth::roles.users', compact('role'));
    }

    /**
     * Get role users data for DataTable.
     */
    public function getRoleUsersData(Request $request, Role $role)
    {
        $query = $role->users()
            ->with(['tenant', 'branch'])
            ->select(['id', 'name', 'email', 'tenant_id', 'branch_id', 'position', 'is_active', 'created_at']);

        return DataTables::of($query)
            ->addIndexColumn()
            ->addColumn('tenant_name', function ($user) {
                return $user->tenant ? $user->tenant->name : '-';
            })
            ->addColumn('branch_name', function ($user) {
                return $user->branch ? $user->branch->name : '-';
            })
            ->addColumn('status_badge', function ($user) {
                return $user->is_active 
                    ? '<span class="badge badge-success">نشط</span>'
                    : '<span class="badge badge-secondary">غير نشط</span>';
            })
            ->addColumn('action', function ($user) {
                return '<a href="' . route('users.show', $user) . '" class="btn btn-sm btn-info" title="عرض المستخدم"><i class="fa fa-eye"></i></a>';
            })
            ->rawColumns(['status_badge', 'action'])
            ->make(true);
    }
}
