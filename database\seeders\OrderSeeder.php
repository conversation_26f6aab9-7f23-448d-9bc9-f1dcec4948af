<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Branch;
use App\Models\Customer;
use App\Models\Table;
use App\Models\MenuItem;
use App\Models\User;
use Modules\Orders\Helpers\OrderHelper;
use Modules\Kitchen\Services\KitchenService;

class OrderSeeder extends Seeder
{
    protected $kitchenService;

    public function __construct()
    {
        $this->kitchenService = new KitchenService();
    }

    public function run()
    {
        $this->command->info('Seeding Orders and demonstrating KOT creation logic...');

        $branches = Branch::all();
        $customers = Customer::all();
        $tables = Table::all();
        $menuItems = MenuItem::where('is_active', true)->get();
        $users = User::where('is_active', true)->get();

        if ($branches->isEmpty()) {
            $this->command->warn('No branches found. Please run BranchSeeder first.');
            return;
        }

        if ($menuItems->isEmpty()) {
            $this->command->warn('No menu items found. Please run MenuItemSeeder first.');
            return;
        }

        foreach ($branches as $branch) {
            $this->createOrdersForBranch($branch, $customers, $tables, $menuItems, $users);
        }

        $this->command->info('Order seeding completed with KOT creation demonstration!');
    }

    private function createOrdersForBranch($branch, $customers, $tables, $menuItems, $users)
    {
        // Get branch-specific data
        $branchTables = $tables->where('branch_id', $branch->id);
        $branchUsers = $users->where('branch_id', $branch->id);
        $branchCustomers = $customers->take(5); // Use first 5 customers for demo

        // Create different types of orders to demonstrate KOT logic
        $orderConfigs = [
            [
                'type' => 'dine_in',
                'status' => 'confirmed', // This will trigger KOT creation
                'description' => 'Confirmed dine-in order (should create KOT)',
                'item_count' => 3,
            ],
            [
                'type' => 'takeaway',
                'status' => 'confirmed', // This will trigger KOT creation
                'description' => 'Confirmed takeaway order (should create KOT)',
                'item_count' => 2,
            ],
            [
                'type' => 'dine_in',
                'status' => 'pending', // This will NOT trigger KOT creation
                'description' => 'Pending dine-in order (no KOT yet)',
                'item_count' => 4,
            ],
            [
                'type' => 'delivery',
                'status' => 'confirmed', // This will trigger KOT creation
                'description' => 'Confirmed delivery order (should create KOT)',
                'item_count' => 5,
            ],
            [
                'type' => 'dine_in',
                'status' => 'cancelled', // This will NOT trigger KOT creation
                'description' => 'Cancelled order (no KOT)',
                'item_count' => 2,
            ],
        ];

        foreach ($orderConfigs as $index => $config) {
            $this->createSampleOrder($branch, $config, $index + 1, $branchTables, $branchCustomers, $branchUsers, $menuItems);
        }
    }

    private function createSampleOrder($branch, $config, $orderNumber, $tables, $customers, $users, $menuItems)
    {
        $this->command->info("Creating {$config['description']} for branch: {$branch->name}");

        // Safety checks
        if ($customers->isEmpty()) {
            $this->command->warn("No customers available for branch {$branch->name}");
            return;
        }

        if ($users->isEmpty()) {
            $this->command->warn("No users available for branch {$branch->name}");
            return;
        }

        if ($menuItems->count() < $config['item_count']) {
            $this->command->warn("Not enough menu items available. Required: {$config['item_count']}, Available: {$menuItems->count()}");
            $config['item_count'] = min($config['item_count'], $menuItems->count());
            if ($config['item_count'] === 0) {
                $this->command->warn("No menu items available, skipping order creation");
                return;
            }
        }

        // Select random data
        $customer = $customers->random();
        $table = $config['type'] === 'dine_in' && $tables->isNotEmpty() ? $tables->random() : null;
        $cashier = $users->random();

        // Create order
        $order = Order::create([
            'branch_id' => $branch->id,
            'customer_id' => $customer->id,
            'table_id' => $table?->id,
            'order_number' => OrderHelper::generateOrderNumber(),
            'status' => $config['status'],
            'order_type' => $config['type'],
            'pax' => $config['type'] === 'dine_in' ? rand(1, 6) : null,
            'subtotal' => 0,
            'tax_amount' => 0,
            'total_amount' => 0,
            'notes' => "Sample {$config['description']} - Order #{$orderNumber}",
            'cashier_id' => $cashier->id,
            'delivery_address' => $config['type'] === 'delivery' ? '123 Sample Street, City' : null,
            'created_at' => now()->subMinutes(rand(10, 120)), // Random time in last 2 hours
        ]);

        // Add order items
        $selectedItems = $menuItems->random($config['item_count']);
        $subtotal = 0;

        foreach ($selectedItems as $menuItem) {
            $quantity = rand(1, 3);
            $unitPrice = $menuItem->price ?? rand(10, 50);
            $totalPrice = $quantity * $unitPrice;

            OrderItem::create([
                'order_id' => $order->id,
                'menu_item_id' => $menuItem->id,
                'menu_item_name' => $menuItem->name,
                'quantity' => $quantity,
                'unit_price' => $unitPrice,
                'total_price' => $totalPrice,
                'special_instructions' => rand(1, 4) === 1 ? 'Extra spicy' : null,
            ]);

            $subtotal += $totalPrice;
        }

        // Update order totals
        $taxAmount = $subtotal * 0.1; // 10% tax
        $totalAmount = $subtotal + $taxAmount;

        $order->update([
            'subtotal' => $subtotal,
            'tax_amount' => $taxAmount,
            'total_amount' => $totalAmount,
        ]);

        // Demonstrate KOT creation logic
        if ($config['status'] === 'confirmed' && in_array($config['type'], ['dine_in', 'takeaway', 'delivery'])) {
            try {
                // Load order items with menu items for KOT creation
                $order->load(['orderItems.menuItem']);
                
                // Check if order has active KOT (should be false for new orders)
                if (!$order->hasActiveKot()) {
                    $kotOrders = $this->kitchenService->createKotFromOrder($order);
                    $this->command->info("✅ Created " . count($kotOrders) . " KOT(s) for order {$order->order_number}");
                    
                    foreach ($kotOrders as $kotOrder) {
                        $this->command->info("   - KOT #{$kotOrder->kot_number} for {$kotOrder->kitchen->name}");
                    }
                } else {
                    $this->command->info("⚠️  Order {$order->order_number} already has active KOT");
                }
            } catch (\Exception $e) {
                $this->command->error("❌ Failed to create KOT for order {$order->order_number}: " . $e->getMessage());
            }
        } else {
            $this->command->info("ℹ️  Order {$order->order_number} ({$config['status']}) - No KOT created (as expected)");
        }
    }
}