@extends('layouts.master')

@section('title', 'Create Transaction')

@push('styles')
<style>
    .form-group {
        margin-bottom: 1rem;
    }
    
    .form-label {
        display: block;
        font-size: 0.875rem;
        font-weight: 500;
        color: #374151;
        margin-bottom: 0.5rem;
    }
    
    .form-control {
        width: 100%;
        padding: 0.5rem 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }
    
    .form-control:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    .form-control.is-invalid {
        border-color: #ef4444;
    }
    
    .invalid-feedback {
        display: block;
        width: 100%;
        margin-top: 0.25rem;
        font-size: 0.75rem;
        color: #ef4444;
    }
    
    .transaction-type-card {
        border: 2px solid #e5e7eb;
        border-radius: 0.5rem;
        padding: 1rem;
        cursor: pointer;
        transition: all 0.2s ease-in-out;
    }
    
    .transaction-type-card:hover {
        border-color: #3b82f6;
        background-color: #f8fafc;
    }
    
    .transaction-type-card.selected {
        border-color: #3b82f6;
        background-color: #eff6ff;
    }
    
    .order-card {
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
        padding: 1rem;
        cursor: pointer;
        transition: all 0.2s ease-in-out;
    }
    
    .order-card:hover {
        border-color: #3b82f6;
        background-color: #f8fafc;
    }
    
    .order-card.selected {
        border-color: #3b82f6;
        background-color: #eff6ff;
    }
    
    .order-status {
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 10px;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .status-pending { background-color: #fef3c7; color: #92400e; }
    .status-confirmed { background-color: #dbeafe; color: #1e40af; }
    .status-preparing { background-color: #fde68a; color: #d97706; }
    .status-ready { background-color: #d1fae5; color: #065f46; }
    .status-completed { background-color: #e0e7ff; color: #3730a3; }
    .status-cancelled { background-color: #f3f4f6; color: #374151; }
</style>
@endpush

@section('content')
<!-- Page Header -->
<div class="mb-6">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="mb-4 sm:mb-0">
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-plus-circle mr-3"></i>
                Create Transaction
            </h1>
            <p class="text-sm text-gray-600 mt-1">Create a new transaction from an order or as a standalone transaction</p>
        </div>
        <div class="flex gap-2">
            <a href="{{ route('transactions.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-md transition-colors duration-200">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to List
            </a>
        </div>
    </div>
</div>

<!-- Transaction Type Selection -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Transaction Type</h3>
        <p class="text-sm text-gray-600 mt-1">Choose how you want to create this transaction</p>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="transaction-type-card" data-type="from_order">
                <div class="flex items-center mb-3">
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-shopping-cart text-blue-600"></i>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900">From Order</h4>
                        <p class="text-sm text-gray-600">Create transaction from an existing order</p>
                    </div>
                </div>
                <p class="text-xs text-gray-500">Select an order that doesn't have a transaction yet and create a transaction for it.</p>
            </div>
            
            <div class="transaction-type-card" data-type="standalone">
                <div class="flex items-center mb-3">
                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-receipt text-green-600"></i>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900">Standalone</h4>
                        <p class="text-sm text-gray-600">Create a standalone transaction</p>
                    </div>
                </div>
                <p class="text-xs text-gray-500">Create a transaction without linking it to any order. Useful for miscellaneous charges.</p>
            </div>
        </div>
    </div>
</div>

<!-- Order Selection (for from_order type) -->
<div id="order-selection" class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6" style="display: none;">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Select Order</h3>
        <p class="text-sm text-gray-600 mt-1">Choose an order to create a transaction for</p>
    </div>
    <div class="p-6">
        @if($orders->count() > 0)
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
            @foreach($orders as $order)
            <div class="order-card" data-order-id="{{ $order->id }}" data-order-total="{{ $order->total_amount }}">
                <div class="flex justify-between items-start mb-2">
                    <div>
                        <h4 class="font-semibold text-gray-900">{{ $order->order_number }}</h4>
                        <p class="text-sm text-gray-600">{{ $order->created_at->format('M d, Y H:i') }}</p>
                    </div>
                    <span class="order-status status-{{ $order->status }}">
                        {{ ucfirst($order->status) }}
                    </span>
                </div>
                
                <div class="space-y-1 text-sm">
                    @if($order->customer)
                    <div class="flex justify-between">
                        <span class="text-gray-600">Customer:</span>
                        <span class="font-medium">{{ $order->customer->name }}</span>
                    </div>
                    @endif
                    
                    @if($order->table)
                    <div class="flex justify-between">
                        <span class="text-gray-600">Table:</span>
                        <span class="font-medium">{{ $order->table->table_number }}</span>
                    </div>
                    @endif
                    
                    <div class="flex justify-between">
                        <span class="text-gray-600">Total:</span>
                        <span class="font-medium">${{ number_format($order->total_amount, 2) }}</span>
                    </div>
                    
                    @if($order->items_count)
                    <div class="flex justify-between">
                        <span class="text-gray-600">Items:</span>
                        <span class="font-medium">{{ $order->items_count }}</span>
                    </div>
                    @endif
                </div>
            </div>
            @endforeach
        </div>
        @else
        <div class="text-center py-8">
            <i class="fas fa-shopping-cart text-gray-400 text-4xl mb-4"></i>
            <h4 class="text-lg font-medium text-gray-900 mb-2">No Orders Available</h4>
            <p class="text-gray-600">There are no orders without transactions available. All orders already have transactions or there are no orders in the system.</p>
        </div>
        @endif
    </div>
</div>

<!-- Transaction Form -->
<div id="transaction-form" class="bg-white rounded-lg shadow-sm border border-gray-200" style="display: none;">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Transaction Details</h3>
        <p class="text-sm text-gray-600 mt-1">Enter the transaction information</p>
    </div>
    
    <form id="create-transaction-form" action="{{ route('transactions.store') }}" method="POST">
        @csrf
        <input type="hidden" id="transaction_type" name="transaction_type" value="">
        <input type="hidden" id="order_id" name="order_id" value="">
        
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Left Column -->
                <div>
                    <div class="form-group">
                        <label for="total_amount" class="form-label">Total Amount *</label>
                        <input type="number" step="0.01" min="0" id="total_amount" name="total_amount" class="form-control" required>
                        @error('total_amount')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="paid_amount" class="form-label">Paid Amount</label>
                        <input type="number" step="0.01" min="0" id="paid_amount" name="paid_amount" class="form-control" value="0">
                        @error('paid_amount')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="tax_amount" class="form-label">Tax Amount</label>
                        <input type="number" step="0.01" min="0" id="tax_amount" name="tax_amount" class="form-control" value="0">
                        @error('tax_amount')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                
                <!-- Right Column -->
                <div>
                    <div class="form-group">
                        <label for="discount_amount" class="form-label">Discount Amount</label>
                        <input type="number" step="0.01" min="0" id="discount_amount" name="discount_amount" class="form-control" value="0">
                        @error('discount_amount')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="service_charge" class="form-label">Service Charge</label>
                        <input type="number" step="0.01" min="0" id="service_charge" name="service_charge" class="form-control" value="0">
                        @error('service_charge')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="status" class="form-label">Status</label>
                        <select id="status" name="status" class="form-control">
                            <option value="due">Due</option>
                            <option value="paid">Paid</option>
                            <option value="partially_paid">Partially Paid</option>
                        </select>
                        @error('status')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="notes" class="form-label">Notes</label>
                <textarea id="notes" name="notes" rows="3" class="form-control" placeholder="Optional notes about this transaction..."></textarea>
                @error('notes')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
            
            <!-- Order Information Display (for from_order type) -->
            <div id="selected-order-info" style="display: none;" class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 class="font-semibold text-blue-900 mb-2">Selected Order Information</h4>
                <div id="order-details" class="text-sm text-blue-800"></div>
            </div>
            
            <!-- Amount Calculation Summary -->
            <div class="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
                <h4 class="font-semibold text-gray-900 mb-3">Amount Summary</h4>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Subtotal:</span>
                        <span id="subtotal-display" class="font-medium">$0.00</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Tax:</span>
                        <span id="tax-display" class="font-medium">$0.00</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Service Charge:</span>
                        <span id="service-display" class="font-medium">$0.00</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Discount:</span>
                        <span id="discount-display" class="font-medium">-$0.00</span>
                    </div>
                    <div class="flex justify-between font-semibold text-lg border-t pt-2">
                        <span class="text-gray-900">Total:</span>
                        <span id="total-display" class="text-gray-900">$0.00</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Paid:</span>
                        <span id="paid-display" class="font-medium">$0.00</span>
                    </div>
                    <div class="flex justify-between font-semibold">
                        <span class="text-gray-900">Due:</span>
                        <span id="due-display" class="text-gray-900">$0.00</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="px-6 py-4 border-t border-gray-200 flex justify-end gap-2">
            <a href="{{ route('transactions.index') }}" class="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md transition-colors duration-200">
                Cancel
            </a>
            <button type="submit" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors duration-200">
                <i class="fas fa-save mr-2"></i>
                Create Transaction
            </button>
        </div>
    </form>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    let selectedType = '';
    let selectedOrder = null;
    
    // Transaction type selection
    $('.transaction-type-card').on('click', function() {
        $('.transaction-type-card').removeClass('selected');
        $(this).addClass('selected');
        
        selectedType = $(this).data('type');
        $('#transaction_type').val(selectedType);
        
        if (selectedType === 'from_order') {
            $('#order-selection').show();
            $('#transaction-form').hide();
        } else {
            $('#order-selection').hide();
            $('#transaction-form').show();
            resetForm();
        }
    });
    
    // Order selection
    $('.order-card').on('click', function() {
        $('.order-card').removeClass('selected');
        $(this).addClass('selected');
        
        selectedOrder = {
            id: $(this).data('order-id'),
            total: parseFloat($(this).data('order-total')),
            number: $(this).find('h4').text(),
            customer: $(this).find('.text-gray-600:contains("Customer:")').next().text() || 'N/A',
            table: $(this).find('.text-gray-600:contains("Table:")').next().text() || 'N/A'
        };
        
        $('#order_id').val(selectedOrder.id);
        $('#total_amount').val(selectedOrder.total.toFixed(2));
        
        // Show order info
        $('#order-details').html(`
            <div class="grid grid-cols-2 gap-4">
                <div><strong>Order Number:</strong> ${selectedOrder.number}</div>
                <div><strong>Total Amount:</strong> $${selectedOrder.total.toFixed(2)}</div>
                <div><strong>Customer:</strong> ${selectedOrder.customer}</div>
                <div><strong>Table:</strong> ${selectedOrder.table}</div>
            </div>
        `);
        $('#selected-order-info').show();
        
        $('#transaction-form').show();
        updateAmountSummary();
    });
    
    // Amount calculation
    $('#total_amount, #paid_amount, #tax_amount, #discount_amount, #service_charge').on('input', function() {
        updateAmountSummary();
    });
    
    function updateAmountSummary() {
        const total = parseFloat($('#total_amount').val()) || 0;
        const paid = parseFloat($('#paid_amount').val()) || 0;
        const tax = parseFloat($('#tax_amount').val()) || 0;
        const discount = parseFloat($('#discount_amount').val()) || 0;
        const service = parseFloat($('#service_charge').val()) || 0;
        
        const subtotal = total - tax - service + discount;
        const due = total - paid;
        
        $('#subtotal-display').text('$' + subtotal.toFixed(2));
        $('#tax-display').text('$' + tax.toFixed(2));
        $('#service-display').text('$' + service.toFixed(2));
        $('#discount-display').text('-$' + discount.toFixed(2));
        $('#total-display').text('$' + total.toFixed(2));
        $('#paid-display').text('$' + paid.toFixed(2));
        $('#due-display').text('$' + due.toFixed(2));
        
        // Update status based on payment
        if (paid >= total) {
            $('#status').val('paid');
        } else if (paid > 0) {
            $('#status').val('partially_paid');
        } else {
            $('#status').val('due');
        }
    }
    
    function resetForm() {
        $('#order_id').val('');
        $('#total_amount').val('');
        $('#paid_amount').val('0');
        $('#tax_amount').val('0');
        $('#discount_amount').val('0');
        $('#service_charge').val('0');
        $('#status').val('due');
        $('#notes').val('');
        $('#selected-order-info').hide();
        updateAmountSummary();
    }
    
    // Form submission
    $('#create-transaction-form').on('submit', function(e) {
        if (selectedType === 'from_order' && !selectedOrder) {
            e.preventDefault();
            Swal.fire({
                icon: 'warning',
                title: 'Order Required',
                text: 'Please select an order to create the transaction from.'
            });
            return false;
        }
        
        if (!selectedType) {
            e.preventDefault();
            Swal.fire({
                icon: 'warning',
                title: 'Transaction Type Required',
                text: 'Please select a transaction type.'
            });
            return false;
        }
    });
    
    // Initialize amount summary
    updateAmountSummary();
});
</script>
@endpush