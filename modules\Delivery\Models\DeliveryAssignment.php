<?php

namespace Modules\Delivery\Models;

use App\Models\Order;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class DeliveryAssignment extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'delivery_personnel_id',
        'delivery_zone_id',
        'external_delivery_id',
        'status',
        'assigned_at',
        'picked_up_at',
        'delivered_at',
        'delivery_notes',
        'failure_reason',
        'delivery_proof',
        'distance_km',
        'estimated_duration_minutes',
        'actual_duration_minutes',
        'delivery_fee_earned',
    ];

    protected $casts = [
        'assigned_at' => 'datetime',
        'picked_up_at' => 'datetime',
        'delivered_at' => 'datetime',
        'delivery_proof' => 'array',
        'distance_km' => 'decimal:2',
        'delivery_fee_earned' => 'decimal:2',
    ];

    /**
     * Get the order for this assignment.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the delivery personnel for this assignment.
     */
    public function deliveryPersonnel(): BelongsTo
    {
        return $this->belongsTo(DeliveryPersonnel::class);
    }

    /**
     * Get the delivery zone for this assignment.
     */
    public function deliveryZone(): BelongsTo
    {
        return $this->belongsTo(DeliveryZone::class);
    }

    /**
     * Get tracking records for this assignment.
     */
    public function trackingRecords(): HasMany
    {
        return $this->hasMany(DeliveryTracking::class);
    }

    /**
     * Get the latest tracking record.
     */
    public function latestTracking(): HasOne
    {
        return $this->hasOne(DeliveryTracking::class)->latest('recorded_at');
    }

    /**
     * Get delivery review for this assignment.
     */
    public function review(): HasOne
    {
        return $this->hasOne(DeliveryReview::class);
    }

    /**
     * Get delivery tips for this assignment.
     */
    public function tips(): HasMany
    {
        return $this->hasMany(DeliveryTip::class);
    }

    /**
     * Get the total tip amount for this assignment.
     */
    public function getTotalTipAmount(): float
    {
        return $this->tips()->where('status', 'paid')->sum('amount');
    }

    /**
     * Mark assignment as picked up.
     */
    public function markAsPickedUp(): void
    {
        if ($this->status !== 'assigned') {
            throw new \Exception('Can only mark assigned deliveries as picked up');
        }

        $this->update([
            'status' => 'picked_up',
            'picked_up_at' => now(),
        ]);

        // Update order status
        $this->order->update(['status' => 'ready']);
        
        // Update personnel status to on_delivery
        $this->deliveryPersonnel->update(['status' => 'on_delivery']);
    }

    /**
     * Mark assignment as in transit.
     */
    public function markAsInTransit(): void
    {
        if (!in_array($this->status, ['assigned', 'picked_up'])) {
            throw new \Exception('Can only mark assigned or picked up deliveries as in transit');
        }

        $this->update([
            'status' => 'in_transit',
            'picked_up_at' => $this->picked_up_at ?? now(),
        ]);
    }

    /**
     * Mark assignment as delivered.
     */
    public function markAsDelivered(string $deliveryProof = null): void
    {
        if (!in_array($this->status, ['picked_up', 'in_transit'])) {
            throw new \Exception('Can only mark picked up or in transit deliveries as delivered');
        }

        $deliveredAt = now();
        $actualDuration = $this->picked_up_at ? 
            $this->picked_up_at->diffInMinutes($deliveredAt) : null;

        $this->update([
            'status' => 'delivered',
            'delivered_at' => $deliveredAt,
            'delivery_proof' => $deliveryProof,
            'actual_duration_minutes' => $actualDuration,
        ]);

        // Update personnel stats
        $this->deliveryPersonnel->increment('total_deliveries');
        if ($this->delivery_fee_earned) {
            $this->deliveryPersonnel->increment('total_earnings', $this->delivery_fee_earned);
        }
        
        // Update personnel status - check if they have more active deliveries
        if ($this->deliveryPersonnel->activeDeliveries()->where('id', '!=', $this->id)->count() == 0) {
            $this->deliveryPersonnel->update(['status' => 'active']);
        }
    }

    /**
     * Mark assignment as failed.
     */
    public function markAsFailed(string $reason): void
    {
        if (in_array($this->status, ['delivered', 'cancelled'])) {
            throw new \Exception('Cannot mark delivered or cancelled deliveries as failed');
        }

        $this->update([
            'status' => 'failed',
            'failure_reason' => $reason,
        ]);

        // Update delivery personnel status to active (available for new deliveries)
        $this->deliveryPersonnel->update(['status' => 'active']);
    }

    /**
     * Cancel the assignment.
     */
    public function cancel(string $reason = null): void
    {
        if (in_array($this->status, ['delivered', 'failed'])) {
            throw new \Exception('Cannot cancel delivered or failed deliveries');
        }

        $this->update([
            'status' => 'cancelled',
            'failure_reason' => $reason,
        ]);
        
        // Update delivery personnel status to active (available for new deliveries)
        $this->deliveryPersonnel->update(['status' => 'active']);
    }

    /**
     * Calculate estimated delivery time based on distance and traffic.
     */
    public function calculateEstimatedDuration(): int
    {
        if (!$this->distance_km) {
            return $this->deliveryZone?->estimated_delivery_time_minutes ?? 30;
        }

        // Base calculation: 30 km/h average speed + 5 minutes preparation
        $baseMinutes = ($this->distance_km / 30) * 60 + 5;
        
        // Add traffic factor (20% increase during peak hours)
        $hour = now()->hour;
        $isPeakHour = ($hour >= 11 && $hour <= 14) || ($hour >= 18 && $hour <= 21);
        
        if ($isPeakHour) {
            $baseMinutes *= 1.2;
        }

        return (int) ceil($baseMinutes);
    }

    /**
     * Get current location of the delivery.
     */
    public function getCurrentLocation(): ?array
    {
        $latest = $this->latestTracking;
        
        if (!$latest) {
            return null;
        }

        return [
            'latitude' => $latest->latitude,
            'longitude' => $latest->longitude,
            'accuracy' => $latest->accuracy,
            'speed' => $latest->speed,
            'bearing' => $latest->bearing,
            'recorded_at' => $latest->recorded_at,
        ];
    }

    /**
     * Scope for active assignments.
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['assigned', 'picked_up', 'in_transit']);
    }

    /**
     * Scope for completed assignments.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'delivered');
    }

    /**
     * Scope for assignments by personnel.
     */
    public function scopeByPersonnel($query, int $personnelId)
    {
        return $query->where('delivery_personnel_id', $personnelId);
    }
}