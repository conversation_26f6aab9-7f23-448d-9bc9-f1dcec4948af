@extends('layouts.master')

@section('title', 'تفاصيل الاشتراك')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h1 class="text-xl font-semibold text-gray-900">تفاصيل الاشتراك #{{ $subscription->id }}</h1>
                <div class="flex gap-2">
                    <a href="{{ route('subscriptions-web.edit', $subscription) }}" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                        <i class="fas fa-edit text-sm"></i>
                        تعديل
                    </a>
                    <a href="{{ route('subscriptions-web.index') }}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                        <i class="fas fa-arrow-right text-sm"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
        
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">المستأجر</label>
                        <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{{ $subscription->tenant->name ?? 'غير محدد' }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">الباقة</label>
                        <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{{ $subscription->subscriptionPlan->name ?? 'غير محدد' }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">تاريخ البداية</label>
                        <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{{ $subscription->start_date ? \Carbon\Carbon::parse($subscription->start_date)->format('Y-m-d') : 'غير محدد' }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">تاريخ النهاية</label>
                        <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{{ $subscription->end_date ? \Carbon\Carbon::parse($subscription->end_date)->format('Y-m-d') : 'غير محدد' }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">المبلغ</label>
                        <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{{ number_format($subscription->amount, 2) }} {{ $subscription->subscriptionPlan->currency ?? 'USD' }}</p>
                    </div>
                </div>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">الحالة</label>
                        <p class="bg-gray-50 px-3 py-2 rounded-lg">
                            @switch($subscription->status)
                                @case('active')
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle mr-1"></i>
                                        نشط
                                    </span>
                                    @break
                                @case('pending')
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        <i class="fas fa-clock mr-1"></i>
                                        معلق
                                    </span>
                                    @break
                                @case('suspended')
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                        <i class="fas fa-pause-circle mr-1"></i>
                                        موقوف
                                    </span>
                                    @break
                                @case('cancelled')
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        <i class="fas fa-times-circle mr-1"></i>
                                        ملغي
                                    </span>
                                    @break
                                @default
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        {{ $subscription->status }}
                                    </span>
                            @endswitch
                        </p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">طريقة الدفع</label>
                        <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{{ $subscription->payment_method ?? 'غير محدد' }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">تجديد تلقائي</label>
                        <p class="bg-gray-50 px-3 py-2 rounded-lg">
                            @if($subscription->auto_renew)
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check mr-1"></i>
                                    مفعل
                                </span>
                            @else
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    <i class="fas fa-times mr-1"></i>
                                    غير مفعل
                                </span>
                            @endif
                        </p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">تاريخ الإنشاء</label>
                        <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{{ $subscription->created_at ? $subscription->created_at->format('Y-m-d H:i') : 'غير محدد' }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">آخر تحديث</label>
                        <p class="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{{ $subscription->updated_at ? $subscription->updated_at->format('Y-m-d H:i') : 'غير محدد' }}</p>
                    </div>
                </div>
            </div>
            
            @if($subscription->notes)
            <div class="mt-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">ملاحظات</label>
                <div class="bg-gray-50 px-4 py-3 rounded-lg">
                    <p class="text-gray-900 whitespace-pre-wrap">{{ $subscription->notes }}</p>
                </div>
            </div>
            @endif
            
            <!-- Action Buttons -->
            <div class="mt-8 pt-6 border-t border-gray-200">
                <div class="flex flex-wrap gap-3">
                    @if($subscription->status === 'active')
                        <form method="POST" action="{{ route('subscriptions-web.suspend', $subscription) }}" class="inline">
                            @csrf
                            <button type="button" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors" onclick="confirmSuspend(this.closest('form'))"
                                <i class="fas fa-pause text-sm"></i>
                                إيقاف الاشتراك
                            </button>
                        </form>
                        
                        <form method="POST" action="{{ route('subscriptions-web.cancel', $subscription) }}" class="inline">
                            @csrf
                            <button type="button" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors" onclick="confirmCancel(this.closest('form'))"
                                <i class="fas fa-times text-sm"></i>
                                إلغاء الاشتراك
                            </button>
                        </form>
                    @elseif($subscription->status === 'suspended')
                        <form method="POST" action="{{ route('subscriptions-web.reactivate', $subscription) }}" class="inline">
                            @csrf
                            <button type="button" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors" onclick="confirmReactivate(this.closest('form'))"
                                <i class="fas fa-play text-sm"></i>
                                إعادة تفعيل
                            </button>
                        </form>
                    @endif
                    
                    @if(in_array($subscription->status, ['active', 'pending']))
                        <a href="{{ route('subscriptions-web.upgrade', $subscription) }}" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                            <i class="fas fa-arrow-up text-sm"></i>
                            ترقية الباقة
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function confirmSuspend(form) {
    Swal.fire({
        title: 'تأكيد العملية',
        text: 'هل أنت متأكد من إيقاف هذا الاشتراك؟',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#f59e0b',
        cancelButtonColor: '#d33',
        confirmButtonText: 'نعم، إيقاف',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            form.submit();
        }
    });
}

function confirmCancel(form) {
    Swal.fire({
        title: 'تأكيد العملية',
        text: 'هل أنت متأكد من إلغاء هذا الاشتراك؟',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc2626',
        cancelButtonColor: '#d33',
        confirmButtonText: 'نعم، إلغاء',
        cancelButtonText: 'تراجع'
    }).then((result) => {
        if (result.isConfirmed) {
            form.submit();
        }
    });
}

function confirmReactivate(form) {
    Swal.fire({
        title: 'تأكيد العملية',
        text: 'هل أنت متأكد من إعادة تفعيل هذا الاشتراك؟',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#16a34a',
        cancelButtonColor: '#d33',
        confirmButtonText: 'نعم، إعادة تفعيل',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            form.submit();
        }
    });
}
</script>
@endpush