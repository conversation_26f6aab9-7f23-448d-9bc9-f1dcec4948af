<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('menu_categories', function (Blueprint $table) {
            // Check if tenant_id column doesn't exist and add it
            if (!Schema::hasColumn('menu_categories', 'tenant_id')) {
                $table->unsignedBigInteger('tenant_id')->after('id')->default(1);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('menu_categories', function (Blueprint $table) {
            // Remove tenant_id column if it exists
            if (Schema::hasColumn('menu_categories', 'tenant_id')) {
                $table->dropColumn('tenant_id');
            }
        });
    }
};
