<?php

namespace Modules\Reservation\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;
use App\Models\Table;
use App\Models\Area;
use Exception;

class QRTestController extends Controller
{
    /**
     * Display QR test page.
     */
    public function index(): View
    {
        $tables = Table::with(['area', 'branch'])->get();
        $areas = Area::all();
        
        return view('reservation::qr.test', compact('tables', 'areas'));
    }

    /**
     * Generate QR code for testing.
     */
    public function generateQR(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'table_id' => 'required|exists:tables,id',
                'type' => 'required|in:table,menu,order',
            ]);

            $table = Table::with(['area', 'branch'])->find($request->table_id);

            if (!$table->qr_code) {
                $table->qr_code = 'TBL_' . $table->id . '_' . time();
                $table->save();
            }

            $urls = [
                'table' => url("/restaurant/table/{$table->qr_code}"),
                'menu' => url("/restaurant/table/{$table->qr_code}/menu"),
                'order' => url("/restaurant/table/{$table->qr_code}/order"),
            ];

            return response()->json([
                'success' => true,
                'data' => [
                    'qr_code' => $table->qr_code,
                    'table_id' => $table->id,
                    'table_number' => $table->table_number,
                    'area' => $table->area->name ?? null,
                    'branch' => $table->branch->name ?? null,
                    'url' => $urls[$request->type],
                    'all_urls' => $urls,
                ],
                'message' => 'QR code generated successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate QR code',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate QR code for testing.
     */
    public function validateQR(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'qr_code' => 'required|string',
            ]);

            $table = Table::with(['area', 'branch'])
                ->where('qr_code', $request->qr_code)
                ->first();

            if (!$table) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid QR code'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'table_id' => $table->id,
                    'table_number' => $table->table_number,
                    'area' => $table->area->name ?? null,
                    'branch' => $table->branch->name ?? null,
                    'capacity' => $table->capacity,
                    'status' => $table->status,
                ],
                'message' => 'QR code validated successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to validate QR code',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate custom QR code for testing.
     */
    public function generateCustomQR(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'content' => 'required|string',
                'type' => 'required|in:url,text',
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'content' => $request->content,
                    'type' => $request->type,
                    'qr_data' => base64_encode($request->content),
                ],
                'message' => 'Custom QR code generated successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate custom QR code',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Batch generate QR codes for testing.
     */
    public function batchGenerate(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'area_id' => 'nullable|exists:areas,id',
                'table_count' => 'required|integer|min:1|max:50',
            ]);

            $query = Table::with(['area', 'branch']);
            
            if ($request->area_id) {
                $query->where('area_id', $request->area_id);
            }

            $tables = $query->limit($request->table_count)->get();
            $results = [];

            foreach ($tables as $table) {
                if (!$table->qr_code) {
                    $table->qr_code = 'TBL_' . $table->id . '_' . time();
                    $table->save();
                }

                $results[] = [
                    'table_id' => $table->id,
                    'table_number' => $table->table_number,
                    'qr_code' => $table->qr_code,
                    'area' => $table->area->name ?? null,
                    'url' => url("/restaurant/table/{$table->qr_code}"),
                ];
            }

            return response()->json([
                'success' => true,
                'data' => $results,
                'message' => 'Batch QR codes generated successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate batch QR codes',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get table information for testing.
     */
    public function getTableInfo(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'table_id' => 'required|exists:tables,id',
            ]);

            $table = Table::with(['area', 'branch', 'reservations' => function($query) {
                $query->where('reservation_datetime', '>=', now())
                      ->orderBy('reservation_datetime');
            }])->find($request->table_id);

            return response()->json([
                'success' => true,
                'data' => [
                    'table' => [
                        'id' => $table->id,
                        'table_number' => $table->table_number,
                        'capacity' => $table->capacity,
                        'status' => $table->status,
                        'qr_code' => $table->qr_code,
                    ],
                    'area' => $table->area ? [
                        'id' => $table->area->id,
                        'name' => $table->area->name,
                    ] : null,
                    'branch' => $table->branch ? [
                        'id' => $table->branch->id,
                        'name' => $table->branch->name,
                    ] : null,
                    'upcoming_reservations' => $table->reservations->take(5),
                ],
                'message' => 'Table information retrieved successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve table information',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}