{"info": {"name": "Orders Module API Collection", "description": "Comprehensive API collection for the Orders module including order management, POS operations, and kitchen integration.", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://127.0.0.1:8000", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}], "item": [{"name": "Orders Management", "item": [{"name": "List Orders", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/orders?status=confirmed&per_page=15&page=1", "host": ["{{base_url}}"], "path": ["api", "orders"], "query": [{"key": "status", "value": "confirmed", "description": "pending, confirmed, preparing, ready, delivered, cancelled"}, {"key": "customer_id", "value": "", "description": "Filter by customer ID", "disabled": true}, {"key": "table_id", "value": "", "description": "Filter by table ID", "disabled": true}, {"key": "order_type", "value": "", "description": "dine_in, takeaway, delivery", "disabled": true}, {"key": "date_from", "value": "", "description": "Filter from date (YYYY-MM-DD)", "disabled": true}, {"key": "date_to", "value": "", "description": "Filter to date (YYYY-MM-DD)", "disabled": true}, {"key": "per_page", "value": "15", "description": "Items per page"}, {"key": "page", "value": "1", "description": "Page number"}]}}}, {"name": "Create Order", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"customer_id\": 1,\n  \"table_id\": 1,\n  \"order_type\": \"dine_in\",\n  \"status\": \"pending\",\n  \"subtotal\": 25.50,\n  \"tax_amount\": 2.55,\n  \"discount_amount\": 0,\n  \"total_amount\": 28.05,\n  \"notes\": \"No spicy food\",\n  \"order_items\": [\n    {\n      \"menu_item_id\": 1,\n      \"quantity\": 2,\n      \"unit_price\": 12.75,\n      \"total_price\": 25.50,\n      \"notes\": \"Extra cheese\",\n      \"addons\": [\n        {\n          \"addon_id\": 1,\n          \"quantity\": 1,\n          \"price\": 2.00\n        }\n      ]\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/orders", "host": ["{{base_url}}"], "path": ["api", "orders"]}}}, {"name": "Get Order", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/orders/1", "host": ["{{base_url}}"], "path": ["api", "orders", "1"]}}}, {"name": "Update Order", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"customer_id\": 1,\n  \"table_id\": 2,\n  \"order_type\": \"dine_in\",\n  \"subtotal\": 30.00,\n  \"tax_amount\": 3.00,\n  \"discount_amount\": 5.00,\n  \"total_amount\": 28.00,\n  \"notes\": \"Updated order - no spicy food, extra napkins\"\n}"}, "url": {"raw": "{{base_url}}/api/orders/1", "host": ["{{base_url}}"], "path": ["api", "orders", "1"]}}}, {"name": "Delete Order", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/orders/1", "host": ["{{base_url}}"], "path": ["api", "orders", "1"]}}}]}, {"name": "Order Status Management", "item": [{"name": "Update Order Status", "request": {"method": "PATCH", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"confirmed\"\n}"}, "url": {"raw": "{{base_url}}/api/orders/1/status", "host": ["{{base_url}}"], "path": ["api", "orders", "1", "status"]}}}, {"name": "Confirm Order", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"notes\": \"Order confirmed by manager\"\n}"}, "url": {"raw": "{{base_url}}/api/orders/1/confirm", "host": ["{{base_url}}"], "path": ["api", "orders", "1", "confirm"]}}}]}, {"name": "Order Items Management", "item": [{"name": "Add Order Item", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"menu_item_id\": 2,\n  \"quantity\": 1,\n  \"unit_price\": 15.00,\n  \"total_price\": 15.00,\n  \"notes\": \"Medium spice level\",\n  \"addons\": [\n    {\n      \"addon_id\": 2,\n      \"quantity\": 1,\n      \"price\": 3.00\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/orders/1/items", "host": ["{{base_url}}"], "path": ["api", "orders", "1", "items"]}}}, {"name": "Remove Order Item", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/orders/1/items/1", "host": ["{{base_url}}"], "path": ["api", "orders", "1", "items", "1"]}}}, {"name": "Get Order Items", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/orders/1/items", "host": ["{{base_url}}"], "path": ["api", "orders", "1", "items"]}}}]}, {"name": "<PERSON>u Item Information", "item": [{"name": "Get <PERSON>u <PERSON>em <PERSON>", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/menu-items/1/addons", "host": ["{{base_url}}"], "path": ["api", "menu-items", "1", "addons"]}}}]}, {"name": "POS Operations", "item": [{"name": "Get POS Dashboard Data", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/pos/dashboard", "host": ["{{base_url}}"], "path": ["api", "pos", "dashboard"]}}}, {"name": "Get POS Form Data", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/pos/form-data", "host": ["{{base_url}}"], "path": ["api", "pos", "form-data"]}}}, {"name": "Create POS Order", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"customer_id\": 1,\n  \"table_id\": 1,\n  \"order_type\": \"dine_in\",\n  \"payment_method\": \"cash\",\n  \"subtotal\": 45.00,\n  \"tax_amount\": 4.50,\n  \"discount_amount\": 0,\n  \"total_amount\": 49.50,\n  \"notes\": \"POS Order - Table 1\",\n  \"order_items\": [\n    {\n      \"menu_item_id\": 1,\n      \"quantity\": 2,\n      \"unit_price\": 15.00,\n      \"total_price\": 30.00,\n      \"notes\": \"No onions\"\n    },\n    {\n      \"menu_item_id\": 2,\n      \"quantity\": 1,\n      \"unit_price\": 15.00,\n      \"total_price\": 15.00,\n      \"notes\": \"Extra spicy\",\n      \"addons\": [\n        {\n          \"addon_id\": 1,\n          \"quantity\": 1,\n          \"price\": 2.50\n        }\n      ]\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/pos/orders", "host": ["{{base_url}}"], "path": ["api", "pos", "orders"]}}}, {"name": "Get POS Menu Item <PERSON>s", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/pos/menu-items/1/addons", "host": ["{{base_url}}"], "path": ["api", "pos", "menu-items", "1", "addons"]}}}, {"name": "Get POS Menu Item Details", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/pos/menu-items/1/details", "host": ["{{base_url}}"], "path": ["api", "pos", "menu-items", "1", "details"]}}}, {"name": "Calculate Order Totals", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"order_items\": [\n    {\n      \"menu_item_id\": 1,\n      \"quantity\": 2,\n      \"unit_price\": 15.00,\n      \"addons\": [\n        {\n          \"addon_id\": 1,\n          \"quantity\": 1,\n          \"price\": 2.50\n        }\n      ]\n    },\n    {\n      \"menu_item_id\": 2,\n      \"quantity\": 1,\n      \"unit_price\": 12.00\n    }\n  ],\n  \"discount_percentage\": 10,\n  \"tax_percentage\": 10\n}"}, "url": {"raw": "{{base_url}}/api/pos/calculate-totals", "host": ["{{base_url}}"], "path": ["api", "pos", "calculate-totals"]}}}]}, {"name": "Public Endpoints", "item": [{"name": "Get Order Statuses", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/order-statuses", "host": ["{{base_url}}"], "path": ["api", "order-statuses"]}}}, {"name": "Get Order Types", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/order-types", "host": ["{{base_url}}"], "path": ["api", "order-types"]}}}]}]}