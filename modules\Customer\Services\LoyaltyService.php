<?php

namespace Modules\Customer\Services;

use App\Models\Customer;
use App\Models\LoyaltyTransaction;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Collection;

class LoyaltyService
{
    /**
     * Add loyalty points to customer
     */
    public function addPoints(Customer $customer, float $points, string $reason = 'Points added', ?int $orderId = null): LoyaltyTransaction
    {
        return DB::transaction(function () use ($customer, $points, $reason, $orderId) {
            // Update customer loyalty points
            $customer->increment('loyalty_points', $points);

            // Create loyalty transaction record
            return LoyaltyTransaction::create([
                'customer_id' => $customer->id,
                'order_id' => $orderId,
                'points' => $points,
                'type' => 'earned',
                'description' => $reason,
                'processed_by' => Auth::id(),
                'processed_at' => now(),
            ]);
        });
    }

    /**
     * Redeem loyalty points from customer
     */
    public function redeemPoints(Customer $customer, float $points, string $reason = 'Points redeemed', ?int $orderId = null): LoyaltyTransaction
    {
        if ($customer->loyalty_points < $points) {
            throw new \Exception('Insufficient loyalty points');
        }

        return DB::transaction(function () use ($customer, $points, $reason, $orderId) {
            // Update customer loyalty points
            $customer->decrement('loyalty_points', $points);

            // Create loyalty transaction record
            return LoyaltyTransaction::create([
                'customer_id' => $customer->id,
                'order_id' => $orderId,
                'points' => -$points,
                'type' => 'redeemed',
                'description' => $reason,
                'processed_by' => Auth::id(),
                'processed_at' => now(),
            ]);
        });
    }

    /**
     * Get customer loyalty history
     */
    public function getLoyaltyHistory(Customer $customer): Collection
    {
        return LoyaltyTransaction::where('customer_id', $customer->id)
            ->with(['processedBy', 'order'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get customer current points
     */
    public function getCurrentPoints(Customer $customer): float
    {
        return $customer->loyalty_points;
    }

    /**
     * Calculate points for order amount
     */
    public function calculatePointsForOrder(float $orderAmount): float
    {
        // 1 point for every $10 spent (configurable)
        $pointsPerDollar = 0.1;
        return floor($orderAmount * $pointsPerDollar);
    }

    /**
     * Calculate discount amount for points
     */
    public function calculateDiscountForPoints(float $points): float
    {
        // 1 point = $0.01 discount (configurable)
        $dollarPerPoint = 0.01;
        return $points * $dollarPerPoint;
    }

    /**
     * Get loyalty transactions for DataTables
     */
    public function getTransactionsData(Customer $customer, array $filters = [])
    {
        $query = LoyaltyTransaction::where('customer_id', $customer->id)
            ->with(['processedBy', 'order']);

        // Apply filters
        if (!empty($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        if (!empty($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        return $query->orderBy('created_at', 'desc');
    }

    /**
     * Get loyalty statistics for customer
     */
    public function getCustomerLoyaltyStats(Customer $customer): array
    {
        $totalEarned = LoyaltyTransaction::where('customer_id', $customer->id)
            ->where('type', 'earned')
            ->sum('points');

        $totalRedeemed = LoyaltyTransaction::where('customer_id', $customer->id)
            ->where('type', 'redeemed')
            ->sum('points');

        $transactionCount = LoyaltyTransaction::where('customer_id', $customer->id)->count();

        return [
            'current_points' => $customer->loyalty_points,
            'total_earned' => $totalEarned,
            'total_redeemed' => abs($totalRedeemed),
            'transaction_count' => $transactionCount,
        ];
    }

    /**
     * Get top loyalty customers
     */
    public function getTopLoyaltyCustomers(int $limit = 10): Collection
    {
        return Customer::where('loyalty_points', '>', 0)
            ->orderBy('loyalty_points', 'desc')
            ->limit($limit)
            ->get();
    }
}