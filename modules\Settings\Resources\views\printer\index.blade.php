@extends('layouts.app')

@section('title', 'إعدادات الطابعات')

@section('content')
<div class="container-fluid" dir="rtl">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('settings.index') }}">الإعدادات</a></li>
                        <li class="breadcrumb-item active">الطابعات</li>
                    </ol>
                </div>
                <h4 class="page-title">إعدادات الطابعات</h4>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-sm-6">
                            <h4 class="header-title">قائمة الطابعات</h4>
                        </div>
                        <div class="col-sm-6">
                            <div class="text-sm-end">
                                <a href="{{ route('settings.printer.create') }}" class="btn btn-success mb-2">
                                    <i class="mdi mdi-plus-circle me-1"></i> إضافة طابعة جديدة
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Filter Section -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="branch-filter" class="form-label">تصفية حسب الفرع</label>
                            <select class="form-select" id="branch-filter" name="branch_id">
                                <option value="">جميع الفروع</option>
                                @foreach($branches as $branch)
                                    <option value="{{ $branch->id }}" {{ request('branch_id') == $branch->id ? 'selected' : '' }}>
                                        {{ $branch->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="type-filter" class="form-label">تصفية حسب النوع</label>
                            <select class="form-select" id="type-filter" name="type">
                                <option value="">جميع الأنواع</option>
                                @foreach($printerTypes as $key => $value)
                                    <option value="{{ $key }}">{{ $value }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="button" class="btn btn-primary" id="apply-filters">
                                <i class="mdi mdi-filter me-1"></i> تطبيق التصفية
                            </button>
                        </div>
                    </div>

                    <!-- Printers Table -->
                    <div class="table-responsive">
                        <table class="table table-centered table-nowrap table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>اسم الطابعة</th>
                                    <th>النوع</th>
                                    <th>نوع الاتصال</th>
                                    <th>الفرع</th>
                                    <th>الحالة</th>
                                    <th>افتراضي</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($printers as $printer)
                                    <tr>
                                        <td>
                                            <h5 class="font-14 my-1">{{ $printer->name }}</h5>
                                            <span class="text-muted font-13">{{ $printer->paper_size ?? '80mm' }}</span>
                                        </td>
                                        <td>
                                            <span class="badge badge-soft-primary">
                                                {{ $printerTypes[$printer->type] ?? $printer->type }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-soft-info">
                                                {{ $connectionTypes[$printer->connection_type] ?? $printer->connection_type }}
                                            </span>
                                        </td>
                                        <td>
                                            {{ $printer->branch ? $printer->branch->name : 'جميع الفروع' }}
                                        </td>
                                        <td>
                                            @if($printer->is_active)
                                                <span class="badge badge-soft-success">نشط</span>
                                            @else
                                                <span class="badge badge-soft-danger">غير نشط</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($printer->is_default)
                                                <i class="mdi mdi-star text-warning" title="طابعة افتراضية"></i>
                                            @else
                                                <i class="mdi mdi-star-outline text-muted"></i>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-soft-primary btn-sm test-connection" 
                                                        data-id="{{ $printer->id }}" title="اختبار الاتصال">
                                                    <i class="mdi mdi-wifi"></i>
                                                </button>
                                                <a href="{{ route('settings.printer.edit', $printer->id) }}" 
                                                   class="btn btn-soft-info btn-sm" title="تعديل">
                                                    <i class="mdi mdi-pencil"></i>
                                                </a>
                                                <button type="button" class="btn btn-soft-danger btn-sm delete-printer" 
                                                        data-id="{{ $printer->id }}" title="حذف">
                                                    <i class="mdi mdi-delete"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="mdi mdi-printer-off font-24"></i>
                                                <p class="mt-2">لا توجد طابعات مكونة</p>
                                                <a href="{{ route('settings.printer.create') }}" class="btn btn-primary btn-sm">
                                                    إضافة طابعة جديدة
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Connection Test Modal -->
<div class="modal fade" id="connectionTestModal" tabindex="-1" aria-labelledby="connectionTestModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="connectionTestModalLabel">اختبار اتصال الطابعة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="connection-test-result">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري الاختبار...</span>
                        </div>
                        <p class="mt-2">جاري اختبار الاتصال...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Apply filters
    $('#apply-filters').click(function() {
        const branchId = $('#branch-filter').val();
        const type = $('#type-filter').val();
        
        let url = new URL(window.location.href);
        
        if (branchId) {
            url.searchParams.set('branch_id', branchId);
        } else {
            url.searchParams.delete('branch_id');
        }
        
        if (type) {
            url.searchParams.set('type', type);
        } else {
            url.searchParams.delete('type');
        }
        
        window.location.href = url.toString();
    });

    // Test connection
    $('.test-connection').click(function() {
        const printerId = $(this).data('id');
        
        $('#connectionTestModal').modal('show');
        $('#connection-test-result').html(`
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري الاختبار...</span>
                </div>
                <p class="mt-2">جاري اختبار الاتصال...</p>
            </div>
        `);
        
        $.ajax({
            url: `/api/settings/printers/${printerId}/test-connection`,
            method: 'POST',
            headers: {
                'Authorization': 'Bearer ' + localStorage.getItem('auth_token'),
                'Content-Type': 'application/json'
            },
            success: function(response) {
                if (response.success && response.data.connected) {
                    $('#connection-test-result').html(`
                        <div class="alert alert-success">
                            <i class="mdi mdi-check-circle me-2"></i>
                            تم الاتصال بالطابعة بنجاح
                        </div>
                        <p><strong>سلسلة الاتصال:</strong> ${response.data.connection_string}</p>
                    `);
                } else {
                    $('#connection-test-result').html(`
                        <div class="alert alert-danger">
                            <i class="mdi mdi-alert-circle me-2"></i>
                            فشل في الاتصال بالطابعة
                        </div>
                    `);
                }
            },
            error: function() {
                $('#connection-test-result').html(`
                    <div class="alert alert-danger">
                        <i class="mdi mdi-alert-circle me-2"></i>
                        حدث خطأ أثناء اختبار الاتصال
                    </div>
                `);
            }
        });
    });

    // Delete printer
    $('.delete-printer').click(function() {
        const printerId = $(this).data('id');
        
        if (confirm('هل أنت متأكد من حذف هذه الطابعة؟')) {
            $.ajax({
                url: `/api/settings/printers/${printerId}`,
                method: 'DELETE',
                headers: {
                    'Authorization': 'Bearer ' + localStorage.getItem('auth_token'),
                    'Content-Type': 'application/json'
                },
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert('فشل في حذف الطابعة');
                    }
                },
                error: function() {
                    alert('حدث خطأ أثناء حذف الطابعة');
                }
            });
        }
    });
});
</script>
@endsection
