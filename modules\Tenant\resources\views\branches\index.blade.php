@extends('layouts.master')

@section('title', 'إدارة الفروع')
@push('styles')
<meta name="csrf-token" content="{{ csrf_token() }}">
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
    /* Custom DataTables Tailwind Styling */
    .dataTables_wrapper {
        font-family: inherit;
    }
    
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_processing,
    .dataTables_wrapper .dataTables_paginate {
        color: #374151;
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }
    .dataTables_processing{
        margin: auto
    }
    .dataTables_wrapper .dataTables_length select,
    .dataTables_wrapper .dataTables_filter input {
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
    
    .dataTables_wrapper .dataTables_filter input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.5rem 0.75rem !important;
        margin: 0 0.125rem !important;
        border-radius: 0.375rem !important;
        border: 1px solid #d1d5db !important;
        background: white !important;
        color: #374151 !important;
        text-decoration: none !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background: #f3f4f6 !important;
        border-color: #9ca3af !important;
        color: #374151 !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background: #3b82f6 !important;
        border-color: #3b82f6 !important;
        color: white !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
        background: #f9fafb !important;
        color: #9ca3af !important;
        border-color: #e5e7eb !important;
    }
    
    table.dataTable {
        border-collapse: separate !important;
        border-spacing: 0 !important;
    }
    
    table.dataTable thead th {
        background-color: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
        font-weight: 600;
        color: #374151;
        padding: 0.75rem;
    }
    
    table.dataTable tbody td {
        padding: 0.75rem;
        border-bottom: 1px solid #f3f4f6;
    }
    
    table.dataTable tbody tr:hover {
        background-color: #f9fafb;
    }
    
    .select2-container--default .select2-selection--single {
        height: 42px;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        padding: 0.5rem;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 26px;
        padding-left: 0;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 40px;
    }
    
    .modal-backdrop {
        background-color: rgba(0, 0, 0, 0.5);
    }
    
    .animate-fadeIn {
        animation: fadeIn 0.3s ease-in-out;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: scale(0.95); }
        to { opacity: 1; transform: scale(1); }
    }
    
    .animate-slideDown {
        animation: slideDown 0.3s ease-in-out;
    }
    
    @keyframes slideDown {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>
@endpush
@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h1 class="text-2xl font-bold text-gray-900 flex items-center gap-3">
                    <i class="fas fa-building text-blue-600"></i>
                    إدارة الفروع
                </h1>
                <button onclick="openCreateModal()" 
                        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                    <i class="fas fa-plus"></i>
                    إضافة فرع جديد
                </button>
            </div>
        </div>
        
        <!-- Filters -->
        <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="statusFilter" class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                    <select id="statusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                    </select>
                </div>
                <div>
                    <label for="tenantFilter" class="block text-sm font-medium text-gray-700 mb-2">المستأجر</label>
                    <select id="tenantFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">جميع المستأجرين</option>
                        @foreach($tenants as $tenant)
                            <option value="{{ $tenant->id }}">{{ $tenant->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div>
                    <label for="serviceFilter" class="block text-sm font-medium text-gray-700 mb-2">الخدمات</label>
                    <select id="serviceFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">جميع الخدمات</option>
                        <option value="dine_in">تناول في المكان</option>
                        <option value="takeaway">استلام</option>
                        <option value="delivery">توصيل</option>
                        <option value="online_ordering">طلب أونلاين</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button onclick="resetFilters()" 
                            class="w-full bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors">
                        إعادة تعيين
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Branches Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">قائمة الفروع</h2>
        </div>
        <div class="overflow-x-auto">
            <table id="branchesTable" class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">#</th>
                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">اسم الفرع</th>
                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">الكود</th>
                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">المستأجر</th>
                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">معلومات الاتصال</th>
                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">المدير</th>
                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">الخدمات</th>
                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">الحالة</th>
                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <!-- DataTable will populate this -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Create Branch Modal -->
<div id="createModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">إضافة فرع جديد</h3>
            <button onclick="closeCreateModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <form id="createForm" action="{{ route('branches.store') }}" method="POST">
            @csrf
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Basic Information -->
                <div class="md:col-span-2">
                    <h4 class="text-md font-medium text-gray-900 mb-3 border-b pb-2">المعلومات الأساسية</h4>
                </div>
                
                <div>
                    <label for="tenant_id" class="block text-sm font-medium text-gray-700 mb-2">المستأجر *</label>
                    <select name="tenant_id" id="tenant_id" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">اختر المستأجر</option>
                        @foreach($tenants as $tenant)
                            <option value="{{ $tenant->id }}">{{ $tenant->name }}</option>
                        @endforeach
                    </select>
                </div>
                
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">اسم الفرع *</label>
                    <input type="text" name="name" id="name" required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <div>
                    <label for="code" class="block text-sm font-medium text-gray-700 mb-2">كود الفرع</label>
                    <input type="text" name="code" id="code" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="سيتم إنشاؤه تلقائياً إذا ترك فارغاً">
                </div>
                
                <div>
                    <label for="manager_name" class="block text-sm font-medium text-gray-700 mb-2">اسم المدير *</label>
                    <input type="text" name="manager_name" id="manager_name" required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <!-- Contact Information -->
                <div class="md:col-span-2">
                    <h4 class="text-md font-medium text-gray-900 mb-3 border-b pb-2 mt-4">معلومات الاتصال</h4>
                </div>
                
                <div class="md:col-span-2">
                    <label for="address" class="block text-sm font-medium text-gray-700 mb-2">العنوان *</label>
                    <textarea name="address" id="address" required rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"></textarea>
                </div>
                
                <div>
                    <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف *</label>
                    <input type="text" name="phone" id="phone" required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                    <input type="email" name="email" id="email" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <!-- Business Settings -->
                <div class="md:col-span-2">
                    <h4 class="text-md font-medium text-gray-900 mb-3 border-b pb-2 mt-4">إعدادات العمل</h4>
                </div>
                
                <div>
                    <label for="seating_capacity" class="block text-sm font-medium text-gray-700 mb-2">سعة الجلوس</label>
                    <input type="number" name="seating_capacity" id="seating_capacity" min="1"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <div>
                    <label for="delivery_radius" class="block text-sm font-medium text-gray-700 mb-2">نطاق التوصيل (كم)</label>
                    <input type="number" name="delivery_radius" id="delivery_radius" min="0" step="0.1"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <div>
                    <label for="timezone" class="block text-sm font-medium text-gray-700 mb-2">المنطقة الزمنية *</label>
                    <select name="timezone" id="timezone" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="Asia/Riyadh">Asia/Riyadh</option>
                        <option value="Asia/Dubai">Asia/Dubai</option>
                        <option value="Asia/Kuwait">Asia/Kuwait</option>
                        <option value="Asia/Qatar">Asia/Qatar</option>
                        <option value="Asia/Bahrain">Asia/Bahrain</option>
                    </select>
                </div>
                
                <!-- Services -->
                <div class="md:col-span-2">
                    <h4 class="text-md font-medium text-gray-900 mb-3 border-b pb-2 mt-4">الخدمات المتاحة</h4>
                </div>
                
                <div class="md:col-span-2">
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="flex items-center">
                            <input type="checkbox" name="is_dine_in_enabled" id="is_dine_in_enabled" value="1" checked
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="is_dine_in_enabled" class="mr-2 block text-sm text-gray-900">تناول في المكان</label>
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" name="is_takeaway_enabled" id="is_takeaway_enabled" value="1" checked
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="is_takeaway_enabled" class="mr-2 block text-sm text-gray-900">استلام</label>
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" name="is_delivery_enabled" id="is_delivery_enabled" value="1"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="is_delivery_enabled" class="mr-2 block text-sm text-gray-900">توصيل</label>
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" name="is_online_ordering_enabled" id="is_online_ordering_enabled" value="1"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="is_online_ordering_enabled" class="mr-2 block text-sm text-gray-900">طلب أونلاين</label>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="flex justify-end gap-3 mt-6 pt-4 border-t">
                <button type="button" onclick="closeCreateModal()" 
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 border border-gray-300 rounded-lg hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
                    إلغاء
                </button>
                <button type="submit" 
                        class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    حفظ الفرع
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Edit Branch Modal -->
<div id="editModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">تعديل الفرع</h3>
            <button onclick="closeEditModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <form id="editForm" method="POST">
            @csrf
            @method('PUT')
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Basic Information -->
                <div class="md:col-span-2">
                    <h4 class="text-md font-medium text-gray-900 mb-3 border-b pb-2">المعلومات الأساسية</h4>
                </div>
                
                <div>
                    <label for="edit_tenant_id" class="block text-sm font-medium text-gray-700 mb-2">المستأجر *</label>
                    <select name="tenant_id" id="edit_tenant_id" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">اختر المستأجر</option>
                        @foreach($tenants as $tenant)
                            <option value="{{ $tenant->id }}">{{ $tenant->name }}</option>
                        @endforeach
                    </select>
                </div>
                
                <div>
                    <label for="edit_name" class="block text-sm font-medium text-gray-700 mb-2">اسم الفرع *</label>
                    <input type="text" name="name" id="edit_name" required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <div>
                    <label for="edit_code" class="block text-sm font-medium text-gray-700 mb-2">كود الفرع</label>
                    <input type="text" name="code" id="edit_code" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <div>
                    <label for="edit_manager_name" class="block text-sm font-medium text-gray-700 mb-2">اسم المدير *</label>
                    <input type="text" name="manager_name" id="edit_manager_name" required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <!-- Contact Information -->
                <div class="md:col-span-2">
                    <h4 class="text-md font-medium text-gray-900 mb-3 border-b pb-2 mt-4">معلومات الاتصال</h4>
                </div>
                
                <div class="md:col-span-2">
                    <label for="edit_address" class="block text-sm font-medium text-gray-700 mb-2">العنوان *</label>
                    <textarea name="address" id="edit_address" required rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"></textarea>
                </div>
                
                <div>
                    <label for="edit_phone" class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف *</label>
                    <input type="text" name="phone" id="edit_phone" required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <div>
                    <label for="edit_email" class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                    <input type="email" name="email" id="edit_email" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <!-- Business Settings -->
                <div class="md:col-span-2">
                    <h4 class="text-md font-medium text-gray-900 mb-3 border-b pb-2 mt-4">إعدادات العمل</h4>
                </div>
                
                <div>
                    <label for="edit_seating_capacity" class="block text-sm font-medium text-gray-700 mb-2">سعة الجلوس</label>
                    <input type="number" name="seating_capacity" id="edit_seating_capacity" min="1"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <div>
                    <label for="edit_delivery_radius" class="block text-sm font-medium text-gray-700 mb-2">نطاق التوصيل (كم)</label>
                    <input type="number" name="delivery_radius" id="edit_delivery_radius" min="0" step="0.1"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <div>
                    <label for="edit_timezone" class="block text-sm font-medium text-gray-700 mb-2">المنطقة الزمنية *</label>
                    <select name="timezone" id="edit_timezone" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="Asia/Riyadh">Asia/Riyadh</option>
                        <option value="Asia/Dubai">Asia/Dubai</option>
                        <option value="Asia/Kuwait">Asia/Kuwait</option>
                        <option value="Asia/Qatar">Asia/Qatar</option>
                        <option value="Asia/Bahrain">Asia/Bahrain</option>
                    </select>
                </div>
                
                <!-- Services -->
                <div class="md:col-span-2">
                    <h4 class="text-md font-medium text-gray-900 mb-3 border-b pb-2 mt-4">الخدمات المتاحة</h4>
                </div>
                
                <div class="md:col-span-2">
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="flex items-center">
                            <input type="checkbox" name="is_dine_in_enabled" id="edit_is_dine_in_enabled" value="1"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="edit_is_dine_in_enabled" class="mr-2 block text-sm text-gray-900">تناول في المكان</label>
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" name="is_takeaway_enabled" id="edit_is_takeaway_enabled" value="1"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="edit_is_takeaway_enabled" class="mr-2 block text-sm text-gray-900">استلام</label>
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" name="is_delivery_enabled" id="edit_is_delivery_enabled" value="1"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="edit_is_delivery_enabled" class="mr-2 block text-sm text-gray-900">توصيل</label>
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" name="is_online_ordering_enabled" id="edit_is_online_ordering_enabled" value="1"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="edit_is_online_ordering_enabled" class="mr-2 block text-sm text-gray-900">طلب أونلاين</label>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="flex justify-end gap-3 mt-6 pt-4 border-t">
                <button type="button" onclick="closeEditModal()" 
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 border border-gray-300 rounded-lg hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
                    إلغاء
                </button>
                <button type="submit" 
                        class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    حفظ التغييرات
                </button>
            </div>
        </form>
    </div>
</div>

<!-- View Branch Modal -->
<div id="viewModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">تفاصيل الفرع</h3>
            <button onclick="closeViewModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div id="viewContent" class="space-y-4">
            <!-- Content will be populated by JavaScript -->
        </div>
        
        <div class="flex justify-end mt-6 pt-4 border-t">
            <button onclick="closeViewModal()" 
                    class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 border border-gray-300 rounded-lg hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
                إغلاق
            </button>
        </div>
    </div>
</div>

@endsection

@push('styles')
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.tailwindcss.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
@endpush

@push('scripts')
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.tailwindcss.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable
    window.branchesTable = $('#branchesTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("branches.data") }}',
            data: function(d) {
                d.status = $('#statusFilter').val();
                d.tenant_id = $('#tenantFilter').val();
                d.service = $('#serviceFilter').val();
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'name', name: 'name' },
            { data: 'code', name: 'code' },
            { data: 'tenant_name', name: 'tenant.name' },
            { data: 'contact_info', name: 'contact_info', orderable: false, searchable: false },
            { data: 'manager_name', name: 'manager_name' },
            { data: 'services', name: 'services', orderable: false, searchable: false },
            { data: 'status_badge', name: 'status' },
            { data: 'action', name: 'action', orderable: false, searchable: false }
        ],
        order: [[1, 'asc']],
        pageLength: 25,
        responsive: true,
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json'
        }
    });

    // Filter change handlers
    $('#statusFilter, #tenantFilter, #serviceFilter').change(function() {
        branchesTable.draw();
    });

    // Form submissions
    $('#createForm').on('submit', function(e) {
        e.preventDefault();
        
        $.ajax({
            url: $(this).attr('action'),
            method: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                closeCreateModal();
                branchesTable.draw();
                Swal.fire({
                    icon: 'success',
                    title: 'تم بنجاح!',
                    text: 'تم إنشاء الفرع بنجاح',
                    confirmButtonText: 'موافق'
                });
            },
            error: function(xhr) {
                let errors = xhr.responseJSON?.errors;
                if (errors) {
                    let errorMessage = Object.values(errors).flat().join('\n');
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ في البيانات!',
                        text: errorMessage,
                        confirmButtonText: 'موافق'
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ!',
                        text: 'حدث خطأ أثناء إنشاء الفرع',
                        confirmButtonText: 'موافق'
                    });
                }
            }
        });
    });

    $('#editForm').on('submit', function(e) {
        e.preventDefault();
        
        $.ajax({
            url: $(this).attr('action'),
            method: 'PUT',
            data: $(this).serialize(),
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                closeEditModal();
                branchesTable.draw();
                Swal.fire({
                    icon: 'success',
                    title: 'تم بنجاح!',
                    text: 'تم تحديث الفرع بنجاح',
                    confirmButtonText: 'موافق'
                });
            },
            error: function(xhr) {
                let errors = xhr.responseJSON?.errors;
                if (errors) {
                    let errorMessage = Object.values(errors).flat().join('\n');
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ في البيانات!',
                        text: errorMessage,
                        confirmButtonText: 'موافق'
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ!',
                        text: 'حدث خطأ أثناء تحديث الفرع',
                        confirmButtonText: 'موافق'
                    });
                }
            }
        });
    });
});

// Modal functions
function openCreateModal() {
    document.getElementById('createModal').classList.remove('hidden');
}

function closeCreateModal() {
    document.getElementById('createModal').classList.add('hidden');
    document.getElementById('createForm').reset();
}

function editBranch(branchId) {
    fetch(`/admin/branches/${branchId}/edit`, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        // Populate form fields
        document.getElementById('edit_tenant_id').value = data.tenant_id || '';
        document.getElementById('edit_name').value = data.name || '';
        document.getElementById('edit_code').value = data.code || '';
        document.getElementById('edit_manager_name').value = data.manager_name || '';
        document.getElementById('edit_address').value = data.address || '';
        document.getElementById('edit_phone').value = data.phone || '';
        document.getElementById('edit_email').value = data.email || '';
        document.getElementById('edit_seating_capacity').value = data.seating_capacity || '';
        document.getElementById('edit_delivery_radius').value = data.delivery_radius || '';
        document.getElementById('edit_timezone').value = data.timezone || '';
        
        // Set checkboxes
        document.getElementById('edit_is_dine_in_enabled').checked = data.is_dine_in_enabled;
        document.getElementById('edit_is_takeaway_enabled').checked = data.is_takeaway_enabled;
        document.getElementById('edit_is_delivery_enabled').checked = data.is_delivery_enabled;
        document.getElementById('edit_is_online_ordering_enabled').checked = data.is_online_ordering_enabled;
        
        // Set form action
        document.getElementById('editForm').action = `/admin/branches/${branchId}`;
        
        // Show modal
        document.getElementById('editModal').classList.remove('hidden');
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'خطأ!',
            text: 'حدث خطأ أثناء تحميل بيانات الفرع',
            confirmButtonText: 'موافق'
        });
    });
}

function closeEditModal() {
    document.getElementById('editModal').classList.add('hidden');
    document.getElementById('editForm').reset();
}

function viewBranch(branchId) {
    fetch(`/admin/branches/${branchId}`, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        const content = `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h4 class="font-medium text-gray-900 mb-2">المعلومات الأساسية</h4>
                    <div class="space-y-2 text-sm">
                        <div><span class="font-medium">الاسم:</span> ${data.name}</div>
                        <div><span class="font-medium">الكود:</span> ${data.code || '-'}</div>
                        <div><span class="font-medium">المستأجر:</span> ${data.tenant?.name || '-'}</div>
                        <div><span class="font-medium">المدير:</span> ${data.manager_name}</div>
                        <div><span class="font-medium">الحالة:</span> 
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${data.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                                ${data.status === 'active' ? 'نشط' : 'غير نشط'}
                            </span>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h4 class="font-medium text-gray-900 mb-2">معلومات الاتصال</h4>
                    <div class="space-y-2 text-sm">
                        <div><span class="font-medium">الهاتف:</span> ${data.phone}</div>
                        <div><span class="font-medium">البريد:</span> ${data.email || '-'}</div>
                        <div><span class="font-medium">العنوان:</span> ${data.address}</div>
                    </div>
                </div>
                
                <div>
                    <h4 class="font-medium text-gray-900 mb-2">إعدادات العمل</h4>
                    <div class="space-y-2 text-sm">
                        <div><span class="font-medium">سعة الجلوس:</span> ${data.seating_capacity || '-'}</div>
                        <div><span class="font-medium">نطاق التوصيل:</span> ${data.delivery_radius ? data.delivery_radius + ' كم' : '-'}</div>
                        <div><span class="font-medium">المنطقة الزمنية:</span> ${data.timezone}</div>
                    </div>
                </div>
                
                <div>
                    <h4 class="font-medium text-gray-900 mb-2">الخدمات المتاحة</h4>
                    <div class="space-y-2 text-sm">
                        <div class="flex flex-wrap gap-1">
                            ${data.is_dine_in_enabled ? '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">تناول في المكان</span>' : ''}
                            ${data.is_takeaway_enabled ? '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-cyan-100 text-cyan-800">استلام</span>' : ''}
                            ${data.is_delivery_enabled ? '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">توصيل</span>' : ''}
                            ${data.is_online_ordering_enabled ? '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">طلب أونلاين</span>' : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.getElementById('viewContent').innerHTML = content;
        document.getElementById('viewModal').classList.remove('hidden');
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'خطأ!',
            text: 'حدث خطأ أثناء تحميل بيانات الفرع',
            confirmButtonText: 'موافق'
        });
    });
}

function closeViewModal() {
    document.getElementById('viewModal').classList.add('hidden');
}

function deleteBranch(branchId) {
    Swal.fire({
        title: 'هل أنت متأكد؟',
        text: 'هل أنت متأكد من حذف هذا الفرع؟ هذا الإجراء لا يمكن التراجع عنه.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'نعم، احذف!',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`/admin/branches/${branchId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    branchesTable.draw();
                    Swal.fire({
                        icon: 'success',
                        title: 'تم الحذف!',
                        text: data.message || 'تم حذف الفرع بنجاح',
                        confirmButtonText: 'موافق'
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ!',
                        text: data.message || 'حدث خطأ أثناء حذف الفرع',
                        confirmButtonText: 'موافق'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ!',
                    text: 'حدث خطأ أثناء حذف الفرع',
                    confirmButtonText: 'موافق'
                });
            });
        }
    });
}

function changeStatus(branchId, action) {
    const status = action === 'activate' ? 'active' : 'inactive';
    const actions = {
        'activate': 'تفعيل',
        'deactivate': 'إلغاء تفعيل'
    };
    
    Swal.fire({
        title: 'تأكيد العملية',
        text: `هل أنت متأكد من ${actions[action]} هذا الفرع؟`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'نعم، تأكيد',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`/admin/branches/${branchId}/status`, {
                method: 'PATCH',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({ status: status })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    branchesTable.draw();
                    Swal.fire({
                        icon: 'success',
                        title: 'تم بنجاح!',
                        text: data.message || `تم ${actions[action]} الفرع بنجاح`,
                        confirmButtonText: 'موافق'
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ!',
                        text: data.message || `حدث خطأ أثناء ${actions[action]} الفرع`,
                        confirmButtonText: 'موافق'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ!',
                    text: `حدث خطأ أثناء ${actions[action]} الفرع`,
                    confirmButtonText: 'موافق'
                });
            });
        }
    });
}

function resetFilters() {
    $('#statusFilter').val('');
    $('#tenantFilter').val('');
    $('#serviceFilter').val('');
    branchesTable.draw();
}

// Close modals when clicking outside
window.onclick = function(event) {
    const modals = ['createModal', 'editModal', 'viewModal'];
    modals.forEach(modalId => {
        const modal = document.getElementById(modalId);
        if (event.target === modal) {
            closeModal(modalId);
        }
    });
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
    if (modalId === 'createModal') {
        document.getElementById('createForm').reset();
    } else if (modalId === 'editModal') {
        document.getElementById('editForm').reset();
    }
}
</script>
@endpush