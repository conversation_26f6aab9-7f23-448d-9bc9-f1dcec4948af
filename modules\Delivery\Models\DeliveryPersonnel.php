<?php

namespace Modules\Delivery\Models;

use App\Models\User;
use App\Models\Branch;
use App\Models\Tenant;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class DeliveryPersonnel extends Model
{
    use HasFactory;

    protected $table = 'delivery_personnel';

    protected $fillable = [
        'tenant_id',
        'user_id',
        'branch_id',
        'license_number',
        'license_expiry_date',
        'vehicle_type',
        'vehicle_plate_number',
        'vehicle_model',
        'phone_number',
        'emergency_contact_name',
        'emergency_contact_phone',
        'status',
        'current_latitude',
        'current_longitude',
        'last_location_update',
        'max_concurrent_deliveries',
        'delivery_radius_km',
        'rating',
        'total_deliveries',
        'total_earnings',
        'working_hours',
        'is_verified',
        'verified_at',
        'hourly_rate',
        'commission_rate',
    ];

    protected $casts = [
        'current_latitude' => 'decimal:8',
        'current_longitude' => 'decimal:8',
        'delivery_radius_km' => 'decimal:2',
        'rating' => 'decimal:2',
        'total_earnings' => 'decimal:2',
        'hourly_rate' => 'decimal:2',
        'commission_rate' => 'decimal:2',
        'working_hours' => 'array',
        'is_verified' => 'boolean',
        'last_location_update' => 'datetime',
        'verified_at' => 'datetime',
        'license_expiry_date' => 'date',
    ];

    /**
     * Get the tenant this delivery personnel belongs to.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the user associated with this delivery personnel.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the branch this delivery personnel belongs to.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get all delivery assignments for this personnel.
     */
    public function deliveryAssignments(): HasMany
    {
        return $this->hasMany(DeliveryAssignment::class);
    }

    /**
     * Get active delivery assignments.
     */
    public function activeDeliveries(): HasMany
    {
        return $this->hasMany(DeliveryAssignment::class)
            ->whereIn('status', ['assigned', 'picked_up', 'in_transit']);
    }

    /**
     * Check if personnel is available for new deliveries.
     */
    public function isAvailable(): bool
    {
        return $this->status === 'active' && 
               $this->is_verified && 
               $this->activeDeliveries()->count() < $this->max_concurrent_deliveries;
    }

    /**
     * Update location.
     */
    public function updateLocation(float $latitude, float $longitude): void
    {
        $this->update([
            'current_latitude' => $latitude,
            'current_longitude' => $longitude,
            'last_location_update' => now(),
        ]);
    }

    /**
     * Calculate distance from a point.
     */
    public function distanceFrom(float $latitude, float $longitude): float
    {
        if (!$this->current_latitude || !$this->current_longitude) {
            return PHP_FLOAT_MAX;
        }

        $earthRadius = 6371; // km
        $latDiff = deg2rad($latitude - $this->current_latitude);
        $lonDiff = deg2rad($longitude - $this->current_longitude);
        
        $a = sin($latDiff / 2) * sin($latDiff / 2) +
             cos(deg2rad($this->current_latitude)) * cos(deg2rad($latitude)) *
             sin($lonDiff / 2) * sin($lonDiff / 2);
        
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        
        return $earthRadius * $c;
    }

    /**
     * Scope for available personnel.
     */
    public function scopeAvailable($query)
    {
        return $query->where('status', 'active')->get();
                    // ->where('is_verified', true)
                    // ->where(function ($q) {
                    //     $q->whereDoesntHave('activeDeliveries')
                    //       ->orWhereHas('activeDeliveries', function ($subQuery) {
                    //           $subQuery->selectRaw('COUNT(*)')
                    //                    ->havingRaw('COUNT(*) < delivery_personnel.max_concurrent_deliveries');
                    //       });
                    // });
    }

    /**
     * Scope for personnel within radius.
     */
    public function scopeWithinRadius($query, float $latitude, float $longitude, float $radiusKm = 10)
    {
        return $query->whereRaw(
            '(6371 * acos(cos(radians(?)) * cos(radians(current_latitude)) * cos(radians(current_longitude) - radians(?)) + sin(radians(?)) * sin(radians(current_latitude)))) <= ?',
            [$latitude, $longitude, $latitude, $radiusKm]
        );
    }
}