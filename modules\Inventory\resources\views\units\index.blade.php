@extends('layouts.master')

@section('title', 'الوحدات')

@section('css')
    <!-- DataTables CSS -->
    <link href="{{ URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css') }}" rel="stylesheet" />
    <link href="{{ URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css') }}" rel="stylesheet">
    <link href="{{ URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css') }}" rel="stylesheet" />
    <link href="{{ URL::asset('assets/plugins/datatable/css/jquery.dataTables.min.css') }}" rel="stylesheet">
    <link href="{{ URL::asset('assets/plugins/datatable/css/responsive.dataTables.min.css') }}" rel="stylesheet">
    <link href="{{ URL::asset('assets/plugins/select2/css/select2.min.css') }}" rel="stylesheet">
@endsection

@section('page-header')
    <!-- breadcrumb -->
    <div class="breadcrumb-header justify-content-between">
        <div class="my-auto">
            <div class="d-flex">
                <h4 class="content-title mb-0 my-auto">المخزون</h4>
                <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ الوحدات</span>
            </div>
        </div>
    </div>
    <!-- breadcrumb -->
@endsection

@section('content')
    <!-- row -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card mg-b-20">
                <div class="card-header pb-0">
                    <div class="d-flex justify-content-between">
                        <h4 class="card-title mg-b-0">الوحدات</h4>
                        <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addUnitModal">
                            <i class="fas fa-plus"></i> إضافة وحدة جديدة
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="unitsTable" class="table key-buttons text-md-nowrap">
                            <thead>
                                <tr>
                                    <th class="border-bottom-0">#</th>
                                    <th class="border-bottom-0">اسم الوحدة</th>
                                    <th class="border-bottom-0">الاختصار</th>
                                    <th class="border-bottom-0">النوع</th>
                                    <th class="border-bottom-0">الوحدة الأساسية</th>
                                    <th class="border-bottom-0">معامل التحويل</th>
                                    <th class="border-bottom-0">الحالة</th>
                                    <th class="border-bottom-0">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- row closed -->

    @include('Inventory::units.modals')
@endsection

@section('js')
    <!-- DataTables JS -->
    <script src="{{ URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js') }}"></script>
    <script src="{{ URL::asset('assets/plugins/datatable/js/dataTables.dataTables.min.js') }}"></script>
    <script src="{{ URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js') }}"></script>
    <script src="{{ URL::asset('assets/plugins/datatable/js/responsive.dataTables.min.js') }}"></script>
    <script src="{{ URL::asset('assets/plugins/datatable/js/jquery.dataTables.js') }}"></script>
    <script src="{{ URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js') }}"></script>
    <script src="{{ URL::asset('assets/plugins/datatable/js/dataTables.buttons.min.js') }}"></script>
    <script src="{{ URL::asset('assets/plugins/datatable/js/buttons.bootstrap4.min.js') }}"></script>
    <script src="{{ URL::asset('assets/plugins/select2/js/select2.min.js') }}"></script>

    <script>
        $(document).ready(function() {
            // Initialize DataTable
            var table = $('#unitsTable').DataTable({
                processing: true,
                serverSide: true,
                ajax: "{{ route('inventory.api.units.datatable') }}",
                columns: [
                    {data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false},
                    {data: 'name', name: 'name'},
                    {data: 'abbreviation', name: 'abbreviation'},
                    {data: 'type', name: 'type'},
                    {data: 'base_unit_name', name: 'baseUnit.name'},
                    {data: 'conversion_factor', name: 'conversion_factor'},
                    {data: 'status', name: 'status'},
                    {data: 'action', name: 'action', orderable: false, searchable: false}
                ],
                language: {
                    url: "{{ asset('assets/plugins/datatable/Arabic.json') }}"
                }
            });

            // Initialize Select2
            $('.select2').select2();

            // Load base units
            loadBaseUnits();

            // Add Unit
            $('#addUnitForm').on('submit', function(e) {
                e.preventDefault();
                var formData = new FormData(this);
                
                $.ajax({
                    url: "{{ route('inventory.api.units.store') }}",
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        $('#addUnitModal').modal('hide');
                        table.ajax.reload();
                        showAlert('success', 'تم إضافة الوحدة بنجاح');
                        $('#addUnitForm')[0].reset();
                        loadBaseUnits();
                    },
                    error: function(xhr) {
                        var errors = xhr.responseJSON.errors;
                        var errorMessage = '';
                        $.each(errors, function(key, value) {
                            errorMessage += value[0] + '<br>';
                        });
                        showAlert('error', errorMessage);
                    }
                });
            });

            // Edit Unit
            $(document).on('click', '.edit-unit', function() {
                var id = $(this).data('id');
                
                $.ajax({
                    url: "{{ route('inventory.api.units.show', ':id') }}".replace(':id', id),
                    type: 'GET',
                    success: function(response) {
                        $('#edit_unit_id').val(response.id);
                        $('#edit_name').val(response.name);
                        $('#edit_abbreviation').val(response.abbreviation);
                        $('#edit_type').val(response.type).trigger('change');
                        $('#edit_base_unit_id').val(response.base_unit_id).trigger('change');
                        $('#edit_conversion_factor').val(response.conversion_factor);
                        $('#edit_is_active').prop('checked', response.is_active);
                        $('#editUnitModal').modal('show');
                    }
                });
            });

            // Update Unit
            $('#editUnitForm').on('submit', function(e) {
                e.preventDefault();
                var id = $('#edit_unit_id').val();
                var formData = new FormData(this);
                
                $.ajax({
                    url: "{{ route('inventory.api.units.update', ':id') }}".replace(':id', id),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        $('#editUnitModal').modal('hide');
                        table.ajax.reload();
                        showAlert('success', 'تم تحديث الوحدة بنجاح');
                        loadBaseUnits();
                    },
                    error: function(xhr) {
                        var errors = xhr.responseJSON.errors;
                        var errorMessage = '';
                        $.each(errors, function(key, value) {
                            errorMessage += value[0] + '<br>';
                        });
                        showAlert('error', errorMessage);
                    }
                });
            });

            // Delete Unit
            $(document).on('click', '.delete-unit', function() {
                var id = $(this).data('id');
                var name = $(this).data('name');
                
                if (confirm('هل أنت متأكد من حذف الوحدة "' + name + '"؟')) {
                    $.ajax({
                        url: "{{ route('inventory.api.units.destroy', ':id') }}".replace(':id', id),
                        type: 'DELETE',
                        data: {
                            _token: '{{ csrf_token() }}'
                        },
                        success: function(response) {
                            table.ajax.reload();
                            showAlert('success', 'تم حذف الوحدة بنجاح');
                            loadBaseUnits();
                        },
                        error: function(xhr) {
                            showAlert('error', xhr.responseJSON.message || 'حدث خطأ أثناء الحذف');
                        }
                    });
                }
            });

            // Handle base unit selection change
            $(document).on('change', '#base_unit_id, #edit_base_unit_id', function() {
                var baseUnitId = $(this).val();
                var conversionFactorInput = $(this).attr('id') === 'base_unit_id' ? '#conversion_factor' : '#edit_conversion_factor';
                
                if (baseUnitId === '' || baseUnitId === null) {
                    // This is a base unit
                    $(conversionFactorInput).val('1').prop('readonly', true);
                } else {
                    // This is a derived unit
                    $(conversionFactorInput).prop('readonly', false);
                    if ($(conversionFactorInput).val() === '1') {
                        $(conversionFactorInput).val('');
                    }
                }
            });

            function loadBaseUnits() {
                $.ajax({
                    url: "{{ route('inventory.api.units.list') }}",
                    type: 'GET',
                    success: function(response) {
                        var options = '<option value="">اختر الوحدة الأساسية</option>';
                        $.each(response, function(index, unit) {
                            options += '<option value="' + unit.id + '">' + unit.name + ' (' + unit.abbreviation + ')</option>';
                        });
                        $('#base_unit_id, #edit_base_unit_id').html(options);
                    }
                });
            }

            function showAlert(type, message) {
                var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
                var alert = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                           message +
                           '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                           '<span aria-hidden="true">&times;</span>' +
                           '</button>' +
                           '</div>';
                
                $('.card-body').prepend(alert);
                
                setTimeout(function() {
                    $('.alert').fadeOut();
                }, 5000);
            }
        });
    </script>
@endsection