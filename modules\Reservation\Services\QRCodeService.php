<?php

namespace Modules\Reservation\Services;

use App\Models\Table;
use Modules\Reservation\Contracts\QRCodeServiceInterface;
use Exception;

class QRCodeService implements QRCodeServiceInterface
{
    /**
     * Generate QR code data for table (frontend will generate the actual QR image).
     */
    public function generateTableQR(int $tableId, array $options = []): array
    {
        $table = Table::with(['branch.tenant'])->findOrFail($tableId);
        
        // Generate QR code if table doesn't have one
        if (!$table->qr_code) {
            $table->generateUniqueQRCode();
            $table->refresh();
        }
        
        $qrContent = $this->generateQRContent($table);
        
        $size = $options['size'] ?? config('reservation.qr_code.size', 200);
        
        return [
            'qr_data' => $qrContent,
            'size' => $size,
            'table_id' => $tableId,
            'type' => 'table'
        ];
    }

    /**
     * Generate QR code content.
     */
    public function generateQRContent(\App\Models\Table $table, string $type = 'table'): array
    {
        $baseUrl = config('app.url');
        
        // Generate hash based on branch name and table number
        $hash = $this->generateTableHash($table);
        
        // Get tenant slug for the new URL format
        // First try code, then generate slug from name, finally fallback to 'restaurant'
        $tenantSlug = $table->branch->tenant->code ?? $this->generateSlugFromName($table->branch->tenant->name);
        
        // Create the new URL format: domain/restaurant/{tenantSlug}/{qr_code}
        $tableUrl = $baseUrl . '/restaurant/' . $tenantSlug . '/' . $table->qr_code;
        
        $qrData = [
            'type' => $type,
            'table_id' => $table->id,
            'table_number' => $table->table_number,
            'branch_id' => $table->branch_id,
            'branch_name' => $table->branch->name ?? '',
            'tenant_slug' => $tenantSlug,
            'qr_code' => $table->qr_code,
            'url' => $tableUrl,
            'menu_url' => $tableUrl . '?action=menu',
            'order_url' => $tableUrl . '?action=order',
            'hash' => $hash,
        ];
        
        return $qrData;
    }

    /**
     * Generate a slug from tenant name.
     */
    private function generateSlugFromName(string $name): string
    {
        // Convert to lowercase, replace spaces with hyphens, and remove special characters
        $slug = strtolower(preg_replace('/[^a-zA-Z0-9\s-]/', '', $name));
        $slug = str_replace(' ', '-', $slug);
        $slug = preg_replace('/-+/', '-', $slug); // Replace multiple hyphens with single hyphen
        $slug = trim($slug, '-'); // Remove leading/trailing hyphens
        
        return $slug ?: 'restaurant';
    }

    /**
     * Generate the hash for a table.
     */
    private function generateTableHash(Table $table): string
    {
        // Create a hash based on branch name and table number
        $branchName = $table->branch->name ?? 'default';
        $tableNumber = $table->table_number;
        
        // Convert to lowercase and replace spaces with hyphens
        $hash = strtolower(str_replace(' ', '-', $branchName . '-' . $tableNumber));
        
        return $hash;
    }

    /**
     * Validate QR code.
     */
    public function validateQRCode(string $qrCode): array
    {
        try {
            $data = json_decode($qrCode, true);
            
            if (!$data || !isset($data['type']) || $data['type'] !== 'table') {
                return [
                    'valid' => false,
                    'error' => 'Invalid QR code format or type'
                ];
            }
            
            if (!isset($data['table_id']) || !isset($data['qr_code'])) {
                return [
                    'valid' => false,
                    'error' => 'Missing required QR code data'
                ];
            }
            
            // Verify table exists and QR code matches
            $table = Table::where('id', $data['table_id'])
                ->where('qr_code', $data['qr_code'])
                ->first();
            
            if ($table === null) {
                return [
                    'valid' => false,
                    'error' => 'Table not found or QR code mismatch'
                ];
            }
            
            return [
                'valid' => true,
                'table_id' => $data['table_id'],
                'qr_code' => $data['qr_code'],
                'table' => $table
            ];
        } catch (Exception $e) {
            return [
                'valid' => false,
                'error' => 'QR code validation failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get table information from QR code.
     */
    public function getTableFromQR(string $qrCode): ?array
    {
        try {
            $data = json_decode($qrCode, true);
            
            $validation = $this->validateQRCode($qrCode);
            
            if (!$validation['valid']) {
                return null;
            }
            
            $table = Table::with(['branch', 'area'])
                ->where('id', $data['table_id'])
                ->where('qr_code', $data['qr_code'])
                ->first();
            
            if (!$table) {
                return null;
            }
            
            return [
                'table' => $table,
                'qr_data' => $data,
                'is_valid' => true,
            ];
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * Generate unique QR code string.
     */
    public function generateUniqueQRString(int $tableId): string
    {
        $table = Table::findOrFail($tableId);
        
        do {
            $qrString = 'TBL_' . $table->branch_id . '_' . $tableId . '_' . strtoupper(uniqid());
        } while (Table::where('qr_code', $qrString)->exists());
        
        // Update table with new QR code
        $table->update(['qr_code' => $qrString]);
        
        return $qrString;
    }

    /**
     * Generate QR code data for menu access.
     */
    public function generateMenuQR(int $tableId, array $options = []): array
    {
        $table = Table::with(['branch'])->findOrFail($tableId);
        
        // Generate QR code if table doesn't have one
        if (!$table->qr_code) {
            $table->generateUniqueQRCode();
            $table->refresh();
        }
        
        // Generate hash and create URL in new format
        $hash = $this->generateTableHash($table);
        $menuUrl = config('app.url') . '/restaurant/table/' . $table->qr_code . '?hash=' . $hash . '&action=menu';
        
        $size = $options['size'] ?? config('reservation.qr_code.size', 200);
        
        return [
            'qr_data' => $menuUrl,
            'size' => $size,
            'table_id' => $tableId,
            'type' => 'menu'
        ];
    }

    /**
     * Generate QR code data for direct ordering.
     */
    public function generateOrderQR(int $tableId, array $options = []): array
    {
        $table = Table::with(['branch'])->findOrFail($tableId);
        
        // Generate QR code if table doesn't have one
        if (!$table->qr_code) {
            $table->generateUniqueQRCode();
            $table->refresh();
        }
        
        // Generate hash and create URL in new format
        $hash = $this->generateTableHash($table);
        $orderUrl = config('app.url') . '/restaurant/table/' . $table->qr_code . '?hash=' . $hash . '&action=order';
        
        $size = $options['size'] ?? config('reservation.qr_code.size', 200);
        
        return [
            'qr_data' => $orderUrl,
            'size' => $size,
            'table_id' => $tableId,
            'type' => 'order'
        ];
    }

    /**
     * Batch generate QR codes for multiple tables.
     */
    public function batchGenerate(array $tableIds, array $options = []): array
    {
        $results = [];
        
        foreach ($tableIds as $tableId) {
            try {
                $results[$tableId] = [
                    'success' => true,
                    'qr_data' => $this->generateTableQR($tableId, $options),
                    'menu_qr_data' => $this->generateMenuQR($tableId, $options),
                    'order_qr_data' => $this->generateOrderQR($tableId, $options),
                ];
            } catch (Exception $e) {
                $results[$tableId] = [
                    'success' => false,
                    'error' => $e->getMessage(),
                ];
            }
        }
        
        return $results;
    }
}