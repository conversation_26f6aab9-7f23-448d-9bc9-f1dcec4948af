<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\OrderItemAddon;

class OrderItemAddonSeeder extends Seeder
{
    public function run()
    {
        $orderItem = \App\Models\OrderItem::first();
        $addon = \App\Models\MenuItemAddon::first();

        if (! $orderItem || ! $addon) {
            $this->command->info('No OrderItem or MenuItemAddon found, skipping OrderItemAddon seeding.');
            return;
        }

        OrderItemAddon::create([
            'order_item_id' => $orderItem->id,
            'addon_id' => $addon->id,
            'order_item_id' => 1,
            'addon_id' => 1,
            'quantity' => 1,
            'unit_price' => 2.00,
            'total_price' => 2.00,
            // Add other required fields here
        ]);
    }
}