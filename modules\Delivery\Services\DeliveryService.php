<?php

namespace Modules\Delivery\Services;

use App\Models\Order;
use Modules\Delivery\Models\DeliveryAssignment;
use Modules\Delivery\Models\DeliveryPersonnel;
use Modules\Delivery\Models\DeliveryZone;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DeliveryService
{
    public function __construct(
        private DeliveryPersonnelService $personnelService,
        private DeliveryZoneService $zoneService
    ) {}

    /**
     * Create a delivery assignment for an order
     */
    public function createDeliveryAssignment(array $data): DeliveryAssignment
    {
        DB::beginTransaction();
        
        try {
            $order = Order::findOrFail($data['order_id']);
            
            // Validate order can be assigned for delivery
            if (!in_array($order->status, ['confirmed', 'preparing', 'ready'])) {
                throw new \Exception('Order is not ready for delivery assignment');
            }

            // Auto-assign personnel if not provided
            if (!isset($data['delivery_personnel_id'])) {
                $personnel = $this->autoAssignPersonnel($order);
                if (!$personnel) {
                    throw new \Exception('No available delivery personnel found');
                }
                $data['delivery_personnel_id'] = $personnel->id;
            }

            $assignment = DeliveryAssignment::create([
                'order_id' => $data['order_id'],
                'delivery_personnel_id' => $data['delivery_personnel_id'],
                'delivery_zone_id' => $data['delivery_zone_id'] ?? null,
                'external_delivery_id' => $data['external_delivery_id'] ?? null,
                'status' => 'assigned',
                'assigned_at' => now(),
                'estimated_duration_minutes' => $data['estimated_duration_minutes'] ?? 30,
                'delivery_fee_earned' => $data['delivery_fee_earned'] ?? $order->delivery_fee,
                'delivery_notes' => $data['delivery_notes'] ?? null,
            ]);

            // Update order status to confirmed (delivery assignment created)
            $order->update(['status' => 'confirmed']);

            // Update personnel status
            $personnel = DeliveryPersonnel::find($data['delivery_personnel_id']);
            if ($personnel->activeDeliveries()->count() >= $personnel->max_concurrent_deliveries) {
                $personnel->update(['status' => 'on_delivery']);
            }

            DB::commit();
            
            Log::info('Delivery assignment created', [
                'assignment_id' => $assignment->id,
                'order_id' => $order->id,
                'personnel_id' => $data['delivery_personnel_id']
            ]);

            return $assignment->load(['order', 'deliveryPersonnel', 'deliveryZone']);
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create delivery assignment', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Auto-assign available personnel (simplified - no location requirements)
     */
    public function autoAssignPersonnel(Order $order): ?DeliveryPersonnel
    {
        Log::info('Auto-assigning personnel for order', ['order_id' => $order->id]);

        // Simply get the first available personnel without location constraints
        $personnel = DeliveryPersonnel::available()->first();

        Log::info('Auto-assign result', [
            'order_id' => $order->id,
            'personnel_found' => $personnel ? $personnel->id : null
        ]);

        return $personnel;
    }

    /**
     * Update delivery status using order status
     */
    public function updateDeliveryStatus(int $assignmentId, string $status, array $data = []): DeliveryAssignment
    {
        DB::beginTransaction();
        
        try {
            $assignment = DeliveryAssignment::with(['order', 'deliveryPersonnel'])->findOrFail($assignmentId);
            
            // Update assignment based on status
            switch ($status) {
                case 'picked_up':
                    $assignment->update([
                        'status' => 'picked_up',
                        'picked_up_at' => now(),
                    ]);
                    $assignment->order->update(['status' => 'out_for_delivery']);
                    break;
                    
                case 'in_transit':
                    $assignment->update([
                        'status' => 'in_transit',
                        'picked_up_at' => $assignment->picked_up_at ?? now(),
                    ]);
                    $assignment->order->update(['status' => 'out_for_delivery']);
                    break;
                    
                case 'delivered':
                    $deliveredAt = now();
                    $actualDuration = $assignment->picked_up_at ? 
                        $assignment->picked_up_at->diffInMinutes($deliveredAt) : null;
                        
                    $assignment->update([
                        'status' => 'delivered',
                        'delivered_at' => $deliveredAt,
                        'delivery_proof' => $data['delivery_proof'] ?? null,
                        'actual_duration_minutes' => $actualDuration,
                        'delivery_notes' => $data['delivery_notes'] ?? $assignment->delivery_notes,
                    ]);
                    
                    $assignment->order->update(['status' => 'delivered']);
                    
                    // Update personnel stats and status
                    $assignment->deliveryPersonnel->increment('total_deliveries');
                    if ($assignment->delivery_fee_earned) {
                        $assignment->deliveryPersonnel->increment('total_earnings', $assignment->delivery_fee_earned);
                    }
                    
                    // Check if personnel can take more deliveries
                    if ($assignment->deliveryPersonnel->activeDeliveries()->count() <= 1) {
                        $assignment->deliveryPersonnel->update(['status' => 'active']);
                    }
                    break;
                    
                case 'failed':
                    $assignment->update([
                        'status' => 'failed',
                        'failure_reason' => $data['failure_reason'] ?? 'Delivery failed',
                    ]);
                    $assignment->order->update(['status' => 'delivery_failed']);
                    $assignment->deliveryPersonnel->update(['status' => 'active']);
                    break;
                    
                case 'cancelled':
                    $assignment->update([
                        'status' => 'cancelled',
                        'failure_reason' => $data['cancellation_reason'] ?? 'Delivery cancelled',
                    ]);
                    $assignment->order->update(['status' => 'cancelled']);
                    $assignment->deliveryPersonnel->update(['status' => 'active']);
                    break;
                    
                default:
                    throw new \Exception("Invalid delivery status: {$status}");
            }

            DB::commit();
            
            Log::info('Delivery status updated', [
                'assignment_id' => $assignmentId,
                'status' => $status,
                'order_id' => $assignment->order_id
            ]);

            return $assignment->fresh(['order', 'deliveryPersonnel', 'deliveryZone']);
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update delivery status', [
                'assignment_id' => $assignmentId,
                'status' => $status,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get delivery assignments with filters
     */
    public function getDeliveryAssignments(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = DeliveryAssignment::with(['order', 'deliveryPersonnel.user', 'deliveryZone']);

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['personnel_id'])) {
            $query->where('delivery_personnel_id', $filters['personnel_id']);
        }

        if (isset($filters['branch_id'])) {
            $query->whereHas('order', function ($q) use ($filters) {
                $q->where('branch_id', $filters['branch_id']);
            });
        }

        if (isset($filters['date_from'])) {
            $query->whereDate('assigned_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('assigned_at', '<=', $filters['date_to']);
        }

        return $query->orderBy('assigned_at', 'desc')->paginate($perPage);
    }

    /**
     * Get active deliveries for a personnel
     */
    public function getActiveDeliveriesForPersonnel(int $personnelId): Collection
    {
        return DeliveryAssignment::with(['order', 'deliveryZone'])
            ->where('delivery_personnel_id', $personnelId)
            ->whereIn('status', ['assigned', 'picked_up', 'in_transit'])
            ->orderBy('assigned_at')
            ->get();
    }

    /**
     * Calculate delivery fee based on zone and distance
     */
    public function calculateDeliveryFee(float $lat, float $lng, ?int $branchId = null): array
    {
        $zone = $this->zoneService->findZoneByCoordinates($lat, $lng);
        
        if (!$zone) {
            return [
                'fee' => 0,
                'zone' => null,
                'message' => 'Delivery not available in this area'
            ];
        }

        return [
            'fee' => $zone->delivery_fee,
            'zone' => $zone,
            'minimum_order' => $zone->minimum_order_amount,
            'estimated_time' => $zone->estimated_delivery_time_minutes,
            'message' => 'Delivery available'
        ];
    }

    /**
     * Update delivery assignment
     */
    public function updateDeliveryAssignment(int $assignmentId, array $data): DeliveryAssignment
    {
        DB::beginTransaction();
        
        try {
            $assignment = DeliveryAssignment::with(['order', 'deliveryPersonnel'])->findOrFail($assignmentId);
            
            // Validate assignment can be updated
            if (in_array($assignment->status, ['delivered', 'cancelled'])) {
                throw new \Exception('Cannot update completed or cancelled delivery assignment');
            }
            
            $updateData = [];
            
            // Handle personnel change
            if (isset($data['delivery_personnel_id']) && 
                $data['delivery_personnel_id'] != $assignment->delivery_personnel_id) {
                
                // Validate new personnel exists and is available
                $newPersonnel = DeliveryPersonnel::findOrFail($data['delivery_personnel_id']);
                if (!$newPersonnel->is_available) {
                    throw new \Exception('Selected delivery personnel is not available');
                }
                
                // Update old personnel status
                $oldPersonnel = $assignment->deliveryPersonnel;
                if ($oldPersonnel && $oldPersonnel->activeDeliveries()->count() <= 1) {
                    $oldPersonnel->update(['status' => 'active']);
                }
                
                $updateData['delivery_personnel_id'] = $data['delivery_personnel_id'];
                $updateData['assigned_at'] = now();
                
                // Update new personnel status
                if ($newPersonnel->activeDeliveries()->count() + 1 >= $newPersonnel->max_concurrent_deliveries) {
                    $newPersonnel->update(['status' => 'on_delivery']);
                }
            }
            
            // Handle other updateable fields
            $allowedFields = [
                'pickup_time', 'delivery_time', 'special_instructions', 
                'priority', 'delivery_notes', 'estimated_duration_minutes'
            ];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = $data[$field];
                }
            }
            
            // Update the assignment
            if (!empty($updateData)) {
                $assignment->update($updateData);
            }
            
            DB::commit();
            
            Log::info('Delivery assignment updated', [
                'assignment_id' => $assignmentId,
                'updated_fields' => array_keys($updateData)
            ]);
            
            return $assignment->fresh(['order', 'deliveryPersonnel', 'deliveryZone']);
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update delivery assignment', [
                'assignment_id' => $assignmentId,
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Get delivery statistics
     */
    public function getDeliveryStatistics(array $filters = []): array
    {
        $query = DeliveryAssignment::query();

        if (isset($filters['date_from'])) {
            $query->whereDate('assigned_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('assigned_at', '<=', $filters['date_to']);
        }

        if (isset($filters['branch_id'])) {
            $query->whereHas('order', function ($q) use ($filters) {
                $q->where('branch_id', $filters['branch_id']);
            });
        }

        $stats = $query->selectRaw('
            COUNT(*) as total_deliveries,
            COUNT(CASE WHEN status = "delivered" THEN 1 END) as successful_deliveries,
            COUNT(CASE WHEN status = "failed" THEN 1 END) as failed_deliveries,
            COUNT(CASE WHEN status = "cancelled" THEN 1 END) as cancelled_deliveries,
            AVG(CASE WHEN status = "delivered" THEN actual_duration_minutes END) as avg_delivery_time,
            SUM(CASE WHEN status = "delivered" THEN delivery_fee_earned ELSE 0 END) as total_earnings
        ')->first();

        $successRate = $stats->total_deliveries > 0 ? 
            ($stats->successful_deliveries / $stats->total_deliveries) * 100 : 0;

        return [
            'total_deliveries' => $stats->total_deliveries,
            'successful_deliveries' => $stats->successful_deliveries,
            'failed_deliveries' => $stats->failed_deliveries,
            'cancelled_deliveries' => $stats->cancelled_deliveries,
            'success_rate' => round($successRate, 2),
            'average_delivery_time' => round($stats->avg_delivery_time ?? 0, 2),
            'total_earnings' => $stats->total_earnings ?? 0,
        ];
    }
}