@extends('layouts.master')

@section('title', 'فئات المواد')

@section('page-header')
<!-- Breadcrumb -->
<nav class="flex mb-8" aria-label="Breadcrumb">
    <ol class="inline-flex items-center space-x-1 md:space-x-3 rtl:space-x-reverse">
        <li class="inline-flex items-center">
            <a href="{{ route('dashboard') }}" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                <i class="fas fa-home ml-2"></i>
                الرئيسية
            </a>
        </li>
        <li>
            <div class="flex items-center">
                <i class="fas fa-chevron-left text-gray-400 mx-2"></i>
                <a href="#" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">المخزون</a>
            </div>
        </li>
        <li aria-current="page">
            <div class="flex items-center">
                <i class="fas fa-chevron-left text-gray-400 mx-2"></i>
                <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">فئات المواد</span>
            </div>
        </li>
    </ol>
</nav>
@endsection

@section('content')
<div class="space-y-6">
    <!-- Page Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 flex items-center gap-3">
                        <div class="w-10 h-10 bg-gradient-to-r from-emerald-600 to-emerald-700 rounded-lg flex items-center justify-center">
                            <i class="fas fa-tags text-white"></i>
                        </div>
                        فئات المواد
                    </h1>
                    <p class="mt-1 text-sm text-gray-600">إدارة وتنظيم فئات المواد والمنتجات</p>
                </div>
                <button type="button" onclick="openModal('addCategoryModal')" class="px-6 py-3 bg-gradient-to-r from-emerald-600 to-emerald-700 text-white font-medium rounded-lg hover:from-emerald-700 hover:to-emerald-800 transition-all duration-200 flex items-center gap-2 shadow-lg hover:shadow-xl">
                    <i class="fas fa-plus"></i>
                    إضافة فئة جديدة
                </button>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="statusFilter" class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                    <select id="statusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                    </select>
                </div>
                <div>
                    <label for="parentFilter" class="block text-sm font-medium text-gray-700 mb-2">الفئة الأساسية</label>
                    <select id="parentFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                        <option value="">جميع الفئات</option>
                        <option value="main">فئات رئيسية</option>
                        <option value="sub">فئات فرعية</option>
                    </select>
                </div>
                <div>
                    <label for="productsCountFilter" class="block text-sm font-medium text-gray-700 mb-2">عدد المنتجات</label>
                    <select id="productsCountFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                        <option value="">الكل</option>
                        <option value="empty">فارغة</option>
                        <option value="has_products">تحتوي على منتجات</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button type="button" id="resetFilters" class="w-full px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg font-medium transition-colors duration-200 flex items-center justify-center gap-2">
                        <i class="fas fa-undo"></i>
                        إعادة تعيين
                    </button>
                </div>
            </div>
        </div>

        <!-- DataTable Controls -->
        <div class="p-6 border-b border-gray-200">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div class="flex flex-col sm:flex-row gap-4">
                    <!-- Global Search -->
                    <div class="relative">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <input type="text" id="globalSearch" placeholder="البحث في الفئات..." class="w-full sm:w-64 pl-3 pr-10 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                    </div>
                </div>

                <!-- Export Buttons -->
                <div class="flex gap-2">
                    <button type="button" id="exportExcel" class="px-3 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center gap-2">
                        <i class="fas fa-file-excel"></i>
                        Excel
                    </button>
                    <button type="button" id="exportPdf" class="px-3 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 transition-colors duration-200 flex items-center gap-2">
                        <i class="fas fa-file-pdf"></i>
                        PDF
                    </button>
                    <button type="button" id="printTable" class="px-3 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 transition-colors duration-200 flex items-center gap-2">
                        <i class="fas fa-print"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>

        <!-- DataTable Container -->
        <div class="overflow-hidden">
            <div class="overflow-x-auto">
                <table id="categoriesTable" class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">#</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">اسم الفئة</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">الفئة الأساسية</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">عدد المنتجات</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">ترتيب العرض</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">الحالة</th>
                            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                    </tbody>
                </table>
            </div>

            <!-- DataTable Info and Pagination -->
            <div class="bg-gray-50 px-6 py-3 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700" id="tableInfo">
                        <!-- DataTable info will be inserted here -->
                    </div>
                    <div id="tablePagination">
                        <!-- DataTable pagination will be inserted here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@include('inventory::categories.modals')
@endsection

@push('styles')
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.dataTables.min.css">
<style>
    /* Custom DataTable styling for Tailwind */
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_processing,
    .dataTables_wrapper .dataTables_paginate {
        color: #374151;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.5rem 0.75rem;
        margin: 0 0.125rem;
        border-radius: 0.375rem;
        border: 1px solid #d1d5db;
        background: white;
        color: #374151;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background: #f3f4f6;
        border-color: #9ca3af;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background: #10b981;
        border-color: #10b981;
        color: white;
    }
</style>
@endpush

@push('scripts')
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>

<!-- Modal Functions -->
<script>
function openModal(modalId) {
    document.getElementById(modalId).classList.remove('hidden');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

// Close modal when clicking outside
document.addEventListener('click', function(event) {
    const modals = ['addCategoryModal', 'editCategoryModal', 'statusModal'];
    modals.forEach(modalId => {
        const modal = document.getElementById(modalId);
        if (event.target === modal) {
            closeModal(modalId);
        }
    });
});

// Close modal with Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        const modals = ['addCategoryModal', 'editCategoryModal', 'statusModal'];
        modals.forEach(modalId => {
            const modal = document.getElementById(modalId);
            if (!modal.classList.contains('hidden')) {
                closeModal(modalId);
            }
        });
    }
});

$(document).ready(function() {
    let table = $('#categoriesTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: "{{ route('inventory.api.categories.datatable') }}",
            data: function(d) {
                d.status = $('#statusFilter').val();
                d.parent_filter = $('#parentFilter').val();
                d.products_count_filter = $('#productsCountFilter').val();
            }
        },
        columns: [
            {data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false},
            {data: 'name', name: 'name'},
            {data: 'parent_name', name: 'parent.name'},
            {data: 'products_count', name: 'products_count'},
            {data: 'sort_order', name: 'sort_order'},
            {data: 'status', name: 'status'},
            {data: 'action', name: 'action', orderable: false, searchable: false}
        ],
        order: [[0, 'desc']],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json'
        },
        responsive: true,
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        dom: 'Bfrtip',
        buttons: [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ]
    });

    // Enhanced Search and Filter Functionality
    $('#globalSearch').on('keyup', function() {
        table.search(this.value).draw();
    });

    // Export Functions
    $('#exportExcel').on('click', function() {
        table.button('.buttons-excel').trigger();
    });

    $('#exportPdf').on('click', function() {
        table.button('.buttons-pdf').trigger();
    });

    $('#printTable').on('click', function() {
        table.button('.buttons-print').trigger();
    });

    // Filter change handlers
    $('#statusFilter, #parentFilter, #productsCountFilter').change(function() {
        table.draw();
    });

    // Reset filters
    $('#resetFilters').click(function() {
        $('#statusFilter, #parentFilter, #productsCountFilter').val('');
        table.draw();
    });

    // Load parent categories
    loadParentCategories();

    // Add Category
    $('#addCategoryForm').on('submit', function(e) {
        e.preventDefault();
        var formData = new FormData(this);

        $.ajax({
            url: "{{ route('inventory.api.categories.store') }}",
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                closeModal('addCategoryModal');
                table.ajax.reload();
                showToast('تم إضافة الفئة بنجاح', 'success');
                $('#addCategoryForm')[0].reset();
                loadParentCategories();
            },
            error: function(xhr) {
                let message = xhr.responseJSON?.message || 'حدث خطأ أثناء إضافة الفئة';
                showToast(message, 'error');
            }
        });
    });

    // Edit Category
    $(document).on('click', '.edit-category', function() {
        var id = $(this).data('id');

        $.ajax({
            url: "{{ route('inventory.api.categories.show', ':id') }}".replace(':id', id),
            type: 'GET',
            success: function(response) {
                $('#edit_category_id').val(response.id);
                $('#edit_name').val(response.name);
                $('#edit_description').val(response.description);
                $('#edit_parent_id').val(response.parent_id);
                $('#edit_sort_order').val(response.sort_order);
                $('#edit_is_active').prop('checked', response.is_active);
                openModal('editCategoryModal');
            }
        });
    });

    // Update Category
    $('#editCategoryForm').on('submit', function(e) {
        e.preventDefault();
        var id = $('#edit_category_id').val();
        var formData = new FormData(this);

        $.ajax({
            url: "{{ route('inventory.api.categories.update', ':id') }}".replace(':id', id),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                closeModal('editCategoryModal');
                table.ajax.reload();
                showToast('تم تحديث الفئة بنجاح', 'success');
                loadParentCategories();
            },
            error: function(xhr) {
                let message = xhr.responseJSON?.message || 'حدث خطأ أثناء تحديث الفئة';
                showToast(message, 'error');
            }
        });
    });

    // Delete Category
    $(document).on('click', '.delete-category', function() {
        var id = $(this).data('id');
        var name = $(this).data('name');

        if (confirm('هل أنت متأكد من حذف الفئة "' + name + '"؟')) {
            $.ajax({
                url: "{{ route('inventory.api.categories.destroy', ':id') }}".replace(':id', id),
                type: 'DELETE',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    table.ajax.reload();
                    showToast('تم حذف الفئة بنجاح', 'success');
                    loadParentCategories();
                },
                error: function(xhr) {
                    showToast(xhr.responseJSON?.message || 'حدث خطأ أثناء الحذف', 'error');
                }
            });
        }
    });

    function loadParentCategories() {
        $.ajax({
            url: "{{ route('inventory.api.categories.list') }}",
            type: 'GET',
            success: function(response) {
                var options = '<option value="">اختر الفئة الأساسية</option>';
                $.each(response, function(index, category) {
                    options += '<option value="' + category.id + '">' + category.name + '</option>';
                });
                $('#parent_id, #edit_parent_id').html(options);
            }
        });
    }

    // Toast notification function
    window.showToast = function(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg text-white font-medium ${
            type === 'success' ? 'bg-green-600' :
            type === 'error' ? 'bg-red-600' :
            type === 'warning' ? 'bg-yellow-600' : 'bg-blue-600'
        }`;
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.remove();
        }, 3000);
    };
});
</script>
@endpush