<?php

namespace Modules\Reservation\Contracts;

use App\Models\WaiterRequest;
use Illuminate\Database\Eloquent\Collection;

interface WaiterRequestServiceInterface
{
    /**
     * Get all waiter requests with optional filters.
     */
    public function getAllRequests(array $filters = []): Collection;

    /**
     * Create a new waiter request.
     */
    public function createRequest(array $data): WaiterRequest;

    /**
     * Get a waiter request by ID.
     */
    public function getRequestById(int $id): WaiterRequest;

    /**
     * Update a waiter request.
     */
    public function updateRequest(int $id, array $data): WaiterRequest;

    /**
     * Delete a waiter request.
     */
    public function deleteRequest(int $id): bool;

    /**
     * Complete a waiter request.
     */
    public function completeRequest(int $id): WaiterRequest;

    /**
     * Cancel a waiter request.
     */
    public function cancelRequest(int $id): WaiterRequest;

    /**
     * Get requests for a specific waiter.
     */
    public function getWaiterRequests(int $waiterId): Collection;

    /**
     * Get requests for a specific table.
     */
    public function getTableRequests(int $tableId): Collection;

    /**
     * Get pending requests.
     */
    public function getPendingRequests(): Collection;

    /**
     * Assign a waiter to a request.
     */
    public function assignWaiter(int $requestId, int $waiterId): WaiterRequest;
}