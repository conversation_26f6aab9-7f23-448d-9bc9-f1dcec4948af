<?php

namespace Modules\Menu\Http\Controllers\Api;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Modules\Menu\Http\Requests\StoreCategoryRequest;
use Modules\Menu\Http\Requests\UpdateCategoryRequest;
use Modules\Menu\Services\CategoryService;
use App\Helpers\BranchHelper;
use App\Helpers\ResponseHelper;

class CategoryController extends Controller
{
    protected $categoryService;

    public function __construct(CategoryService $categoryService)
    {
        $this->categoryService = $categoryService;
    }

    /**
     * Display a listing of categories
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            
            // Convert request parameters to filters array
            $filters = $request->only([
                'menu_id', 'is_active', 'parent_category_id', 
                'search', 'sort_by', 'sort_direction', 'per_page'
            ]);
            
            $categories = $this->categoryService->getCategoriesForBranch($branchId, $filters);
            
            return ResponseHelper::success($categories, 'Categories retrieved successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve categories: ' . $e->getMessage());
        }
    }

    /**
     * Store a newly created category
     */
    public function store(StoreCategoryRequest $request): JsonResponse
    {
        try {
            $data = BranchHelper::addBranchAndTenantIds($request->validated());
            $category = $this->categoryService->createCategory($data);
            
            return ResponseHelper::success($category, 'Category created successfully', 201);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to create category: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified category
     */
    public function show(int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $category = $this->categoryService->getCategoryByIdForBranch($id, $branchId);
            
            if (!$category) {
                return ResponseHelper::notFound('Category not found');
            }
            
            return ResponseHelper::success($category, 'Category retrieved successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve category: ' . $e->getMessage());
        }
    }

    /**
     * Update the specified category
     */
    public function update(UpdateCategoryRequest $request, int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $category = $this->categoryService->updateCategoryForBranch($id, $request->validated(), $branchId);
            
            if (!$category) {
                return ResponseHelper::notFound('Category not found');
            }
            
            return ResponseHelper::success($category, 'Category updated successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to update category: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified category
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $deleted = $this->categoryService->deleteCategoryForBranch($id, $branchId);
            
            if (!$deleted) {
                return ResponseHelper::notFound('Category not found');
            }
            
            return ResponseHelper::success(null, 'Category deleted successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to delete category: ' . $e->getMessage());
        }
    }
}