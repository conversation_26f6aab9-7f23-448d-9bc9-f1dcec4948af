<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Tenant;
use App\Models\User;
use Modules\Transaction\Models\Transaction;
use Modules\Transaction\Models\Payment;
use Modules\Transaction\Models\PaymentMethod;

try {
    // Get the tenant and user
    $tenant = Tenant::where('code', 'DEFAULT_TENANT')->first();
    $user = User::where('email', '<EMAIL>')->first();
    
    if (!$tenant || !$user) {
        echo "Error: Tenant or user not found\n";
        exit(1);
    }
    
    echo "Using Tenant ID: {$tenant->id}, User ID: {$user->id}\n";
    
    // Get payment methods
    $paymentMethods = PaymentMethod::where('is_active', true)->get();
    echo "Found " . $paymentMethods->count() . " payment methods\n";
    
    // Create sample transactions and payments
    for ($i = 1; $i <= 5; $i++) {
        $amount = rand(50, 200);
        
        // Create a transaction without order
        $transaction = Transaction::create([
            'tenant_id' => $tenant->id,
            'order_id' => null, // No order for now
            'transaction_number' => 'TXN-' . str_pad($i, 6, '0', STR_PAD_LEFT),
            'transaction_type' => 'sale',
            'total_amount' => $amount,
            'paid_amount' => $amount,
            'due_amount' => 0,
            'status' => 'completed',
            'processed_by' => $user->id,
            'created_at' => now()->subDays(rand(0, 30)),
        ]);
        
        // Create a payment for the transaction
        $paymentMethod = $paymentMethods->random();
        $payment = Payment::create([
            'tenant_id' => $tenant->id,
            'transaction_id' => $transaction->id,
            'payment_method_id' => $paymentMethod->id,
            'payment_number' => 'PAY-' . str_pad($i, 6, '0', STR_PAD_LEFT),
            'amount' => $transaction->total_amount,
            'status' => 'completed',
            'payment_date' => $transaction->created_at,
            'reference_number' => 'REF-' . rand(100000, 999999),
            'payment_details' => "Payment via {$paymentMethod->name}",
            'processed_by' => $user->id,
            'created_at' => $transaction->created_at,
        ]);
        
        echo "Created Transaction: {$transaction->transaction_number}, Payment: {$payment->payment_number} (${$payment->amount})\n";
    }
    
    echo "\nSample data created successfully!\n";
    echo "Total payments created: " . Payment::where('tenant_id', $tenant->id)->count() . "\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}