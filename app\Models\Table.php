<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

class Table extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'branch_id',
        'area_id',
        'table_number',
        'table_name',
        'seating_capacity',
        'section',
        'status',
        'qr_code',
        'position_coordinates',
        'notes',
        'is_active',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'position_coordinates' => 'array',
            'is_active' => 'boolean',
            'seating_capacity' => 'integer',
        ];
    }

    /**
     * The attributes that should be appended to arrays.
     *
     * @var array
     */
    protected $appends = [
        'reservations_count',
        'active_reservations_count',
        'orders_count',
        'qr_table_url',
        'qr_menu_url',
        'qr_order_url',
        'is_available',
        'current_reservation',
    ];

    // Relationships
    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function tenant()
    {
        return $this->hasOneThrough(Tenant::class, Branch::class, 'id', 'id', 'branch_id', 'tenant_id');
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function reservations()
    {
        return $this->hasMany(Reservation::class);
    }

    public function area()
    {
        return $this->belongsTo(Area::class);
    }

    public function activeReservations()
    {
        return $this->hasMany(Reservation::class)->whereHas('reservationStatus', function ($q) {
            $q->whereIn('name', ['pending', 'confirmed', 'seated']);
        });
    }

    public function currentReservation()
    {
        return $this->hasOne(Reservation::class)
            ->whereHas('reservationStatus', function ($q) {
                $q->where('name', 'seated');
            })
            ->latest('seated_at');
    }

    // Scopes
    public function scopeForBranch(Builder $query, $branchId): Builder
    {
        return $query->where('branch_id', $branchId);
    }

    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    public function scopeAvailable(Builder $query): Builder
    {
        return $query->where('status', 'available')->where('is_active', true);
    }

    public function scopeOccupied(Builder $query): Builder
    {
        return $query->where('status', 'occupied');
    }

    public function scopeReserved(Builder $query): Builder
    {
        return $query->where('status', 'reserved');
    }

    public function scopeForCapacity(Builder $query, int $capacity): Builder
    {
        return $query->where('seating_capacity', '>=', $capacity);
    }

    public function scopeInArea(Builder $query, $areaId): Builder
    {
        return $query->where('area_id', $areaId);
    }

    public function scopeBySection(Builder $query, string $section): Builder
    {
        return $query->where('section', $section);
    }

    public function scopeWithQRCode(Builder $query): Builder
    {
        return $query->whereNotNull('qr_code');
    }

    // Computed Attributes
    public function getReservationsCountAttribute(): int
    {
        return $this->reservations()->count();
    }

    public function getActiveReservationsCountAttribute(): int
    {
        return $this->activeReservations()->count();
    }

    public function getOrdersCountAttribute(): int
    {
        return $this->orders()->count();
    }

    public function getQrTableUrlAttribute(): ?string
    {
        if (!$this->branch || !$this->branch->tenant) {
            return null;
        }

        $tenantSlug = $this->generateTenantSlug();
        return url("/restaurant/table/{$this->id}?hash={$tenantSlug}");
    }

    public function getQrMenuUrlAttribute(): ?string
    {
        if (!$this->branch || !$this->branch->tenant) {
            return null;
        }

        $tenantSlug = $this->generateTenantSlug();
        return url("/restaurant/table/{$this->qr_code}?hash={$tenantSlug}&action=menu");
    }

    public function getQrOrderUrlAttribute(): ?string
    {
        if (!$this->branch || !$this->branch->tenant) {
            return null;
        }

        $tenantSlug = $this->generateTenantSlug();
        return url("/restaurant/table/{$this->id}?hash={$tenantSlug}&action=order");
    }

    public function getIsAvailableAttribute(): bool
    {
        return $this->status === 'available' && $this->is_active;
    }

    public function getCurrentReservationAttribute()
    {
        return $this->currentReservation()->first();
    }

    // Helper Methods
    public function isAvailableForReservation(): bool
    {
        return $this->is_available && $this->active_reservations_count === 0;
    }

    public function canAccommodateParty(int $partySize): bool
    {
        return $this->seating_capacity >= $partySize;
    }

    public function updateStatus(string $status): bool
    {
        $validStatuses = ['available', 'occupied', 'reserved', 'cleaning', 'out_of_order'];
        
        if (!in_array($status, $validStatuses)) {
            return false;
        }

        return $this->update(['status' => $status]);
    }

    public function generateUniqueQRCode(): string
    {
        do {
            $qrCode = 'TBL_' . strtoupper(Str::random(8));
        } while (self::where('qr_code', $qrCode)->exists());

        $this->update(['qr_code' => $qrCode]);
        
        return $qrCode;
    }

    /**
     * Generate QR code for this table (alias for generateUniqueQRCode)
     */
    public function generateQrCode(): string
    {
        return $this->generateUniqueQRCode();
    }

    /**
     * Set QR code manually
     */
    public function setQrCode(string $qrCode): bool
    {
        // Check if QR code already exists for another table
        if (self::where('qr_code', $qrCode)->where('id', '!=', $this->id)->exists()) {
            return false;
        }

        return $this->update(['qr_code' => $qrCode]);
    }

    /**
     * Get QR code URL for this table
     */
    public function getQrCodeUrl(): string
    {
        $baseUrl = request()->getSchemeAndHttpHost();
        $tenantSlug = $this->generateTenantSlug();
        return "{$baseUrl}/restaurant/table/{$this->qr_code}?hash={$tenantSlug}";
    }

    /**
     * Generate the hash for this table.
     */
    public function generateTableHash(): string
    {
        // Load branch if not already loaded
        if (!$this->relationLoaded('branch')) {
            $this->load('branch');
        }
        
        // Create a hash based on branch name and table number
        $branchName = $this->branch->name ?? 'default';
        $tableNumber = $this->table_number;
        
        // Convert to lowercase and replace spaces with hyphens
        $hash = strtolower(str_replace(' ', '-', $branchName . '-' . $tableNumber));
        
        return $hash;
    }

    /**
     * Generate the tenant slug for this table.
     */
    public function generateTenantSlug(): string
    {
        // Load branch and tenant if not already loaded
        if (!$this->relationLoaded('branch')) {
            $this->load('branch.tenant');
        } elseif (!$this->branch->relationLoaded('tenant')) {
            $this->branch->load('tenant');
        }
        
        // First try code, then generate slug from name, finally fallback to 'default'
        if ($this->branch->tenant->code) {
            return $this->branch->tenant->code;
        }
        
        // Create a slug based on tenant name
        $tenantName = $this->branch->tenant->name ?? 'default';
        
        // Convert to lowercase, replace spaces with hyphens, and remove special characters
        $slug = strtolower(preg_replace('/[^a-zA-Z0-9\s-]/', '', $tenantName));
        $slug = str_replace(' ', '-', $slug);
        $slug = preg_replace('/-+/', '-', $slug); // Replace multiple hyphens with single hyphen
        $slug = trim($slug, '-'); // Remove leading/trailing hyphens
        
        return $slug ?: 'default';
    }

    public function markAsOccupied(): bool
    {
        return $this->updateStatus('occupied');
    }

    public function markAsAvailable(): bool
    {
        return $this->updateStatus('available');
    }

    public function markAsReserved(): bool
    {
        return $this->updateStatus('reserved');
    }

    public function markAsCleaning(): bool
    {
        return $this->updateStatus('cleaning');
    }

    public function markAsOutOfOrder(): bool
    {
        return $this->updateStatus('out_of_order');
    }

    public function getOccupancyStatistics(): array
    {
        $totalReservations = $this->reservations()->count();
        $completedReservations = $this->reservations()
            ->whereHas('reservationStatus', function ($q) {
                $q->where('name', 'completed');
            })
            ->count();

        return [
            'total_reservations' => $totalReservations,
            'completed_reservations' => $completedReservations,
            'active_reservations' => $this->active_reservations_count,
            'total_orders' => $this->orders_count,
            'utilization_rate' => $totalReservations > 0 ? round(($completedReservations / $totalReservations) * 100, 2) : 0,
        ];
    }

    public static function getAvailableTablesForCapacity(int $branchId, int $partySize, ?int $areaId = null)
    {
        $query = self::forBranch($branchId)
            ->available()
            ->forCapacity($partySize)
            ->orderBy('seating_capacity');

        if ($areaId) {
            $query->inArea($areaId);
        }

        return $query->get();
    }

    public static function getTableStatistics(int $branchId): array
    {
        $tables = self::forBranch($branchId)->active();

        return [
            'total_tables' => $tables->count(),
            'total_capacity' => $tables->sum('seating_capacity'),
            'available_tables' => $tables->available()->count(),
            'occupied_tables' => $tables->occupied()->count(),
            'reserved_tables' => $tables->reserved()->count(),
            'cleaning_tables' => $tables->where('status', 'cleaning')->count(),
            'out_of_order_tables' => $tables->where('status', 'out_of_order')->count(),
        ];
    }
}