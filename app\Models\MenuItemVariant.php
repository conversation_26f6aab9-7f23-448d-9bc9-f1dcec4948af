<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MenuItemVariant extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'branch_id',
        'menu_item_id',
        'name',
        'name_en',
        'code',
        'type',
        'price_modifier',
        'cost_modifier',
        'is_default',
        'is_active',
        'sort_order',
        'color_code',
        'image',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'price_modifier' => 'decimal:2',
            'cost_modifier' => 'decimal:2',
            'is_default' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'integer',
        ];
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForBranch($query, $branchId)
    {
        return $query->whereHas('menuItem.menu', function ($q) use ($branchId) {
            $q->where('branch_id', $branchId);
        });
    }

    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    // Relationships
    public function menuItem()
    {
        return $this->belongsTo(MenuItem::class);
    }

    public function orderItems()
    {
        return $this->hasMany(OrderItem::class, 'variant_id');
    }
}