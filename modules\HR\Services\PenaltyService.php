<?php

namespace Modules\HR\Services;

use App\Models\User;
use App\Models\StaffPenalty;
use Illuminate\Database\Eloquent\Collection;
use Carbon\Carbon;

class PenaltyService
{
    /**
     * Apply penalty to staff
     */
    public function applyPenalty(int $userId, string $type, float $amount, string $reason, ?string $date = null): array
    {
        $user = User::findOrFail($userId);
        $penaltyDate = $date ? Carbon::parse($date) : now();

        $this->validatePenaltyType($type);
        $this->validatePenaltyAmount($amount);

        $penalty = StaffPenalty::create([
            'tenant_id' => $user->tenant_id,
            'user_id' => $userId,
            'type' => $type,
            'amount' => $amount,
            'reason' => $reason,
            'applied_date' => $penaltyDate,
            'status' => 'active',
        ]);

        return [
            'success' => true,
            'message' => 'Penalty applied successfully',
            'penalty_id' => $penalty->id,
            'penalty_amount' => $amount,
            'penalty_type' => $type,
        ];
    }

    /**
     * Get staff penalties
     */
    public function getStaffPenalties(int $userId, ?string $startDate = null, ?string $endDate = null): Collection
    {
        $query = StaffPenalty::where('user_id', $userId)
            ->where('status', 'active')
            ->with('user');

        if ($startDate) {
            $query->where('applied_date', '>=', $startDate);
        }

        if ($endDate) {
            $query->where('applied_date', '<=', $endDate);
        }

        return $query->orderBy('applied_date', 'desc')->get();
    }

    /**
     * Waive a penalty
     */
    public function waivePenalty(int $penaltyId, string $reason = null): bool
    {
        $penalty = StaffPenalty::findOrFail($penaltyId);
        
        if ($penalty->status !== 'active') {
            throw new \InvalidArgumentException('Only active penalties can be waived');
        }

        $penalty->waive($reason);
        return true;
    }

    /**
     * Get penalty statistics for a tenant
     */
    public function getPenaltyStats(int $tenantId, string $startDate, string $endDate): array
    {
        $penalties = StaffPenalty::where('tenant_id', $tenantId)
            ->betweenDates($startDate, $endDate)
            ->get();

        $totalAmount = $penalties->sum('amount');
        $totalCount = $penalties->count();
        $activeCount = $penalties->where('status', 'active')->count();
        $waivedCount = $penalties->where('status', 'waived')->count();
        $paidCount = $penalties->where('status', 'paid')->count();

        $byType = $penalties->groupBy('type')->map(function ($group) {
            return [
                'count' => $group->count(),
                'total_amount' => $group->sum('amount'),
                'average_amount' => $group->avg('amount'),
            ];
        });

        return [
            'total_amount' => $totalAmount,
            'total_count' => $totalCount,
            'active_count' => $activeCount,
            'waived_count' => $waivedCount,
            'paid_count' => $paidCount,
            'average_penalty' => $totalCount > 0 ? round($totalAmount / $totalCount, 2) : 0,
            'by_type' => $byType,
        ];
    }

    /**
     * Get penalty trends
     */
    public function getPenaltyTrends(int $tenantId, string $startDate, string $endDate, string $groupBy = 'month'): array
    {
        $penalties = StaffPenalty::where('tenant_id', $tenantId)
            ->betweenDates($startDate, $endDate)
            ->get();

        $trends = [];
        
        foreach ($penalties as $penalty) {
            $period = $this->getPeriodKey($penalty->applied_date, $groupBy);
            
            if (!isset($trends[$period])) {
                $trends[$period] = [
                    'period' => $period,
                    'count' => 0,
                    'total_amount' => 0,
                    'by_type' => [],
                ];
            }
            
            $trends[$period]['count']++;
            $trends[$period]['total_amount'] += $penalty->amount;
            
            if (!isset($trends[$period]['by_type'][$penalty->type])) {
                $trends[$period]['by_type'][$penalty->type] = 0;
            }
            $trends[$period]['by_type'][$penalty->type]++;
        }

        return array_values($trends);
    }

    /**
     * Validate penalty type
     */
    private function validatePenaltyType(string $type): void
    {
        $allowedTypes = ['late', 'absence', 'misconduct', 'other'];
        
        if (!in_array($type, $allowedTypes)) {
            throw new \InvalidArgumentException("Invalid penalty type. Allowed types: " . implode(', ', $allowedTypes));
        }
    }

    /**
     * Validate penalty amount
     */
    private function validatePenaltyAmount(float $amount): void
    {
        if ($amount <= 0) {
            throw new \InvalidArgumentException('Penalty amount must be greater than zero');
        }

        $maxPenalty = config('hr.max_penalty_amount', 1000);
        if ($amount > $maxPenalty) {
            throw new \InvalidArgumentException("Penalty amount cannot exceed {$maxPenalty}");
        }
    }

    /**
     * Get period key for grouping
     */
    private function getPeriodKey(Carbon $date, string $groupBy): string
    {
        return match ($groupBy) {
            'day' => $date->format('Y-m-d'),
            'week' => $date->format('Y-W'),
            'month' => $date->format('Y-m'),
            'year' => $date->format('Y'),
            default => $date->format('Y-m'),
        };
    }
}