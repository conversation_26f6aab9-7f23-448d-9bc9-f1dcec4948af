<?php

namespace Modules\HR\Services;

use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * Main HR Service - Acts as a facade for specialized HR services
 * This service coordinates between different HR modules and provides
 * a unified interface for HR operations.
 */
class HRService
{
    private AttendanceService $attendanceService;
    private PayrollService $payrollService;
    private PenaltyService $penaltyService;
    private ShiftService $shiftService;
    private PerformanceService $performanceService;

    public function __construct(
        AttendanceService $attendanceService,
        PayrollService $payrollService,
        PenaltyService $penaltyService,
        ShiftService $shiftService,
        PerformanceService $performanceService
    ) {
        $this->attendanceService = $attendanceService;
        $this->payrollService = $payrollService;
        $this->penaltyService = $penaltyService;
        $this->shiftService = $shiftService;
        $this->performanceService = $performanceService;
    }

    /**
     * Get all staff members for a tenant with pagination
     */
    public function getStaff(int $tenantId, array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = User::where('tenant_id', $tenantId)
            ->where('role', 'staff')
            ->with(['branch', 'position']);

        // Apply filters
        if (!empty($filters['branch_id'])) {
            $query->where('branch_id', $filters['branch_id']);
        }

        if (!empty($filters['position_id'])) {
            $query->where('position_id', $filters['position_id']);
        }

        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (!empty($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('email', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('phone', 'like', '%' . $filters['search'] . '%');
            });
        }

        return $query->paginate($perPage);
    }

    /**
     * Get staff member by ID
     */
    public function getStaffById(int $staffId, int $tenantId): ?User
    {
        return User::where('id', $staffId)
            ->where('tenant_id', $tenantId)
            ->where('role', 'staff')
            ->with(['branch', 'position'])
            ->first();
    }

    // Attendance-related methods (delegated to AttendanceService)
    public function checkIn(int $staffId, int $tenantId, array $data = [])
    {
        return $this->attendanceService->checkIn($staffId, $tenantId, $data);
    }

    public function checkOut(int $staffId, int $tenantId, array $data = [])
    {
        return $this->attendanceService->checkOut($staffId, $tenantId, $data);
    }

    public function getAttendance(int $tenantId, array $filters = [], int $perPage = 15)
    {
        return $this->attendanceService->getAttendance($tenantId, $filters, $perPage);
    }

    public function getAttendanceStats(int $tenantId, string $startDate, string $endDate, ?int $branchId = null)
    {
        return $this->attendanceService->getAttendanceStats($tenantId, $startDate, $endDate, $branchId);
    }

    public function processBulkAttendance(int $tenantId, array $attendanceData)
    {
        return $this->attendanceService->processBulkAttendance($tenantId, $attendanceData);
    }

    // Shift-related methods (delegated to ShiftService)
    public function assignShift(int $shiftId, int $staffId, int $tenantId, array $data = [])
    {
        return $this->shiftService->assignShift($shiftId, $staffId, $tenantId, $data);
    }

    public function acceptShiftAssignment(int $assignmentId, int $tenantId)
    {
        return $this->shiftService->acceptShiftAssignment($assignmentId, $tenantId);
    }

    public function declineShiftAssignment(int $assignmentId, int $tenantId, ?string $reason = null)
    {
        return $this->shiftService->declineShiftAssignment($assignmentId, $tenantId, $reason);
    }

    public function createShift(int $tenantId, array $data)
    {
        return $this->shiftService->createShift($tenantId, $data);
    }

    public function updateShift(int $shiftId, int $tenantId, array $data)
    {
        return $this->shiftService->updateShift($shiftId, $tenantId, $data);
    }

    public function getShifts(int $tenantId, array $filters = [], int $perPage = 15)
    {
        return $this->shiftService->getShifts($tenantId, $filters, $perPage);
    }

    public function getShiftTypes(int $tenantId, ?int $branchId = null)
    {
        return $this->shiftService->getShiftTypes($tenantId, $branchId);
    }

    public function createShiftType(int $tenantId, array $data)
    {
        return $this->shiftService->createShiftType($tenantId, $data);
    }

    public function updateShiftType(int $shiftTypeId, int $tenantId, array $data)
    {
        return $this->shiftService->updateShiftType($shiftTypeId, $tenantId, $data);
    }

    public function getStaffSchedule(int $tenantId, string $startDate, string $endDate, ?int $branchId = null, ?int $staffId = null)
    {
        return $this->shiftService->getStaffSchedule($tenantId, $startDate, $endDate, $branchId, $staffId);
    }

    public function requestShiftReplacement(int $assignmentId, int $tenantId, ?string $reason = null)
    {
        return $this->shiftService->requestShiftReplacement($assignmentId, $tenantId, $reason);
    }

    public function getUnderstaffedShifts(int $tenantId, ?int $branchId = null)
    {
        return $this->shiftService->getUnderstaffedShifts($tenantId, $branchId);
    }

    public function getAvailableStaffForShift(int $shiftId, int $tenantId)
    {
        return $this->shiftService->getAvailableStaffForShift($shiftId, $tenantId);
    }

    // Payroll-related methods (delegated to PayrollService)
    public function getStaffWorkingHours(int $staffId, int $tenantId, string $startDate, string $endDate)
    {
        return $this->payrollService->getStaffWorkingHours($staffId, $tenantId, $startDate, $endDate);
    }

    public function calculateSalary(int $staffId, int $tenantId, string $startDate, string $endDate)
    {
        return $this->payrollService->calculateSalary($staffId, $tenantId, $startDate, $endDate);
    }

    public function generatePayslip(int $staffId, int $tenantId, int $payPeriodId)
    {
        return $this->payrollService->generatePayslip($staffId, $tenantId, $payPeriodId);
    }

    // Penalty-related methods (delegated to PenaltyService)
    public function applyPenalty(int $staffId, int $tenantId, array $data)
    {
        return $this->penaltyService->applyPenalty($staffId, $tenantId, $data);
    }

    public function getStaffPenalties(int $staffId, int $tenantId, array $filters = [], int $perPage = 15)
    {
        return $this->penaltyService->getStaffPenalties($staffId, $tenantId, $filters, $perPage);
    }

    public function waivePenalty(int $penaltyId, int $tenantId, ?string $reason = null)
    {
        return $this->penaltyService->waivePenalty($penaltyId, $tenantId, $reason);
    }

    public function getPenaltyStats(int $tenantId, string $startDate, string $endDate, ?int $branchId = null)
    {
        return $this->penaltyService->getPenaltyStats($tenantId, $startDate, $endDate, $branchId);
    }

    // Performance-related methods (delegated to PerformanceService)
    public function getStaffPerformanceMetrics(int $staffId, int $tenantId, string $startDate, string $endDate)
    {
        return $this->performanceService->getStaffPerformanceMetrics($staffId, $tenantId, $startDate, $endDate);
    }

    public function calculatePerformanceScore(int $staffId, int $tenantId, string $startDate, string $endDate)
    {
        return $this->performanceService->calculatePerformanceScore($staffId, $tenantId, $startDate, $endDate);
    }

    public function getTeamPerformanceSummary(int $tenantId, string $startDate, string $endDate, ?int $branchId = null)
    {
        return $this->performanceService->getTeamPerformanceSummary($tenantId, $startDate, $endDate, $branchId);
    }
}