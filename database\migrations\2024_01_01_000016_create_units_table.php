<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('units', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('tenants')->onDelete('cascade');
            $table->string('name', 50);
            $table->string('abbreviation', 10);
            $table->string('type', 20)->comment('weight, volume, count');
            $table->decimal('base_unit_conversion', 15, 6)->nullable()->comment('Conversion to base unit');
            $table->timestamps();
            
            // Unique constraints within tenant
            $table->unique(['tenant_id', 'name']);
            $table->unique(['tenant_id', 'abbreviation']);
            
            // Performance indexes
            $table->index(['tenant_id', 'type']);
            $table->index(['type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('units');
    }
};