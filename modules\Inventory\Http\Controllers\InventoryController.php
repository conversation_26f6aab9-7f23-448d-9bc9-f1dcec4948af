<?php

namespace Modules\Inventory\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Modules\Inventory\Http\Requests\StoreInventoryRequest;
use Modules\Inventory\Http\Requests\UpdateInventoryRequest;
use Modules\Inventory\Services\InventoryService;

class InventoryController extends Controller
{
    protected $inventoryService;

    public function __construct(InventoryService $inventoryService)
    {
        $this->inventoryService = $inventoryService;
    }

    /**
     * Display a listing of inventory items.
     */
    public function index(Request $request)
    {
        $items = $this->inventoryService->getAllItems($request->all());
        return response()->json($items);
    }

    /**
     * Get inventory items for DataTable using Yajra DataTables
     */
    public function datatable(Request $request)
    {
        $user = Auth::user();

        if (!$user->branch_id) {
            return response()->json([
                'error' => 'User is not assigned to any branch'
            ], 400);
        }

        $query = \App\Models\BranchInventory::with(['product.unit', 'product.category', 'branch'])
            ->where('branch_id', $user->branch_id);

        // Apply filters
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->whereHas('product', function ($q) use ($searchTerm) {
                $q->where('name', 'like', "%{$searchTerm}%")
                  ->orWhere('code', 'like', "%{$searchTerm}%")
                  ->orWhere('barcode', 'like', "%{$searchTerm}%");
            });
        }

        if ($request->filled('category')) {
            $query->whereHas('product', function ($q) use ($request) {
                $q->where('category_id', $request->category);
            });
        }

        if ($request->filled('stock_status')) {
            switch ($request->stock_status) {
                case 'low':
                    $query->whereColumn('current_stock', '<=', 'minimum_level');
                    break;
                case 'out':
                    $query->where('current_stock', '<=', 0);
                    break;
                case 'normal':
                    $query->whereColumn('current_stock', '>', 'minimum_level');
                    break;
            }
        }

        return \Yajra\DataTables\Facades\DataTables::of($query)
            ->addIndexColumn()
            ->addColumn('product_name', function ($row) {
                return $row->product->name ?? 'N/A';
            })
            ->addColumn('product_code', function ($row) {
                return $row->product->code ?? 'N/A';
            })
            ->addColumn('category', function ($row) {
                return $row->product->category->name ?? 'غير محدد';
            })
            ->addColumn('unit', function ($row) {
                return $row->product->unit->name ?? 'N/A';
            })
            ->addColumn('stock_level', function ($row) {
                $current = $row->current_stock;
                $minimum = $row->minimum_level;

                if ($current <= 0) {
                    $status = 'نفد من المخزون';
                    $class = 'badge-danger';
                } elseif ($current <= $minimum) {
                    $status = 'مخزون منخفض';
                    $class = 'badge-warning';
                } else {
                    $status = 'مخزون طبيعي';
                    $class = 'badge-success';
                }

                return "<span class='badge {$class}'>{$status}</span>";
            })
            ->addColumn('stock_info', function ($row) {
                return "
                    <div class='stock-info'>
                        <div><strong>الحالي:</strong> {$row->current_stock}</div>
                        <div><strong>الحد الأدنى:</strong> {$row->minimum_level}</div>
                        <div><strong>المتاح:</strong> {$row->available_stock}</div>
                    </div>
                ";
            })
            ->addColumn('value', function ($row) {
                $totalValue = $row->current_stock * $row->unit_cost;
                return number_format($totalValue, 2) . ' ر.س';
            })
            ->addColumn('last_movement', function ($row) {
                return $row->last_movement_at ? $row->last_movement_at->format('Y-m-d H:i') : 'لا يوجد';
            })
            ->addColumn('actions', function ($row) {
                return "
                    <div class='btn-group' role='group'>
                        <button type='button' class='btn btn-sm btn-info' onclick='viewItem({$row->id})' title='عرض'>
                            <i class='fas fa-eye'></i>
                        </button>
                        <button type='button' class='btn btn-sm btn-warning' onclick='editStock({$row->id})' title='تعديل المخزون'>
                            <i class='fas fa-edit'></i>
                        </button>
                        <button type='button' class='btn btn-sm btn-success' onclick='updateStock({$row->id})' title='تحديث المخزون'>
                            <i class='fas fa-plus'></i>
                        </button>
                        <button type='button' class='btn btn-sm btn-primary' onclick='viewMovements({$row->id})' title='الحركات'>
                            <i class='fas fa-history'></i>
                        </button>
                    </div>
                ";
            })
            ->rawColumns(['stock_level', 'stock_info', 'actions'])
            ->make(true);
    }

    /**
     * Store a newly created inventory item.
     */
    public function store(StoreInventoryRequest $request)
    {
        $item = $this->inventoryService->createItem($request->validated());
        return response()->json($item, 201);
    }

    /**
     * Display the specified inventory item.
     */
    public function show(string $id)
    {
        $item = $this->inventoryService->getItemById($id);
        return response()->json($item);
    }

    /**
     * Update the specified inventory item.
     */
    public function update(UpdateInventoryRequest $request, string $id)
    {
        $item = $this->inventoryService->updateItem($id, $request->validated());
        return response()->json($item);
    }

    /**
     * Remove the specified inventory item.
     */
    public function destroy(string $id)
    {
        $this->inventoryService->deleteItem($id);
        return response()->json(null, 204);
    }

    /**
     * Get low stock items
     */
    public function getLowStockItems()
    {
        $items = $this->inventoryService->getLowStockItems();
        return response()->json($items);
    }

    /**
     * Update stock quantity
     */
    public function updateStock(Request $request, string $id)
    {
        $request->validate([
            'quantity' => 'required|integer',
            'type' => 'required|in:add,subtract,set',
            'reason' => 'nullable|string|max:255'
        ]);

        $item = $this->inventoryService->updateStock(
            $id, 
            $request->quantity, 
            $request->type, 
            $request->reason
        );
        
        return response()->json($item);
    }

    /**
     * Get inventory movements
     */
    public function getMovements(string $id)
    {
        $movements = $this->inventoryService->getItemMovements($id);
        return response()->json($movements);
    }

    /**
     * Bulk update stock levels
     */
    public function bulkUpdateStock(Request $request)
    {
        $request->validate([
            'items' => 'required|array',
            'items.*.id' => 'required|integer|exists:branch_inventories,id',
            'items.*.quantity' => 'required|integer',
            'items.*.type' => 'required|in:add,subtract,set',
            'items.*.reason' => 'nullable|string|max:255'
        ]);

        $results = [];
        foreach ($request->items as $item) {
            try {
                $result = $this->inventoryService->updateStock(
                    $item['id'],
                    $item['quantity'],
                    $item['type'],
                    $item['reason'] ?? 'Bulk update'
                );
                $results[] = ['id' => $item['id'], 'status' => 'success', 'data' => $result];
            } catch (\Exception $e) {
                $results[] = ['id' => $item['id'], 'status' => 'error', 'message' => $e->getMessage()];
            }
        }

        return response()->json(['results' => $results]);
    }

    /**
     * Get inventory analytics
     */
    public function getAnalytics()
    {
        $analytics = $this->inventoryService->getInventoryAnalytics();
        return response()->json($analytics);
    }

    /**
     * Get reorder suggestions
     */
    public function getReorderSuggestions()
    {
        $suggestions = $this->inventoryService->getReorderSuggestions();
        return response()->json($suggestions);
    }

    /**
     * Get product categories
     */
    public function getCategories()
    {
        $categories = [
            'ingredients' => 'Ingredients',
            'beverages' => 'Beverages',
            'packaging' => 'Packaging',
            'cleaning' => 'Cleaning Supplies',
            'equipment' => 'Equipment',
            'general' => 'General'
        ];
        return response()->json($categories);
    }

    /**
     * Get measurement units
     */
    public function getUnits()
    {
        // This would typically come from a units table
        $units = [
            ['id' => 1, 'name' => 'Kilogram', 'symbol' => 'kg'],
            ['id' => 2, 'name' => 'Gram', 'symbol' => 'g'],
            ['id' => 3, 'name' => 'Liter', 'symbol' => 'L'],
            ['id' => 4, 'name' => 'Milliliter', 'symbol' => 'ml'],
            ['id' => 5, 'name' => 'Piece', 'symbol' => 'pcs'],
            ['id' => 6, 'name' => 'Box', 'symbol' => 'box'],
            ['id' => 7, 'name' => 'Bottle', 'symbol' => 'btl']
        ];
        return response()->json($units);
    }

    /**
     * Get dashboard statistics
     */
    public function getDashboardStats()
    {
        $stats = $this->inventoryService->getInventoryAnalytics();
        return response()->json($stats);
    }

    /**
     * Import inventory from file
     */
    public function importInventory(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:csv,xlsx,xls',
            'format' => 'required|in:csv,excel'
        ]);

        // This would implement file import logic
        return response()->json([
            'message' => 'Import functionality not yet implemented',
            'status' => 'pending'
        ], 501);
    }

    /**
     * Export inventory to file
     */
    public function exportInventory(Request $request)
    {
        $request->validate([
            'format' => 'required|in:csv,excel,pdf'
        ]);

        // This would implement file export logic
        return response()->json([
            'message' => 'Export functionality not yet implemented',
            'status' => 'pending'
        ], 501);
    }
}