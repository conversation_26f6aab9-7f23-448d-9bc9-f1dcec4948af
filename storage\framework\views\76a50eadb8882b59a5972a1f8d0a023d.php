<?php $__env->startSection('title', 'Transaction Management'); ?>

<?php $__env->startPush('styles'); ?>
<meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
    /* Custom DataTables Tailwind Styling */
    .dataTables_wrapper {
        font-family: inherit;
    }
    
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_processing,
    .dataTables_wrapper .dataTables_paginate {
        color: #374151;
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }
    .dataTables_processing{
        margin: auto
    }
    .dataTables_wrapper .dataTables_length select,
    .dataTables_wrapper .dataTables_filter input {
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
    
    .dataTables_wrapper .dataTables_filter input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.5rem 0.75rem !important;
        margin: 0 0.125rem !important;
        border-radius: 0.375rem !important;
        border: 1px solid #d1d5db !important;
        background: white !important;
        color: #374151 !important;
        text-decoration: none !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background: #f3f4f6 !important;
        border-color: #9ca3af !important;
        color: #374151 !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background: #3b82f6 !important;
        border-color: #3b82f6 !important;
        color: white !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
        background: #f9fafb !important;
        color: #9ca3af !important;
        border-color: #e5e7eb !important;
    }
    
    table.dataTable {
        border-collapse: separate !important;
        border-spacing: 0 !important;
    }
    
    table.dataTable thead th {
        background-color: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
        font-weight: 600;
        color: #374151;
        padding: 0.75rem;
    }
    
    table.dataTable tbody td {
        padding: 0.75rem;
        border-bottom: 1px solid #f3f4f6;
    }
    
    table.dataTable tbody tr:hover {
        background-color: #f9fafb;
    }
    
    .select2-container--default .select2-selection--single {
        height: 42px;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        padding: 0.5rem;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 26px;
        padding-left: 0;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 40px;
    }
    .transaction-status {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
    }
    .status-paid { background-color: #d1fae5; color: #065f46; }
    .status-due { background-color: #fee2e2; color: #991b1b; }
    .status-partially_paid { background-color: #fef3c7; color: #92400e; }
    .status-cancelled { background-color: #f3f4f6; color: #374151; }
    .status-overpaid { background-color: #dbeafe; color: #1e40af; }
    
    .modal {
        display: none;
    }
    .modal.show {
        display: flex;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<!-- Page Header -->
<div class="mb-6">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="mb-4 sm:mb-0">
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-receipt mr-3"></i>
                Transaction Management
            </h1>
            <p class="text-sm text-gray-600 mt-1">Manage and track all transactions</p>
        </div>
        <div class="flex gap-2">
            <button type="button" id="create-transaction-btn" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors duration-200">
                <i class="fas fa-plus mr-2"></i>
                Create Transaction
            </button>
            <button type="button" id="refresh-table-btn" class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-md transition-colors duration-200">
                <i class="fas fa-sync-alt mr-2"></i>
                Refresh
            </button>
        </div>
    </div>
</div>

<!-- Filters Card -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Filters</h3>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
                <label for="status-filter" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select id="status-filter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">All Statuses</option>
                    <option value="due">Due</option>
                    <option value="paid">Paid</option>
                    <option value="partially_paid">Partially Paid</option>
                    <option value="cancelled">Cancelled</option>
                    <option value="overpaid">Overpaid</option>
                </select>
            </div>
            <div>
                <label for="date-from" class="block text-sm font-medium text-gray-700 mb-2">Date From</label>
                <input type="date" id="date-from" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
            <div>
                <label for="date-to" class="block text-sm font-medium text-gray-700 mb-2">Date To</label>
                <input type="date" id="date-to" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
            <div class="flex items-end">
                <button type="button" id="apply-filters-btn" class="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors duration-200">
                    Apply Filters
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Transactions Table Card -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">Transactions</h3>
            <div class="flex items-center gap-2">
                <label for="per-page" class="text-sm text-gray-700">Show:</label>
                <select id="per-page" class="px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="10">10</option>
                    <option value="25" selected>25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
                <span class="text-sm text-gray-700">entries</span>
            </div>
        </div>
    </div>
    <div class="overflow-x-auto">
        <table id="transactions-table" class="w-full">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Transaction Number</th>
                    
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Paid Amount</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Amount</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <!-- DataTable will populate this -->
            </tbody>
        </table>
    </div>
</div>

<!-- Create/Edit Transaction Modal -->
<div id="transaction-modal" class="modal fixed inset-0 bg-black bg-opacity-50 z-50 items-center justify-center">
    <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 id="modal-title" class="text-lg font-medium text-gray-900">Create Transaction</h3>
                <button type="button" id="close-modal" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <form id="transaction-form">
            <div class="p-6 space-y-4">
                <input type="hidden" id="transaction-id" name="transaction_id">
                
                <!-- Transaction Type -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Transaction Type</label>
                    <div class="flex gap-4">
                        <label class="flex items-center">
                            <input type="radio" name="transaction_type" value="order" class="mr-2" checked>
                            <span class="text-sm">From Order</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="transaction_type" value="standalone" class="mr-2">
                            <span class="text-sm">Standalone</span>
                        </label>
                    </div>
                </div>

                <!-- Order Selection (for order-based transactions) -->
                <div id="order-selection">
                    <label for="order_id" class="block text-sm font-medium text-gray-700 mb-2">Select Order</label>
                    <select id="order_id" name="order_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Select an order...</option>
                        <?php $__currentLoopData = $orders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($order->id); ?>" data-total="<?php echo e($order->total_amount); ?>">
                                <?php echo e($order->order_number); ?> - $<?php echo e(number_format($order->total_amount, 2)); ?>

                                <?php if($order->customer): ?>
                                    (<?php echo e($order->customer->name); ?>)
                                <?php endif; ?>
                                <?php if($order->table): ?>
                                    - Table <?php echo e($order->table->table_number); ?>

                                <?php endif; ?>
                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <!-- Standalone Transaction Fields -->
                <div id="standalone-fields" style="display: none;">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="total_amount" class="block text-sm font-medium text-gray-700 mb-2">Total Amount <span class="text-red-500">*</span></label>
                            <input type="number" id="total_amount" name="total_amount" step="0.01" min="0" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label for="paid_amount" class="block text-sm font-medium text-gray-700 mb-2">Paid Amount</label>
                            <input type="number" id="paid_amount" name="paid_amount" step="0.01" min="0" value="0" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label for="tax_amount" class="block text-sm font-medium text-gray-700 mb-2">Tax Amount</label>
                            <input type="number" id="tax_amount" name="tax_amount" step="0.01" min="0" value="0" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label for="discount_amount" class="block text-sm font-medium text-gray-700 mb-2">Discount Amount</label>
                            <input type="number" id="discount_amount" name="discount_amount" step="0.01" min="0" value="0" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label for="service_charge" class="block text-sm font-medium text-gray-700 mb-2">Service Charge</label>
                            <input type="number" id="service_charge" name="service_charge" step="0.01" min="0" value="0" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                    </div>
                </div>

                <!-- Edit Fields (shown only when editing) -->
                <div id="edit-fields" style="display: none;">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="edit_total_amount" class="block text-sm font-medium text-gray-700 mb-2">Total Amount <span class="text-red-500">*</span></label>
                            <input type="number" id="edit_total_amount" name="total_amount" step="0.01" min="0" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label for="edit_status" class="block text-sm font-medium text-gray-700 mb-2">Status <span class="text-red-500">*</span></label>
                            <select id="edit_status" name="status" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Select Status</option>
                                <option value="due">Due</option>
                                <option value="partially_paid">Partially Paid</option>
                                <option value="paid">Paid</option>
                                <option value="overpaid">Overpaid</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>
                        <div>
                            <label for="edit_tax_amount" class="block text-sm font-medium text-gray-700 mb-2">Tax Amount</label>
                            <input type="number" id="edit_tax_amount" name="tax_amount" step="0.01" min="0" value="0" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label for="edit_discount_amount" class="block text-sm font-medium text-gray-700 mb-2">Discount Amount</label>
                            <input type="number" id="edit_discount_amount" name="discount_amount" step="0.01" min="0" value="0" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label for="edit_service_charge" class="block text-sm font-medium text-gray-700 mb-2">Service Charge</label>
                            <input type="number" id="edit_service_charge" name="service_charge" step="0.01" min="0" value="0" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                    </div>
                </div>

                <!-- Notes -->
                <div>
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                    <textarea id="notes" name="notes" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"></textarea>
                </div>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end gap-2">
                <button type="button" id="cancel-btn" class="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md transition-colors duration-200">
                    Cancel
                </button>
                <button type="submit" id="save-btn" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors duration-200">
                    Save Transaction
                </button>
            </div>
        </form>
    </div>
</div>

<!-- View Transaction Modal -->
<div id="view-modal" class="modal fixed inset-0 bg-black bg-opacity-50 z-50 items-center justify-center">
    <div class="bg-white rounded-lg shadow-xl max-w-3xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">Transaction Details</h3>
                <button type="button" id="close-view-modal" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div id="view-content" class="p-6">
            <!-- Transaction details will be loaded here -->
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.tailwindcss.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
$(document).ready(function() {
    let table;
    let isEditing = false;

    // Initialize DataTable
    function initDataTable() {
        table = $('#transactions-table').DataTable({
            processing: true,
            serverSide: true,
            ajax: {
                url: '/api/transactions',
                type: 'GET',
                data: function(d) {
                    d.status = $('#status-filter').val();
                    d.date_from = $('#date-from').val();
                    d.date_to = $('#date-to').val();
                }
            },
            columns: [
                { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
                { data: 'transaction_number', name: 'transaction_number' },
                // { data: 'order_number', name: 'order_number' },
                { data: 'status_badge', name: 'status', orderable: false },
                { data: 'formatted_total', name: 'total_amount' },
                { data: 'formatted_paid', name: 'paid_amount' },
                { data: 'formatted_due', name: 'due_amount' },
                { data: 'formatted_date', name: 'created_at' },
                { 
                    data: null, 
                    name: 'actions', 
                    orderable: false, 
                    searchable: false,
                    render: function(data, type, row) {
                        return `
                            <div class="flex gap-1">
                                <button class="view-btn px-2 py-1 bg-blue-500 hover:bg-blue-600 text-white text-xs rounded" data-id="${row.DT_RowIndex}">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="edit-btn px-2 py-1 bg-yellow-500 hover:bg-yellow-600 text-white text-xs rounded" data-id="${row.DT_RowIndex}">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="delete-btn px-2 py-1 bg-red-500 hover:bg-red-600 text-white text-xs rounded" data-id="${row.DT_RowIndex}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        `;
                    }
                }
            ],
            pageLength: 25,
            lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
            order: [[7, 'desc']],
            responsive: true,
            language: {
                processing: "Loading transactions...",
                emptyTable: "No transactions found",
                zeroRecords: "No matching transactions found"
            }
        });
    }

    // Initialize table
    initDataTable();

    // Handle per page change
    $('#per-page').on('change', function() {
        table.page.len($(this).val()).draw();
    });

    // Apply filters
    $('#apply-filters-btn').on('click', function() {
        table.ajax.reload();
    });

    // Refresh table
    $('#refresh-table-btn').on('click', function() {
        table.ajax.reload();
    });

    // Transaction type toggle
    $('input[name="transaction_type"]').on('change', function() {
        if ($(this).val() === 'order') {
            $('#order-selection').show();
            $('#standalone-fields').hide();
            $('#edit-fields').hide();
        } else {
            $('#order-selection').hide();
            $('#standalone-fields').show();
            $('#edit-fields').hide();
        }
    });

    // Create transaction button
    $('#create-transaction-btn').on('click', function() {
        isEditing = false;
        $('#modal-title').text('Create Transaction');
        $('#transaction-form')[0].reset();
        $('#transaction-id').val('');
        $('#order-selection').show();
        $('#standalone-fields').hide();
        $('#edit-fields').hide();
        $('input[name="transaction_type"][value="order"]').prop('checked', true);
        $('#transaction-modal').addClass('show');
    });

    // Close modals
    $('#close-modal, #cancel-btn').on('click', function() {
        $('#transaction-modal').removeClass('show');
    });

    $('#close-view-modal').on('click', function() {
        $('#view-modal').removeClass('show');
    });

    // Submit transaction form
    $('#transaction-form').on('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const transactionId = $('#transaction-id').val();
        const url = isEditing ? `/api/transactions/${transactionId}` : '/api/transactions';
        const method = isEditing ? 'PUT' : 'POST';

        // Convert FormData to regular object for JSON
        const data = {};
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }

        $.ajax({
            url: url,
            method: method,
            data: JSON.stringify(data),
            contentType: 'application/json',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                Swal.fire({
                    icon: 'success',
                    title: 'Success!',
                    text: response.message || 'Transaction saved successfully',
                    timer: 2000,
                    showConfirmButton: false
                });
                $('#transaction-modal').removeClass('show');
                table.ajax.reload();
            },
            error: function(xhr) {
                let message = 'An error occurred';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: message
                });
            }
        });
    });

    // View transaction
    $(document).on('click', '.view-btn', function() {
        const rowData = table.row($(this).closest('tr')).data();
        const transactionId = rowData.id; // Use the actual transaction ID
        
        $.ajax({
            url: `/api/transactions/${transactionId}`,
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                const transaction = response.data;
                $('#view-content').html(`
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Transaction Information</h4>
                            <div class="space-y-2">
                                <div><span class="font-medium">Number:</span> ${transaction.transaction_number}</div>
                                <div><span class="font-medium">Status:</span> <span class="transaction-status status-${transaction.status}">${transaction.status.replace('_', ' ').toUpperCase()}</span></div>
                                <div><span class="font-medium">Total Amount:</span> $${parseFloat(transaction.total_amount).toFixed(2)}</div>
                                <div><span class="font-medium">Paid Amount:</span> $${parseFloat(transaction.paid_amount).toFixed(2)}</div>
                                <div><span class="font-medium">Due Amount:</span> $${parseFloat(transaction.due_amount).toFixed(2)}</div>
                                <div><span class="font-medium">Created:</span> ${new Date(transaction.created_at).toLocaleString()}</div>
                            </div>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Additional Details</h4>
                            <div class="space-y-2">
                                <div><span class="font-medium">Tax Amount:</span> $${parseFloat(transaction.tax_amount || 0).toFixed(2)}</div>
                                <div><span class="font-medium">Discount:</span> $${parseFloat(transaction.discount_amount || 0).toFixed(2)}</div>
                                <div><span class="font-medium">Service Charge:</span> $${parseFloat(transaction.service_charge || 0).toFixed(2)}</div>
                                ${transaction.notes ? `<div><span class="font-medium">Notes:</span> ${transaction.notes}</div>` : ''}
                            </div>
                        </div>
                    </div>
                `);
                $('#view-modal').addClass('show');
            },
            error: function(xhr) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'Failed to load transaction details'
                });
            }
        });
    });

    // Edit transaction
    $(document).on('click', '.edit-btn', function() {
        const rowData = table.row($(this).closest('tr')).data();
        const transactionId = rowData.id; // Use the actual transaction ID
        
        $.ajax({
            url: `/api/transactions/${transactionId}`,
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                const transaction = response.data;
                isEditing = true;
                $('#modal-title').text('Edit Transaction');
                $('#transaction-id').val(transaction.id);
                $('#notes').val(transaction.notes || '');
                
                // Hide transaction type selection and other fields for editing
                $('#order-selection').hide();
                $('#standalone-fields').hide();
                $('#edit-fields').show();
                
                // Populate edit fields
                $('#edit_total_amount').val(transaction.total_amount);
                $('#edit_status').val(transaction.status);
                $('#edit_tax_amount').val(transaction.tax_amount || 0);
                $('#edit_discount_amount').val(transaction.discount_amount || 0);
                $('#edit_service_charge').val(transaction.service_charge || 0);
                
                $('#transaction-modal').addClass('show');
            },
            error: function(xhr) {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'Failed to load transaction details'
                });
            }
        });
    });

    // Delete transaction
    $(document).on('click', '.delete-btn', function() {
        const rowData = table.row($(this).closest('tr')).data();
        const transactionId = rowData.id; // Use the actual transaction ID
        
        Swal.fire({
            title: 'Are you sure?',
            text: 'This will void the transaction. This action cannot be undone!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc2626',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'Yes, void it!',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: `/api/transactions/${transactionId}`,
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Voided!',
                            text: 'Transaction has been voided successfully',
                            timer: 2000,
                            showConfirmButton: false
                        });
                        table.ajax.reload();
                    },
                    error: function(xhr) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error!',
                            text: 'Failed to void transaction'
                        });
                    }
                });
            }
        });
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Transaction\Providers/../resources/views/transactions/index.blade.php ENDPATH**/ ?>