<?php

namespace App\Models\Settings;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class SystemSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'data_type',
        'description',
        'is_public',
    ];

    protected $casts = [
        'is_public' => 'boolean',
    ];

    // Scopes
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    public function scopeByKey($query, $key)
    {
        return $query->where('key', $key);
    }

    // Static methods for easy access
    public static function get($key, $default = null)
    {
        $cacheKey = "system_setting_{$key}";
        
        return Cache::remember($cacheKey, 3600, function () use ($key, $default) {
            $setting = static::where('key', $key)->first();
            
            if (!$setting) {
                return $default;
            }
            
            return static::castValue($setting->value, $setting->data_type);
        });
    }

    public static function set($key, $value, $dataType = null, $description = null, $isPublic = false)
    {
        if ($dataType === null) {
            $dataType = static::detectDataType($value);
        }

        $setting = static::updateOrCreate(
            ['key' => $key],
            [
                'value' => is_array($value) ? json_encode($value) : $value,
                'data_type' => $dataType,
                'description' => $description,
                'is_public' => $isPublic,
            ]
        );

        // Clear cache
        Cache::forget("system_setting_{$key}");

        return $setting;
    }

    public static function forget($key)
    {
        $setting = static::where('key', $key)->first();
        
        if ($setting) {
            $setting->delete();
            Cache::forget("system_setting_{$key}");
            return true;
        }
        
        return false;
    }

    protected static function castValue($value, $dataType)
    {
        switch ($dataType) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'integer':
                return (int) $value;
            case 'float':
                return (float) $value;
            case 'json':
                return json_decode($value, true);
            case 'array':
                return is_string($value) ? json_decode($value, true) : $value;
            default:
                return $value;
        }
    }

    protected static function detectDataType($value)
    {
        if (is_bool($value)) {
            return 'boolean';
        } elseif (is_int($value)) {
            return 'integer';
        } elseif (is_float($value)) {
            return 'float';
        } elseif (is_array($value)) {
            return 'json';
        } else {
            return 'string';
        }
    }

    // Default system settings
    public static function getDefaults(): array
    {
        return [
            'app_name' => 'نظام نقاط البيع',
            'app_version' => '1.0.0',
            'default_timezone' => 'Asia/Riyadh',
            'default_language' => 'ar',
            'default_currency' => 'SAR',
            'date_format' => 'Y-m-d',
            'time_format' => 'H:i',
            'datetime_format' => 'Y-m-d H:i:s',
            'pagination_limit' => 20,
            'session_timeout' => 30,
            'max_file_upload_size' => 10240, // KB
            'allowed_file_types' => ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx'],
            'maintenance_mode' => false,
            'registration_enabled' => true,
            'email_verification_required' => false,
        ];
    }
}
