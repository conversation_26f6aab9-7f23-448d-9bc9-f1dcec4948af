<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;

class UserSeeder extends Seeder
{
    public function run()
    {
        if (class_exists(\Database\Factories\UserFactory::class)) {
            User::factory()->count(10)->create();
        } else {
            User::create([
                'name' => 'Admin',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                // Add other required fields here
            ]);
        }
    }
} 