<?php

require_once 'vendor/autoload.php';

function testEndpoint($url, $headers = [], $description = '') {
    echo "\n" . str_repeat('=', 60) . "\n";
    echo "Testing: $description\n";
    echo "URL: $url\n";
    echo str_repeat('-', 60) . "\n";
    
    try {
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => implode("\r\n", array_map(function($k, $v) {
                    return "$k: $v";
                }, array_keys($headers), $headers)),
                'ignore_errors' => true
            ]
        ]);
        
        $response = file_get_contents($url, false, $context);
        $httpCode = null;
        
        if (isset($http_response_header)) {
            foreach ($http_response_header as $header) {
                if (preg_match('/HTTP\/\d\.\d\s+(\d+)/', $header, $matches)) {
                    $httpCode = (int)$matches[1];
                    break;
                }
            }
        }
        
        echo "Status Code: $httpCode\n";
        
        if ($response !== false) {
            $data = json_decode($response, true);
            if ($data) {
                echo "Success: " . ($data['success'] ? 'true' : 'false') . "\n";
                echo "Message: " . $data['message'] . "\n";
                if (isset($data['data'])) {
                    if (is_array($data['data'])) {
                        echo "Data Count: " . count($data['data']) . "\n";
                        if (count($data['data']) > 0) {
                            echo "Sample Data Keys: " . implode(', ', array_keys($data['data'][0])) . "\n";
                        }
                    } else {
                        echo "Data: " . $data['data'] . "\n";
                    }
                }
            } else {
                echo "Response: $response\n";
            }
        } else {
            echo "Failed to get response\n";
        }
        
        return $httpCode;
        
    } catch (Exception $e) {
        echo "Error: " . $e->getMessage() . "\n";
        return null;
    }
}

echo "COMPREHENSIVE ENDPOINT TESTING\n";
echo "==============================\n";

$baseUrl = 'http://localhost:8000';

// Test public endpoints
$publicEndpoints = [
    '/menu/offers/featured' => 'Public Featured Offers (Real Data)',
    '/menu/categories/public' => 'Public Categories',
    '/menu/menus/public' => 'Public Menus',
    '/menu/menu-items/public' => 'Public Menu Items',
    '/menu/banners/display' => 'Public Banners',
    '/menu/events/upcoming' => 'Public Events'
];

echo "\n🌐 TESTING PUBLIC ENDPOINTS (No Authentication Required)\n";
foreach ($publicEndpoints as $endpoint => $description) {
    $status = testEndpoint($baseUrl . $endpoint, ['Accept' => 'application/json'], $description);
    if ($status === 200) {
        echo "✅ PASS\n";
    } else {
        echo "❌ FAIL\n";
    }
}

// Test authenticated endpoints
$authEndpoints = [
    '/api/menu/offers/featured' => 'Authenticated Featured Offers',
    '/api/menu/offers' => 'Authenticated Offers List'
];

echo "\n🔐 TESTING AUTHENTICATED ENDPOINTS (Should Return 401)\n";
foreach ($authEndpoints as $endpoint => $description) {
    $status = testEndpoint($baseUrl . $endpoint, [
        'Accept' => 'application/json',
        'Authorization' => 'Bearer invalid-token'
    ], $description);
    if ($status === 401) {
        echo "✅ PASS (Correctly requires authentication)\n";
    } else {
        echo "❌ FAIL (Should return 401)\n";
    }
}

echo "\n" . str_repeat('=', 60) . "\n";
echo "SUMMARY\n";
echo "=======\n";
echo "✅ Public endpoints should return 200 OK\n";
echo "✅ Authenticated endpoints should return 401 Unauthorized\n";
echo "✅ Featured offers endpoint should return real offer data\n";
echo "\nTesting completed!\n";