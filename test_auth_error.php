<?php

require_once 'vendor/autoload.php';

use Illuminate\Http\Request;
use App\Models\User;
use Laravel\Sanctum\PersonalAccessToken;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

echo "=== TESTING FEATURED OFFERS WITH PROPER AUTHENTICATION ===\n";

try {
    // Get a user from the database
    $user = User::first();
    if (!$user) {
        echo "No users found in database\n";
        exit(1);
    }
    
    echo "Using user: {$user->email} (Branch ID: {$user->branch_id})\n";
    
    // Create a personal access token for the user
    $token = $user->createToken('test-token')->plainTextToken;
    echo "Generated token: " . substr($token, 0, 20) . "...\n";
    
    // Test the authenticated endpoint
    $request = Request::create('/api/menu/offers/featured', 'GET');
    $request->headers->set('Authorization', 'Bearer ' . $token);
    $request->headers->set('Accept', 'application/json');
    
    $response = $kernel->handle($request);
    echo "\nStatus Code: " . $response->getStatusCode() . "\n";
    echo "Response: " . $response->getContent() . "\n";
    
    if ($response->getStatusCode() === 500) {
        echo "\n=== CHECKING LARAVEL LOGS FOR ERRORS ===\n";
        $logFile = storage_path('logs/laravel.log');
        if (file_exists($logFile)) {
            $logs = file_get_contents($logFile);
            $lines = explode("\n", $logs);
            $recentLines = array_slice($lines, -10); // Get last 10 lines
            echo "Recent log entries:\n";
            foreach ($recentLines as $line) {
                if (!empty(trim($line)) && (strpos($line, 'ERROR') !== false || strpos($line, 'Exception') !== false)) {
                    echo $line . "\n";
                }
            }
        }
    }
    
    // Clean up - delete the test token
    PersonalAccessToken::where('tokenable_id', $user->id)
                      ->where('name', 'test-token')
                      ->delete();
    
    $kernel->terminate($request, $response);
    
} catch (Exception $e) {
    echo "Exception caught: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}