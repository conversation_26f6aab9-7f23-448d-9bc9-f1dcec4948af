<?php

namespace Modules\Inventory\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Modules\Inventory\Services\InventoryService;
use Modules\Inventory\Services\SupplierService;
use Modules\Inventory\Services\PurchaseOrderService;

class InventoryWebController extends Controller
{
    protected $inventoryService;
    protected $supplierService;
    protected $purchaseOrderService;

    public function __construct(
        InventoryService $inventoryService,
        SupplierService $supplierService,
        PurchaseOrderService $purchaseOrderService
    ) {
        $this->inventoryService = $inventoryService;
        $this->supplierService = $supplierService;
        $this->purchaseOrderService = $purchaseOrderService;
    }

    /**
     * Display inventory dashboard
     */
    public function index()
    {
        try {
            $analytics = $this->inventoryService->getInventoryAnalytics();
            $lowStockItems = $this->inventoryService->getLowStockItems();
            $reorderSuggestions = $this->inventoryService->getReorderSuggestions();
        } catch (\Exception $e) {
            // Fallback data if services fail
            $analytics = [
                'total_items' => 0,
                'low_stock_items' => 0,
                'out_of_stock_items' => 0,
                'total_value' => 0,
            ];
            $lowStockItems = [];
            $reorderSuggestions = [];
        }

        return view('Inventory::dashboard.index', compact('analytics', 'lowStockItems', 'reorderSuggestions'));
    }

    /**
     * Display inventory items listing
     */
    public function items()
    {
        return view('Inventory::items.index');
    }

    /**
     * Show form for creating new inventory item
     */
    public function create()
    {
        return view('Inventory::items.create');
    }

    /**
     * Display specific inventory item
     */
    public function show($id)
    {
        $item = $this->inventoryService->getItemById($id);
        return view('Inventory::items.show', compact('item'));
    }

    /**
     * Show form for editing inventory item
     */
    public function edit($id)
    {
        $item = $this->inventoryService->getItemById($id);
        return view('Inventory::items.edit', compact('item'));
    }

    /**
     * Display stock management page
     */
    public function stock()
    {
        try {
            $lowStockItems = $this->inventoryService->getLowStockItems();
            $criticalItems = $this->inventoryService->getCriticalStockItems();
            $recentMovements = $this->inventoryService->getRecentMovements();
            $analytics = $this->inventoryService->getStockAnalytics();
        } catch (\Exception $e) {
            // Fallback data if services fail
            $lowStockItems = collect([]);
            $criticalItems = collect([]);
            $recentMovements = collect([]);
            $analytics = [
                'critical_stock' => 0,
                'low_stock' => 0,
                'normal_stock' => 0,
                'overstock' => 0,
            ];
        }

        return view('Inventory::stock.index', compact('lowStockItems', 'criticalItems', 'recentMovements', 'analytics'));
    }

    /**
     * Display low stock items
     */
    public function lowStock()
    {
        $lowStockItems = $this->inventoryService->getLowStockItems();
        return view('Inventory::stock.low-stock', compact('lowStockItems'));
    }

    /**
     * Display stock alerts
     */
    public function alerts()
    {
        return view('Inventory::stock.alerts');
    }

    /**
     * Display inventory movements
     */
    public function movements()
    {
        try {
            $stats = $this->inventoryService->getMovementStats();
        } catch (\Exception $e) {
            // Fallback data if service fails
            $stats = [
                'total_in' => 0,
                'total_out' => 0,
                'total_adjustments' => 0,
                'total_transfers' => 0,
            ];
        }

        return view('Inventory::stock.movements', compact('stats'));
    }

    /**
     * Display movements for specific item
     */
    public function itemMovements($id)
    {
        $item = $this->inventoryService->getItemById($id);
        $movements = $this->inventoryService->getItemMovements($id);
        return view('Inventory::stock.item-movements', compact('item', 'movements'));
    }

    /**
     * Display suppliers listing
     */
    public function suppliers()
    {
        try {
            $analytics = $this->supplierService->getSupplierAnalytics();
        } catch (\Exception $e) {
            // Fallback data if service fails
            $analytics = [
                'total_suppliers' => 0,
                'active_suppliers' => 0,
                'pending_suppliers' => 0,
                'total_products' => 0,
            ];
        }

        return view('Inventory::suppliers.index', compact('analytics'));
    }

    /**
     * Show form for creating new supplier
     */
    public function createSupplier()
    {
        return view('Inventory::suppliers.create');
    }

    /**
     * Display specific supplier
     */
    public function showSupplier($id)
    {
        // This will be implemented when SupplierService is ready
        return view('Inventory::suppliers.show', compact('id'));
    }

    /**
     * Show form for editing supplier
     */
    public function editSupplier($id)
    {
        // This will be implemented when SupplierService is ready
        return view('Inventory::suppliers.edit', compact('id'));
    }

    /**
     * Display products for specific supplier
     */
    public function supplierProducts($id)
    {
        // This will be implemented when SupplierService is ready
        return view('Inventory::suppliers.products', compact('id'));
    }

    /**
     * Display purchase orders listing
     */
    public function purchaseOrders()
    {
        try {
            $analytics = $this->purchaseOrderService->getPurchaseOrderAnalytics();
        } catch (\Exception $e) {
            // Fallback data if service fails
            $analytics = [
                'total_orders' => 0,
                'pending_orders' => 0,
                'completed_orders' => 0,
                'total_amount' => 0,
            ];
        }

        return view('Inventory::purchase-orders.index', compact('analytics'));
    }

    /**
     * Show form for creating new purchase order
     */
    public function createPurchaseOrder()
    {
        return view('Inventory::purchase-orders.create');
    }

    /**
     * Display specific purchase order
     */
    public function showPurchaseOrder($id)
    {
        // This will be implemented when PurchaseOrderService is ready
        return view('Inventory::purchase-orders.show', compact('id'));
    }

    /**
     * Show form for editing purchase order
     */
    public function editPurchaseOrder($id)
    {
        // This will be implemented when PurchaseOrderService is ready
        return view('Inventory::purchase-orders.edit', compact('id'));
    }

    /**
     * Print purchase order
     */
    public function printPurchaseOrder($id)
    {
        // This will be implemented when PurchaseOrderService is ready
        return view('Inventory::purchase-orders.print', compact('id'));
    }

    /**
     * Display purchase orders for specific supplier
     */
    public function purchaseOrdersBySupplier($id)
    {
        // This will be implemented when PurchaseOrderService is ready
        return view('Inventory::purchase-orders.supplier', compact('id'));
    }

    /**
     * Display analytics dashboard
     */
    public function analytics()
    {
        try {
            $analytics = $this->inventoryService->getInventoryAnalytics();
            $kpis = $this->inventoryService->getKPIs();
            $metrics = $this->inventoryService->getMetrics();
        } catch (\Exception $e) {
            // Fallback data if services fail
            $analytics = [
                'total_items' => 0,
                'low_stock_items' => 0,
                'out_of_stock_items' => 0,
                'total_value' => 0,
                'recent_movements' => 0,
            ];
            $kpis = [
                'total_items' => 0,
                'inventory_value' => 0,
                'low_stock_items' => 0,
                'turnover_rate' => 0,
            ];
            $metrics = [
                'turnover_rate' => 0,
                'avg_storage_days' => 0,
                'accuracy_rate' => 0,
                'storage_cost' => 0,
            ];
        }

        return view('Inventory::analytics.dashboard', compact('analytics', 'kpis', 'metrics'));
    }

    /**
     * Display reports page
     */
    public function reports()
    {
        return view('Inventory::reports.index');
    }
}
