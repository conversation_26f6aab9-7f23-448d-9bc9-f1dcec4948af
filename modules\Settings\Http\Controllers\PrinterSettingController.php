<?php

namespace Modules\Settings\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Settings\PrinterSetting;
use App\Services\SettingsService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class PrinterSettingController extends Controller
{
    protected $settingsService;

    public function __construct(SettingsService $settingsService)
    {
        $this->settingsService = $settingsService;
    }

    public function index(Request $request): JsonResponse
    {
        $tenantId = $request->user()->tenant_id;
        $branchId = $request->input('branch_id');
        $type = $request->input('type');

        $printers = $this->settingsService->getPrinterSettings($tenantId, $branchId, $type);

        return response()->json([
            'success' => true,
            'data' => $printers
        ]);
    }

    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'type' => 'required|in:receipt,kitchen,label,invoice',
            'connection_type' => 'required|in:usb,network,bluetooth,serial',
            'connection_config' => 'required|array',
            'paper_size' => 'string|max:20',
            'print_template' => 'nullable|array',
            'auto_cut' => 'boolean',
            'cash_drawer' => 'boolean',
            'copies' => 'integer|min:1|max:10',
            'is_default' => 'boolean',
            'is_active' => 'boolean',
            'branch_id' => 'nullable|exists:branches,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $tenantId = $request->user()->tenant_id;
            $branchId = $request->input('branch_id');

            // If setting as default, unset other defaults of same type
            if ($request->input('is_default', false)) {
                PrinterSetting::where('tenant_id', $tenantId)
                    ->where('branch_id', $branchId)
                    ->where('type', $request->input('type'))
                    ->update(['is_default' => false]);
            }

            $printer = PrinterSetting::create([
                'tenant_id' => $tenantId,
                'branch_id' => $branchId,
                'name' => $request->input('name'),
                'type' => $request->input('type'),
                'connection_type' => $request->input('connection_type'),
                'connection_config' => $request->input('connection_config'),
                'paper_size' => $request->input('paper_size', '80mm'),
                'print_template' => $request->input('print_template'),
                'auto_cut' => $request->input('auto_cut', true),
                'cash_drawer' => $request->input('cash_drawer', false),
                'copies' => $request->input('copies', 1),
                'is_default' => $request->input('is_default', false),
                'is_active' => $request->input('is_active', true),
            ]);

            return response()->json([
                'success' => true,
                'data' => $printer,
                'message' => 'Printer setting created successfully'
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create printer setting: ' . $e->getMessage()
            ], 500);
        }
    }

    public function show(Request $request, $id): JsonResponse
    {
        $tenantId = $request->user()->tenant_id;
        
        $printer = PrinterSetting::where('tenant_id', $tenantId)
            ->where('id', $id)
            ->first();

        if (!$printer) {
            return response()->json([
                'success' => false,
                'message' => 'Printer setting not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $printer
        ]);
    }

    public function update(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'string|max:100',
            'type' => 'in:receipt,kitchen,label,invoice',
            'connection_type' => 'in:usb,network,bluetooth,serial',
            'connection_config' => 'array',
            'paper_size' => 'string|max:20',
            'print_template' => 'nullable|array',
            'auto_cut' => 'boolean',
            'cash_drawer' => 'boolean',
            'copies' => 'integer|min:1|max:10',
            'is_default' => 'boolean',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $tenantId = $request->user()->tenant_id;
            
            $printer = PrinterSetting::where('tenant_id', $tenantId)
                ->where('id', $id)
                ->first();

            if (!$printer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Printer setting not found'
                ], 404);
            }

            // If setting as default, unset other defaults of same type
            if ($request->input('is_default', false) && !$printer->is_default) {
                PrinterSetting::where('tenant_id', $tenantId)
                    ->where('branch_id', $printer->branch_id)
                    ->where('type', $request->input('type', $printer->type))
                    ->where('id', '!=', $id)
                    ->update(['is_default' => false]);
            }

            $printer->update($request->only([
                'name', 'type', 'connection_type', 'connection_config',
                'paper_size', 'print_template', 'auto_cut', 'cash_drawer',
                'copies', 'is_default', 'is_active'
            ]));

            return response()->json([
                'success' => true,
                'data' => $printer,
                'message' => 'Printer setting updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update printer setting: ' . $e->getMessage()
            ], 500);
        }
    }

    public function destroy(Request $request, $id): JsonResponse
    {
        try {
            $tenantId = $request->user()->tenant_id;
            
            $printer = PrinterSetting::where('tenant_id', $tenantId)
                ->where('id', $id)
                ->first();

            if (!$printer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Printer setting not found'
                ], 404);
            }

            $printer->delete();

            return response()->json([
                'success' => true,
                'message' => 'Printer setting deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete printer setting: ' . $e->getMessage()
            ], 500);
        }
    }

    public function testConnection(Request $request, $id): JsonResponse
    {
        try {
            $tenantId = $request->user()->tenant_id;
            
            $printer = PrinterSetting::where('tenant_id', $tenantId)
                ->where('id', $id)
                ->first();

            if (!$printer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Printer setting not found'
                ], 404);
            }

            $connectionResult = $printer->testConnection();

            return response()->json([
                'success' => true,
                'data' => [
                    'connected' => $connectionResult,
                    'connection_string' => $printer->getConnectionString()
                ],
                'message' => $connectionResult ? 'Connection successful' : 'Connection failed'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to test connection: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getDefaultTemplate(Request $request, $id): JsonResponse
    {
        try {
            $tenantId = $request->user()->tenant_id;
            
            $printer = PrinterSetting::where('tenant_id', $tenantId)
                ->where('id', $id)
                ->first();

            if (!$printer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Printer setting not found'
                ], 404);
            }

            $template = $printer->getDefaultTemplate();

            return response()->json([
                'success' => true,
                'data' => $template
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get default template: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getAvailableTypes(): JsonResponse
    {
        $types = [
            'receipt' => 'إيصال',
            'kitchen' => 'مطبخ',
            'label' => 'ملصق',
            'invoice' => 'فاتورة'
        ];

        return response()->json([
            'success' => true,
            'data' => $types
        ]);
    }

    public function getConnectionTypes(): JsonResponse
    {
        $types = [
            'usb' => 'USB',
            'network' => 'شبكة',
            'bluetooth' => 'بلوتوث',
            'serial' => 'منفذ تسلسلي'
        ];

        return response()->json([
            'success' => true,
            'data' => $types
        ]);
    }
}
