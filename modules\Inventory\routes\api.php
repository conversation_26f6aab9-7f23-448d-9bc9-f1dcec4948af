<?php

use Illuminate\Support\Facades\Route;
use Modules\Inventory\Http\Controllers\InventoryController;
use Modules\Inventory\Http\Controllers\SupplierController;
use Modules\Inventory\Http\Controllers\PurchaseOrderController;
use Modules\Inventory\Http\Controllers\InventoryLogController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::prefix('inventory')->name('inventory.')->group(function () {
    
    // Inventory Items Management
    Route::apiResource('items', InventoryController::class);
    Route::prefix('items')->name('items.')->group(function () {
        Route::get('low-stock', [InventoryController::class, 'getLowStockItems'])->name('low-stock');
        Route::post('{id}/update-stock', [InventoryController::class, 'updateStock'])->name('update-stock');
        Route::get('{id}/movements', [InventoryController::class, 'getMovements'])->name('movements');
        Route::post('bulk-update', [InventoryController::class, 'bulkUpdateStock'])->name('bulk-update');
        Route::get('analytics', [InventoryController::class, 'getAnalytics'])->name('analytics');
        Route::get('reorder-suggestions', [InventoryController::class, 'getReorderSuggestions'])->name('reorder-suggestions');
    });
    
    // Suppliers Management
    Route::apiResource('suppliers', SupplierController::class);
    Route::prefix('suppliers')->name('suppliers.')->group(function () {
        Route::get('{id}/products', [SupplierController::class, 'getSupplierProducts'])->name('products');
        Route::get('{id}/purchase-orders', [SupplierController::class, 'getSupplierPurchaseOrders'])->name('purchase-orders');
        Route::get('{id}/performance', [SupplierController::class, 'getSupplierPerformance'])->name('performance');
        Route::post('{id}/add-product', [SupplierController::class, 'addProductToSupplier'])->name('add-product');
        Route::get('categories', [SupplierController::class, 'getSupplierCategories'])->name('categories');
    });
    
    // Purchase Orders Management
    Route::apiResource('purchase-orders', PurchaseOrderController::class);
    Route::prefix('purchase-orders')->name('purchase-orders.')->group(function () {
        Route::post('{id}/approve', [PurchaseOrderController::class, 'approve'])->name('approve');
        Route::post('{id}/reject', [PurchaseOrderController::class, 'reject'])->name('reject');
        Route::post('{id}/receive', [PurchaseOrderController::class, 'receive'])->name('receive');
        Route::post('{id}/cancel', [PurchaseOrderController::class, 'cancel'])->name('cancel');
        Route::get('{id}/pdf', [PurchaseOrderController::class, 'generatePDF'])->name('pdf');
        
        // Purchase Order Items
        Route::prefix('{id}/items')->name('items.')->group(function () {
            Route::get('/', [PurchaseOrderController::class, 'getItems'])->name('index');
            Route::post('/', [PurchaseOrderController::class, 'addItem'])->name('store');
            Route::put('{itemId}', [PurchaseOrderController::class, 'updateItem'])->name('update');
            Route::delete('{itemId}', [PurchaseOrderController::class, 'removeItem'])->name('destroy');
        });
    });
    
    // Inventory Logs and Analytics
    Route::prefix('logs')->name('logs.')->group(function () {
        Route::get('/', [InventoryLogController::class, 'index'])->name('index');
        Route::get('item/{inventoryId}', [InventoryLogController::class, 'getItemLogs'])->name('item');
        Route::get('user/{userId}', [InventoryLogController::class, 'getUserLogs'])->name('user');
        Route::get('movements/summary', [InventoryLogController::class, 'getMovementsSummary'])->name('movements.summary');
        Route::get('stock-history/{inventoryId}', [InventoryLogController::class, 'getStockHistory'])->name('stock-history');
        Route::get('valuation-history', [InventoryLogController::class, 'getValuationHistory'])->name('valuation-history');
        Route::get('low-stock-alerts', [InventoryLogController::class, 'getLowStockAlerts'])->name('low-stock-alerts');
        Route::get('waste-report', [InventoryLogController::class, 'getWasteReport'])->name('waste-report');
        Route::get('export', [InventoryLogController::class, 'exportLogs'])->name('export');
        Route::get('audit-trail/{inventoryId}', [InventoryLogController::class, 'getAuditTrail'])->name('audit-trail');
        Route::get('discrepancy-report', [InventoryLogController::class, 'getDiscrepancyReport'])->name('discrepancy-report');
    });
    
    // Additional utility routes
    Route::prefix('utilities')->name('utilities.')->group(function () {
        Route::get('categories', [InventoryController::class, 'getCategories'])->name('categories');
        Route::get('units', [InventoryController::class, 'getUnits'])->name('units');
        Route::get('dashboard-stats', [InventoryController::class, 'getDashboardStats'])->name('dashboard-stats');
        Route::post('import', [InventoryController::class, 'importInventory'])->name('import');
        Route::get('export', [InventoryController::class, 'exportInventory'])->name('export');
    });
});
