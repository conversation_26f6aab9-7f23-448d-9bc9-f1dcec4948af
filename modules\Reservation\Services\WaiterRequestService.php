<?php

namespace Modules\Reservation\Services;

use App\Models\WaiterRequest;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Reservation\Contracts\WaiterRequestServiceInterface;

class WaiterRequestService implements WaiterRequestServiceInterface
{
    /**
     * Get all waiter requests with optional filters.
     */
    public function getAllRequests(array $filters = []): Collection
    {
        $query = WaiterRequest::with(['table', 'waiter', 'branch']);

        if (isset($filters['table_id'])) {
            $query->where('table_id', $filters['table_id']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['waiter_id'])) {
            $query->where('waiter_id', $filters['waiter_id']);
        }

        if (isset($filters['branch_id'])) {
            $query->where('branch_id', $filters['branch_id']);
        }

        return $query->orderBy('created_at', 'desc')->get();
    }

    /**
     * Create a new waiter request.
     */
    public function createRequest(array $data): WaiterRequest
    {
        return WaiterRequest::create($data);
    }

    /**
     * Get a waiter request by ID.
     */
    public function getRequestById(int $id): WaiterRequest
    {
        return WaiterRequest::with(['table', 'waiter', 'branch'])->findOrFail($id);
    }

    /**
     * Update a waiter request.
     */
    public function updateRequest(int $id, array $data): WaiterRequest
    {
        $waiterRequest = WaiterRequest::findOrFail($id);
        $waiterRequest->update($data);
        return $waiterRequest->load(['table', 'waiter', 'branch']);
    }

    /**
     * Delete a waiter request.
     */
    public function deleteRequest(int $id): bool
    {
        $waiterRequest = WaiterRequest::findOrFail($id);
        return $waiterRequest->delete();
    }

    /**
     * Complete a waiter request.
     */
    public function completeRequest(int $id): WaiterRequest
    {
        $waiterRequest = WaiterRequest::findOrFail($id);
        $waiterRequest->update([
            'status' => 'completed',
            'response_time' => now()
        ]);
        return $waiterRequest->load(['table', 'waiter', 'branch']);
    }

    /**
     * Cancel a waiter request.
     */
    public function cancelRequest(int $id): WaiterRequest
    {
        $waiterRequest = WaiterRequest::findOrFail($id);
        $waiterRequest->update([
            'status' => 'cancelled',
            'response_time' => now()
        ]);
        return $waiterRequest->load(['table', 'waiter', 'branch']);
    }

    /**
     * Get requests for a specific waiter.
     */
    public function getWaiterRequests(int $waiterId): Collection
    {
        return WaiterRequest::with(['table', 'branch'])
            ->byWaiter($waiterId)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get requests for a specific table.
     */
    public function getTableRequests(int $tableId): Collection
    {
        return WaiterRequest::with(['waiter', 'branch'])
            ->where('table_id', $tableId)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get pending requests.
     */
    public function getPendingRequests(): Collection
    {
        return WaiterRequest::with(['table', 'waiter', 'branch'])
            ->pending()
            ->orderBy('created_at', 'asc')
            ->get();
    }

    /**
     * Assign a waiter to a request.
     */
    public function assignWaiter(int $requestId, int $waiterId): WaiterRequest
    {
        $waiterRequest = WaiterRequest::findOrFail($requestId);
        $waiterRequest->update(['waiter_id' => $waiterId]);
        return $waiterRequest->load(['table', 'waiter', 'branch']);
    }
}