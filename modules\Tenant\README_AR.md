# وحدة المستأجرين والاشتراكات - Tenant & Subscription Module

وحدة المستأجرين والاشتراكات هي نظام شامل لإدارة العملاء والخطط والفواتير في نظام نقاط البيع متعدد المستأجرين. توفر هذه الوحدة إدارة كاملة للاشتراكات والخطط والفواتير مع دعم متعدد المتاجر.

## 🏢 الميزات الرئيسية

### 👥 إدارة المستأجرين (Tenant Management)
- **إنشاء وإدارة المستأجرين**: تسجيل عملاء جدد وإدارة بياناتهم
- **تفعيل وإلغاء تفعيل المستأجرين**: التحكم في حالة الحسابات
- **عزل البيانات**: فصل بيانات كل مستأجر بشكل آمن
- **إدارة متعددة المتاجر**: دعم عدة متاجر لكل مستأجر
- **تتبع الاستخدام**: مراقبة استخدام الموارد والحدود

### 📋 إدارة الخطط (Subscription Plans)
- **خطط متنوعة**: خطط أساسية ومتقدمة ومؤسسية
- **دورات فوترة مرنة**: شهرية وسنوية ومخصصة
- **حدود قابلة للتخصيص**: عدد المستخدمين والطلبات والتخزين
- **ميزات متدرجة**: ميزات مختلفة لكل خطة
- **تسعير ديناميكي**: أسعار قابلة للتعديل

### 💳 نظام الفوترة (Billing System)
- **فواتير تلقائية**: إنشاء فواتير دورية
- **طرق دفع متعددة**: بطاقات ائتمان ومحافظ رقمية
- **تجديد تلقائي**: تجديد الاشتراكات تلقائياً
- **إدارة المدفوعات**: تتبع المدفوعات والمتأخرات
- **تقارير مالية**: تقارير شاملة للإيرادات

## 🏗️ هيكل الوحدة

### النماذج (Models)
```php
Models/
├── Tenant.php                    // نموذج المستأجر
├── Subscription.php              // نموذج الاشتراك
├── Plan.php                      // نموذج الخطة
├── Invoice.php                   // نموذج الفاتورة
├── Payment.php                   // نموذج الدفعة
├── Usage.php                     // نموذج الاستخدام
└── Feature.php                   // نموذج الميزة
```

### المتحكمات (Controllers)
```php
Controllers/
├── TenantController.php          // إدارة المستأجرين
├── SubscriptionController.php    // إدارة الاشتراكات
├── PlanController.php            // إدارة الخطط
├── BillingController.php         // إدارة الفوترة
├── UsageController.php           // تتبع الاستخدام
└── Web/
    ├── TenantWebController.php   // واجهة المستأجرين
    └── BillingWebController.php  // واجهة الفوترة
```

### الخدمات (Services)
```php
Services/
├── TenantService.php             // خدمة المستأجرين
├── SubscriptionService.php       // خدمة الاشتراكات
├── BillingService.php            // خدمة الفوترة
├── UsageTrackingService.php      // تتبع الاستخدام
└── PaymentService.php            // خدمة المدفوعات
```

## 📊 الخطط والاشتراكات

### 1. الخطة الأساسية (Basic Plan)
```php
$basicPlan = [
    'name' => 'الخطة الأساسية',
    'slug' => 'basic',
    'description' => 'مثالية للمطاعم الصغيرة',
    'price' => 29.99,
    'billing_cycle' => 'monthly',
    'features' => [
        'max_users' => 3,
        'max_orders_per_month' => 1000,
        'max_menu_items' => 100,
        'max_tables' => 20,
        'storage_limit' => '1GB',
        'support_level' => 'email',
        'features' => [
            'pos_system' => true,
            'inventory_management' => true,
            'basic_reports' => true,
            'customer_management' => false,
            'multi_branch' => false,
            'advanced_analytics' => false
        ]
    ]
];
```

### 2. الخطة المتقدمة (Professional Plan)
```php
$professionalPlan = [
    'name' => 'الخطة المتقدمة',
    'slug' => 'professional',
    'description' => 'للمطاعم المتوسطة والمتنامية',
    'price' => 59.99,
    'billing_cycle' => 'monthly',
    'features' => [
        'max_users' => 10,
        'max_orders_per_month' => 5000,
        'max_menu_items' => 500,
        'max_tables' => 50,
        'storage_limit' => '5GB',
        'support_level' => 'phone_email',
        'features' => [
            'pos_system' => true,
            'inventory_management' => true,
            'basic_reports' => true,
            'advanced_reports' => true,
            'customer_management' => true,
            'loyalty_program' => true,
            'multi_branch' => true,
            'online_ordering' => true,
            'advanced_analytics' => false
        ]
    ]
];
```

### 3. الخطة المؤسسية (Enterprise Plan)
```php
$enterprisePlan = [
    'name' => 'الخطة المؤسسية',
    'slug' => 'enterprise',
    'description' => 'للسلاسل الكبيرة والمؤسسات',
    'price' => 149.99,
    'billing_cycle' => 'monthly',
    'features' => [
        'max_users' => -1, // غير محدود
        'max_orders_per_month' => -1, // غير محدود
        'max_menu_items' => -1, // غير محدود
        'max_tables' => -1, // غير محدود
        'storage_limit' => '50GB',
        'support_level' => '24_7_priority',
        'features' => [
            'pos_system' => true,
            'inventory_management' => true,
            'basic_reports' => true,
            'advanced_reports' => true,
            'customer_management' => true,
            'loyalty_program' => true,
            'multi_branch' => true,
            'online_ordering' => true,
            'delivery_management' => true,
            'advanced_analytics' => true,
            'api_access' => true,
            'white_labeling' => true,
            'custom_integrations' => true
        ]
    ]
];
```

## 🛠️ الاستخدام والأمثلة

### إنشاء مستأجر جديد
```php
use Modules\Tenant\Services\TenantService;

$tenantService = app(TenantService::class);

// إنشاء مستأجر جديد
$tenantData = [
    'name' => 'مطعم الأصالة',
    'email' => '<EMAIL>',
    'phone' => '+970591234567',
    'domain' => 'asala-restaurant',
    'plan_id' => 2, // الخطة المتقدمة
    'billing_cycle' => 'monthly',
    'owner' => [
        'name' => 'أحمد محمد',
        'email' => '<EMAIL>',
        'phone' => '+970591234567'
    ],
    'business_info' => [
        'business_name' => 'مطعم الأصالة للمأكولات الشعبية',
        'business_type' => 'restaurant',
        'address' => 'رام الله، فلسطين',
        'tax_number' => '*********'
    ]
];

$tenant = $tenantService->createTenant($tenantData);
```

### إدارة الاشتراكات
```php
use Modules\Tenant\Services\SubscriptionService;

$subscriptionService = app(SubscriptionService::class);

// إنشاء اشتراك جديد
$subscription = $subscriptionService->createSubscription([
    'tenant_id' => $tenant->id,
    'plan_id' => 2,
    'billing_cycle' => 'monthly',
    'start_date' => now(),
    'payment_method' => 'credit_card',
    'auto_renew' => true
]);

// ترقية الاشتراك
$subscriptionService->upgradePlan($subscription->id, 3); // ترقية للخطة المؤسسية

// تجديد الاشتراك
$subscriptionService->renewSubscription($subscription->id);

// إلغاء الاشتراك
$subscriptionService->cancelSubscription($subscription->id, 'end_of_period');
```

### تتبع الاستخدام
```php
use Modules\Tenant\Services\UsageTrackingService;

$usageService = app(UsageTrackingService::class);

// تسجيل استخدام
$usageService->recordUsage($tenant->id, 'orders', 1); // طلب واحد
$usageService->recordUsage($tenant->id, 'storage', 1024); // 1KB تخزين

// التحقق من الحدود
$canCreateOrder = $usageService->checkLimit($tenant->id, 'orders');
$canUploadFile = $usageService->checkLimit($tenant->id, 'storage', 2048);

// الحصول على إحصائيات الاستخدام
$usage = $usageService->getUsageStats($tenant->id);
```

### إدارة الفوترة
```php
use Modules\Tenant\Services\BillingService;

$billingService = app(BillingService::class);

// إنشاء فاتورة
$invoice = $billingService->createInvoice([
    'tenant_id' => $tenant->id,
    'subscription_id' => $subscription->id,
    'amount' => 59.99,
    'currency' => 'USD',
    'due_date' => now()->addDays(30),
    'items' => [
        [
            'description' => 'الخطة المتقدمة - شهر يناير 2024',
            'quantity' => 1,
            'unit_price' => 59.99,
            'total' => 59.99
        ]
    ]
]);

// معالجة الدفع
$payment = $billingService->processPayment([
    'invoice_id' => $invoice->id,
    'amount' => 59.99,
    'payment_method' => 'stripe',
    'payment_token' => 'tok_visa'
]);

// إرسال تذكير بالدفع
$billingService->sendPaymentReminder($invoice->id);
```

## 🔌 واجهات برمجة التطبيقات (API Endpoints)

### إدارة المستأجرين
```http
GET /api/tenants                           # قائمة المستأجرين
POST /api/tenants                          # إنشاء مستأجر جديد
GET /api/tenants/{id}                      # تفاصيل مستأجر
PUT /api/tenants/{id}                      # تحديث مستأجر
DELETE /api/tenants/{id}                   # حذف مستأجر
POST /api/tenants/{id}/activate            # تفعيل مستأجر
POST /api/tenants/{id}/deactivate          # إلغاء تفعيل مستأجر
```

### إدارة الاشتراكات
```http
GET /api/subscriptions                     # قائمة الاشتراكات
POST /api/subscriptions                    # إنشاء اشتراك جديد
GET /api/subscriptions/{id}                # تفاصيل اشتراك
PUT /api/subscriptions/{id}                # تحديث اشتراك
POST /api/subscriptions/{id}/upgrade       # ترقية الخطة
POST /api/subscriptions/{id}/downgrade     # تخفيض الخطة
POST /api/subscriptions/{id}/renew         # تجديد الاشتراك
POST /api/subscriptions/{id}/cancel        # إلغاء الاشتراك
```

### إدارة الخطط
```http
GET /api/plans                             # قائمة الخطط
POST /api/plans                            # إنشاء خطة جديدة
GET /api/plans/{id}                        # تفاصيل خطة
PUT /api/plans/{id}                        # تحديث خطة
DELETE /api/plans/{id}                     # حذف خطة
```

### إدارة الفوترة
```http
GET /api/invoices                          # قائمة الفواتير
POST /api/invoices                         # إنشاء فاتورة
GET /api/invoices/{id}                     # تفاصيل فاتورة
PUT /api/invoices/{id}                     # تحديث فاتورة
POST /api/invoices/{id}/pay                # دفع فاتورة
POST /api/invoices/{id}/send               # إرسال فاتورة
GET /api/invoices/{id}/pdf                 # تحميل فاتورة PDF
```

### تتبع الاستخدام
```http
GET /api/usage/{tenantId}                  # إحصائيات الاستخدام
POST /api/usage/{tenantId}/record          # تسجيل استخدام
GET /api/usage/{tenantId}/limits           # حدود الاستخدام
```

## 🎯 أمثلة عملية

### مثال 1: إعداد نظام اشتراكات كامل
```php
// إعداد خطط الاشتراك
$plans = [
    [
        'name' => 'الخطة الأساسية',
        'slug' => 'basic',
        'price' => 29.99,
        'billing_cycle' => 'monthly',
        'features' => [
            'max_users' => 3,
            'max_orders_per_month' => 1000,
            'storage_limit' => 1024, // 1GB in MB
            'support_level' => 'email'
        ]
    ],
    [
        'name' => 'الخطة المتقدمة',
        'slug' => 'professional',
        'price' => 59.99,
        'billing_cycle' => 'monthly',
        'features' => [
            'max_users' => 10,
            'max_orders_per_month' => 5000,
            'storage_limit' => 5120, // 5GB in MB
            'support_level' => 'phone_email'
        ]
    ]
];

foreach ($plans as $planData) {
    Plan::create($planData);
}
```

### مثال 2: نظام فوترة تلقائي
```php
// مهمة مجدولة للفوترة التلقائية
class AutoBillingJob implements ShouldQueue
{
    public function handle()
    {
        $subscriptions = Subscription::where('status', 'active')
            ->where('next_billing_date', '<=', now())
            ->get();
            
        foreach ($subscriptions as $subscription) {
            try {
                // إنشاء فاتورة جديدة
                $invoice = $this->billingService->createInvoice([
                    'tenant_id' => $subscription->tenant_id,
                    'subscription_id' => $subscription->id,
                    'amount' => $subscription->plan->price,
                    'currency' => 'USD',
                    'due_date' => now()->addDays(30)
                ]);
                
                // محاولة الدفع التلقائي
                if ($subscription->auto_renew && $subscription->payment_method) {
                    $payment = $this->billingService->processPayment([
                        'invoice_id' => $invoice->id,
                        'amount' => $invoice->amount,
                        'payment_method' => $subscription->payment_method
                    ]);
                    
                    if ($payment->status === 'completed') {
                        // تجديد الاشتراك
                        $subscription->update([
                            'next_billing_date' => $this->calculateNextBillingDate($subscription)
                        ]);
                    }
                } else {
                    // إرسال إشعار بالفاتورة
                    $this->billingService->sendInvoiceNotification($invoice->id);
                }
                
            } catch (Exception $e) {
                Log::error("Auto billing failed for subscription {$subscription->id}: " . $e->getMessage());
            }
        }
    }
}
```

### مثال 3: نظام حدود الاستخدام
```php
// Middleware للتحقق من حدود الاستخدام
class CheckUsageLimits
{
    public function handle($request, Closure $next, $resource)
    {
        $tenant = auth()->user()->tenant;
        
        if (!$this->usageService->checkLimit($tenant->id, $resource)) {
            $plan = $tenant->subscription->plan;
            $limit = $plan->features[$resource] ?? 0;
            
            return response()->json([
                'error' => 'تم تجاوز حد الاستخدام',
                'message' => "لقد تجاوزت الحد المسموح ({$limit}) لـ {$resource}",
                'upgrade_url' => route('subscription.upgrade')
            ], 429);
        }
        
        return $next($request);
    }
}

// استخدام Middleware
Route::post('/orders', [OrderController::class, 'store'])
    ->middleware('check.usage:orders');
    
Route::post('/upload', [FileController::class, 'upload'])
    ->middleware('check.usage:storage');
```

### مثال 4: لوحة تحكم المستأجر
```blade
{{-- resources/views/tenant/dashboard.blade.php --}}
@extends('layouts.app')

@section('content')
<div class="tenant-dashboard">
    {{-- معلومات الاشتراك --}}
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5>معلومات الاشتراك</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <h6>الخطة الحالية</h6>
                            <p class="text-primary">{{ $subscription->plan->name }}</p>
                        </div>
                        <div class="col-md-3">
                            <h6>تاريخ التجديد</h6>
                            <p>{{ $subscription->next_billing_date->format('Y-m-d') }}</p>
                        </div>
                        <div class="col-md-3">
                            <h6>الحالة</h6>
                            <span class="badge badge-success">نشط</span>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('subscription.upgrade') }}" class="btn btn-primary">
                                ترقية الخطة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    {{-- إحصائيات الاستخدام --}}
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h3 class="text-primary">{{ $usage['users']['current'] }}</h3>
                    <p>المستخدمين</p>
                    <small class="text-muted">
                        من أصل {{ $usage['users']['limit'] }}
                    </small>
                    <div class="progress mt-2">
                        <div class="progress-bar" style="width: {{ $usage['users']['percentage'] }}%"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h3 class="text-success">{{ number_format($usage['orders']['current']) }}</h3>
                    <p>الطلبات هذا الشهر</p>
                    <small class="text-muted">
                        من أصل {{ number_format($usage['orders']['limit']) }}
                    </small>
                    <div class="progress mt-2">
                        <div class="progress-bar bg-success" style="width: {{ $usage['orders']['percentage'] }}%"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h3 class="text-info">{{ $usage['storage']['current_formatted'] }}</h3>
                    <p>التخزين المستخدم</p>
                    <small class="text-muted">
                        من أصل {{ $usage['storage']['limit_formatted'] }}
                    </small>
                    <div class="progress mt-2">
                        <div class="progress-bar bg-info" style="width: {{ $usage['storage']['percentage'] }}%"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h3 class="text-warning">${{ number_format($billing['current_month'], 2) }}</h3>
                    <p>فاتورة هذا الشهر</p>
                    <small class="text-muted">
                        الفاتورة القادمة: {{ $billing['next_invoice_date'] }}
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    {{-- الفواتير الأخيرة --}}
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5>الفواتير الأخيرة</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>التاريخ</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($invoices as $invoice)
                                <tr>
                                    <td>{{ $invoice->invoice_number }}</td>
                                    <td>{{ $invoice->created_at->format('Y-m-d') }}</td>
                                    <td>${{ number_format($invoice->amount, 2) }}</td>
                                    <td>
                                        <span class="badge badge-{{ $invoice->status_color }}">
                                            {{ $invoice->status_text }}
                                        </span>
                                    </td>
                                    <td>
                                        <a href="{{ route('invoices.show', $invoice->id) }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            عرض
                                        </a>
                                        @if($invoice->status === 'pending')
                                        <a href="{{ route('invoices.pay', $invoice->id) }}" 
                                           class="btn btn-sm btn-success">
                                            دفع
                                        </a>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
```

## 🔒 الأمان وعزل البيانات

### نظام عزل البيانات
```php
// Middleware لعزل بيانات المستأجرين
class TenantScope
{
    public function handle($request, Closure $next)
    {
        $tenant = auth()->user()->tenant;
        
        // تطبيق نطاق المستأجر على جميع الاستعلامات
        app()->instance('current_tenant', $tenant);
        
        // إضافة شرط المستأجر تلقائياً
        Model::addGlobalScope(new TenantScopeFilter($tenant->id));
        
        return $next($request);
    }
}

// Global Scope للنماذج
class TenantScopeFilter implements Scope
{
    protected $tenantId;
    
    public function __construct($tenantId)
    {
        $this->tenantId = $tenantId;
    }
    
    public function apply(Builder $builder, Model $model)
    {
        if ($model->isTenantScoped()) {
            $builder->where('tenant_id', $this->tenantId);
        }
    }
}
```

### حماية البيانات الحساسة
```php
// تشفير بيانات المستأجرين الحساسة
class EncryptedTenantData
{
    protected $encryptedFields = [
        'payment_method_token',
        'api_keys',
        'webhook_secrets'
    ];
    
    public function setPaymentMethodTokenAttribute($value)
    {
        $this->attributes['payment_method_token'] = encrypt($value);
    }
    
    public function getPaymentMethodTokenAttribute($value)
    {
        return $value ? decrypt($value) : null;
    }
}
```

## 📊 التقارير والتحليلات

### تقارير الإيرادات
```php
// خدمة تقارير الإيرادات
class RevenueReportService
{
    public function getMonthlyRevenue($year = null)
    {
        $year = $year ?? now()->year;
        
        return Payment::where('status', 'completed')
            ->whereYear('created_at', $year)
            ->selectRaw('MONTH(created_at) as month, SUM(amount) as revenue')
            ->groupBy('month')
            ->orderBy('month')
            ->get();
    }
    
    public function getRevenueByPlan($startDate = null, $endDate = null)
    {
        $query = Payment::join('invoices', 'payments.invoice_id', '=', 'invoices.id')
            ->join('subscriptions', 'invoices.subscription_id', '=', 'subscriptions.id')
            ->join('plans', 'subscriptions.plan_id', '=', 'plans.id')
            ->where('payments.status', 'completed');
            
        if ($startDate) {
            $query->where('payments.created_at', '>=', $startDate);
        }
        
        if ($endDate) {
            $query->where('payments.created_at', '<=', $endDate);
        }
        
        return $query->selectRaw('plans.name, SUM(payments.amount) as revenue, COUNT(*) as payments_count')
            ->groupBy('plans.id', 'plans.name')
            ->orderBy('revenue', 'desc')
            ->get();
    }
}
```

### تقارير الاستخدام
```php
// تقرير استخدام المستأجرين
class UsageReportService
{
    public function getTenantUsageReport($tenantId, $period = 'month')
    {
        $startDate = $this->getStartDate($period);
        
        return Usage::where('tenant_id', $tenantId)
            ->where('created_at', '>=', $startDate)
            ->selectRaw('resource_type, SUM(quantity) as total_usage')
            ->groupBy('resource_type')
            ->get();
    }
    
    public function getTopUsageTenants($resourceType, $limit = 10)
    {
        return Usage::join('tenants', 'usage.tenant_id', '=', 'tenants.id')
            ->where('resource_type', $resourceType)
            ->whereMonth('usage.created_at', now()->month)
            ->selectRaw('tenants.name, SUM(usage.quantity) as total_usage')
            ->groupBy('tenants.id', 'tenants.name')
            ->orderBy('total_usage', 'desc')
            ->limit($limit)
            ->get();
    }
}
```

## ⚡ الأداء والتحسين

### تحسين الاستعلامات
```php
// تحسين استعلامات المستأجرين
class OptimizedTenantQueries
{
    public function getTenantsWithSubscriptions()
    {
        return Tenant::with(['subscription.plan', 'usage'])
            ->whereHas('subscription', function ($query) {
                $query->where('status', 'active');
            })
            ->get();
    }
    
    public function getTenantDashboardData($tenantId)
    {
        // استعلام واحد للحصول على جميع البيانات المطلوبة
        return DB::select("
            SELECT 
                t.name as tenant_name,
                s.status as subscription_status,
                p.name as plan_name,
                s.next_billing_date,
                (SELECT COUNT(*) FROM users WHERE tenant_id = t.id) as users_count,
                (SELECT SUM(quantity) FROM usage WHERE tenant_id = t.id AND resource_type = 'orders' AND created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)) as monthly_orders,
                (SELECT SUM(quantity) FROM usage WHERE tenant_id = t.id AND resource_type = 'storage') as storage_used
            FROM tenants t
            JOIN subscriptions s ON t.id = s.tenant_id
            JOIN plans p ON s.plan_id = p.id
            WHERE t.id = ?
        ", [$tenantId]);
    }
}
```

### التخزين المؤقت
```php
// تخزين بيانات المستأجرين في الذاكرة المؤقتة
class CachedTenantService
{
    public function getTenantWithPlan($tenantId)
    {
        return Cache::remember("tenant.{$tenantId}.with_plan", 3600, function () use ($tenantId) {
            return Tenant::with('subscription.plan')->find($tenantId);
        });
    }
    
    public function getTenantUsageLimits($tenantId)
    {
        return Cache::remember("tenant.{$tenantId}.limits", 1800, function () use ($tenantId) {
            $tenant = $this->getTenantWithPlan($tenantId);
            return $tenant->subscription->plan->features;
        });
    }
    
    public function clearTenantCache($tenantId)
    {
        Cache::forget("tenant.{$tenantId}.with_plan");
        Cache::forget("tenant.{$tenantId}.limits");
        Cache::forget("tenant.{$tenantId}.usage");
    }
}
```

## 🔧 استكشاف الأخطاء

### الأخطاء الشائعة وحلولها

1. **فشل في معالجة الدفع**
```php
// معالجة أخطاء الدفع
public function handlePaymentFailure($invoiceId, $error)
{
    $invoice = Invoice::find($invoiceId);
    
    // تسجيل الخطأ
    Log::error("Payment failed for invoice {$invoiceId}: {$error}");
    
    // تحديث حالة الفاتورة
    $invoice->update(['status' => 'failed']);
    
    // إرسال إشعار للمستأجر
    $this->notificationService->sendPaymentFailureNotification($invoice);
    
    // إذا كان الاشتراك سينتهي، إرسال تحذير
    if ($invoice->subscription->expires_at <= now()->addDays(3)) {
        $this->notificationService->sendSubscriptionExpiryWarning($invoice->subscription);
    }
}
```

2. **تجاوز حدود الاستخدام**
```php
// معالجة تجاوز الحدود
public function handleUsageExceeded($tenantId, $resourceType)
{
    $tenant = Tenant::find($tenantId);
    $usage = $this->usageService->getCurrentUsage($tenantId, $resourceType);
    $limit = $this->usageService->getLimit($tenantId, $resourceType);
    
    // تسجيل التجاوز
    UsageViolation::create([
        'tenant_id' => $tenantId,
        'resource_type' => $resourceType,
        'current_usage' => $usage,
        'limit' => $limit,
        'exceeded_by' => $usage - $limit
    ]);
    
    // إرسال إشعار
    $this->notificationService->sendUsageExceededNotification($tenant, $resourceType);
    
    // اقتراح ترقية الخطة
    $this->subscriptionService->suggestPlanUpgrade($tenant);
}
```

3. **مشاكل عزل البيانات**
```php
// التحقق من عزل البيانات
public function validateDataIsolation($tenantId, $resourceId, $resourceType)
{
    $resource = app($resourceType)->find($resourceId);
    
    if (!$resource || $resource->tenant_id !== $tenantId) {
        throw new UnauthorizedAccessException(
            "Unauthorized access to {$resourceType} {$resourceId} by tenant {$tenantId}"
        );
    }
    
    return $resource;
}
```

## 📚 المراجع والموارد

### الوثائق التقنية
- [Laravel Multi-Tenancy](https://laravel.com/docs/database#multiple-database-connections)
- [Stripe API Documentation](https://stripe.com/docs/api)
- [PayPal API Documentation](https://developer.paypal.com/docs/api/)

### أمثلة إضافية
- [إعداد نظام متعدد المستأجرين](./examples/multi-tenant-setup.md)
- [تكامل بوابات الدفع](./examples/payment-gateway-integration.md)
- [نظام الفوترة المتقدم](./examples/advanced-billing-system.md)

---

**ملاحظة**: تأكد من إجراء نسخ احتياطية منتظمة لبيانات المستأجرين والاشتراكات، وراقب الأداء باستمرار لضمان تجربة مستخدم مثلى.