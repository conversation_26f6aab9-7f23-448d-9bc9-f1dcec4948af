@extends('layouts.master')

@section('title', 'إضافة باقة جديدة')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h1 class="text-2xl font-bold text-gray-900 flex items-center gap-3">
                    <i class="fas fa-plus text-purple-600"></i>
                    إضافة باقة جديدة
                </h1>
                <a href="{{ route('packages.index') }}" 
                   class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                    <i class="fas fa-arrow-right"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>

    <!-- Create Form -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">معلومات الباقة الجديدة</h2>
        </div>
        
        <form action="{{ route('packages.store') }}" method="POST" class="p-6" onsubmit="handleFormSubmit(this, 'create'); return false;">
            @csrf
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Basic Information -->
                <div class="md:col-span-2">
                    <h3 class="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">
                        <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                        المعلومات الأساسية
                    </h3>
                </div>
                
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                        اسم الباقة <span class="text-red-500">*</span>
                    </label>
                    <input type="text" name="name" id="name" value="{{ old('name') }}" required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 @error('name') border-red-500 @enderror"
                           placeholder="أدخل اسم الباقة">
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="price" class="block text-sm font-medium text-gray-700 mb-2">
                        السعر (ريال سعودي) <span class="text-red-500">*</span>
                    </label>
                    <input type="number" name="price" id="price" value="{{ old('price') }}" required min="0" step="0.01"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 @error('price') border-red-500 @enderror"
                           placeholder="0.00">
                    @error('price')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="billing_cycle" class="block text-sm font-medium text-gray-700 mb-2">
                        دورة الفوترة <span class="text-red-500">*</span>
                    </label>
                    <select name="billing_cycle" id="billing_cycle" required 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 @error('billing_cycle') border-red-500 @enderror">
                        <option value="">اختر دورة الفوترة</option>
                        <option value="monthly" {{ old('billing_cycle') == 'monthly' ? 'selected' : '' }}>شهري</option>
                        <option value="quarterly" {{ old('billing_cycle') == 'quarterly' ? 'selected' : '' }}>ربع سنوي</option>
                        <option value="yearly" {{ old('billing_cycle') == 'yearly' ? 'selected' : '' }}>سنوي</option>
                    </select>
                    @error('billing_cycle')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                        الحالة <span class="text-red-500">*</span>
                    </label>
                    <select name="status" id="status" required 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 @error('status') border-red-500 @enderror">
                        <option value="1" {{ old('status') == '1' ? 'selected' : '' }}>نشط</option>
                        <option value="0" {{ old('status') == '0' ? 'selected' : '' }}>غير نشط</option>
                    </select>
                    @error('status')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <div class="md:col-span-2">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                        وصف الباقة
                    </label>
                    <textarea name="description" id="description" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 @error('description') border-red-500 @enderror"
                              placeholder="أدخل وصف مفصل للباقة">{{ old('description') }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <!-- Limitations -->
                <div class="md:col-span-2">
                    <h3 class="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200 mt-6">
                        <i class="fas fa-cogs text-orange-600 mr-2"></i>
                        القيود والحدود
                    </h3>
                </div>
                
                <div>
                    <label for="max_branches" class="block text-sm font-medium text-gray-700 mb-2">
                        الحد الأقصى للفروع
                    </label>
                    <input type="number" name="max_branches" id="max_branches" value="{{ old('max_branches') }}" min="1"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 @error('max_branches') border-red-500 @enderror"
                           placeholder="عدد الفروع المسموحة">
                    @error('max_branches')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="max_users" class="block text-sm font-medium text-gray-700 mb-2">
                        الحد الأقصى للمستخدمين
                    </label>
                    <input type="number" name="max_users" id="max_users" value="{{ old('max_users') }}" min="1"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 @error('max_users') border-red-500 @enderror"
                           placeholder="عدد المستخدمين المسموح">
                    @error('max_users')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="storage_limit" class="block text-sm font-medium text-gray-700 mb-2">
                        حد التخزين (جيجابايت)
                    </label>
                    <input type="number" name="storage_limit" id="storage_limit" value="{{ old('storage_limit') }}" min="0.1" step="0.1"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 @error('storage_limit') border-red-500 @enderror"
                           placeholder="مساحة التخزين بالجيجابايت">
                    @error('storage_limit')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-2">
                        ترتيب العرض
                    </label>
                    <input type="number" name="sort_order" id="sort_order" value="{{ old('sort_order', 0) }}" min="0"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 @error('sort_order') border-red-500 @enderror"
                           placeholder="ترتيب الباقة في القائمة">
                    @error('sort_order')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                
                <!-- Features -->
                <div class="md:col-span-2">
                    <h3 class="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200 mt-6">
                        <i class="fas fa-star text-yellow-600 mr-2"></i>
                        الميزات المتاحة
                    </h3>
                </div>
                
                <div class="md:col-span-2">
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <input type="checkbox" name="has_pos" id="has_pos" value="1" 
                                   {{ old('has_pos') ? 'checked' : '' }}
                                   class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                            <label for="has_pos" class="mr-3 block text-sm font-medium text-gray-900">
                                <i class="fas fa-cash-register text-green-600 mr-2"></i>
                                نظام نقاط البيع
                            </label>
                        </div>
                        
                        <div class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <input type="checkbox" name="has_inventory" id="has_inventory" value="1" 
                                   {{ old('has_inventory') ? 'checked' : '' }}
                                   class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                            <label for="has_inventory" class="mr-3 block text-sm font-medium text-gray-900">
                                <i class="fas fa-boxes text-blue-600 mr-2"></i>
                                إدارة المخزون
                            </label>
                        </div>
                        
                        <div class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <input type="checkbox" name="has_reports" id="has_reports" value="1" 
                                   {{ old('has_reports') ? 'checked' : '' }}
                                   class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                            <label for="has_reports" class="mr-3 block text-sm font-medium text-gray-900">
                                <i class="fas fa-chart-bar text-indigo-600 mr-2"></i>
                                التقارير
                            </label>
                        </div>
                        
                        <div class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <input type="checkbox" name="has_delivery" id="has_delivery" value="1" 
                                   {{ old('has_delivery') ? 'checked' : '' }}
                                   class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                            <label for="has_delivery" class="mr-3 block text-sm font-medium text-gray-900">
                                <i class="fas fa-truck text-orange-600 mr-2"></i>
                                خدمة التوصيل
                            </label>
                        </div>
                        
                        <div class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <input type="checkbox" name="has_hr" id="has_hr" value="1" 
                                   {{ old('has_hr') ? 'checked' : '' }}
                                   class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                            <label for="has_hr" class="mr-3 block text-sm font-medium text-gray-900">
                                <i class="fas fa-users text-red-600 mr-2"></i>
                                إدارة الموارد البشرية
                            </label>
                        </div>
                        
                        <div class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <input type="checkbox" name="has_api" id="has_api" value="1" 
                                   {{ old('has_api') ? 'checked' : '' }}
                                   class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                            <label for="has_api" class="mr-3 block text-sm font-medium text-gray-900">
                                <i class="fas fa-code text-gray-600 mr-2"></i>
                                واجهة برمجة التطبيقات
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Submit Buttons -->
            <div class="flex justify-end gap-3 mt-8 pt-6 border-t border-gray-200">
                <a href="{{ route('packages.index') }}" 
                   class="px-6 py-2 text-sm font-medium text-gray-700 bg-gray-200 border border-gray-300 rounded-lg hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                    <i class="fas fa-times mr-2"></i>
                    إلغاء
                </a>
                <button type="submit" 
                        class="px-6 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-colors">
                    <i class="fas fa-save mr-2"></i>
                    حفظ الباقة
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
@endpush