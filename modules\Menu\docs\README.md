# Menu Module Documentation

## Overview

The Menu Module is a comprehensive system for managing restaurant menus, categories, menu items, addons, variants, events, banners, and offers in the Restaurant POS system. It provides a complete solution for menu management with branch-level isolation, hierarchical organization, extensive customization options, and promotional campaign management.

## Table of Contents

1. [Architecture](#architecture)
2. [Features](#features)
3. [Components](#components)
4. [Database Structure](#database-structure)
5. [API Endpoints](#api-endpoints)
6. [Usage Examples](#usage-examples)
7. [Configuration](#configuration)

## Architecture

The Menu Module follows a modular architecture with clear separation of concerns:

```
Menu/
├── Http/
│   ├── Controllers/
│   │   ├── Api/
│   │   │   ├── Public/
│   │   │   └── Sanctum/
│   │   └── Web/
│   ├── Requests/
│   ├── Resources/
│   └── Middleware/
├── Services/
├── Entities/
├── Providers/
├── resources/
└── routes/
```

## Features

### Core Features
- **Menu Management**: Create, update, delete, and organize menus
- **Category Management**: Hierarchical category system with parent-child relationships
- **Menu Item Management**: Comprehensive item management with pricing, images, and nutritional info
- **Addon System**: Flexible addon system with grouping and requirements
- **Variant System**: Product variants with pricing and default selection
- **Event Management**: Restaurant events with registration, scheduling, and analytics
- **Banner Management**: Promotional banners with targeting, scheduling, and performance tracking
- **Offer Management**: Discount offers, promo codes, and promotional campaigns
- **Branch Isolation**: Complete data isolation per restaurant branch
- **Multi-language Support**: RTL and LTR language support
- **Image Management**: Multiple image support for menu items
- **Nutritional Information**: Calories, allergens, and dietary information
- **Time-based Availability**: Menu availability by time and days
- **Featured Items**: Highlight special or popular items
- **Sort Order Management**: Custom ordering for all entities

### Advanced Features
- **DataTable Integration**: Server-side processing with search, filter, and pagination
- **Bulk Operations**: Bulk delete and update operations
- **Status Management**: Active/inactive status for all entities
- **Default Management**: Default menu and variant selection
- **Recipe Integration**: Link menu items to recipes
- **Inventory Integration**: SKU and barcode management
- **Spice Level Management**: 1-5 spice level rating
- **Preparation Time**: Estimated preparation time for items
- **Cost Management**: Cost price tracking for profit analysis

## Components

### Services

#### MenuService
Handles all menu-related operations including CRUD operations, branch isolation, and DataTable integration.

**Key Methods:**
- `getMenusForBranch()` - Retrieve menus for a specific branch
- `createMenu()` - Create new menu
- `updateMenu()` - Update existing menu
- `deleteMenu()` - Delete menu with validation
- `getDefaultMenuForBranch()` - Get default menu for branch

#### CategoryService
Manages hierarchical category system with parent-child relationships.

**Key Methods:**
- `getCategoriesForBranch()` - Get categories for branch
- `createCategory()` - Create new category
- `updateCategory()` - Update category
- `deleteCategory()` - Delete category with relationship validation
- `getRootCategoriesForMenu()` - Get top-level categories

#### MenuItemService
Comprehensive menu item management with all related features.

**Key Methods:**
- `getMenuItemsForBranch()` - Get menu items for branch
- `createMenuItem()` - Create new menu item
- `updateMenuItem()` - Update menu item
- `deleteMenuItem()` - Delete menu item
- `getFeaturedMenuItemsForBranch()` - Get featured items
- `getMenuItemsByCategory()` - Get items by category

#### AddonService
Manages addon system with grouping and requirement settings.

**Key Methods:**
- `getAddonsForBranch()` - Get addons for branch
- `createAddon()` - Create new addon
- `updateAddon()` - Update addon
- `deleteAddon()` - Delete addon
- `getAddonsForMenuItem()` - Get addons for specific menu item
- `getGroupedAddonsForMenuItem()` - Get grouped addons

#### VariantService
Handles product variants with default management.

**Key Methods:**
- `getVariantsForBranch()` - Get variants for branch
- `createVariant()` - Create new variant
- `updateVariant()` - Update variant
- `deleteVariant()` - Delete variant
- `setDefaultVariant()` - Set default variant for menu item
- `getDefaultVariantForMenuItem()` - Get default variant

#### EventService
Manages restaurant events with comprehensive scheduling and analytics.

**Key Methods:**
- `getAllEvents()` - Get all events with filters
- `getEventById()` - Get event by ID
- `createEvent()` - Create new event
- `updateEvent()` - Update existing event
- `deleteEvent()` - Delete event
- `getFeaturedEvents()` - Get featured events
- `getUpcomingEvents()` - Get upcoming events
- `registerForEvent()` - Register customer for event
- `getEventAnalytics()` - Get event performance analytics
- `toggleEventStatus()` - Toggle event active status
- `duplicateEvent()` - Duplicate existing event

#### BannerService
Handles promotional banner management with targeting and performance tracking.

**Key Methods:**
- `getAllBanners()` - Get all banners with filters
- `getBannerById()` - Get banner by ID
- `createBanner()` - Create new banner
- `updateBanner()` - Update existing banner
- `deleteBanner()` - Delete banner
- `getBannersForDisplay()` - Get banners for public display
- `getFeaturedBanners()` - Get featured banners
- `recordImpression()` - Record banner impression
- `recordClick()` - Record banner click
- `getBannerAnalytics()` - Get banner performance analytics
- `generatePerformanceReport()` - Generate performance reports
- `toggleBannerStatus()` - Toggle banner active status
- `duplicateBanner()` - Duplicate existing banner
- `resetBannerMetrics()` - Reset banner performance metrics

#### OfferService
Manages promotional offers, discounts, and campaigns with comprehensive business logic.

**Key Methods:**
- `getAllOffers()` - Get all offers with filters
- `getOfferById()` - Get offer by ID
- `getOfferByPromoCode()` - Get offer by promo code
- `createOffer()` - Create new offer
- `updateOffer()` - Update existing offer
- `deleteOffer()` - Delete offer
- `getFeaturedOffers()` - Get featured offers
- `getCurrentOffers()` - Get currently active offers
- `applyOfferToOrder()` - Apply offer to order
- `validatePromoCode()` - Validate promo code
- `recordOfferUsage()` - Record offer usage
- `getOfferAnalytics()` - Get offer performance analytics
- `generatePerformanceReport()` - Generate offer performance reports
- `toggleOfferStatus()` - Toggle offer active status
- `duplicateOffer()` - Duplicate existing offer

### Controllers

#### Web Controllers
Handle web interface requests with DataTable integration and form processing.

#### API Controllers
- **Public Controllers**: Handle public API requests (no authentication)
- **Sanctum Controllers**: Handle authenticated API requests with token-based auth

#### VariantController
Handles HTTP requests for menu item variants.

**Endpoints:**
- `GET /api/variants` - List variants
- `POST /api/variants` - Create variant
- `GET /api/variants/{id}` - Show variant
- `PUT /api/variants/{id}` - Update variant
- `DELETE /api/variants/{id}` - Delete variant
- `POST /api/variants/{id}/set-default` - Set as default

#### EventController
Handles HTTP requests for restaurant events with comprehensive management features.

**Endpoints:**
- `GET /api/events` - List events
- `POST /api/events` - Create event
- `GET /api/events/{id}` - Show event
- `PUT /api/events/{id}` - Update event
- `DELETE /api/events/{id}` - Delete event
- `GET /api/events/featured` - Get featured events
- `GET /api/events/upcoming` - Get upcoming events
- `POST /api/events/{id}/register` - Register for event
- `GET /api/events/{id}/analytics` - Get event analytics
- `POST /api/events/{id}/toggle-status` - Toggle event status
- `POST /api/events/{id}/duplicate` - Duplicate event

#### BannerController
Handles HTTP requests for promotional banners with targeting and analytics.

**Endpoints:**
- `GET /api/banners` - List banners
- `POST /api/banners` - Create banner
- `GET /api/banners/{id}` - Show banner
- `PUT /api/banners/{id}` - Update banner
- `DELETE /api/banners/{id}` - Delete banner
- `GET /api/banners/featured` - Get featured banners
- `GET /api/banners/type/{type}` - Get banners by type
- `GET /api/banners/position/{position}` - Get banners by position
- `POST /api/banners/{id}/impression` - Record impression
- `POST /api/banners/{id}/click` - Record click
- `GET /api/banners/{id}/analytics` - Get banner analytics
- `GET /api/banners/{id}/performance-report` - Get performance report
- `POST /api/banners/{id}/toggle-status` - Toggle banner status
- `POST /api/banners/{id}/duplicate` - Duplicate banner
- `POST /api/banners/{id}/reset-metrics` - Reset metrics

#### OfferController
Handles HTTP requests for promotional offers and discount management.

**Endpoints:**
- `GET /api/offers` - List offers
- `POST /api/offers` - Create offer
- `GET /api/offers/{id}` - Show offer
- `PUT /api/offers/{id}` - Update offer
- `DELETE /api/offers/{id}` - Delete offer
- `GET /api/offers/featured` - Get featured offers
- `GET /api/offers/current` - Get current offers
- `GET /api/offers/type/{type}` - Get offers by type
- `POST /api/offers/apply` - Apply offer to order
- `POST /api/offers/validate-promo` - Validate promo code
- `POST /api/offers/{id}/record-usage` - Record offer usage
- `GET /api/offers/{id}/analytics` - Get offer analytics
- `GET /api/offers/{id}/performance-report` - Get performance report
- `POST /api/offers/{id}/toggle-status` - Toggle offer status
- `POST /api/offers/{id}/duplicate` - Duplicate offer
- `GET /api/offers/applicable` - Get applicable offers
- `GET /api/offers/auto-applicable` - Get auto-applicable offers

### Request Classes
Comprehensive validation for all operations:

#### Menu Management
- `StoreMenuRequest` - Menu creation validation
- `UpdateMenuRequest` - Menu update validation

#### Category Management
- `StoreCategoryRequest` - Category creation validation
- `UpdateCategoryRequest` - Category update validation

#### Menu Item Management
- `StoreMenuItemRequest` - Menu item creation validation
- `UpdateMenuItemRequest` - Menu item update validation

#### Addon Management
- `StoreAddonRequest` - Addon creation validation
- `UpdateAddonRequest` - Addon update validation

#### Variant Management
- `StoreVariantRequest` - Variant creation validation
- `UpdateVariantRequest` - Variant update validation

#### Event Management
- `StoreEventRequest` - Event creation validation with comprehensive rules for scheduling, pricing, and requirements
- `UpdateEventRequest` - Event update validation with conditional logic for existing events

#### Banner Management
- `StoreBannerRequest` - Banner creation validation with targeting and display settings
- `UpdateBannerRequest` - Banner update validation with conditional and cross-field validation

#### Offer Management
- `StoreOfferRequest` - Offer creation validation with complex discount logic and conditions
- `UpdateOfferRequest` - Offer update validation with extensive business rule validation

### Resource Classes
API response formatting:

- `MenuResource`
- `CategoryResource`
- `MenuItemResource`
- `AddonResource`
- `VariantResource`

## Database Structure

### Core Tables

#### menus
```sql
- id (Primary Key)
- branch_id (Foreign Key to branches)
- name (VARCHAR 100)
- code (VARCHAR 50)
- description (TEXT)
- menu_type (VARCHAR 50)
- start_time (TIME)
- end_time (TIME)
- available_days (JSON)
- is_active (BOOLEAN)
- is_default (BOOLEAN)
- sort_order (INTEGER)
- created_at, updated_at
```

#### menu_categories
```sql
- id (Primary Key)
- menu_id (Foreign Key to menus)
- parent_category_id (Foreign Key to menu_categories)
- name (VARCHAR 100)
- code (VARCHAR 50)
- description (TEXT)
- icon (VARCHAR 100)
- image_url (VARCHAR 500)
- sort_order (INTEGER)
- is_active (BOOLEAN)
- created_at, updated_at
```

#### menu_items
```sql
- id (Primary Key)
- menu_id (Foreign Key to menus)
- category_id (Foreign Key to menu_categories)
- name (VARCHAR 255)
- code (VARCHAR 50)
- description (TEXT)
- short_description (TEXT)
- base_price (DECIMAL)
- cost_price (DECIMAL)
- image_urls (JSON)
- prep_time_minutes (INTEGER)
- calories (INTEGER)
- nutritional_info (JSON)
- allergens (JSON)
- dietary_info (JSON)
- recipe_id (Foreign Key to recipes)
- barcode (VARCHAR 100)
- sku (VARCHAR 100)
- is_active (BOOLEAN)
- is_featured (BOOLEAN)
- is_spicy (BOOLEAN)
- spice_level (INTEGER 1-5)
- sort_order (INTEGER)
- created_at, updated_at
```

#### menu_item_addons
```sql
- id (Primary Key)
- menu_item_id (Foreign Key to menu_items)
- name (VARCHAR 100)
- code (VARCHAR 50)
- description (TEXT)
- price (DECIMAL)
- cost_price (DECIMAL)
- addon_group_name (VARCHAR 100)
- is_required (BOOLEAN)
- allow_multiple (BOOLEAN)
- max_selections (INTEGER)
- sort_order (INTEGER)
- is_active (BOOLEAN)
- created_at, updated_at
```

#### menu_item_variants
```sql
- id (Primary Key)
- menu_item_id (Foreign Key to menu_items)
- name (VARCHAR 100)
- code (VARCHAR 50)
- description (TEXT)
- price (DECIMAL)
- cost_price (DECIMAL)
- sku (VARCHAR 100)
- barcode (VARCHAR 100)
- is_default (BOOLEAN)
- sort_order (INTEGER)
- is_active (BOOLEAN)
- created_at, updated_at
```

#### events
```sql
- id (Primary Key)
- branch_id (Foreign Key to branches)
- title (VARCHAR 255)
- title_en (VARCHAR 255)
- description (TEXT)
- description_en (TEXT)
- event_type (ENUM)
- start_date (DATE)
- end_date (DATE)
- start_time (TIME)
- end_time (TIME)
- location (VARCHAR 255)
- max_participants (INTEGER)
- current_participants (INTEGER)
- price (DECIMAL)
- is_free (BOOLEAN)
- requires_registration (BOOLEAN)
- registration_deadline (DATETIME)
- is_featured (BOOLEAN)
- is_active (BOOLEAN)
- image_url (VARCHAR 500)
- special_requirements (TEXT)
- contact_info (VARCHAR 255)
- sort_order (INTEGER)
- created_at, updated_at
```

#### banners
```sql
- id (Primary Key)
- branch_id (Foreign Key to branches)
- title (VARCHAR 255)
- title_en (VARCHAR 255)
- content (TEXT)
- content_en (TEXT)
- banner_type (ENUM)
- position (ENUM)
- image_url (VARCHAR 500)
- link_url (VARCHAR 500)
- target_audience (ENUM)
- start_date (DATETIME)
- end_date (DATETIME)
- max_impressions (INTEGER)
- current_impressions (INTEGER)
- max_clicks (INTEGER)
- current_clicks (INTEGER)
- is_animated (BOOLEAN)
- animation_type (VARCHAR 100)
- animation_duration (INTEGER)
- auto_hide (BOOLEAN)
- auto_hide_delay (INTEGER)
- is_featured (BOOLEAN)
- is_active (BOOLEAN)
- priority (INTEGER)
- sort_order (INTEGER)
- created_at, updated_at
```

#### offers
```sql
- id (Primary Key)
- branch_id (Foreign Key to branches)
- title (VARCHAR 255)
- title_en (VARCHAR 255)
- description (TEXT)
- description_en (TEXT)
- offer_type (ENUM)
- discount_type (ENUM)
- discount_value (DECIMAL)
- minimum_order_amount (DECIMAL)
- maximum_discount_amount (DECIMAL)
- buy_quantity (INTEGER)
- get_quantity (INTEGER)
- applicable_items (JSON)
- applicable_categories (JSON)
- excluded_items (JSON)
- excluded_categories (JSON)
- combo_items (JSON)
- combo_price (DECIMAL)
- promo_code (VARCHAR 100)
- is_promo_code_required (BOOLEAN)
- start_date (DATETIME)
- end_date (DATETIME)
- usage_limit (INTEGER)
- usage_limit_per_customer (INTEGER)
- current_usage (INTEGER)
- is_stackable (BOOLEAN)
- stackable_with (JSON)
- auto_apply (BOOLEAN)
- auto_apply_conditions (JSON)
- is_featured (BOOLEAN)
- is_active (BOOLEAN)
- priority (INTEGER)
- sort_order (INTEGER)
- created_at, updated_at
```

## API Endpoints

### Public API Endpoints
- `GET /api/public/menus/{restaurantId}` - Get public menus
- `GET /api/public/menu-items/{restaurantId}` - Get public menu items

#### Events (Public)
- `GET /api/public/events` - List public events
- `GET /api/public/events/{id}` - Get specific event
- `GET /api/public/events/featured` - Get featured events
- `GET /api/public/events/upcoming` - Get upcoming events
- `POST /api/public/events/{id}/register` - Register for event

#### Banners (Public)
- `GET /api/public/banners` - List public banners
- `GET /api/public/banners/{id}` - Get specific banner
- `GET /api/public/banners/featured` - Get featured banners
- `GET /api/public/banners/type/{type}` - Get banners by type
- `GET /api/public/banners/position/{position}` - Get banners by position
- `POST /api/public/banners/{id}/impression` - Record impression
- `POST /api/public/banners/{id}/click` - Record click

#### Offers (Public)
- `GET /api/public/offers` - List public offers
- `GET /api/public/offers/{id}` - Get specific offer
- `GET /api/public/offers/featured` - Get featured offers
- `GET /api/public/offers/current` - Get current offers
- `GET /api/public/offers/type/{type}` - Get offers by type
- `POST /api/public/offers/validate-promo` - Validate promo code
- `GET /api/public/offers/applicable` - Get applicable offers
- `GET /api/public/offers/auto-applicable` - Get auto-applicable offers

### Authenticated API Endpoints (Sanctum)

#### Menus
- `GET /api/menus` - List menus for branch
- `POST /api/menus` - Create menu
- `GET /api/menus/{id}` - Get menu details
- `PUT /api/menus/{id}` - Update menu
- `DELETE /api/menus/{id}` - Delete menu

#### Categories
- `GET /api/categories` - List categories for branch
- `POST /api/categories` - Create category
- `GET /api/categories/{id}` - Get category details
- `PUT /api/categories/{id}` - Update category
- `DELETE /api/categories/{id}` - Delete category

#### Menu Items
- `GET /api/menu-items` - List menu items for branch
- `POST /api/menu-items` - Create menu item
- `GET /api/menu-items/{id}` - Get menu item details
- `PUT /api/menu-items/{id}` - Update menu item
- `DELETE /api/menu-items/{id}` - Delete menu item

#### Addons
- `GET /api/addons` - List addons for branch
- `POST /api/addons` - Create addon
- `GET /api/addons/{id}` - Get addon details
- `PUT /api/addons/{id}` - Update addon
- `DELETE /api/addons/{id}` - Delete addon

#### Variants
- `GET /api/variants` - List variants for branch
- `POST /api/variants` - Create variant
- `GET /api/variants/{id}` - Get variant details
- `PUT /api/variants/{id}` - Update variant
- `DELETE /api/variants/{id}` - Delete variant

#### Events
- `GET /api/events` - List all events
- `POST /api/events` - Create new event
- `GET /api/events/{id}` - Get specific event
- `PUT /api/events/{id}` - Update event
- `DELETE /api/events/{id}` - Delete event
- `GET /api/events/featured` - Get featured events
- `GET /api/events/upcoming` - Get upcoming events
- `POST /api/events/{id}/register` - Register for event
- `GET /api/events/{id}/analytics` - Get event analytics
- `POST /api/events/{id}/toggle-status` - Toggle event status
- `POST /api/events/{id}/duplicate` - Duplicate event

#### Banners
- `GET /api/banners` - List all banners
- `POST /api/banners` - Create new banner
- `GET /api/banners/{id}` - Get specific banner
- `PUT /api/banners/{id}` - Update banner
- `DELETE /api/banners/{id}` - Delete banner
- `GET /api/banners/featured` - Get featured banners
- `GET /api/banners/type/{type}` - Get banners by type
- `GET /api/banners/position/{position}` - Get banners by position
- `POST /api/banners/{id}/impression` - Record impression
- `POST /api/banners/{id}/click` - Record click
- `GET /api/banners/{id}/analytics` - Get banner analytics
- `GET /api/banners/{id}/performance-report` - Get performance report
- `POST /api/banners/{id}/toggle-status` - Toggle banner status
- `POST /api/banners/{id}/duplicate` - Duplicate banner
- `POST /api/banners/{id}/reset-metrics` - Reset metrics

#### Offers
- `GET /api/offers` - List all offers
- `POST /api/offers` - Create new offer
- `GET /api/offers/{id}` - Get specific offer
- `PUT /api/offers/{id}` - Update offer
- `DELETE /api/offers/{id}` - Delete offer
- `GET /api/offers/featured` - Get featured offers
- `GET /api/offers/current` - Get current offers
- `GET /api/offers/type/{type}` - Get offers by type
- `POST /api/offers/apply` - Apply offer to order
- `POST /api/offers/validate-promo` - Validate promo code
- `POST /api/offers/{id}/record-usage` - Record offer usage
- `GET /api/offers/{id}/analytics` - Get offer analytics
- `GET /api/offers/{id}/performance-report` - Get performance report
- `POST /api/offers/{id}/toggle-status` - Toggle offer status
- `POST /api/offers/{id}/duplicate` - Duplicate offer
- `GET /api/offers/applicable` - Get applicable offers
- `GET /api/offers/auto-applicable` - Get auto-applicable offers

### Web Endpoints
- `GET /menus` - Menu management page
- `GET /categories` - Category management page
- `GET /menu-items` - Menu item management page
- `GET /addons` - Addon management page
- `GET /variants` - Variant management page
- `GET /events` - Event management page
- `GET /banners` - Banner management page
- `GET /offers` - Offer management page

## Usage Examples

### Creating a Menu
```php
use Modules\Menu\Services\MenuService;

$menuService = new MenuService();
$menu = $menuService->createMenu([
    'name' => 'Dinner Menu',
    'name_en' => 'Dinner Menu',
    'description' => 'Our evening dinner selection',
    'description_en' => 'Our evening dinner selection',
    'is_active' => true,
    'sort_order' => 1
]);
```

### Creating a Category
```php
use Modules\Menu\Services\CategoryService;

$categoryService = new CategoryService();
$category = $categoryService->createCategory([
    'menu_id' => 1,
    'name' => 'Appetizers',
    'name_en' => 'Appetizers',
    'description' => 'Start your meal with our delicious appetizers',
    'description_en' => 'Start your meal with our delicious appetizers',
    'is_active' => true,
    'sort_order' => 1
]);
```

### Creating a Menu Item
```php
use Modules\Menu\Services\MenuItemService;

$menuItemService = new MenuItemService();
$menuItem = $menuItemService->createMenuItem([
    'category_id' => 1,
    'name' => 'Caesar Salad',
    'name_en' => 'Caesar Salad',
    'description' => 'Fresh romaine lettuce with caesar dressing',
    'description_en' => 'Fresh romaine lettuce with caesar dressing',
    'price' => 12.99,
    'is_active' => true,
    'is_featured' => true,
    'sort_order' => 1
]);
```

### Creating an Event
```php
use Modules\Menu\Services\EventService;

$eventService = new EventService();
$event = $eventService->createEvent([
    'title' => 'Wine Tasting Evening',
    'title_en' => 'Wine Tasting Evening',
    'description' => 'Join us for an exclusive wine tasting experience',
    'description_en' => 'Join us for an exclusive wine tasting experience',
    'event_type' => 'tasting',
    'start_date' => '2024-02-15',
    'end_date' => '2024-02-15',
    'start_time' => '19:00:00',
    'end_time' => '22:00:00',
    'location' => 'Main Dining Room',
    'max_participants' => 30,
    'price' => 75.00,
    'requires_registration' => true,
    'registration_deadline' => '2024-02-13 23:59:59',
    'is_featured' => true,
    'is_active' => true
]);
```

### Creating a Banner
```php
use Modules\Menu\Services\BannerService;

$bannerService = new BannerService();
$banner = $bannerService->createBanner([
    'title' => 'Special Valentine\'s Menu',
    'title_en' => 'Special Valentine\'s Menu',
    'content' => 'Celebrate love with our romantic dinner menu',
    'content_en' => 'Celebrate love with our romantic dinner menu',
    'banner_type' => 'promotional',
    'position' => 'header',
    'image_url' => '/images/valentines-banner.jpg',
    'link_url' => '/menu/valentines',
    'target_audience' => 'all',
    'start_date' => '2024-02-01 00:00:00',
    'end_date' => '2024-02-14 23:59:59',
    'max_impressions' => 10000,
    'is_featured' => true,
    'is_active' => true
]);
```

### Creating an Offer
```php
use Modules\Menu\Services\OfferService;

$offerService = new OfferService();
$offer = $offerService->createOffer([
    'title' => '20% Off Dinner',
    'title_en' => '20% Off Dinner',
    'description' => 'Get 20% off your dinner order',
    'description_en' => 'Get 20% off your dinner order',
    'offer_type' => 'percentage',
    'discount_type' => 'percentage',
    'discount_value' => 20.00,
    'minimum_order_amount' => 50.00,
    'maximum_discount_amount' => 25.00,
    'promo_code' => 'DINNER20',
    'is_promo_code_required' => true,
    'start_date' => '2024-02-01 00:00:00',
    'end_date' => '2024-02-29 23:59:59',
    'usage_limit' => 100,
    'usage_limit_per_customer' => 1,
    'is_featured' => true,
    'is_active' => true
]);
```

### Applying an Offer to Order
```php
use Modules\Menu\Services\OfferService;

$offerService = new OfferService();
$result = $offerService->applyOfferToOrder([
    'offer_id' => 1,
    'order_total' => 75.00,
    'order_items' => [
        ['menu_item_id' => 1, 'quantity' => 2, 'price' => 25.00],
        ['menu_item_id' => 2, 'quantity' => 1, 'price' => 25.00]
    ],
    'customer_id' => 123,
    'promo_code' => 'DINNER20'
]);

if ($result['success']) {
    echo "Discount applied: $" . $result['discount_amount'];
    echo "Final total: $" . $result['final_total'];
}
```

## Configuration

### Environment Variables
```env
# Menu Module Configuration
MENU_DEFAULT_PAGE_SIZE=10
MENU_MAX_IMAGE_SIZE=2048
MENU_ALLOWED_IMAGE_TYPES=jpg,jpeg,png,webp
MENU_CACHE_TTL=3600
```

### Service Provider Registration
The MenuServiceProvider is automatically registered and handles:
- Service binding
- Route registration
- Middleware registration
- View composer registration

## Security Features

- **Branch Isolation**: All operations are isolated by branch
- **Authentication**: API endpoints require valid authentication
- **Authorization**: Role-based access control
- **Input Validation**: Comprehensive request validation
- **SQL Injection Prevention**: Eloquent ORM usage
- **XSS Prevention**: Output escaping and sanitization

## Performance Optimizations

- **Database Indexing**: Optimized indexes for common queries
- **Eager Loading**: Relationship preloading to reduce N+1 queries
- **Caching**: Strategic caching for frequently accessed data
- **Pagination**: Server-side pagination for large datasets
- **Query Optimization**: Optimized database queries

## Testing

The module includes comprehensive tests:
- Unit tests for services
- Feature tests for API endpoints
- Integration tests for workflows
- Performance tests for optimization

## Contributing

When contributing to the Menu Module:
1. Follow PSR-12 coding standards
2. Add comprehensive PHPDoc comments
3. Include unit tests for new features
4. Update documentation for changes
5. Follow the existing architecture patterns

## Support

For support and questions:
- Check the API documentation
- Review the test cases for usage examples
- Consult the service class documentation
- Contact the development team

---

**Version**: 1.0.0  
**Last Updated**: 2024  
**Maintainer**: Restaurant POS Development Team