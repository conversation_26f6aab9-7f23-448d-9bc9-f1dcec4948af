@extends('layouts.master')

@section('title', 'Payment Details')

@section('content')
<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-credit-card mr-3"></i>
                Payment Details
            </h1>
            <p class="text-sm text-gray-600 mt-1">Payment #{{ $payment->payment_number }}</p>
        </div>
        <div class="mt-4 sm:mt-0 flex space-x-3">
            <a href="{{ route('payments.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Payments
            </a>
            
            @if($payment->canBeCancelled())
                <button type="button" id="cancel-payment-btn" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500">
                    <i class="fas fa-ban mr-2"></i>
                    Cancel Payment
                </button>
            @endif
            
            @if($payment->canBeRefunded())
                <button type="button" id="refund-payment-btn" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                    <i class="fas fa-undo mr-2"></i>
                    Refund Payment
                </button>
            @endif
        </div>
    </div>

    <!-- Payment Information -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Payment Information</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Payment Number -->
                <div>
                    <label class="block text-sm font-medium text-gray-700">Payment Number</label>
                    <p class="mt-1 text-sm text-gray-900">{{ $payment->payment_number }}</p>
                </div>

                <!-- Amount -->
                <div>
                    <label class="block text-sm font-medium text-gray-700">Amount</label>
                    <p class="mt-1 text-lg font-semibold text-gray-900">${{ number_format($payment->amount, 2) }}</p>
                </div>

                <!-- Status -->
                <div>
                    <label class="block text-sm font-medium text-gray-700">Status</label>
                    <div class="mt-1">
                        @php
                            $statusColors = [
                                'completed' => 'bg-green-100 text-green-800',
                                'pending' => 'bg-yellow-100 text-yellow-800',
                                'failed' => 'bg-red-100 text-red-800',
                                'cancelled' => 'bg-gray-100 text-gray-800',
                                'refunded' => 'bg-purple-100 text-purple-800',
                            ];
                            $colorClass = $statusColors[$payment->status] ?? 'bg-gray-100 text-gray-800';
                        @endphp
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $colorClass }}">
                            {{ ucfirst(str_replace('_', ' ', $payment->status)) }}
                        </span>
                    </div>
                </div>

                <!-- Payment Method -->
                <div>
                    <label class="block text-sm font-medium text-gray-700">Payment Method</label>
                    <p class="mt-1 text-sm text-gray-900">{{ $payment->paymentMethod ? $payment->paymentMethod->name : 'Unknown' }}</p>
                </div>

                <!-- Reference Number -->
                <div>
                    <label class="block text-sm font-medium text-gray-700">Reference Number</label>
                    <p class="mt-1 text-sm text-gray-900">{{ $payment->reference_number ?: '-' }}</p>
                </div>

                <!-- Change Amount -->
                @if($payment->change_amount > 0)
                <div>
                    <label class="block text-sm font-medium text-gray-700">Change Amount</label>
                    <p class="mt-1 text-sm text-gray-900">${{ number_format($payment->change_amount, 2) }}</p>
                </div>
                @endif

                <!-- Payment Date -->
                <div>
                    <label class="block text-sm font-medium text-gray-700">Payment Date</label>
                    <p class="mt-1 text-sm text-gray-900">{{ $payment->payment_date ? $payment->payment_date->format('M d, Y H:i') : '-' }}</p>
                </div>

                <!-- Processed By -->
                <div>
                    <label class="block text-sm font-medium text-gray-700">Processed By</label>
                    <p class="mt-1 text-sm text-gray-900">{{ $payment->processedBy ? $payment->processedBy->name : 'System' }}</p>
                </div>

                <!-- Created At -->
                <div>
                    <label class="block text-sm font-medium text-gray-700">Created At</label>
                    <p class="mt-1 text-sm text-gray-900">{{ $payment->created_at->format('M d, Y H:i') }}</p>
                </div>
            </div>

            <!-- Notes -->
            @if($payment->notes)
            <div class="mt-6">
                <label class="block text-sm font-medium text-gray-700">Notes</label>
                <div class="mt-1 p-3 bg-gray-50 rounded-md">
                    <p class="text-sm text-gray-900">{{ $payment->notes }}</p>
                </div>
            </div>
            @endif

            <!-- Payment Details (JSON) -->
            @if($payment->payment_details)
            <div class="mt-6">
                <label class="block text-sm font-medium text-gray-700">Payment Details</label>
                <div class="mt-1 p-3 bg-gray-50 rounded-md">
                    <pre class="text-xs text-gray-900 whitespace-pre-wrap">{{ json_encode($payment->payment_details, JSON_PRETTY_PRINT) }}</pre>
                </div>
            </div>
            @endif
        </div>
    </div>

    <!-- Transaction Information -->
    @if($payment->transaction)
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Related Transaction</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Transaction Number -->
                <div>
                    <label class="block text-sm font-medium text-gray-700">Transaction Number</label>
                    <p class="mt-1 text-sm text-gray-900">
                        <a href="{{ route('transactions.show', $payment->transaction->id) }}" class="text-blue-600 hover:text-blue-800">
                            {{ $payment->transaction->transaction_number }}
                        </a>
                    </p>
                </div>

                <!-- Order Number -->
                @if($payment->transaction->order)
                <div>
                    <label class="block text-sm font-medium text-gray-700">Order Number</label>
                    <p class="mt-1 text-sm text-gray-900">{{ $payment->transaction->order->order_number }}</p>
                </div>
                @endif

                <!-- Transaction Status -->
                <div>
                    <label class="block text-sm font-medium text-gray-700">Transaction Status</label>
                    <div class="mt-1">
                        @php
                            $transactionStatusColors = [
                                'completed' => 'bg-green-100 text-green-800',
                                'pending' => 'bg-yellow-100 text-yellow-800',
                                'partial' => 'bg-blue-100 text-blue-800',
                                'failed' => 'bg-red-100 text-red-800',
                                'cancelled' => 'bg-gray-100 text-gray-800',
                                'voided' => 'bg-red-100 text-red-800',
                            ];
                            $transactionColorClass = $transactionStatusColors[$payment->transaction->status] ?? 'bg-gray-100 text-gray-800';
                        @endphp
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $transactionColorClass }}">
                            {{ ucfirst(str_replace('_', ' ', $payment->transaction->status)) }}
                        </span>
                    </div>
                </div>

                <!-- Total Amount -->
                <div>
                    <label class="block text-sm font-medium text-gray-700">Total Amount</label>
                    <p class="mt-1 text-sm text-gray-900">${{ number_format($payment->transaction->total_amount, 2) }}</p>
                </div>

                <!-- Paid Amount -->
                <div>
                    <label class="block text-sm font-medium text-gray-700">Paid Amount</label>
                    <p class="mt-1 text-sm text-gray-900">${{ number_format($payment->transaction->paid_amount, 2) }}</p>
                </div>

                <!-- Due Amount -->
                <div>
                    <label class="block text-sm font-medium text-gray-700">Due Amount</label>
                    <p class="mt-1 text-sm text-gray-900 {{ $payment->transaction->due_amount > 0 ? 'font-semibold text-red-600' : 'text-green-600' }}">
                        ${{ number_format($payment->transaction->due_amount, 2) }}
                    </p>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

<!-- Cancel Payment Modal -->
<div id="cancel-payment-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Cancel Payment</h3>
                <button type="button" class="close-modal text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="cancel-payment-form" action="{{ route('payments.cancel', $payment->id) }}" method="POST">
                @csrf
                <div class="mb-4">
                    <label for="cancel-reason" class="block text-sm font-medium text-gray-700 mb-2">Reason for cancellation</label>
                    <textarea id="cancel-reason" name="reason" rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Enter reason for cancellation..."></textarea>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" class="close-modal px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                        Cancel
                    </button>
                    <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-md text-sm font-medium hover:bg-red-700">
                        Cancel Payment
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Refund Payment Modal -->
<div id="refund-payment-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Refund Payment</h3>
                <button type="button" class="close-modal text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="refund-payment-form" action="{{ route('payments.refund', $payment->id) }}" method="POST">
                @csrf
                <div class="mb-4">
                    <label for="refund-amount" class="block text-sm font-medium text-gray-700 mb-2">Refund Amount</label>
                    <input type="number" id="refund-amount" name="refund_amount" step="0.01" min="0" max="{{ $payment->amount }}" value="{{ $payment->amount }}" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="0.00">
                    <p class="mt-1 text-xs text-gray-500">Maximum refund amount: ${{ number_format($payment->amount, 2) }}</p>
                </div>
                <div class="mb-4">
                    <label for="refund-reason" class="block text-sm font-medium text-gray-700 mb-2">Reason for refund</label>
                    <textarea id="refund-reason" name="reason" rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Enter reason for refund..."></textarea>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" class="close-modal px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                        Cancel
                    </button>
                    <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-md text-sm font-medium hover:bg-red-700">
                        Process Refund
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Cancel payment button
    $('#cancel-payment-btn').on('click', function() {
        $('#cancel-payment-modal').removeClass('hidden');
    });

    // Refund payment button
    $('#refund-payment-btn').on('click', function() {
        $('#refund-payment-modal').removeClass('hidden');
    });

    // Close modals
    $(document).on('click', '.close-modal', function() {
        $(this).closest('.fixed').addClass('hidden');
    });

    // Close modal when clicking outside
    $(document).on('click', '.fixed', function(e) {
        if (e.target === this) {
            $(this).addClass('hidden');
        }
    });

    // Handle cancel payment form submission
    $('#cancel-payment-form').on('submit', function(e) {
        e.preventDefault();
        
        Swal.fire({
            title: 'Are you sure?',
            text: 'This action will cancel the payment and cannot be undone.',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, cancel payment'
        }).then((result) => {
            if (result.isConfirmed) {
                // Show loading state
                $(this).find('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Cancelling...');
                
                // Submit form
                this.submit();
            }
        });
    });

    // Handle refund payment form submission
    $('#refund-payment-form').on('submit', function(e) {
        e.preventDefault();
        
        var refundAmount = parseFloat($('#refund-amount').val());
        var maxAmount = parseFloat($('#refund-amount').attr('max'));
        
        if (refundAmount > maxAmount) {
            Swal.fire('Error', 'Refund amount cannot exceed the payment amount', 'error');
            return;
        }
        
        Swal.fire({
            title: 'Are you sure?',
            text: `This will refund $${refundAmount.toFixed(2)} and cannot be undone.`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, process refund'
        }).then((result) => {
            if (result.isConfirmed) {
                // Show loading state
                $(this).find('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Processing...');
                
                // Submit form
                this.submit();
            }
        });
    });

    // Validate refund amount
    $('#refund-amount').on('input', function() {
        var amount = parseFloat($(this).val());
        var maxAmount = parseFloat($(this).attr('max'));
        
        if (amount > maxAmount) {
            $(this).val(maxAmount.toFixed(2));
        }
    });
});
</script>
@endpush