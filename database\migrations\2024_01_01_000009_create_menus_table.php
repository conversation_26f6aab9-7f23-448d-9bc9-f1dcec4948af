<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('menus', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('tenants')->onDelete('cascade');
            $table->foreignId('branch_id')->constrained('branches')->onDelete('cascade');
            $table->string('name', 100);
            $table->string('code', 50);
            $table->text('description')->nullable();
            $table->string('menu_type', 50)->default('main')->comment('main, breakfast, lunch, dinner, drinks, etc.');
            $table->time('start_time')->nullable()->comment('Menu available from time');
            $table->time('end_time')->nullable()->comment('Menu available until time');
            $table->json('available_days')->nullable()->comment('Days of week when menu is available');
            $table->boolean('is_active')->default(true);
            $table->boolean('is_default')->default(false)->comment('Default menu for the branch');
            $table->integer('sort_order')->default(0);
            $table->timestamps();
            
            $table->unique(['tenant_id', 'branch_id', 'code']);
            $table->index(['branch_id', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('menus');
    }
};