<?php

namespace Modules\Menu\Http\Controllers\Api;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Modules\Menu\Http\Requests\StoreAddonRequest;
use Modules\Menu\Http\Requests\UpdateAddonRequest;
use Modules\Menu\Services\AddonService;
use App\Helpers\BranchHelper;
use App\Helpers\ResponseHelper;

class AddonController extends Controller
{
    protected $addonService;

    public function __construct(AddonService $addonService)
    {
        $this->addonService = $addonService;
    }

    /**
     * Display a listing of addons
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            
            // Convert request parameters to filters array
            $filters = $request->only([
                'menu_item_id', 'addon_group_name', 'is_required', 
                'min_price', 'max_price', 'search', 'sort_by', 
                'sort_direction', 'per_page'
            ]);
            
            $addons = $this->addonService->getAddonsForBranch($branchId, $filters);
            
            return ResponseHelper::success($addons, 'Addons retrieved successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve addons: ' . $e->getMessage());
        }
    }

    /**
     * Store a newly created addon
     */
    public function store(StoreAddonRequest $request): JsonResponse
    {
        try {
            $data = BranchHelper::addBranchAndTenantIds($request->validated());
            $addon = $this->addonService->createAddon($data);
            
            return ResponseHelper::success($addon, 'Addon created successfully', 201);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to create addon: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified addon
     */
    public function show(int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $addon = $this->addonService->getAddonByIdForBranch($id, $branchId);
            
            if (!$addon) {
                return ResponseHelper::notFound('Addon not found');
            }
            
            return ResponseHelper::success($addon, 'Addon retrieved successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve addon: ' . $e->getMessage());
        }
    }

    /**
     * Update the specified addon
     */
    public function update(UpdateAddonRequest $request, int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $addon = $this->addonService->updateAddonForBranch($id, $request->validated(), $branchId);
            
            if (!$addon) {
                return ResponseHelper::notFound('Addon not found');
            }
            
            return ResponseHelper::success($addon, 'Addon updated successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to update addon: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified addon
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $deleted = $this->addonService->deleteAddonForBranch($id, $branchId);
            
            if (!$deleted) {
                return ResponseHelper::notFound('Addon not found');
            }
            
            return ResponseHelper::success(null, 'Addon deleted successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to delete addon: ' . $e->getMessage());
        }
    }
}