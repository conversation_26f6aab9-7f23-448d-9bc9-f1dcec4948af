<?php

namespace Modules\Menu\Http\Controllers\Web;

use App\Models\Offer;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Modules\Menu\Services\OfferService;
use Modules\Menu\Http\Requests\StoreOfferRequest;
use Modules\Menu\Http\Requests\UpdateOfferRequest;


/**
 * Offer Controller
 * 
 * Handles HTTP requests for offer management in the restaurant system.
 * Provides endpoints for CRUD operations, offer application, validation,
 * and analytics.
 * 
 * @package Modules\Menu\Http\Controllers
 * <AUTHOR> POS Team
 * @version 1.0.0
 */
class OfferController extends Controller
{
    protected OfferService $offerService;

    public function __construct(OfferService $offerService)
    {
        $this->offerService = $offerService;
    }

    /**
     * Display a listing of offers
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request)
    {
        try {
            if ($request->ajax()) {
                $filters = $request->only([
                    'branch_id', 'offer_type', 'discount_type', 'is_active', 
                    'is_featured', 'is_public', 'search', 'sort_by', 'sort_order'
                ]);

                $perPage = $request->get('per_page', 15);
                $offers = $this->offerService->getAllOffers($filters, $perPage);

                return response()->json([
                    'success' => true,
                    'message' => 'Offers retrieved successfully',
                    'data' => $offers
                ]);
            }

            return view('menu::offers');
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve offers',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created offer
     *
     * @param StoreOfferRequest $request
     * @return JsonResponse
     */
    public function store(StoreOfferRequest $request): JsonResponse
    {
        try {
            $offer = $this->offerService->createOffer($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Offer created successfully',
                'data' => $offer
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create offer',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified offer
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        try {
            $offer = $this->offerService->getOfferById($id);

            if (!$offer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Offer not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Offer retrieved successfully',
                'data' => $offer
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve offer',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified offer
     *
     * @param UpdateOfferRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(UpdateOfferRequest $request, int $id): JsonResponse
    {
        try {
            $offer = $this->offerService->updateOffer($id, $request->validated());

            if (!$offer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Offer not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Offer updated successfully',
                'data' => $offer
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update offer',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified offer
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $deleted = $this->offerService->deleteOffer($id);

            if (!$deleted) {
                return response()->json([
                    'success' => false,
                    'message' => 'Offer not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Offer deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete offer',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get featured offers
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function featured(Request $request): JsonResponse
    {
        try {
            $branchId = $request->get('branch_id');
            $limit = $request->get('limit', 10);
            
            $offers = $this->offerService->getFeaturedOffers($branchId, $limit);

            return response()->json([
                'success' => true,
                'message' => 'Featured offers retrieved successfully',
                'data' => $offers
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve featured offers',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get current offers
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function current(Request $request): JsonResponse
    {
        try {
            $branchId = $request->get('branch_id');
            $limit = $request->get('limit', 10);
            
            $offers = $this->offerService->getCurrentOffers($branchId, $limit);

            return response()->json([
                'success' => true,
                'message' => 'Current offers retrieved successfully',
                'data' => $offers
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve current offers',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Apply offer to order
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function apply(Request $request, int $id): JsonResponse
    {
        try {
            $request->validate([
                'order_data' => 'required|array',
                'customer_id' => 'nullable|integer',
                'promo_code' => 'nullable|string'
            ]);

            $result = $this->offerService->applyOfferToOrder(
                $id,
                $request->get('order_data'),
                $request->get('customer_id')
            );

            return response()->json([
                'success' => true,
                'message' => 'Offer applied successfully',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to apply offer',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate promo code
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function validatePromoCode(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'promo_code' => 'required|string',
                'branch_id' => 'nullable|integer|exists:branches,id',
                'customer_id' => 'nullable|integer',
                'order_data' => 'nullable|array'
            ]);

            $result = $this->offerService->validatePromoCode(
                $request->get('promo_code'),
                $request->get('order_data', []),
                $request->get('customer_id'),
                $request->get('branch_id')
            );

            return response()->json([
                'success' => true,
                'message' => 'Promo code is valid',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to validate promo code',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get offer by promo code
     *
     * @param Request $request
     * @param string $promoCode
     * @return JsonResponse
     */
    public function byPromoCode(Request $request, string $promoCode): JsonResponse
    {
        try {
            $branchId = $request->get('branch_id');
            $offer = $this->offerService->getOfferByPromoCode($promoCode, $branchId);

            if (!$offer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Offer not found with this promo code'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Offer retrieved successfully',
                'data' => $offer
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve offer',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Record offer usage
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function recordUsage(Request $request, int $id): JsonResponse
    {
        try {
            $request->validate([
                'customer_id' => 'required|integer',
                'order_id' => 'required|integer',
                'discount_amount' => 'required|numeric|min:0',
                'usage_data' => 'nullable|array'
            ]);

            $recorded = $this->offerService->recordOfferUsage($id, [
                'customer_id' => $request->get('customer_id'),
                'order_id' => $request->get('order_id'),
                'order_total' => $request->get('order_total', 0),
                'discount_amount' => $request->get('discount_amount'),
                'usage_data' => $request->get('usage_data', [])
            ]);

            if (!$recorded) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to record offer usage. Offer may not be found or usage limit reached.'
                ], 400);
            }

            return response()->json([
                'success' => true,
                'message' => 'Offer usage recorded successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to record offer usage',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get offer analytics
     *
     * @param int $id
     * @return JsonResponse
     */
    public function analytics(int $id): JsonResponse
    {
        try {
            $analytics = $this->offerService->getOfferAnalytics($id);

            if (!$analytics) {
                return response()->json([
                    'success' => false,
                    'message' => 'Offer not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Offer analytics retrieved successfully',
                'data' => $analytics
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve offer analytics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get offer performance report
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function performance(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'start_date' => 'required|date',
                'end_date' => 'required|date|after_or_equal:start_date',
                'branch_id' => 'nullable|integer|exists:branches,id',
                'offer_type' => 'nullable|string',
                'discount_type' => 'nullable|string'
            ]);

            $filters = $request->only(['branch_id', 'offer_type', 'discount_type']);
            $startDate = $request->get('start_date');
            $endDate = $request->get('end_date');

            $report = $this->offerService->getOffersPerformanceReport(array_merge($filters, [
                'date_from' => $startDate,
                'date_to' => $endDate
            ]));

            return response()->json([
                'success' => true,
                'message' => 'Offer performance report retrieved successfully',
                'data' => $report
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve offer performance report',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle offer status
     *
     * @param int $id
     * @return JsonResponse
     */
    public function toggleStatus(int $id): JsonResponse
    {
        try {
            $offer = $this->offerService->toggleOfferStatus($id);

            if (!$offer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Offer not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Offer status updated successfully',
                'data' => $offer
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update offer status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Duplicate an offer
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function duplicate(Request $request, int $id): JsonResponse
    {
        try {
            $request->validate([
                'name' => 'required|string|max:255',
                'code' => 'required|string|max:50|unique:offers,code',
                'start_date' => 'required|date|after_or_equal:today',
                'end_date' => 'required|date|after_or_equal:start_date'
            ]);

            $duplicatedOffer = $this->offerService->duplicateOffer($id, [
                'name' => $request->get('name'),
                'code' => $request->get('code'),
                'start_date' => $request->get('start_date'),
                'end_date' => $request->get('end_date')
            ]);

            if (!$duplicatedOffer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Offer not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Offer duplicated successfully',
                'data' => $duplicatedOffer
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to duplicate offer',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get offers by type
     *
     * @param Request $request
     * @param string $type
     * @return JsonResponse
     */
    public function byType(Request $request, string $type): JsonResponse
    {
        try {
            $branchId = $request->get('branch_id');
            $limit = $request->get('limit', 10);
            
            $offers = Offer::forBranch($branchId)
                ->active()
                ->public()
                ->byType($type)
                ->current()
                ->orderBy('priority', 'desc')
                ->orderBy('sort_order', 'asc')
                ->limit($limit)
                ->get();

            return response()->json([
                'success' => true,
                'message' => "Offers of type '{$type}' retrieved successfully",
                'data' => $offers
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve offers by type',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get applicable offers for items
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function applicable(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'item_ids' => 'required|array',
                'item_ids.*' => 'integer|exists:menu_items,id',
                'branch_id' => 'nullable|integer|exists:branches,id',
                'customer_id' => 'nullable|integer'
            ]);

            $itemIds = $request->get('item_ids');
            $branchId = $request->get('branch_id');
            $customerId = $request->get('customer_id');

            $offers = Offer::forBranch($branchId)
                ->active()
                ->public()
                ->current()
                ->where(function ($query) use ($itemIds) {
                    $query->whereNull('applicable_items')
                        ->orWhereJsonContains('applicable_items', $itemIds);
                })
                ->where(function ($query) use ($itemIds) {
                    $query->whereNull('excluded_items')
                        ->orWhereJsonDoesntContain('excluded_items', $itemIds);
                })
                ->orderBy('priority', 'desc')
                ->get()
                ->filter(function ($offer) use ($customerId) {
                    return $offer->isCustomerEligible($customerId);
                });

            return response()->json([
                'success' => true,
                'message' => 'Applicable offers retrieved successfully',
                'data' => $offers->values()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve applicable offers',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get auto-applicable offers
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function autoApplicable(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'order_data' => 'required|array',
                'branch_id' => 'nullable|integer|exists:branches,id',
                'customer_id' => 'nullable|integer'
            ]);

            $orderData = $request->get('order_data');
            $branchId = $request->get('branch_id');
            $customerId = $request->get('customer_id');

            $offers = Offer::forBranch($branchId)
                ->active()
                ->public()
                ->current()
                ->where('auto_apply', true)
                ->orderBy('priority', 'desc')
                ->get()
                ->filter(function ($offer) use ($orderData, $customerId) {
                    // Check if offer is applicable to the order
                    return $offer->isCustomerEligible($customerId) && 
                           $this->checkAutoApplyConditions($offer, $orderData);
                });

            return response()->json([
                'success' => true,
                'message' => 'Auto-applicable offers retrieved successfully',
                'data' => $offers->values()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve auto-applicable offers',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check auto apply conditions for an offer
     *
     * @param Offer $offer
     * @param array $orderData
     * @return bool
     */
    private function checkAutoApplyConditions(Offer $offer, array $orderData): bool
    {
        if (empty($offer->auto_apply_conditions)) {
            return true;
        }

        $conditions = $offer->auto_apply_conditions;
        $orderTotal = $orderData['total'] ?? 0;
        $itemCount = $orderData['item_count'] ?? 0;

        // Check minimum order amount
        if (isset($conditions['min_order_amount']) && $orderTotal < $conditions['min_order_amount']) {
            return false;
        }

        // Check minimum item count
        if (isset($conditions['min_item_count']) && $itemCount < $conditions['min_item_count']) {
            return false;
        }

        // Check specific items
        if (isset($conditions['required_items']) && !empty($conditions['required_items'])) {
            $orderItemIds = collect($orderData['items'] ?? [])->pluck('id')->toArray();
            $requiredItems = $conditions['required_items'];
            
            if (!array_intersect($requiredItems, $orderItemIds)) {
                return false;
            }
        }

        return true;
    }
}