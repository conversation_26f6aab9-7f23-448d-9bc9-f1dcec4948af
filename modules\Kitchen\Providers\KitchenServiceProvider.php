<?php

namespace Modules\Kitchen\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;
use Modules\Kitchen\Services\KitchenService;
use Modules\Kitchen\Services\KotService;

class KitchenServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register the KitchenService
        $this->app->singleton(KitchenService::class, function () {
            return new KitchenService();
        });

        // Register the KotService
        $this->app->singleton(KotService::class, function () {
            return new KotService();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Load routes
        $this->loadRoutes();
        
        // Load views
        $this->loadViewsFrom(__DIR__ . '/../resources/views', 'kitchen');
        
        // Load migrations
        $this->loadMigrationsFrom(__DIR__ . '/../../../database/migrations');
        
        // Register middleware if needed
        // $router = $this->app['router'];
        // $router->aliasMiddleware('kitchen.access', \Modules\Kitchen\Http\Middleware\KitchenAccessMiddleware::class);
        
        // Publish config if needed
        // $this->publishes([
        //     __DIR__ . '/../config/kitchen.php' => config_path('kitchen.php'),
        // ], 'kitchen-config');
    }

    /**
     * Load the module routes.
     */
    protected function loadRoutes(): void
    {
        // Load API routes
        if (file_exists(__DIR__ . '/../routes/api.php')) {
            Route::prefix('api')
                ->middleware('api')
                ->group(__DIR__ . '/../routes/api.php');
        }

        // Load web routes
        if (file_exists(__DIR__ . '/../routes/web.php')) {
            Route::middleware('web')
                ->group(__DIR__ . '/../routes/web.php');
        }
    }
}
