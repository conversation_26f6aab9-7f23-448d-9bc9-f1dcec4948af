/**
 * POS Menu Manager - Optimized for handling large menu datasets (300+ items)
 * Features: Virtual scrolling, advanced search, lazy loading, and efficient rendering
 */

class POSMenuManager {
    constructor() {
        this.allMenuItems = [];
        this.filteredItems = [];
        this.displayedItems = [];
        this.currentPage = 1;
        this.itemsPerPage = 24; // 3 columns x 8 rows
        this.searchTerm = '';
        this.selectedCategory = 'all';
        this.sortBy = 'name';
        this.isVirtualScrollEnabled = true;
        this.virtualScrollBuffer = 5; // Items to render outside viewport
        
        // Search debouncing
        this.searchTimeout = null;
        this.searchDelay = 300;
        
        // Virtual scroll properties
        this.itemHeight = 280; // Approximate height of each menu item
        this.containerHeight = 600;
        this.visibleItems = Math.ceil(this.containerHeight / this.itemHeight) * 3; // 3 columns
        
        this.init();
    }

    /**
     * Initialize the menu manager
     */
    init() {
        this.setupEventListeners();
        this.setupVirtualScroll();
        this.loadMenuItems();
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Search input with debouncing
        const searchInput = document.getElementById('menuSearch');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.handleSearch(e.target.value);
                }, this.searchDelay);
            });

            // Real-time search suggestions
            searchInput.addEventListener('focus', () => {
                this.showSearchSuggestions();
            });

            searchInput.addEventListener('blur', () => {
                setTimeout(() => this.hideSearchSuggestions(), 200);
            });
        }

        // Category tabs
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('category-tab')) {
                this.handleCategoryChange(e.target.dataset.category);
            }
        });

        // Sort dropdown
        const sortSelect = document.getElementById('sortBy');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                this.handleSortChange(e.target.value);
            });
        }

        // View toggle
        const viewToggle = document.getElementById('viewToggle');
        if (viewToggle) {
            viewToggle.addEventListener('click', () => {
                this.toggleView();
            });
        }

        // Pagination
        document.getElementById('prevPage')?.addEventListener('click', () => {
            this.goToPage(this.currentPage - 1);
        });

        document.getElementById('nextPage')?.addEventListener('click', () => {
            this.goToPage(this.currentPage + 1);
        });
    }

    /**
     * Setup virtual scrolling
     */
    setupVirtualScroll() {
        const container = document.getElementById('menuVirtualContainer');
        if (!container) return;

        let ticking = false;
        container.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(() => {
                    this.handleVirtualScroll();
                    ticking = false;
                });
                ticking = true;
            }
        });
    }

    /**
     * Load menu items from server or storage
     */
    async loadMenuItems() {
        this.showLoading(true);
        
        try {
            // Try to load from POS core first
            if (window.posCore && window.posCore.menuItems.length > 0) {
                this.allMenuItems = window.posCore.menuItems;
            } else {
                // Load from API with proper authentication
                const response = await fetch('/api/pos/form-data', {
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    this.allMenuItems = data.data.menu_items || [];
                } else {
                    throw new Error('Failed to load menu items');
                }
            }

            // Process and index items for fast searching
            this.indexMenuItems();
            this.applyFilters();
            this.renderMenuItems();
            
        } catch (error) {
            console.error('Failed to load menu items:', error);
            this.showError('Failed to load menu items');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * Index menu items for fast searching
     */
    indexMenuItems() {
        this.allMenuItems.forEach(item => {
            // Create searchable text
            item._searchText = [
                item.name,
                item.short_description || '',
                item.category?.name || '',
                item.variants?.map(v => v.name).join(' ') || '',
                item.addons?.map(a => a.name).join(' ') || ''
            ].join(' ').toLowerCase();

            // Add popularity score (can be based on order frequency)
            item._popularity = item.order_count || 0;
        });
    }

    /**
     * Handle search with fuzzy matching
     */
    handleSearch(term) {
        this.searchTerm = term.toLowerCase().trim();
        this.currentPage = 1;
        this.applyFilters();
        this.renderMenuItems();
        
        if (term.length > 0) {
            this.updateSearchSuggestions(term);
        }
    }

    /**
     * Apply filters and sorting
     */
    applyFilters() {
        let filtered = [...this.allMenuItems];

        // Apply category filter
        if (this.selectedCategory !== 'all') {
            filtered = filtered.filter(item => 
                (item.category?.name || 'Uncategorized') === this.selectedCategory
            );
        }

        // Apply search filter
        if (this.searchTerm) {
            filtered = filtered.filter(item => {
                // Exact match gets highest priority
                if (item.name.toLowerCase().includes(this.searchTerm)) {
                    item._searchScore = 100;
                    return true;
                }
                
                // Fuzzy search in all text
                if (item._searchText.includes(this.searchTerm)) {
                    item._searchScore = 50;
                    return true;
                }
                
                // Word boundary matching
                const words = this.searchTerm.split(' ');
                const matches = words.filter(word => 
                    item._searchText.includes(word)
                ).length;
                
                if (matches > 0) {
                    item._searchScore = (matches / words.length) * 30;
                    return true;
                }
                
                return false;
            });

            // Sort by search relevance
            filtered.sort((a, b) => (b._searchScore || 0) - (a._searchScore || 0));
        } else {
            // Apply sorting
            this.applySorting(filtered);
        }

        this.filteredItems = filtered;
        this.updatePagination();
    }

    /**
     * Apply sorting to items
     */
    applySorting(items) {
        switch (this.sortBy) {
            case 'name':
                items.sort((a, b) => a.name.localeCompare(b.name));
                break;
            case 'price':
                items.sort((a, b) => parseFloat(a.base_price) - parseFloat(b.base_price));
                break;
            case 'category':
                items.sort((a, b) => {
                    const catA = a.category?.name || 'Uncategorized';
                    const catB = b.category?.name || 'Uncategorized';
                    return catA.localeCompare(catB);
                });
                break;
            case 'popular':
                items.sort((a, b) => (b._popularity || 0) - (a._popularity || 0));
                break;
        }
    }

    /**
     * Handle virtual scrolling
     */
    handleVirtualScroll() {
        if (!this.isVirtualScrollEnabled) return;

        const container = document.getElementById('menuVirtualContainer');
        const scrollTop = container.scrollTop;
        const startIndex = Math.floor(scrollTop / this.itemHeight) * 3; // 3 columns
        const endIndex = Math.min(
            startIndex + this.visibleItems + this.virtualScrollBuffer,
            this.filteredItems.length
        );

        this.renderVisibleItems(startIndex, endIndex);
    }

    /**
     * Render visible items for virtual scrolling
     */
    renderVisibleItems(startIndex, endIndex) {
        const grid = document.getElementById('menuGrid');
        const content = document.getElementById('menuVirtualContent');
        
        // Calculate total height
        const totalHeight = Math.ceil(this.filteredItems.length / 3) * this.itemHeight;
        content.style.height = totalHeight + 'px';
        
        // Calculate offset
        const offsetY = Math.floor(startIndex / 3) * this.itemHeight;
        grid.style.transform = `translateY(${offsetY}px)`;
        
        // Render items
        const visibleItems = this.filteredItems.slice(startIndex, endIndex);
        this.renderItemsToGrid(visibleItems);
    }

    /**
     * Render menu items
     */
    renderMenuItems() {
        if (this.filteredItems.length === 0) {
            this.showNoResults(true);
            return;
        }

        this.showNoResults(false);

        if (this.isVirtualScrollEnabled && this.filteredItems.length > 50) {
            this.handleVirtualScroll();
        } else {
            // Render with pagination
            const startIndex = (this.currentPage - 1) * this.itemsPerPage;
            const endIndex = Math.min(startIndex + this.itemsPerPage, this.filteredItems.length);
            const pageItems = this.filteredItems.slice(startIndex, endIndex);
            
            this.renderItemsToGrid(pageItems);
            this.updatePagination();
        }
    }

    /**
     * Render items to grid
     */
    renderItemsToGrid(items) {
        const grid = document.getElementById('menuGrid');
        if (!grid) return;

        const itemsHtml = items.map(item => this.createMenuItemHTML(item)).join('');
        grid.innerHTML = itemsHtml;

        // Reattach event listeners
        this.attachItemEventListeners();
    }

    /**
     * Create HTML for a menu item
     */
    createMenuItemHTML(item) {
        const hasVariants = item.variants && item.variants.length > 0;
        const hasAddons = item.addons && item.addons.length > 0;
        const imageUrl = item.image ? item.image : null;
        
        return `
            <div class="menu-item" 
                 data-item-id="${item.id}" 
                 data-item-name="${item.name}" 
                 data-item-price="${item.base_price}" 
                 data-category="${item.category?.name || 'Uncategorized'}">
                
                ${hasVariants || hasAddons ? `
                    <div class="menu-item-badges">
                        ${hasVariants ? '<span class="menu-badge variant" title="Has Variants">V</span>' : ''}
                        ${hasAddons ? '<span class="menu-badge addon" title="Has Add-ons">A</span>' : ''}
                    </div>
                ` : ''}
                
                ${imageUrl ? `
                    <img src="${imageUrl}" alt="${item.name}" class="menu-item-image" 
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <div class="menu-item-placeholder" style="display: none;">
                        <i class="mdi mdi-food text-5xl text-gray-400"></i>
                    </div>
                ` : `
                    <div class="menu-item-placeholder">
                        <i class="mdi mdi-food text-5xl text-gray-400"></i>
                    </div>
                `}
                
                <div class="menu-item-content">
                    <h3 class="menu-item-title">${item.name}</h3>
                    ${item.short_description ? `
                        <p class="menu-item-description line-clamp-2">${item.short_description}</p>
                    ` : ''}
                    <div class="menu-item-price">$${parseFloat(item.base_price).toFixed(2)}</div>
                    <button class="add-to-order-btn" 
                            data-item-id="${item.id}"
                            data-item-name="${item.name}" 
                            data-item-price="${item.base_price}">
                        <i class="mdi mdi-plus mr-2"></i>
                        Add to Order
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Attach event listeners to menu items
     */
    attachItemEventListeners() {
        document.querySelectorAll('.add-to-order-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const itemId = e.target.dataset.itemId;
                const itemName = e.target.dataset.itemName;
                const itemPrice = parseFloat(e.target.dataset.itemPrice);
                
                // Use existing POS functionality
                if (window.posCore) {
                    window.posCore.addToOrder(itemId, 1);
                } else {
                    // Fallback to existing function
                    if (typeof checkItemVariantsAddons === 'function') {
                        checkItemVariantsAddons(itemId, itemName, itemPrice);
                    }
                }
            });
        });
    }

    /**
     * Handle category change
     */
    handleCategoryChange(category) {
        this.selectedCategory = category;
        this.currentPage = 1;
        
        // Update active tab
        document.querySelectorAll('.category-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-category="${category}"]`)?.classList.add('active');
        
        this.applyFilters();
        this.renderMenuItems();
    }

    /**
     * Handle sort change
     */
    handleSortChange(sortBy) {
        this.sortBy = sortBy;
        this.applyFilters();
        this.renderMenuItems();
    }

    /**
     * Update pagination
     */
    updatePagination() {
        const totalPages = Math.ceil(this.filteredItems.length / this.itemsPerPage);
        const pagination = document.getElementById('menuPagination');
        const pageInfo = document.getElementById('pageInfo');
        const prevBtn = document.getElementById('prevPage');
        const nextBtn = document.getElementById('nextPage');

        if (totalPages <= 1) {
            pagination?.classList.add('hidden');
            return;
        }

        pagination?.classList.remove('hidden');
        
        if (pageInfo) {
            pageInfo.textContent = `Page ${this.currentPage} of ${totalPages}`;
        }
        
        if (prevBtn) {
            prevBtn.disabled = this.currentPage <= 1;
        }
        
        if (nextBtn) {
            nextBtn.disabled = this.currentPage >= totalPages;
        }
    }

    /**
     * Go to specific page
     */
    goToPage(page) {
        const totalPages = Math.ceil(this.filteredItems.length / this.itemsPerPage);
        
        if (page < 1 || page > totalPages) return;
        
        this.currentPage = page;
        this.renderMenuItems();
    }

    /**
     * Show/hide loading indicator
     */
    showLoading(show) {
        const loading = document.getElementById('menuLoading');
        if (loading) {
            loading.classList.toggle('hidden', !show);
        }
    }

    /**
     * Show/hide no results message
     */
    showNoResults(show) {
        const noResults = document.getElementById('noResults');
        const grid = document.getElementById('menuGrid');
        
        if (noResults) {
            noResults.classList.toggle('hidden', !show);
        }
        
        if (grid && show) {
            grid.innerHTML = '';
        }
    }

    /**
     * Show error message
     */
    showError(message) {
        if (window.posCore && window.posCore.ui) {
            window.posCore.ui.showError(message);
        } else {
            alert(message);
        }
    }

    /**
     * Clear search
     */
    clearSearch() {
        const searchInput = document.getElementById('menuSearch');
        if (searchInput) {
            searchInput.value = '';
            this.handleSearch('');
        }
    }

    /**
     * Show search suggestions
     */
    showSearchSuggestions() {
        // Implementation for search suggestions dropdown
        const suggestions = document.getElementById('searchResults');
        if (suggestions && this.searchTerm.length > 0) {
            suggestions.classList.remove('hidden');
        }
    }

    /**
     * Hide search suggestions
     */
    hideSearchSuggestions() {
        const suggestions = document.getElementById('searchResults');
        if (suggestions) {
            suggestions.classList.add('hidden');
        }
    }

    /**
     * Update search suggestions
     */
    updateSearchSuggestions(term) {
        const suggestions = document.getElementById('searchResults');
        if (!suggestions) return;

        const matches = this.allMenuItems
            .filter(item => item.name.toLowerCase().includes(term))
            .slice(0, 5);

        if (matches.length > 0) {
            const html = matches.map(item => `
                <div class="p-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0" 
                     onclick="selectSearchSuggestion('${item.name}')">
                    <div class="font-medium">${item.name}</div>
                    <div class="text-sm text-gray-600">$${parseFloat(item.base_price).toFixed(2)}</div>
                </div>
            `).join('');
            
            suggestions.innerHTML = html;
            suggestions.classList.remove('hidden');
        } else {
            suggestions.classList.add('hidden');
        }
    }

    /**
     * Toggle view between grid and list
     */
    toggleView() {
        // Implementation for view toggle
        const grid = document.getElementById('menuGrid');
        const toggle = document.getElementById('viewToggle');
        
        if (grid.classList.contains('grid-cols-3')) {
            // Switch to list view
            grid.className = 'space-y-2 p-4';
            toggle.innerHTML = '<i class="mdi mdi-view-grid"></i>';
        } else {
            // Switch to grid view
            grid.className = 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 p-4';
            toggle.innerHTML = '<i class="mdi mdi-view-list"></i>';
        }
    }
}

// Global function for search suggestions
function selectSearchSuggestion(itemName) {
    const searchInput = document.getElementById('menuSearch');
    if (searchInput) {
        searchInput.value = itemName;
        if (window.menuManager) {
            window.menuManager.handleSearch(itemName);
        }
    }
}

// Global function for clearing search
function clearSearch() {
    if (window.menuManager) {
        window.menuManager.clearSearch();
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.menuManager = new POSMenuManager();
});
