<?php

namespace Modules\Orders\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Modules\Orders\Http\Requests\StoreOrderRequest;
use Modules\Orders\Http\Requests\UpdateOrderRequest;
use Modules\Orders\Services\OrderService;
use Modules\Kitchen\Services\KitchenService;

class OrderController extends Controller
{
    protected $orderService;
    protected $kitchenService;

    public function __construct(OrderService $orderService, KitchenService $kitchenService)
    {
        $this->orderService = $orderService;
        $this->kitchenService = $kitchenService;
    }

    /**
     * Display a listing of orders.
     */
    public function index(Request $request)
    {
        try {
            $user = auth()->user();

            if (!$user->branch_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is not assigned to any branch'
                ], 400);
            }

            $orders = $this->orderService->getOrdersForBranch($user->branch_id, $request->all());
            
            return response()->json([
                'success' => true,
                'data' => $orders
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created order.
     */
    public function store(StoreOrderRequest $request)
    {
        try {
            $user = auth()->user();
            
            $data = $request->validated();
            $data['branch_id'] = $user->branch_id;
            $data['tenant_id'] = $user->tenant_id;
            $data['cashier_id'] = $user->id;

            $order = $this->orderService->createOrder($data);

            return response()->json([
                'success' => true,
                'message' => 'Order created successfully',
                'data' => $order
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Display the specified order.
     */
    public function show(string $id)
    {
        try {
            $user = auth()->user();
            $order = $this->orderService->getOrderById($id, $user->branch_id);
            
            return response()->json([
                'success' => true,
                'data' => $order
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Order not found'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified order.
     */
    public function update(UpdateOrderRequest $request, string $id)
    {
        try {
            $user = auth()->user();
            $order = $this->orderService->updateOrder($id, $request->validated(), $user->branch_id);
            
            return response()->json([
                'success' => true,
                'message' => 'Order updated successfully',
                'data' => $order
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Order not found'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Remove the specified order.
     */
    public function destroy(string $id)
    {
        try {
            $user = auth()->user();
            $this->orderService->deleteOrder($id, $user->branch_id);
            
            return response()->json([
                'success' => true,
                'message' => 'Order deleted successfully'
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Order not found'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Update order status.
     */
    public function updateStatus(Request $request, string $id)
    {
        $request->validate([
            'status' => 'required|string|in:pending,confirmed,preparing,ready,delivered,cancelled'
        ]);

        try {
            $user = auth()->user();
            $order = $this->orderService->updateOrderStatus($id, $request->status, $user->branch_id);
            
            // Generate KOT if order is confirmed and doesn't already have an active KOT
            if ($request->status === 'confirmed') {
                try {
                    if (!$order->hasActiveKot()) {
                        $order->load(['orderItems.menuItem']);
                        $this->kitchenService->createKotFromOrder($order);
                        \Log::info('KOT created for order ' . $order->id . ' during status update');
                    }
                } catch (\Exception $e) {
                    \Log::error('Failed to create KOT for order ' . $order->id . ': ' . $e->getMessage());
                }
            }
            
            return response()->json([
                'success' => true,
                'message' => 'Order status updated successfully',
                'data' => $order
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Order not found'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get order items.
     */
    public function getOrderItems(string $id)
    {
        try {
            $user = auth()->user();
            $orderItems = $this->orderService->getOrderItems($id, $user->branch_id);
            
            return response()->json([
                'success' => true,
                'data' => $orderItems
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Add item to order.
     */
    public function addOrderItem(Request $request, string $id)
    {
        $request->validate([
            'menu_item_id' => 'required|exists:menu_items,id',
            'quantity' => 'required|integer|min:1',
            'price' => 'required|numeric|min:0',
            'notes' => 'nullable|string'
        ]);

        try {
            $user = auth()->user();
            $orderItem = $this->orderService->addOrderItem($id, $request->all(), $user->branch_id);
            
            return response()->json([
                'success' => true,
                'message' => 'Item added to order successfully',
                'data' => $orderItem
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Remove item from order.
     */
    public function removeOrderItem(string $id, string $itemId)
    {
        try {
            $user = auth()->user();
            $this->orderService->removeOrderItem($id, $itemId, $user->branch_id);
            
            return response()->json([
                'success' => true,
                'message' => 'Item removed from order successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Confirm an order and create KOT
     */
    public function confirmOrder(Request $request, string $id)
    {
        try {
            $user = auth()->user();
            
            // Get the order
            $order = \App\Models\Order::where('id', $id)
                ->where('branch_id', $user->branch_id)
                ->firstOrFail();
            
            // Check if order can be confirmed
            if ($order->status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => 'Order cannot be confirmed. Current status: ' . $order->status
                ], 400);
            }
            
            // Update order status to confirmed
            $order = $this->orderService->updateOrderStatus($id, 'confirmed', $user->branch_id);
            
            // Generate KOT if not already exists
            try {
                if (!$order->hasActiveKot()) {
                    $order->load(['orderItems.menuItem']);
                    $kotOrders = $this->kitchenService->createKotFromOrder($order);

                    return response()->json([
                        'success' => true,
                        'message' => 'Order confirmed successfully and KOT created',
                        'data' => [
                            'order' => $order,
                            'kot_orders' => $kotOrders
                        ]
                    ]);
                } else {
                    return response()->json([
                        'success' => true,
                        'message' => 'Order confirmed successfully (KOT already exists)',
                        'data' => [
                            'order' => $order,
                            'kot_orders' => $order->kotOrders
                        ]
                    ]);
                }
            } catch (\Exception $e) {
                \Log::error('Failed to create KOT for order ' . $order->id . ': ' . $e->getMessage());

                return response()->json([
                    'success' => true,
                    'message' => 'Order confirmed successfully, but KOT creation failed',
                    'data' => [
                        'order' => $order,
                        'kot_error' => $e->getMessage()
                    ]
                ]);
            }
            
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Order not found'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to confirm order: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get valid addons for a menu item.
     */
    public function getMenuItemAddons(string $menuItemId)
    {
        try {
            $addons = \App\Models\MenuItemAddon::where('menu_item_id', $menuItemId)
                ->orderBy('sort_order')
                ->orderBy('name')
                ->get(['id', 'name', 'price', 'addon_group_name', 'is_required', 'max_quantity']);

            return response()->json([
                'success' => true,
                'data' => $addons
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get order statuses
     */
    public function getOrderStatuses()
    {
        $statuses = [
            'pending' => 'معلق',
            'confirmed' => 'مؤكد',
            'preparing' => 'قيد التحضير',
            'ready' => 'جاهز',
            'served' => 'تم التقديم',
            'completed' => 'مكتمل',
            'cancelled' => 'ملغي'
        ];

        return response()->json([
            'success' => true,
            'data' => $statuses
        ]);
    }

    /**
     * Get order types
     */
    public function getOrderTypes()
    {
        $types = [
            'dine_in' => 'تناول في المطعم',
            'takeaway' => 'طلب خارجي',
            'delivery' => 'توصيل',
            'online' => 'طلب أونلاين'
        ];

        return response()->json([
            'success' => true,
            'data' => $types
        ]);
    }
}