@extends('layouts.master')

@section('title', 'Report Configuration Settings')

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
@endpush

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg shadow-lg p-6 mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                            <i class="fas fa-chart-bar text-white text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <h1 class="text-2xl font-bold text-white">Report Configuration Settings</h1>
                        <p class="text-indigo-100">Configure report generation, scheduling, and distribution</p>
                    </div>
                </div>
            </div>
            <div class="mt-4 md:mt-0 flex space-x-3">
                <button id="preview-report-btn" class="px-4 py-2 bg-white text-indigo-600 rounded-lg hover:bg-indigo-50 transition-colors duration-200 font-medium">
                    <i class="fas fa-eye mr-2"></i>
                    Preview Report
                </button>
                <button id="generate-report-btn" class="px-4 py-2 bg-indigo-500 text-white rounded-lg hover:bg-indigo-400 transition-colors duration-200 font-medium">
                    <i class="fas fa-file-export mr-2"></i>
                    Generate Report
                </button>
            </div>
        </div>
    </div>

    <!-- Report Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-calendar-day text-blue-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Daily Reports</p>
                    <p class="text-2xl font-bold text-gray-900">12</p>
                    <p class="text-xs text-blue-600">Active</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-calendar-week text-green-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Weekly Reports</p>
                    <p class="text-2xl font-bold text-gray-900">8</p>
                    <p class="text-xs text-green-600">Scheduled</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-calendar-alt text-purple-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Monthly Reports</p>
                    <p class="text-2xl font-bold text-gray-900">5</p>
                    <p class="text-xs text-purple-600">Automated</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-download text-orange-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Downloads</p>
                    <p class="text-2xl font-bold text-gray-900">247</p>
                    <p class="text-xs text-orange-600">This month</p>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Report Types & Templates -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-file-alt mr-2 text-indigo-600"></i>
                Report Types & Templates
            </h3>
            
            <form class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Available Report Types
                    </label>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" name="sales_reports" checked class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                            <span class="ml-2 text-sm text-gray-700">Sales Reports</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="inventory_reports" checked class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                            <span class="ml-2 text-sm text-gray-700">Inventory Reports</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="financial_reports" checked class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                            <span class="ml-2 text-sm text-gray-700">Financial Reports</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="customer_reports" checked class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                            <span class="ml-2 text-sm text-gray-700">Customer Reports</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="staff_reports" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                            <span class="ml-2 text-sm text-gray-700">Staff Performance Reports</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="tax_reports" checked class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                            <span class="ml-2 text-sm text-gray-700">Tax Reports</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="waste_reports" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                            <span class="ml-2 text-sm text-gray-700">Waste Management Reports</span>
                        </label>
                    </div>
                </div>

                <div>
                    <label for="default_template" class="block text-sm font-medium text-gray-700 mb-2">
                        Default Report Template
                    </label>
                    <select id="default_template" name="default_template" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="standard" selected>Standard Template</option>
                        <option value="detailed">Detailed Template</option>
                        <option value="summary">Summary Template</option>
                        <option value="executive">Executive Template</option>
                        <option value="custom">Custom Template</option>
                    </select>
                </div>

                <div>
                    <label for="date_format" class="block text-sm font-medium text-gray-700 mb-2">
                        Date Format
                    </label>
                    <select id="date_format" name="date_format" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="Y-m-d" selected>YYYY-MM-DD</option>
                        <option value="d/m/Y">DD/MM/YYYY</option>
                        <option value="m/d/Y">MM/DD/YYYY</option>
                        <option value="d-M-Y">DD-MMM-YYYY</option>
                    </select>
                </div>

                <div>
                    <label for="currency_format" class="block text-sm font-medium text-gray-700 mb-2">
                        Currency Format
                    </label>
                    <select id="currency_format" name="currency_format" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="$1,234.56" selected>$1,234.56</option>
                        <option value="1,234.56 USD">1,234.56 USD</option>
                        <option value="USD 1,234.56">USD 1,234.56</option>
                        <option value="1.234,56 €">1.234,56 €</option>
                    </select>
                </div>

                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div>
                            <label for="include_charts" class="text-sm font-medium text-gray-700">Include Charts</label>
                            <p class="text-sm text-gray-500">Add visual charts to reports</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="include_charts" name="include_charts" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="include_logo" class="text-sm font-medium text-gray-700">Include Company Logo</label>
                            <p class="text-sm text-gray-500">Add logo to report headers</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="include_logo" name="include_logo" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="watermark" class="text-sm font-medium text-gray-700">Add Watermark</label>
                            <p class="text-sm text-gray-500">Add confidential watermark</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="watermark" name="watermark" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                        </label>
                    </div>
                </div>

                <div class="pt-4">
                    <button type="submit" class="w-full px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-lg hover:bg-indigo-700">
                        <i class="fas fa-save mr-2"></i>
                        Save Template Settings
                    </button>
                </div>
            </form>
        </div>

        <!-- Export & Format Settings -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-file-export mr-2 text-green-600"></i>
                Export & Format Settings
            </h3>
            
            <form class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Export Formats
                    </label>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" name="format_pdf" checked class="rounded border-gray-300 text-green-600 focus:ring-green-500">
                            <span class="ml-2 text-sm text-gray-700">PDF</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="format_excel" checked class="rounded border-gray-300 text-green-600 focus:ring-green-500">
                            <span class="ml-2 text-sm text-gray-700">Excel (.xlsx)</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="format_csv" checked class="rounded border-gray-300 text-green-600 focus:ring-green-500">
                            <span class="ml-2 text-sm text-gray-700">CSV</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="format_html" class="rounded border-gray-300 text-green-600 focus:ring-green-500">
                            <span class="ml-2 text-sm text-gray-700">HTML</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="format_json" class="rounded border-gray-300 text-green-600 focus:ring-green-500">
                            <span class="ml-2 text-sm text-gray-700">JSON</span>
                        </label>
                    </div>
                </div>

                <div>
                    <label for="default_format" class="block text-sm font-medium text-gray-700 mb-2">
                        Default Export Format
                    </label>
                    <select id="default_format" name="default_format" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                        <option value="pdf" selected>PDF</option>
                        <option value="excel">Excel</option>
                        <option value="csv">CSV</option>
                        <option value="html">HTML</option>
                    </select>
                </div>

                <div>
                    <label for="page_orientation" class="block text-sm font-medium text-gray-700 mb-2">
                        Page Orientation (PDF)
                    </label>
                    <select id="page_orientation" name="page_orientation" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                        <option value="portrait" selected>Portrait</option>
                        <option value="landscape">Landscape</option>
                    </select>
                </div>

                <div>
                    <label for="page_size" class="block text-sm font-medium text-gray-700 mb-2">
                        Page Size (PDF)
                    </label>
                    <select id="page_size" name="page_size" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                        <option value="A4" selected>A4</option>
                        <option value="A3">A3</option>
                        <option value="Letter">Letter</option>
                        <option value="Legal">Legal</option>
                    </select>
                </div>

                <div>
                    <label for="compression_level" class="block text-sm font-medium text-gray-700 mb-2">
                        Compression Level
                    </label>
                    <select id="compression_level" name="compression_level" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                        <option value="none">None</option>
                        <option value="low">Low</option>
                        <option value="medium" selected>Medium</option>
                        <option value="high">High</option>
                    </select>
                </div>

                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div>
                            <label for="password_protect" class="text-sm font-medium text-gray-700">Password Protection</label>
                            <p class="text-sm text-gray-500">Protect reports with password</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="password_protect" name="password_protect" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="digital_signature" class="text-sm font-medium text-gray-700">Digital Signature</label>
                            <p class="text-sm text-gray-500">Add digital signature to PDFs</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="digital_signature" name="digital_signature" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                        </label>
                    </div>
                </div>

                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h4 class="text-sm font-medium text-green-900 mb-2">Export Statistics</h4>
                    <div class="space-y-2 text-sm text-green-800">
                        <div class="flex justify-between">
                            <span>PDF Downloads:</span>
                            <span class="font-medium">156 (63%)</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Excel Downloads:</span>
                            <span class="font-medium">67 (27%)</span>
                        </div>
                        <div class="flex justify-between">
                            <span>CSV Downloads:</span>
                            <span class="font-medium">24 (10%)</span>
                        </div>
                    </div>
                </div>

                <div class="pt-4">
                    <button type="submit" class="w-full px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-lg hover:bg-green-700">
                        <i class="fas fa-save mr-2"></i>
                        Save Export Settings
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Automated Reporting & Scheduling -->
    <div class="mt-6 bg-white rounded-lg shadow-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fas fa-clock mr-2 text-blue-600"></i>
            Automated Reporting & Scheduling
        </h3>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div class="space-y-4">
                <h4 class="font-medium text-gray-900">Schedule Settings</h4>
                
                <form class="space-y-4">
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <div>
                                <label for="auto_reports" class="text-sm font-medium text-gray-700">Enable Auto Reports</label>
                                <p class="text-sm text-gray-500">Automatically generate scheduled reports</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" id="auto_reports" name="auto_reports" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                        </div>
                    </div>

                    <div>
                        <label for="daily_report_time" class="block text-sm font-medium text-gray-700 mb-2">
                            Daily Report Time
                        </label>
                        <input type="time" id="daily_report_time" name="daily_report_time" value="08:00"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>

                    <div>
                        <label for="weekly_report_day" class="block text-sm font-medium text-gray-700 mb-2">
                            Weekly Report Day
                        </label>
                        <select id="weekly_report_day" name="weekly_report_day" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="monday" selected>Monday</option>
                            <option value="tuesday">Tuesday</option>
                            <option value="wednesday">Wednesday</option>
                            <option value="thursday">Thursday</option>
                            <option value="friday">Friday</option>
                            <option value="saturday">Saturday</option>
                            <option value="sunday">Sunday</option>
                        </select>
                    </div>

                    <div>
                        <label for="monthly_report_date" class="block text-sm font-medium text-gray-700 mb-2">
                            Monthly Report Date
                        </label>
                        <select id="monthly_report_date" name="monthly_report_date" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="1" selected>1st of month</option>
                            <option value="15">15th of month</option>
                            <option value="last">Last day of month</option>
                        </select>
                    </div>

                    <div>
                        <label for="report_recipients" class="block text-sm font-medium text-gray-700 mb-2">
                            Report Recipients
                        </label>
                        <textarea id="report_recipients" name="report_recipients" rows="3" 
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                  placeholder="<EMAIL>, <EMAIL>"><EMAIL>
<EMAIL>
<EMAIL></textarea>
                    </div>

                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <div>
                                <label for="email_delivery" class="text-sm font-medium text-gray-700">Email Delivery</label>
                                <p class="text-sm text-gray-500">Send reports via email</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" id="email_delivery" name="email_delivery" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                        </div>

                        <div class="flex items-center justify-between">
                            <div>
                                <label for="cloud_storage" class="text-sm font-medium text-gray-700">Cloud Storage</label>
                                <p class="text-sm text-gray-500">Save reports to cloud storage</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" id="cloud_storage" name="cloud_storage" class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after-border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                        </div>

                        <div class="flex items-center justify-between">
                            <div>
                                <label for="archive_reports" class="text-sm font-medium text-gray-700">Archive Old Reports</label>
                                <p class="text-sm text-gray-500">Automatically archive reports older than 1 year</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" id="archive_reports" name="archive_reports" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                        </div>
                    </div>
                </form>
            </div>

            <div class="space-y-4">
                <h4 class="font-medium text-gray-900">Scheduled Reports</h4>
                
                <div class="space-y-3 max-h-64 overflow-y-auto">
                    <div class="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-calendar-day text-blue-600 mr-3"></i>
                            <div>
                                <p class="text-sm font-medium text-gray-900">Daily Sales Report</p>
                                <p class="text-xs text-gray-500">Every day at 8:00 AM</p>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded hover:bg-blue-200">
                                Edit
                            </button>
                            <button class="px-3 py-1 text-xs font-medium text-red-700 bg-red-100 rounded hover:bg-red-200">
                                Delete
                            </button>
                        </div>
                    </div>

                    <div class="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-calendar-week text-green-600 mr-3"></i>
                            <div>
                                <p class="text-sm font-medium text-gray-900">Weekly Inventory Report</p>
                                <p class="text-xs text-gray-500">Every Monday at 9:00 AM</p>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 text-xs font-medium text-green-700 bg-green-100 rounded hover:bg-green-200">
                                Edit
                            </button>
                            <button class="px-3 py-1 text-xs font-medium text-red-700 bg-red-100 rounded hover:bg-red-200">
                                Delete
                            </button>
                        </div>
                    </div>

                    <div class="flex items-center justify-between p-3 bg-purple-50 border border-purple-200 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-calendar-alt text-purple-600 mr-3"></i>
                            <div>
                                <p class="text-sm font-medium text-gray-900">Monthly Financial Report</p>
                                <p class="text-xs text-gray-500">1st of every month at 10:00 AM</p>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 text-xs font-medium text-purple-700 bg-purple-100 rounded hover:bg-purple-200">
                                Edit
                            </button>
                            <button class="px-3 py-1 text-xs font-medium text-red-700 bg-red-100 rounded hover:bg-red-200">
                                Delete
                            </button>
                        </div>
                    </div>

                    <div class="flex items-center justify-between p-3 bg-orange-50 border border-orange-200 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-users text-orange-600 mr-3"></i>
                            <div>
                                <p class="text-sm font-medium text-gray-900">Customer Analytics Report</p>
                                <p class="text-xs text-gray-500">Every Friday at 5:00 PM</p>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 text-xs font-medium text-orange-700 bg-orange-100 rounded hover:bg-orange-200">
                                Edit
                            </button>
                            <button class="px-3 py-1 text-xs font-medium text-red-700 bg-red-100 rounded hover:bg-red-200">
                                Delete
                            </button>
                        </div>
                    </div>
                </div>

                <button class="w-full px-4 py-2 text-sm font-medium text-blue-700 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100">
                    <i class="fas fa-plus mr-2"></i>
                    Add New Scheduled Report
                </button>

                <div class="pt-4">
                    <button class="w-full px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700">
                        <i class="fas fa-save mr-2"></i>
                        Save Schedule Settings
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Access & Permissions -->
    <div class="mt-6 bg-white rounded-lg shadow-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fas fa-shield-alt mr-2 text-red-600"></i>
            Data Access & Permissions
        </h3>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div class="space-y-4">
                <h4 class="font-medium text-gray-900">Role-Based Access</h4>
                
                <div class="space-y-3">
                    <div class="p-3 border border-gray-200 rounded-lg">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-900">Super Admin</span>
                            <span class="text-xs text-green-600 bg-green-100 px-2 py-1 rounded">Full Access</span>
                        </div>
                        <p class="text-xs text-gray-500">Access to all reports and data</p>
                    </div>

                    <div class="p-3 border border-gray-200 rounded-lg">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-900">Manager</span>
                            <span class="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">Limited Access</span>
                        </div>
                        <p class="text-xs text-gray-500">Sales, inventory, and operational reports</p>
                    </div>

                    <div class="p-3 border border-gray-200 rounded-lg">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-900">Staff</span>
                            <span class="text-xs text-yellow-600 bg-yellow-100 px-2 py-1 rounded">Basic Access</span>
                        </div>
                        <p class="text-xs text-gray-500">Basic sales and shift reports only</p>
                    </div>

                    <div class="p-3 border border-gray-200 rounded-lg">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-900">Accountant</span>
                            <span class="text-xs text-purple-600 bg-purple-100 px-2 py-1 rounded">Financial Access</span>
                        </div>
                        <p class="text-xs text-gray-500">Financial and tax reports only</p>
                    </div>
                </div>
            </div>

            <div class="space-y-4">
                <h4 class="font-medium text-gray-900">Data Sensitivity</h4>
                
                <form class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div>
                            <label for="mask_sensitive_data" class="text-sm font-medium text-gray-700">Mask Sensitive Data</label>
                            <p class="text-sm text-gray-500">Hide sensitive information in reports</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="mask_sensitive_data" name="mask_sensitive_data" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="audit_trail" class="text-sm font-medium text-gray-700">Audit Trail</label>
                            <p class="text-sm text-gray-500">Log all report access and downloads</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="audit_trail" name="audit_trail" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="data_retention" class="text-sm font-medium text-gray-700">Data Retention Policy</label>
                            <p class="text-sm text-gray-500">Automatically delete old report data</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="data_retention" name="data_retention" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                        </label>
                    </div>
                </form>

                <div>
                    <label for="retention_period" class="block text-sm font-medium text-gray-700 mb-2">
                        Retention Period (years)
                    </label>
                    <input type="number" id="retention_period" name="retention_period" value="7" min="1" max="10"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500">
                </div>
            </div>

            <div class="space-y-4">
                <h4 class="font-medium text-gray-900">Recent Activity</h4>
                
                <div class="space-y-2 max-h-48 overflow-y-auto">
                    <div class="text-xs p-2 bg-gray-50 rounded">
                        <p class="font-medium">Sales Report Downloaded</p>
                        <p class="text-gray-500">John Doe - 2 hours ago</p>
                    </div>
                    <div class="text-xs p-2 bg-gray-50 rounded">
                        <p class="font-medium">Monthly Report Generated</p>
                        <p class="text-gray-500">System - 1 day ago</p>
                    </div>
                    <div class="text-xs p-2 bg-gray-50 rounded">
                        <p class="font-medium">Inventory Report Viewed</p>
                        <p class="text-gray-500">Jane Smith - 2 days ago</p>
                    </div>
                    <div class="text-xs p-2 bg-gray-50 rounded">
                        <p class="font-medium">Financial Report Exported</p>
                        <p class="text-gray-500">Mike Johnson - 3 days ago</p>
                    </div>
                </div>

                <button class="w-full px-3 py-2 text-sm font-medium text-red-700 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100">
                    <i class="fas fa-history mr-2"></i>
                    View Full Audit Log
                </button>

                <div class="pt-4">
                    <button class="w-full px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-lg hover:bg-red-700">
                        <i class="fas fa-save mr-2"></i>
                        Save Permission Settings
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Form submission handlers
    $('form').on('submit', function(e) {
        e.preventDefault();
        
        Swal.fire({
            icon: 'success',
            title: 'Success!',
            text: 'Report settings updated successfully.',
            timer: 2000,
            showConfirmButton: false
        });
    });

    // Preview report handler
    $('#preview-report-btn').on('click', function() {
        Swal.fire({
            title: 'Select Report Type',
            input: 'select',
            inputOptions: {
                'sales': 'Sales Report',
                'inventory': 'Inventory Report',
                'financial': 'Financial Report',
                'customer': 'Customer Report'
            },
            inputPlaceholder: 'Choose a report type',
            showCancelButton: true,
            confirmButtonText: 'Preview',
            inputValidator: (value) => {
                if (!value) {
                    return 'Please select a report type!';
                }
            }
        }).then((result) => {
            if (result.isConfirmed) {
                Swal.fire({
                    icon: 'info',
                    title: 'Generating Preview...',
                    text: `Creating preview for ${result.value} report.`,
                    timer: 2000,
                    showConfirmButton: false
                });
            }
        });
    });

    // Generate report handler
    $('#generate-report-btn').on('click', function() {
        Swal.fire({
            title: 'Generate Report',
            html: `
                <div class="text-left space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Report Type:</label>
                        <select id="report-type" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                            <option value="sales">Sales Report</option>
                            <option value="inventory">Inventory Report</option>
                            <option value="financial">Financial Report</option>
                            <option value="customer">Customer Report</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Date Range:</label>
                        <select id="date-range" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                            <option value="today">Today</option>
                            <option value="yesterday">Yesterday</option>
                            <option value="this_week">This Week</option>
                            <option value="last_week">Last Week</option>
                            <option value="this_month">This Month</option>
                            <option value="last_month">Last Month</option>
                            <option value="custom">Custom Range</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Format:</label>
                        <select id="report-format" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                            <option value="pdf">PDF</option>
                            <option value="excel">Excel</option>
                            <option value="csv">CSV</option>
                        </select>
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'Generate',
            cancelButtonText: 'Cancel',
            preConfirm: () => {
                const reportType = document.getElementById('report-type').value;
                const dateRange = document.getElementById('date-range').value;
                const format = document.getElementById('report-format').value;
                
                return { reportType, dateRange, format };
            }
        }).then((result) => {
            if (result.isConfirmed) {
                Swal.fire({
                    title: 'Generating Report...',
                    text: 'Please wait while we generate your report.',
                    icon: 'info',
                    showConfirmButton: false,
                    timer: 3000
                }).then(() => {
                    Swal.fire({
                        icon: 'success',
                        title: 'Report Generated',
                        text: 'Your report has been generated and is ready for download.',
                        confirmButtonText: 'Download'
                    });
                });
            }
        });
    });

    // Template change handler
    $('#default_template').on('change', function() {
        const template = $(this).val();
        let description = '';
        
        switch(template) {
            case 'standard':
                description = 'Basic report layout with essential information.';
                break;
            case 'detailed':
                description = 'Comprehensive report with detailed breakdowns and analysis.';
                break;
            case 'summary':
                description = 'Condensed report focusing on key metrics and highlights.';
                break;
            case 'executive':
                description = 'High-level overview designed for executive review.';
                break;
            case 'custom':
                description = 'Customizable template that can be modified to your needs.';
                break;
        }
        
        if (description) {
            Swal.fire({
                title: 'Template: ' + template.charAt(0).toUpperCase() + template.slice(1),
                text: description,
                icon: 'info',
                timer: 3000,
                showConfirmButton: false
            });
        }
    });

    // Format change handler
    $('#default_format').on('change', function() {
        const format = $(this).val();
        
        // Show/hide format-specific options
        if (format === 'pdf') {
            $('#page_orientation, #page_size').closest('div').show();
        } else {
            $('#page_orientation, #page_size').closest('div').hide();
        }
    });

    // Password protection toggle
    $('#password_protect').on('change', function() {
        if ($(this).is(':checked')) {
            Swal.fire({
                title: 'Set Default Password',
                input: 'password',
                inputPlaceholder: 'Enter default password for reports',
                showCancelButton: true,
                confirmButtonText: 'Set Password',
                inputValidator: (value) => {
                    if (!value || value.length < 6) {
                        return 'Password must be at least 6 characters long!';
                    }
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Password Set',
                        text: 'Default password has been set for report protection.',
                        timer: 2000,
                        showConfirmButton: false
                    });
                } else {
                    $(this).prop('checked', false);
                }
            });
        }
    });

    // Scheduled report actions
    $('.bg-blue-50 button:contains("Edit"), .bg-green-50 button:contains("Edit"), .bg-purple-50 button:contains("Edit"), .bg-orange-50 button:contains("Edit")').on('click', function() {
        const reportName = $(this).closest('.flex').find('.text-sm.font-medium').text();
        
        Swal.fire({
            title: 'Edit Scheduled Report',
            text: `Editing: ${reportName}`,
            icon: 'info',
            confirmButtonText: 'Open Editor'
        });
    });

    $('.bg-blue-50 button:contains("Delete"), .bg-green-50 button:contains("Delete"), .bg-purple-50 button:contains("Delete"), .bg-orange-50 button:contains("Delete")').on('click', function() {
        const reportName = $(this).closest('.flex').find('.text-sm.font-medium').text();
        
        Swal.fire({
            title: 'Delete Scheduled Report',
            text: `Are you sure you want to delete: ${reportName}?`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Delete',
            cancelButtonText: 'Cancel',
            confirmButtonColor: '#dc2626'
        }).then((result) => {
            if (result.isConfirmed) {
                $(this).closest('.flex').fadeOut();
                Swal.fire({
                    icon: 'success',
                    title: 'Deleted',
                    text: 'Scheduled report has been deleted.',
                    timer: 2000,
                    showConfirmButton: false
                });
            }
        });
    });

    // Add new scheduled report
    $('button:contains("Add New Scheduled Report")').on('click', function() {
        Swal.fire({
            title: 'Add Scheduled Report',
            html: `
                <div class="text-left space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Report Name:</label>
                        <input type="text" id="new-report-name" class="w-full px-3 py-2 border border-gray-300 rounded-lg" placeholder="Enter report name">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Report Type:</label>
                        <select id="new-report-type" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                            <option value="sales">Sales Report</option>
                            <option value="inventory">Inventory Report</option>
                            <option value="financial">Financial Report</option>
                            <option value="customer">Customer Report</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Frequency:</label>
                        <select id="new-report-frequency" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                            <option value="daily">Daily</option>
                            <option value="weekly">Weekly</option>
                            <option value="monthly">Monthly</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Time:</label>
                        <input type="time" id="new-report-time" class="w-full px-3 py-2 border border-gray-300 rounded-lg" value="09:00">
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'Add Report',
            cancelButtonText: 'Cancel',
            preConfirm: () => {
                const name = document.getElementById('new-report-name').value;
                const type = document.getElementById('new-report-type').value;
                const frequency = document.getElementById('new-report-frequency').value;
                const time = document.getElementById('new-report-time').value;
                
                if (!name) {
                    Swal.showValidationMessage('Please enter a report name');
                    return false;
                }
                
                return { name, type, frequency, time };
            }
        }).then((result) => {
            if (result.isConfirmed) {
                Swal.fire({
                    icon: 'success',
                    title: 'Report Added',
                    text: 'New scheduled report has been added successfully.',
                    timer: 2000,
                    showConfirmButton: false
                });
            }
        });
    });

    // View audit log
    $('button:contains("View Full Audit Log")').on('click', function() {
        Swal.fire({
            title: 'Audit Log',
            text: 'This will open the detailed audit log showing all report access and activities.',
            icon: 'info',
            confirmButtonText: 'Open Log'
        });
    });

    // Data retention toggle
    $('#data_retention').on('change', function() {
        if ($(this).is(':checked')) {
            $('#retention_period').prop('disabled', false);
        } else {
            $('#retention_period').prop('disabled', true);
        }
    });
});
</script>
@endpush