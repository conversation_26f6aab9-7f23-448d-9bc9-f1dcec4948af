<?php

namespace Modules\Delivery\Services;

use Modules\Delivery\Models\DeliveryAssignment;
use Modules\Delivery\Models\DeliveryPersonnel;
use Modules\Delivery\Models\DeliveryZone;
use App\Models\Order;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class DeliveryAnalyticsService
{
    /**
     * Get comprehensive delivery analytics
     */
    public function getDeliveryAnalytics(array $filters = []): array
    {
        $cacheKey = 'delivery_analytics_' . md5(serialize($filters));
        
        return Cache::remember($cacheKey, config('delivery.analytics.cache_duration'), function () use ($filters) {
            return [
                'overview' => $this->getOverviewMetrics($filters),
                'performance' => $this->getPerformanceMetrics($filters),
                'personnel' => $this->getPersonnelMetrics($filters),
                'zones' => $this->getZoneMetrics($filters),
                'trends' => $this->getTrendAnalysis($filters),
            ];
        });
    }

    /**
     * Get overview metrics
     */
    public function getOverviewMetrics(array $filters = []): array
    {
        $query = DeliveryAssignment::query();
        $this->applyFilters($query, $filters);

        $metrics = $query->selectRaw('
            COUNT(*) as total_deliveries,
            COUNT(CASE WHEN status = "delivered" THEN 1 END) as successful_deliveries,
            COUNT(CASE WHEN status = "failed" THEN 1 END) as failed_deliveries,
            COUNT(CASE WHEN status = "cancelled" THEN 1 END) as cancelled_deliveries,
            AVG(CASE WHEN status = "delivered" AND actual_duration_minutes IS NOT NULL 
                THEN actual_duration_minutes END) as avg_delivery_time,
            SUM(CASE WHEN status = "delivered" THEN delivery_fee_earned ELSE 0 END) as total_revenue,
            AVG(CASE WHEN status = "delivered" AND estimated_duration_minutes IS NOT NULL 
                AND actual_duration_minutes IS NOT NULL 
                THEN (actual_duration_minutes - estimated_duration_minutes) END) as avg_time_variance
        ')->first();

        $successRate = $metrics->total_deliveries > 0 ? 
            ($metrics->successful_deliveries / $metrics->total_deliveries) * 100 : 0;

        return [
            'total_deliveries' => $metrics->total_deliveries,
            'successful_deliveries' => $metrics->successful_deliveries,
            'failed_deliveries' => $metrics->failed_deliveries,
            'cancelled_deliveries' => $metrics->cancelled_deliveries,
            'success_rate' => round($successRate, 2),
            'average_delivery_time' => round($metrics->avg_delivery_time ?? 0, 2),
            'total_revenue' => $metrics->total_revenue ?? 0,
            'average_time_variance' => round($metrics->avg_time_variance ?? 0, 2),
        ];
    }

    /**
     * Get performance metrics
     */
    public function getPerformanceMetrics(array $filters = []): array
    {
        $query = DeliveryAssignment::with(['deliveryPersonnel']);
        $this->applyFilters($query, $filters);

        $assignments = $query->get();
        
        // On-time delivery rate (within estimated time + 10 minutes buffer)
        $onTimeDeliveries = $assignments->filter(function ($assignment) {
            return $assignment->status === 'delivered' && 
                   $assignment->actual_duration_minutes <= ($assignment->estimated_duration_minutes + 10);
        })->count();

        $onTimeRate = $assignments->where('status', 'delivered')->count() > 0 ? 
            ($onTimeDeliveries / $assignments->where('status', 'delivered')->count()) * 100 : 0;

        // Average rating from reviews
        $avgRating = $assignments->whereNotNull('review')
            ->avg(function ($assignment) {
                return $assignment->review->rating ?? 0;
            });

        // Peak hours analysis
        $peakHours = $assignments->groupBy(function ($assignment) {
            return $assignment->assigned_at->format('H');
        })->map->count()->sortDesc()->take(3);

        return [
            'on_time_delivery_rate' => round($onTimeRate, 2),
            'average_customer_rating' => round($avgRating ?? 0, 2),
            'peak_delivery_hours' => $peakHours->keys()->toArray(),
            'busiest_hour_count' => $peakHours->first() ?? 0,
        ];
    }

    /**
     * Get personnel performance metrics
     */
    public function getPersonnelMetrics(array $filters = []): array
    {
        $query = DeliveryPersonnel::with(['deliveryAssignments' => function ($q) use ($filters) {
            $this->applyFilters($q, $filters);
        }]);

        if (isset($filters['branch_id'])) {
            $query->where('branch_id', $filters['branch_id']);
        }

        $personnel = $query->get();

        $topPerformers = $personnel->map(function ($person) {
            $assignments = $person->deliveryAssignments;
            $successfulDeliveries = $assignments->where('status', 'delivered')->count();
            $totalDeliveries = $assignments->count();
            $successRate = $totalDeliveries > 0 ? ($successfulDeliveries / $totalDeliveries) * 100 : 0;
            
            return [
                'id' => $person->id,
                'name' => $person->user->name,
                'total_deliveries' => $totalDeliveries,
                'success_rate' => round($successRate, 2),
                'avg_delivery_time' => round($assignments->where('status', 'delivered')->avg('actual_duration_minutes') ?? 0, 2),
                'total_earnings' => $assignments->where('status', 'delivered')->sum('delivery_fee_earned'),
                'rating' => $person->rating,
            ];
        })->sortByDesc('success_rate')->take(10);

        return [
            'total_active_personnel' => $personnel->where('status', 'active')->count(),
            'total_personnel' => $personnel->count(),
            'top_performers' => $topPerformers->values()->toArray(),
            'average_personnel_rating' => round($personnel->avg('rating') ?? 0, 2),
        ];
    }

    /**
     * Get zone performance metrics
     */
    public function getZoneMetrics(array $filters = []): array
    {
        $query = DeliveryZone::with(['deliveryAssignments' => function ($q) use ($filters) {
            $this->applyFilters($q, $filters);
        }]);

        if (isset($filters['branch_id'])) {
            $query->where('branch_id', $filters['branch_id']);
        }

        $zones = $query->get();

        $zonePerformance = $zones->map(function ($zone) {
            $assignments = $zone->deliveryAssignments;
            $successfulDeliveries = $assignments->where('status', 'delivered')->count();
            $totalDeliveries = $assignments->count();
            $successRate = $totalDeliveries > 0 ? ($successfulDeliveries / $totalDeliveries) * 100 : 0;
            
            return [
                'id' => $zone->id,
                'name' => $zone->name,
                'total_deliveries' => $totalDeliveries,
                'success_rate' => round($successRate, 2),
                'avg_delivery_time' => round($assignments->where('status', 'delivered')->avg('actual_duration_minutes') ?? 0, 2),
                'total_revenue' => $assignments->where('status', 'delivered')->sum('delivery_fee_earned'),
                'delivery_fee' => $zone->delivery_fee,
            ];
        })->sortByDesc('total_deliveries');

        return [
            'total_zones' => $zones->count(),
            'active_zones' => $zones->where('is_active', true)->count(),
            'zone_performance' => $zonePerformance->values()->toArray(),
            'most_popular_zone' => $zonePerformance->first(),
        ];
    }

    /**
     * Get trend analysis
     */
    public function getTrendAnalysis(array $filters = []): array
    {
        $query = DeliveryAssignment::query();
        $this->applyFilters($query, $filters);

        // Daily trends for the last 30 days
        $dailyTrends = $query->selectRaw('
            DATE(assigned_at) as date,
            COUNT(*) as total_deliveries,
            COUNT(CASE WHEN status = "delivered" THEN 1 END) as successful_deliveries,
            AVG(CASE WHEN status = "delivered" THEN actual_duration_minutes END) as avg_time,
            SUM(CASE WHEN status = "delivered" THEN delivery_fee_earned ELSE 0 END) as revenue
        ')
        ->where('assigned_at', '>=', now()->subDays(30))
        ->groupBy('date')
        ->orderBy('date')
        ->get();

        // Hourly trends
        $hourlyTrends = $query->selectRaw('
            HOUR(assigned_at) as hour,
            COUNT(*) as total_deliveries,
            AVG(CASE WHEN status = "delivered" THEN actual_duration_minutes END) as avg_time
        ')
        ->groupBy('hour')
        ->orderBy('hour')
        ->get();

        // Weekly trends
        $weeklyTrends = $query->selectRaw('
            DAYNAME(assigned_at) as day_name,
            DAYOFWEEK(assigned_at) as day_number,
            COUNT(*) as total_deliveries,
            AVG(CASE WHEN status = "delivered" THEN actual_duration_minutes END) as avg_time
        ')
        ->groupBy('day_name', 'day_number')
        ->orderBy('day_number')
        ->get();

        return [
            'daily_trends' => $dailyTrends->toArray(),
            'hourly_trends' => $hourlyTrends->toArray(),
            'weekly_trends' => $weeklyTrends->toArray(),
        ];
    }

    /**
     * Get real-time dashboard metrics
     */
    public function getRealTimeDashboard(): array
    {
        $activeDeliveries = DeliveryAssignment::whereIn('status', ['assigned', 'picked_up', 'in_transit'])->count();
        $availablePersonnel = DeliveryPersonnel::where('status', 'active')->where('is_verified', true)->count();
        $busyPersonnel = DeliveryPersonnel::where('status', 'busy')->count();
        
        $todayDeliveries = DeliveryAssignment::whereDate('assigned_at', today())->count();
        $todaySuccessful = DeliveryAssignment::whereDate('assigned_at', today())
            ->where('status', 'delivered')->count();
        
        $todayRevenue = DeliveryAssignment::whereDate('assigned_at', today())
            ->where('status', 'delivered')
            ->sum('delivery_fee_earned');

        return [
            'active_deliveries' => $activeDeliveries,
            'available_personnel' => $availablePersonnel,
            'busy_personnel' => $busyPersonnel,
            'today_deliveries' => $todayDeliveries,
            'today_successful' => $todaySuccessful,
            'today_success_rate' => $todayDeliveries > 0 ? round(($todaySuccessful / $todayDeliveries) * 100, 2) : 0,
            'today_revenue' => $todayRevenue,
        ];
    }

    /**
     * Apply filters to query
     */
    private function applyFilters($query, array $filters): void
    {
        if (isset($filters['date_from'])) {
            $query->whereDate('assigned_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('assigned_at', '<=', $filters['date_to']);
        }

        if (isset($filters['branch_id'])) {
            $query->whereHas('order', function ($q) use ($filters) {
                $q->where('branch_id', $filters['branch_id']);
            });
        }

        if (isset($filters['personnel_id'])) {
            $query->where('delivery_personnel_id', $filters['personnel_id']);
        }

        if (isset($filters['zone_id'])) {
            $query->where('delivery_zone_id', $filters['zone_id']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }
    }
}