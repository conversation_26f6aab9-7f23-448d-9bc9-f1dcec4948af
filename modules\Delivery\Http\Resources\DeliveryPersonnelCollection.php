<?php

namespace Modules\Delivery\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class DeliveryPersonnelCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
            'meta' => [
                'total' => $this->total(),
                'count' => $this->count(),
                'per_page' => $this->perPage(),
                'current_page' => $this->currentPage(),
                'total_pages' => $this->lastPage(),
                'has_more_pages' => $this->hasMorePages(),
            ],
            'links' => [
                'first' => $this->url(1),
                'last' => $this->url($this->lastPage()),
                'prev' => $this->previousPageUrl(),
                'next' => $this->nextPageUrl(),
            ],
            'summary' => [
                'active_personnel' => $this->collection->where('status', 'active')->count(),
                'available_personnel' => $this->collection->filter(function ($personnel) {
                    // Access the underlying model data
                    $model = $personnel->resource ?? $personnel;
                    $currentAssignments = $model->activeDeliveries()->count();
                    return $model->status === 'active' && $currentAssignments < $model->max_concurrent_deliveries;
                })->count(),
                'online_personnel' => $this->collection->filter(function ($personnel) {
                    // Access the underlying model data
                    $model = $personnel->resource ?? $personnel;
                    if (!$model->last_location_update) {
                        return false;
                    }
                    return $model->last_location_update->diffInMinutes(now()) <= 10;
                })->count(),
                'busy_personnel' => $this->collection->where('status', 'busy')->count(),
            ],
        ];
    }
}