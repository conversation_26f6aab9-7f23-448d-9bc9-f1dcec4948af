<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Modules\Tenant\Http\Controllers\Api\BranchController;
use Modules\Tenant\Http\Controllers\Api\TenantController;
use Modules\Tenant\Http\Controllers\Api\SubscriptionController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/


// Tenant Management Routes
Route::prefix('api/tenants')->group(function () {
    // Public routes (no authentication required)
    Route::get('/', [TenantController::class, 'index']);
    Route::post('/', [TenantController::class, 'store']); // Fixed tenant creation route
    
    // Protected routes (require authentication)
    Route::middleware(['auth:sanctum'])->group(function () {
        Route::get('/statistics', [TenantController::class, 'statistics']);
        
        // Individual tenant routes
        Route::prefix('{tenant}')->group(function () {
            Route::get('/', [TenantController::class, 'show']);
            Route::put('/', [TenantController::class, 'update']);
            Route::delete('/', [TenantController::class, 'destroy']);
            Route::post('/activate', [TenantController::class, 'activate']);
            Route::post('/deactivate', [TenantController::class, 'deactivate']);
            Route::get('/statistics', [TenantController::class, 'statistics']);
        });
    });
});

// Branch Management Routes
Route::prefix('api/branches')->group(function () {
    // Public routes (no authentication required)
    Route::get('/', [BranchController::class, 'index']);
    Route::post('/', [BranchController::class, 'store']);
    
    // Protected routes (require authentication)
    Route::middleware(['auth:sanctum'])->group(function () {
        // Individual branch routes
        Route::prefix('{branch}')->group(function () {
            Route::get('/', [BranchController::class, 'show']);
            Route::put('/', [BranchController::class, 'update']);
            Route::delete('/', [BranchController::class, 'destroy']);
            Route::post('/activate', [BranchController::class, 'activate']);
            Route::post('/deactivate', [BranchController::class, 'deactivate']);
            Route::get('/statistics', [BranchController::class, 'statistics']);
        });
    });
});

// Subscription Management Routes
Route::prefix('subscriptions')->middleware(['auth:sanctum'])->group(function () {
    // List all subscriptions (admin only)
    Route::get('/', [SubscriptionController::class, 'index']);
    
    // Get available plans
    Route::get('/plans', [SubscriptionController::class, 'plans']);
    
    // Create new subscription
    Route::post('/', [SubscriptionController::class, 'store']);
    
    // Individual subscription routes
    Route::prefix('{subscription}')->group(function () {
        Route::get('/', [SubscriptionController::class, 'show']);
        Route::put('/', [SubscriptionController::class, 'update']);
        Route::post('/cancel', [SubscriptionController::class, 'cancel']);
        Route::post('/suspend', [SubscriptionController::class, 'suspend']);
        Route::post('/reactivate', [SubscriptionController::class, 'reactivate']);
        Route::post('/upgrade', [SubscriptionController::class, 'upgrade']);
        Route::get('/usage', [SubscriptionController::class, 'usage']);
    });
});

// Tenant-specific routes (require tenant context)
Route::prefix('tenant')->middleware(['auth:sanctum', 'tenant', 'subscription'])->group(function () {
    // Dashboard and statistics
    Route::get('/dashboard', function (Request $request) {
        $tenant = $request->get('tenant');
        $subscriptionStatus = $request->get('subscription_status');
        
        return response()->json([
            'tenant' => $tenant,
            'subscription_status' => $subscriptionStatus,
            'statistics' => \Modules\Tenant\Helpers\TenantHelper::getTenantStats($tenant),
        ]);
    });
    
    // Feature-specific routes with usage limits
    Route::middleware(['usage.limit:branches,create'])->group(function () {
        Route::post('/branches', function () {
            return response()->json(['message' => 'Branch creation endpoint']);
        });
    });
    
    Route::middleware(['usage.limit:users,create'])->group(function () {
        Route::post('/users', function () {
            return response()->json(['message' => 'User creation endpoint']);
        });
    });
    
    Route::middleware(['usage.limit:menu_items,create'])->group(function () {
        Route::post('/menu-items', function () {
            return response()->json(['message' => 'Menu item creation endpoint']);
        });
    });
    
    // Usage statistics
    Route::get('/usage', function (Request $request) {
        $tenant = $request->get('tenant');
        $subscription = $request->get('subscription');
        
        return response()->json([
            'usage_stats' => \Modules\Tenant\Helpers\SubscriptionHelper::getUsageStats($subscription),
            'health_score' => \Modules\Tenant\Helpers\SubscriptionHelper::getHealthScore($subscription),
        ]);
    });
});
