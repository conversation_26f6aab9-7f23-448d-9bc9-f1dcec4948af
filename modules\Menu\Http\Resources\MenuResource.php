<?php

namespace Modules\Menu\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MenuResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            
            'branch_id' => $this->branch_id,
            'name' => $this->name,
            'code' => $this->code,
            'description' => $this->description,
            'menu_type' => $this->menu_type,
            'start_time' => $this->start_time?->format('H:i'),
            'end_time' => $this->end_time?->format('H:i'),
            'available_days' => $this->available_days,
            'is_active' => $this->is_active,
            'is_default' => $this->is_default,
            'sort_order' => $this->sort_order,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            
            // Relationships
            'branch' => $this->whenLoaded('branch'),
            'tenant' => $this->whenLoaded('tenant'),
            'categories' => CategoryResource::collection($this->whenLoaded('categories')),
            'menu_items' => MenuItemResource::collection($this->whenLoaded('menuItems')),
            'active_categories' => CategoryResource::collection($this->whenLoaded('activeCategories')),
            'active_menu_items' => MenuItemResource::collection($this->whenLoaded('activeMenuItems')),
            
            // Computed attributes
            'categories_count' => $this->whenCounted('categories'),
            'menu_items_count' => $this->whenCounted('menuItems'),
        ];
    }
}