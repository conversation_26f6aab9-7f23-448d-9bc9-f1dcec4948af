<?php

namespace Modules\Orders\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Validator;
use App\Models\MenuItemAddon;

class StoreOrderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rules = [
            'customer_id' => 'nullable|exists:customers,id',
            'order_type' => 'required|in:dine_in,takeaway,delivery,online',
            'status' => 'nullable|in:pending,confirmed,preparing,ready,served,completed,cancelled',
            'notes' => 'nullable|string|max:500',
            
            // Order totals
            'subtotal' => 'required|numeric|min:0',
            'tax_amount' => 'nullable|numeric|min:0',
            'discount_amount' => 'nullable|numeric|min:0',
            'total_amount' => 'required|numeric|min:0',
            
            // Items validation
            'items' => 'required|array|min:1',
            'items.*.menu_item_id' => 'required|exists:menu_items,id',
            'items.*.menu_item_name' => 'required|string|max:255',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.subtotal' => 'required|numeric|min:0',
            'items.*.notes' => 'nullable|string|max:255',
            
            // Variant validation
            'items.*.variant_id' => 'nullable|exists:menu_item_variants,id',
            'items.*.variant_name' => 'nullable|string|max:255',
            
            // Addons validation
            'items.*.addons' => 'nullable|array',
            'items.*.addons.*.addon_id' => 'required_with:items.*.addons|exists:menu_item_addons,id',
            'items.*.addons.*.addon_name' => 'required_with:items.*.addons|string|max:255',
            'items.*.addons.*.quantity' => 'required_with:items.*.addons|integer|min:1',
            'items.*.addons.*.unit_price' => 'required_with:items.*.addons|numeric|min:0',
            'items.*.addons.*.total_price' => 'required_with:items.*.addons|numeric|min:0',
            

            
            // Order-level discount
            'order_discount' => 'nullable|array',
            'order_discount.type' => 'required_with:order_discount|in:fixed,percentage',
            'order_discount.value' => 'required_with:order_discount|numeric|min:0',
            'order_discount.amount' => 'nullable|numeric|min:0',
        ];

        // Order type specific validation rules
        if ($this->input('order_type') === 'dine_in') {
            $rules['table_id'] = 'nullable|exists:tables,id'; // Table is now optional
            $rules['pax'] = 'nullable|integer|min:1|max:20'; // Number of people is optional
            $rules['delivery_man_id'] = 'nullable|exists:users,id';
            $rules['customer_id'] = 'nullable|exists:customers,id'; // Optional for dine-in
        } elseif ($this->input('order_type') === 'delivery') {
            $rules['table_id'] = 'nullable|exists:tables,id';
            $rules['pax'] = 'nullable|integer|min:1|max:20';
            $rules['delivery_man_id'] = 'required|exists:users,id';
            $rules['customer_id'] = 'required|exists:customers,id'; // Required for delivery
            $rules['delivery_address'] = 'required|string|max:500';
            $rules['delivery_coordinates'] = 'nullable|array';
            $rules['delivery_coordinates.lat'] = 'required_with:delivery_coordinates|numeric';
            $rules['delivery_coordinates.lng'] = 'required_with:delivery_coordinates|numeric';
        } elseif ($this->input('order_type') === 'takeaway') {
            $rules['table_id'] = 'nullable|exists:tables,id';
            $rules['pax'] = 'nullable|integer|min:1|max:20';
            $rules['delivery_man_id'] = 'nullable|exists:users,id';
            $rules['customer_id'] = 'nullable|exists:customers,id'; // Optional for takeaway
        } else { // online
            $rules['table_id'] = 'nullable|exists:tables,id';
            $rules['pax'] = 'nullable|integer|min:1|max:20';
            $rules['delivery_man_id'] = 'nullable|exists:users,id';
            $rules['customer_id'] = 'required|exists:customers,id'; // Required for online orders
        }

        return $rules;
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator(Validator $validator): void
    {
        $validator->after(function (Validator $validator) {
            $this->validateAddonsForMenuItems($validator);
            $this->validateVariantsForMenuItems($validator);
            $this->validateOrderTotals($validator);
        });
    }

    /**
     * Validate that addons belong to their respective menu items.
     */
    protected function validateAddonsForMenuItems(Validator $validator): void
    {
        $items = $this->input('items', []);
        
        foreach ($items as $itemIndex => $item) {
            if (!isset($item['addons']) || !is_array($item['addons'])) {
                continue;
            }

            $menuItemId = $item['menu_item_id'] ?? null;
            
            foreach ($item['addons'] as $addonIndex => $addon) {
                $addonId = $addon['addon_id'] ?? null;
                
                if ($addonId && $menuItemId) {
                    // Check if addon belongs to the menu item
                    $addonExists = MenuItemAddon::where('id', $addonId)
                        ->where('menu_item_id', $menuItemId)
                        ->exists();
                    
                    if (!$addonExists) {
                        $validator->errors()->add(
                            "items.{$itemIndex}.addons.{$addonIndex}.addon_id",
                            "The selected addon does not belong to the specified menu item."
                        );
                    }
                }
            }
        }
    }

    /**
     * Validate that variants belong to their respective menu items.
     */
    protected function validateVariantsForMenuItems(Validator $validator): void
    {
        $items = $this->input('items', []);
        
        foreach ($items as $itemIndex => $item) {
            $menuItemId = $item['menu_item_id'] ?? null;
            $variantId = $item['variant_id'] ?? null;
            
            if ($variantId && $menuItemId) {
                // Check if variant belongs to the menu item
                $variantExists = \App\Models\MenuItemVariant::where('id', $variantId)
                    ->where('menu_item_id', $menuItemId)
                    ->exists();
                
                if (!$variantExists) {
                    $validator->errors()->add(
                        "items.{$itemIndex}.variant_id",
                        "The selected variant does not belong to the specified menu item."
                    );
                }
            }
        }
    }

    /**
     * Validate discount calculations for each item.
     */
    protected function validateDiscountCalculations(Validator $validator): void
    {
        // Item-level discount validation removed
    }

    /**
     * Validate order totals calculation.
     */
    protected function validateOrderTotals(Validator $validator): void
    {
        $items = $this->input('items', []);
        $providedSubtotal = $this->input('subtotal', 0);
        $providedTaxAmount = $this->input('tax_amount', 0);
        $providedDiscountAmount = $this->input('discount_amount', 0);
        $providedTotalAmount = $this->input('total_amount', 0);

        // Calculate expected subtotal from items
        $calculatedSubtotal = 0;

        foreach ($items as $item) {
            $itemSubtotal = $item['subtotal'] ?? 0;
            $calculatedSubtotal += $itemSubtotal;
        }

        // Validate subtotal
        $tolerance = 0.01;
        if (abs($providedSubtotal - $calculatedSubtotal) > $tolerance) {
            $validator->errors()->add(
                'subtotal',
                "Subtotal calculation is incorrect. Expected: " . number_format($calculatedSubtotal, 2)
            );
        }

        // Calculate expected total with order-level discount only
        $orderDiscountAmount = 0;
        $orderDiscount = $this->input('order_discount');
        if ($orderDiscount && isset($orderDiscount['type'], $orderDiscount['value'])) {
            $discountBase = $calculatedSubtotal;
            if ($orderDiscount['type'] === 'fixed') {
                $orderDiscountAmount = min($orderDiscount['value'], $discountBase);
            } elseif ($orderDiscount['type'] === 'percentage') {
                $orderDiscountAmount = ($discountBase * $orderDiscount['value']) / 100;
            }
        }

        $expectedTotal = $calculatedSubtotal - $orderDiscountAmount + $providedTaxAmount;

        // Validate total amount
        if (abs($providedTotalAmount - $expectedTotal) > $tolerance) {
            $validator->errors()->add(
                'total_amount',
                "Total amount calculation is incorrect. Expected: " . number_format($expectedTotal, 2)
            );
        }
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            // Order type validation
            'order_type.required' => 'Order type is required.',
            'order_type.in' => 'Invalid order type selected.',
            
            // Customer and table validation
            'customer_id.required' => 'Customer is required for this order type.',
            'customer_id.exists' => 'Selected customer does not exist.',
            'table_id.exists' => 'Selected table does not exist.',
            'pax.min' => 'Number of people must be at least 1.',
            'pax.max' => 'Number of people cannot exceed 20.',
            
            // Delivery validation
            'delivery_man_id.required' => 'Delivery person is required for delivery orders.',
            'delivery_man_id.exists' => 'Selected delivery person does not exist.',
            'delivery_address.required' => 'Delivery address is required for delivery orders.',
            
            // Order totals validation
            'subtotal.required' => 'Subtotal is required.',
            'subtotal.numeric' => 'Subtotal must be a valid number.',
            'subtotal.min' => 'Subtotal cannot be negative.',
            'tax_amount.numeric' => 'Tax amount must be a valid number.',
            'tax_amount.min' => 'Tax amount cannot be negative.',
            'discount_amount.numeric' => 'Discount amount must be a valid number.',
            'discount_amount.min' => 'Discount amount cannot be negative.',
            'total_amount.required' => 'Total amount is required.',
            'total_amount.numeric' => 'Total amount must be a valid number.',
            'total_amount.min' => 'Total amount cannot be negative.',
            
            // Items validation
            'items.required' => 'At least one item is required.',
            'items.min' => 'At least one item is required.',
            'items.*.menu_item_id.required' => 'Menu item is required for each order item.',
            'items.*.menu_item_id.exists' => 'Selected menu item does not exist.',
            'items.*.menu_item_name.required' => 'Menu item name is required.',
            'items.*.quantity.required' => 'Quantity is required for each item.',
            'items.*.quantity.min' => 'Quantity must be at least 1.',
            'items.*.unit_price.required' => 'Unit price is required for each item.',
            'items.*.unit_price.min' => 'Unit price must be greater than or equal to 0.',
            'items.*.subtotal.required' => 'Item subtotal is required.',
            'items.*.subtotal.min' => 'Item subtotal cannot be negative.',
            
            // Variant validation
            'items.*.variant_id.exists' => 'Selected variant does not exist.',
            'items.*.variant_name.string' => 'Variant name must be a valid string.',
            
            // Addons validation
            'items.*.addons.*.addon_id.required_with' => 'Addon ID is required when addons are specified.',
            'items.*.addons.*.addon_id.exists' => 'The selected addon does not exist.',
            'items.*.addons.*.addon_name.required_with' => 'Addon name is required when addons are specified.',
            'items.*.addons.*.quantity.required_with' => 'Addon quantity is required when addons are specified.',
            'items.*.addons.*.quantity.min' => 'Addon quantity must be at least 1.',
            'items.*.addons.*.unit_price.required_with' => 'Addon unit price is required when addons are specified.',
            'items.*.addons.*.unit_price.min' => 'Addon unit price cannot be negative.',
            'items.*.addons.*.total_price.required_with' => 'Addon total price is required when addons are specified.',
            'items.*.addons.*.total_price.min' => 'Addon total price cannot be negative.',
            

            
            // Order discount validation
            'order_discount.type.required_with' => 'Order discount type is required when order discount is applied.',
            'order_discount.type.in' => 'Order discount type must be either fixed or percentage.',
            'order_discount.value.required_with' => 'Order discount value is required when order discount is applied.',
            'order_discount.value.min' => 'Order discount value cannot be negative.',
            'order_discount.amount.min' => 'Order discount amount cannot be negative.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'branch_id' => 'branch',
            'customer_id' => 'customer',
            'table_id' => 'table',
            'pax' => 'number of people',
            'order_type' => 'order type',
            'delivery_man_id' => 'delivery person',
            'delivery_address' => 'delivery address',
            'items.*.menu_item_id' => 'menu item',
            'items.*.quantity' => 'quantity',
            'items.*.unit_price' => 'unit price',
        ];
    }
}