@extends('layouts.master')

@section('title', 'Global System Settings')

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
@endpush

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg shadow-lg p-6 mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                            <i class="fas fa-cogs text-white text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <h1 class="text-2xl font-bold text-white">Global System Settings</h1>
                        <p class="text-indigo-100">Configure system-wide settings that apply to all branches and tenants</p>
                    </div>
                </div>
            </div>
            <div class="mt-4 md:mt-0">
                <button class="px-4 py-2 bg-white text-indigo-600 rounded-lg hover:bg-indigo-50 transition-colors duration-200 font-medium">
                    <i class="fas fa-download mr-2"></i>
                    Export Settings
                </button>
            </div>
        </div>
    </div>

    <!-- System Status Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-server text-green-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">System Status</p>
                    <p class="text-lg font-bold text-green-600">Online</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-database text-blue-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Database</p>
                    <p class="text-lg font-bold text-blue-600">Connected</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-building text-yellow-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Active Branches</p>
                    <p class="text-2xl font-bold text-gray-900">12</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-users text-purple-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Users</p>
                    <p class="text-2xl font-bold text-gray-900">156</p>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Application Settings -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-desktop mr-2 text-indigo-600"></i>
                Application Settings
            </h3>
            
            <form class="space-y-4">
                <div>
                    <label for="app_name" class="block text-sm font-medium text-gray-700 mb-2">
                        Application Name
                    </label>
                    <input type="text" id="app_name" name="app_name" value="Restaurant POS System"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                </div>

                <div>
                    <label for="app_version" class="block text-sm font-medium text-gray-700 mb-2">
                        Application Version
                    </label>
                    <input type="text" id="app_version" name="app_version" value="2.1.0" readonly
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500">
                </div>

                <div>
                    <label for="timezone" class="block text-sm font-medium text-gray-700 mb-2">
                        Default Timezone
                    </label>
                    <select id="timezone" name="timezone" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="UTC">UTC</option>
                        <option value="America/New_York">Eastern Time</option>
                        <option value="America/Chicago">Central Time</option>
                        <option value="America/Denver">Mountain Time</option>
                        <option value="America/Los_Angeles">Pacific Time</option>
                        <option value="Asia/Riyadh" selected>Saudi Arabia Time</option>
                        <option value="Europe/London">London Time</option>
                    </select>
                </div>

                <div>
                    <label for="date_format" class="block text-sm font-medium text-gray-700 mb-2">
                        Date Format
                    </label>
                    <select id="date_format" name="date_format" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="Y-m-d">YYYY-MM-DD</option>
                        <option value="m/d/Y">MM/DD/YYYY</option>
                        <option value="d/m/Y" selected>DD/MM/YYYY</option>
                        <option value="d-m-Y">DD-MM-YYYY</option>
                    </select>
                </div>

                <div>
                    <label for="time_format" class="block text-sm font-medium text-gray-700 mb-2">
                        Time Format
                    </label>
                    <select id="time_format" name="time_format" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="24" selected>24 Hour (14:30)</option>
                        <option value="12">12 Hour (2:30 PM)</option>
                    </select>
                </div>

                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div>
                            <label for="maintenance_mode" class="text-sm font-medium text-gray-700">Maintenance Mode</label>
                            <p class="text-sm text-gray-500">Put system in maintenance mode</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="maintenance_mode" name="maintenance_mode" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="debug_mode" class="text-sm font-medium text-gray-700">Debug Mode</label>
                            <p class="text-sm text-gray-500">Enable detailed error logging</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="debug_mode" name="debug_mode" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                        </label>
                    </div>
                </div>

                <div class="pt-4">
                    <button type="submit" class="w-full px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-lg hover:bg-indigo-700">
                        <i class="fas fa-save mr-2"></i>
                        Save Application Settings
                    </button>
                </div>
            </form>
        </div>

        <!-- Database & Performance -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-database mr-2 text-blue-600"></i>
                Database & Performance
            </h3>
            
            <form class="space-y-4">
                <div>
                    <label for="db_backup_frequency" class="block text-sm font-medium text-gray-700 mb-2">
                        Backup Frequency
                    </label>
                    <select id="db_backup_frequency" name="db_backup_frequency" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="hourly">Every Hour</option>
                        <option value="daily" selected>Daily</option>
                        <option value="weekly">Weekly</option>
                        <option value="monthly">Monthly</option>
                    </select>
                </div>

                <div>
                    <label for="backup_retention" class="block text-sm font-medium text-gray-700 mb-2">
                        Backup Retention (days)
                    </label>
                    <input type="number" id="backup_retention" name="backup_retention" value="30" min="1" max="365"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label for="cache_driver" class="block text-sm font-medium text-gray-700 mb-2">
                        Cache Driver
                    </label>
                    <select id="cache_driver" name="cache_driver" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="file">File</option>
                        <option value="redis" selected>Redis</option>
                        <option value="memcached">Memcached</option>
                        <option value="database">Database</option>
                    </select>
                </div>

                <div>
                    <label for="session_lifetime" class="block text-sm font-medium text-gray-700 mb-2">
                        Session Lifetime (minutes)
                    </label>
                    <input type="number" id="session_lifetime" name="session_lifetime" value="120" min="30" max="1440"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div>
                            <label for="enable_caching" class="text-sm font-medium text-gray-700">Enable Caching</label>
                            <p class="text-sm text-gray-500">Improve system performance</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="enable_caching" name="enable_caching" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="auto_backup" class="text-sm font-medium text-gray-700">Automatic Backups</label>
                            <p class="text-sm text-gray-500">Schedule automatic database backups</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="auto_backup" name="auto_backup" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                </div>

                <div class="pt-4 space-y-2">
                    <button type="submit" class="w-full px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700">
                        <i class="fas fa-save mr-2"></i>
                        Save Database Settings
                    </button>
                    <button type="button" class="w-full px-4 py-2 text-sm font-medium text-blue-700 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100">
                        <i class="fas fa-database mr-2"></i>
                        Create Manual Backup
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Email & Notifications -->
    <div class="mt-6 bg-white rounded-lg shadow-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fas fa-envelope mr-2 text-green-600"></i>
            Email & Notification Settings
        </h3>

        <form class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div class="space-y-4">
                <div>
                    <label for="mail_driver" class="block text-sm font-medium text-gray-700 mb-2">
                        Mail Driver
                    </label>
                    <select id="mail_driver" name="mail_driver" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                        <option value="smtp" selected>SMTP</option>
                        <option value="sendmail">Sendmail</option>
                        <option value="mailgun">Mailgun</option>
                        <option value="ses">Amazon SES</option>
                    </select>
                </div>

                <div>
                    <label for="mail_host" class="block text-sm font-medium text-gray-700 mb-2">
                        SMTP Host
                    </label>
                    <input type="text" id="mail_host" name="mail_host" value="smtp.gmail.com"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                </div>

                <div>
                    <label for="mail_port" class="block text-sm font-medium text-gray-700 mb-2">
                        SMTP Port
                    </label>
                    <input type="number" id="mail_port" name="mail_port" value="587"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                </div>

                <div>
                    <label for="mail_username" class="block text-sm font-medium text-gray-700 mb-2">
                        SMTP Username
                    </label>
                    <input type="email" id="mail_username" name="mail_username" placeholder="<EMAIL>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                </div>
            </div>

            <div class="space-y-4">
                <div>
                    <label for="mail_from_name" class="block text-sm font-medium text-gray-700 mb-2">
                        From Name
                    </label>
                    <input type="text" id="mail_from_name" name="mail_from_name" value="Restaurant POS System"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                </div>

                <div>
                    <label for="mail_from_address" class="block text-sm font-medium text-gray-700 mb-2">
                        From Email Address
                    </label>
                    <input type="email" id="mail_from_address" name="mail_from_address" placeholder="<EMAIL>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                </div>

                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div>
                            <label for="mail_encryption" class="text-sm font-medium text-gray-700">Use TLS Encryption</label>
                            <p class="text-sm text-gray-500">Secure email transmission</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="mail_encryption" name="mail_encryption" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="email_notifications" class="text-sm font-medium text-gray-700">Email Notifications</label>
                            <p class="text-sm text-gray-500">Send system notifications via email</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="email_notifications" name="email_notifications" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                        </label>
                    </div>
                </div>

                <div class="pt-4">
                    <button type="button" class="w-full px-4 py-2 text-sm font-medium text-green-700 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 mb-2">
                        <i class="fas fa-paper-plane mr-2"></i>
                        Test Email Configuration
                    </button>
                </div>
            </div>

            <div class="lg:col-span-2 pt-4">
                <button type="submit" class="px-6 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-lg hover:bg-green-700">
                    <i class="fas fa-save mr-2"></i>
                    Save Email Settings
                </button>
            </div>
        </form>
    </div>

    <!-- System Information -->
    <div class="mt-6 bg-white rounded-lg shadow-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fas fa-info-circle mr-2 text-gray-600"></i>
            System Information
        </h3>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="text-center p-4 bg-gray-50 rounded-lg">
                <div class="text-lg font-bold text-gray-900">PHP 8.2.0</div>
                <div class="text-sm text-gray-600">PHP Version</div>
            </div>
            <div class="text-center p-4 bg-gray-50 rounded-lg">
                <div class="text-lg font-bold text-gray-900">Laravel 10.x</div>
                <div class="text-sm text-gray-600">Framework</div>
            </div>
            <div class="text-center p-4 bg-gray-50 rounded-lg">
                <div class="text-lg font-bold text-gray-900">MySQL 8.0</div>
                <div class="text-sm text-gray-600">Database</div>
            </div>
            <div class="text-center p-4 bg-gray-50 rounded-lg">
                <div class="text-lg font-bold text-gray-900">512 MB</div>
                <div class="text-sm text-gray-600">Memory Limit</div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Form submission handlers
    $('form').on('submit', function(e) {
        e.preventDefault();
        
        Swal.fire({
            icon: 'success',
            title: 'Success!',
            text: 'System settings updated successfully.',
            timer: 2000,
            showConfirmButton: false
        });
    });

    // Test email button
    $('button:contains("Test Email")').on('click', function() {
        Swal.fire({
            title: 'Testing Email Configuration',
            text: 'Sending test email...',
            icon: 'info',
            showConfirmButton: false,
            timer: 2000
        }).then(() => {
            Swal.fire({
                icon: 'success',
                title: 'Test Email Sent!',
                text: 'Please check your inbox.',
                timer: 2000,
                showConfirmButton: false
            });
        });
    });

    // Manual backup button
    $('button:contains("Manual Backup")').on('click', function() {
        Swal.fire({
            title: 'Create Manual Backup?',
            text: 'This will create a backup of the current database.',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Yes, create backup',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                Swal.fire({
                    icon: 'success',
                    title: 'Backup Created!',
                    text: 'Database backup has been created successfully.',
                    timer: 2000,
                    showConfirmButton: false
                });
            }
        });
    });
});
</script>
@endpush