<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('waiter_requests', function (Blueprint $table) {
            // Add tenant_id column as nullable first
            $table->unsignedBigInteger('tenant_id')->nullable()->after('id');
        });

        // Update existing records with tenant_id from their branch
        DB::statement('
            UPDATE waiter_requests wr 
            JOIN branches b ON wr.branch_id = b.id 
            SET wr.tenant_id = b.tenant_id 
            WHERE wr.tenant_id IS NULL
        ');

        Schema::table('waiter_requests', function (Blueprint $table) {
            // Make tenant_id non-nullable and add foreign key constraint
            $table->unsignedBigInteger('tenant_id')->nullable(false)->change();
            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
            
            // Add indexes for better performance
            $table->index(['tenant_id', 'branch_id']);
            $table->index(['tenant_id', 'status']);
            $table->index(['tenant_id', 'table_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('waiter_requests', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['tenant_id', 'branch_id']);
            $table->dropIndex(['tenant_id', 'status']);
            $table->dropIndex(['tenant_id', 'table_id']);
            
            // Drop foreign key and column
            $table->dropForeign(['tenant_id']);
            $table->dropColumn('tenant_id');
        });
    }
};
