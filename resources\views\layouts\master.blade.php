<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <title>{{ config('app.name', 'Restaurant POS') }} - @yield('title', 'Dashboard')</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    

    
 
    <!-- Custom Styles -->
    <style>
        .sidebar-transition {
            transition: transform 0.3s ease-in-out;
        }
        
        .content-transition {
            transition: margin-left 0.3s ease-in-out;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .tab-button.active {
            background-color: #3b82f6;
            color: white;
        }
        
		
        /* Mobile sidebar styles are now handled by Tailwind classes */    </style>
    
    @stack('styles')
</head>
<body class="bg-gray-50 font-sans antialiased">
    <div class="min-h-screen">
        <!-- Mobile menu overlay -->
        <div id="mobile-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden md:hidden"></div>
        
        <!-- Sidebar -->
        @include('layouts.main-sidebar')
        
        <!-- Main content wrapper -->
        <div id="main-content" class="content-transition md:ml-64">
            <!-- Header -->
            @include('layouts.main-header')
            
            <!-- Page Content -->
            <main class="p-4 md:p-6">
                @if(session('success'))
                    <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                        <span class="block sm:inline">{{ session('success') }}</span>
                    </div>
                @endif
                
                @if(session('error'))
                    <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                        <span class="block sm:inline">{{ session('error') }}</span>
                    </div>
                @endif
                @yield('page-header')
                @yield('content')
            </main>
        </div>
    </div>
    
    <!-- Scripts -->
    <script>
        $(document).ready(function() {
            // Mobile menu toggle
            $('#mobile-menu-toggle').on('click', function() {
                const sidebar = $('#sidebar');
                const overlay = $('#mobile-overlay');

                if (sidebar.hasClass('-translate-x-full')) {
                    // Open sidebar
                    sidebar.removeClass('-translate-x-full').addClass('translate-x-0');
                    overlay.removeClass('hidden');
                } else {
                    // Close sidebar
                    sidebar.removeClass('translate-x-0').addClass('-translate-x-full');
                    overlay.addClass('hidden');
                }
            });

            // Close mobile menu when overlay is clicked
            $('#mobile-overlay').on('click', function() {
                $('#sidebar').removeClass('translate-x-0').addClass('-translate-x-full');
                $('#mobile-overlay').addClass('hidden');
            });

            // Desktop sidebar toggle
            $('#desktop-menu-toggle').on('click', function() {
                $('#sidebar').toggleClass('hidden');
                if ($('#sidebar').hasClass('hidden')) {
                    $('#main-content').removeClass('md:ml-64').addClass('md:ml-0');
                } else {
                    $('#main-content').removeClass('md:ml-0').addClass('md:ml-64');
                }
            });

            // Close mobile sidebar when clicking outside (additional safety)
            $(document).on('click', function(e) {
                if ($(window).width() < 768) {
                    if (!$(e.target).closest('#sidebar, #mobile-menu-toggle').length) {
                        $('#sidebar').removeClass('translate-x-0').addClass('-translate-x-full');
                        $('#mobile-overlay').addClass('hidden');
                    }
                }
            });

            // Tab functionality
            $('.tab-button').on('click', function() {
                var target = $(this).data('tab');

                // Remove active class from all tabs and content
                $('.tab-button').removeClass('active');
                $('.tab-content').removeClass('active');

                // Add active class to clicked tab and corresponding content
                $(this).addClass('active');
                $('#' + target).addClass('active');
            });

            // Close alerts
            $('.alert-close').on('click', function() {
                $(this).parent().fadeOut();
            });
        });
    </script>
    
  

    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- Custom Modal Utility -->
    <script>
        // Custom Modal Class to replace Flowbite Modal
    //     class CustomModal {
    //         constructor(element, options = {}) {
    //             this.element = element;
    //             this.options = {
    //                 backdrop: options.backdrop !== false,
    //                 keyboard: options.keyboard !== false,
    //                 focus: options.focus !== false,
    //                 ...options
    //             };
    //             this.isOpen = false;
    //             this.init();
    //         }
            
    //         init() {
    //             // Add backdrop if it doesn't exist
    //             if (this.options.backdrop && !this.element.querySelector('.modal-backdrop')) {
    //                 const backdrop = document.createElement('div');
    //                 backdrop.className = 'modal-backdrop fixed inset-0 bg-black bg-opacity-50 z-40';
    //                 this.element.appendChild(backdrop);
                    
    //                 // Close modal when backdrop is clicked
    //                 backdrop.addEventListener('click', (e) => {
    //                     if (e.target === backdrop) {
    //                         this.hide();
    //                     }
    //                 });
    //             }
                
    //             // Handle ESC key
    //             if (this.options.keyboard) {
    //                 document.addEventListener('keydown', (e) => {
    //                     if (e.key === 'Escape' && this.isOpen) {
    //                         this.hide();
    //                     }
    //                 });
    //             }
    //         }
            
    //         show() {
    //             if (this.isOpen) return;
                
    //             this.element.classList.remove('hidden');
    //             this.element.classList.add('flex');
    //             document.body.classList.add('overflow-hidden');
    //             this.isOpen = true;
                
    //             // Focus management
    //             if (this.options.focus) {
    //                 const focusableElement = this.element.querySelector('input, textarea, select, button');
    //                 if (focusableElement) {
    //                     setTimeout(() => focusableElement.focus(), 100);
    //                 }
    //             }
                
    //             // Trigger custom event
    //             this.element.dispatchEvent(new CustomEvent('modal:show'));
    //         }
            
    //         hide() {
    //             if (!this.isOpen) return;
                
    //             this.element.classList.add('hidden');
    //             this.element.classList.remove('flex');
    //             document.body.classList.remove('overflow-hidden');
    //             this.isOpen = false;
                
    //             // Trigger custom event
    //             this.element.dispatchEvent(new CustomEvent('modal:hide'));
    //         }
            
    //         toggle() {
    //             if (this.isOpen) {
    //                 this.hide();
    //             } else {
    //                 this.show();
    //             }
    //         }
    //     }
        
    //     // Global Modal utility functions
    //     window.Modal = CustomModal;
        
    //     // Helper functions for backward compatibility
    //     window.showModal = function(modalId) {
    //         const modal = document.getElementById(modalId);
    //         if (modal) {
    //             if (!modal._customModal) {
    //                 modal._customModal = new CustomModal(modal);
    //             }
    //             modal._customModal.show();
    //         }
    //     };
        
    //     window.hideModal = function(modalId) {
    //         const modal = document.getElementById(modalId);
    //         if (modal && modal._customModal) {
    //             modal._customModal.hide();
    //         }
    //     };
        
    //     window.toggleModal = function(modalId) {
    //         const modal = document.getElementById(modalId);
    //         if (modal) {
    //             if (!modal._customModal) {
    //                 modal._customModal = new CustomModal(modal);
    //             }
    //             modal._customModal.toggle();
    //         }
    //     };
    // </script>
    
    @stack('scripts')
</body>
</html>