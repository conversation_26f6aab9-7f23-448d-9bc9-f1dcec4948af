@extends('layouts.app')

@push('styles')
<!-- DataTables CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.6/css/dataTables.tailwindcss.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.tailwindcss.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.tailwindcss.min.css">

<!-- Font Awesome -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<!-- SweetAlert2 -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css">

<style>
.report-card {
    transition: all 0.3s ease;
    cursor: pointer;
}
.report-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}
.report-icon {
    font-size: 3rem;
}
.report-category-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
</style>
@endpush

@section('content')
<div class="min-h-screen bg-gray-50" dir="rtl">
    <!-- Page Header with Gradient -->
    <div class="bg-gradient-to-r from-purple-600 to-indigo-600 rounded-lg shadow-lg p-6 mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                            <i class="fas fa-chart-bar text-white text-xl"></i>
                        </div>
                    </div>
                    <div class="mr-4 flex-1">
                        <h1 class="text-2xl font-bold text-white">التقارير</h1>
                        <p class="text-purple-100">إدارة وإنشاء التقارير المختلفة</p>
                    </div>
                </div>
            </div>
            <div class="mt-4 md:mt-0 flex space-x-3 space-x-reverse">
                <button type="button" id="generateReportBtn" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-all duration-200 flex items-center">
                    <i class="fas fa-plus ml-2"></i>
                    إنشاء تقرير
                </button>
                <button type="button" onclick="window.location.reload()" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-all duration-200 flex items-center">
                    <i class="fas fa-sync-alt ml-2"></i>
                    تحديث
                </button>
            </div>
        </div>
    </div>

    <!-- Daily Reports Section -->
    <div class="report-category-header rounded-lg shadow-lg p-6 mb-6 text-white">
        <div class="flex items-center">
            <i class="fas fa-calendar-day text-3xl ml-3"></i>
            <div>
                <h3 class="text-xl font-bold mb-1">التقارير اليومية</h3>
                <p class="text-purple-100">تقارير العمليات اليومية والمبيعات</p>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
        <div class="report-card bg-white rounded-lg shadow-lg p-6 hover:shadow-xl" onclick="generateReport('daily_sales_summary')">
            <div class="text-center">
                <div class="report-icon text-blue-600 mb-4">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h5 class="text-lg font-semibold text-gray-800 mb-2">ملخص المبيعات اليومية</h5>
                <p class="text-gray-600 text-sm">إجمالي المبيعات والطلبات والضرائب</p>
            </div>
        </div>

        <div class="report-card bg-white rounded-lg shadow-lg p-6 hover:shadow-xl" onclick="generateReport('daily_stock_drawer')">
            <div class="text-center">
                <div class="report-icon text-green-600 mb-4">
                    <i class="fas fa-boxes"></i>
                </div>
                <h5 class="text-lg font-semibold text-gray-800 mb-2">تقرير المخزون اليومي</h5>
                <p class="text-gray-600 text-sm">حالة المخزون والكميات المباعة</p>
            </div>
        </div>

        <div class="report-card bg-white rounded-lg shadow-lg p-6 hover:shadow-xl" onclick="generateReport('daily_payments')">
            <div class="text-center">
                <div class="report-icon text-cyan-600 mb-4">
                    <i class="fas fa-credit-card"></i>
                </div>
                <h5 class="text-lg font-semibold text-gray-800 mb-2">تقرير المدفوعات اليومية</h5>
                <p class="text-gray-600 text-sm">تفصيل طرق الدفع والمبالغ</p>
            </div>
        </div>

        <div class="report-card bg-white rounded-lg shadow-lg p-6 hover:shadow-xl" onclick="generateReport('daily_cash_drawer')">
            <div class="text-center">
                <div class="report-icon text-yellow-600 mb-4">
                    <i class="fas fa-cash-register"></i>
                </div>
                <h5 class="text-lg font-semibold text-gray-800 mb-2">تقرير الخزنة اليومية</h5>
                <p class="text-gray-600 text-sm">فتح وإغلاق الخزنة والفروقات</p>
            </div>
        </div>

        <div class="report-card bg-white rounded-lg shadow-lg p-6 hover:shadow-xl" onclick="generateReport('daily_discounts')">
            <div class="text-center">
                <div class="report-icon text-red-600 mb-4">
                    <i class="fas fa-percent"></i>
                </div>
                <h5 class="text-lg font-semibold text-gray-800 mb-2">تقرير الخصومات اليومية</h5>
                <p class="text-gray-600 text-sm">الخصومات والعروض المطبقة</p>
            </div>
        </div>

        <div class="report-card bg-white rounded-lg shadow-lg p-6 hover:shadow-xl" onclick="generateReport('daily_returns')">
            <div class="text-center">
                <div class="report-icon text-gray-600 mb-4">
                    <i class="fas fa-undo"></i>
                </div>
                <h5 class="text-lg font-semibold text-gray-800 mb-2">تقرير المرتجعات اليومية</h5>
                <p class="text-gray-600 text-sm">المنتجات المرتجعة وأسبابها</p>
            </div>
        </div>

        <div class="report-card bg-white rounded-lg shadow-lg p-6 hover:shadow-xl" onclick="generateReport('hourly_sales')">
            <div class="text-center">
                <div class="report-icon text-blue-600 mb-4">
                    <i class="fas fa-clock"></i>
                </div>
                <h5 class="text-lg font-semibold text-gray-800 mb-2">تقرير المبيعات بالساعة</h5>
                <p class="text-gray-600 text-sm">تحليل المبيعات حسب الساعات</p>
            </div>
        </div>

        <div class="report-card bg-white rounded-lg shadow-lg p-6 hover:shadow-xl" onclick="generateReport('tax_report')">
            <div class="text-center">
                <div class="report-icon text-green-600 mb-4">
                    <i class="fas fa-receipt"></i>
                </div>
                <h5 class="text-lg font-semibold text-gray-800 mb-2">تقرير الضرائب</h5>
                <p class="text-gray-600 text-sm">تفصيل الضرائب المحصلة</p>
            </div>
        </div>
    </div>

    <!-- Periodic Reports Section -->
    <div class="report-category-header rounded-lg shadow-lg p-6 mb-6 text-white">
        <div class="flex items-center">
            <i class="fas fa-calendar-alt text-3xl ml-3"></i>
            <div>
                <h3 class="text-xl font-bold mb-1">التقارير الدورية</h3>
                <p class="text-purple-100">تقارير الأداء والتحليلات الدورية</p>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
        <div class="report-card bg-white rounded-lg shadow-lg p-6 hover:shadow-xl" onclick="generateReport('product_performance')">
            <div class="text-center">
                <div class="report-icon text-blue-600 mb-4">
                    <i class="fas fa-trending-up"></i>
                </div>
                <h5 class="text-lg font-semibold text-gray-800 mb-2">أداء المنتجات</h5>
                <p class="text-gray-600 text-sm">أفضل وأسوأ المنتجات مبيعاً</p>
            </div>
        </div>

        <div class="report-card bg-white rounded-lg shadow-lg p-6 hover:shadow-xl" onclick="generateReport('staff_performance')">
            <div class="text-center">
                <div class="report-icon text-cyan-600 mb-4">
                    <i class="fas fa-users"></i>
                </div>
                <h5 class="text-lg font-semibold text-gray-800 mb-2">أداء الموظفين</h5>
                <p class="text-gray-600 text-sm">إحصائيات أداء الكاشيرين والخدمة</p>
            </div>
        </div>

        <div class="report-card bg-white rounded-lg shadow-lg p-6 hover:shadow-xl" onclick="generateReport('profit_report')">
            <div class="text-center">
                <div class="report-icon text-green-600 mb-4">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <h5 class="text-lg font-semibold text-gray-800 mb-2">تقرير الأرباح</h5>
                <p class="text-gray-600 text-sm">تحليل الأرباح والهوامش</p>
            </div>
        </div>

        <div class="report-card bg-white rounded-lg shadow-lg p-6 hover:shadow-xl" onclick="generateReport('customer_report')">
            <div class="text-center">
                <div class="report-icon text-yellow-600 mb-4">
                    <i class="fas fa-heart"></i>
                </div>
                <h5 class="text-lg font-semibold text-gray-800 mb-2">تقرير العملاء</h5>
                <p class="text-gray-600 text-sm">أفضل العملاء والعملاء الجدد</p>
            </div>
        </div>
    </div>

    <!-- Advanced Reports Section -->
    <div class="report-category-header rounded-lg shadow-lg p-6 mb-6 text-white">
        <div class="flex items-center">
            <i class="fas fa-chart-pie text-3xl ml-3"></i>
            <div>
                <h3 class="text-xl font-bold mb-1">التقارير المتقدمة</h3>
                <p class="text-purple-100">تقارير متخصصة وتحليلات متقدمة</p>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
        <div class="report-card bg-white rounded-lg shadow-lg p-6 hover:shadow-xl" onclick="generateReport('stock_movement')">
            <div class="text-center">
                <div class="report-icon text-blue-600 mb-4">
                    <i class="fas fa-exchange-alt"></i>
                </div>
                <h5 class="text-lg font-semibold text-gray-800 mb-2">حركة المخزون</h5>
                <p class="text-gray-600 text-sm">تتبع حركات المخزون التفصيلية</p>
            </div>
        </div>

        <div class="report-card bg-white rounded-lg shadow-lg p-6 hover:shadow-xl" onclick="generateReport('reorder_point')">
            <div class="text-center">
                <div class="report-icon text-red-600 mb-4">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h5 class="text-lg font-semibold text-gray-800 mb-2">نقاط إعادة الطلب</h5>
                <p class="text-gray-600 text-sm">المنتجات التي تحتاج إعادة طلب</p>
            </div>
        </div>

        <div class="report-card bg-white rounded-lg shadow-lg p-6 hover:shadow-xl" onclick="generateReport('void_cancelled')">
            <div class="text-center">
                <div class="report-icon text-gray-600 mb-4">
                    <i class="fas fa-ban"></i>
                </div>
                <h5 class="text-lg font-semibold text-gray-800 mb-2">الطلبات الملغية</h5>
                <p class="text-gray-600 text-sm">تحليل الطلبات الملغية وأسبابها</p>
            </div>
        </div>

        <div class="report-card bg-white rounded-lg shadow-lg p-6 hover:shadow-xl" onclick="generateReport('table_turnover')">
            <div class="text-center">
                <div class="report-icon text-cyan-600 mb-4">
                    <i class="fas fa-table"></i>
                </div>
                <h5 class="text-lg font-semibold text-gray-800 mb-2">دوران الطاولات</h5>
                <p class="text-gray-600 text-sm">كفاءة استخدام الطاولات</p>
            </div>
        </div>

        <div class="report-card bg-white rounded-lg shadow-lg p-6 hover:shadow-xl" onclick="generateReport('waste_tracking')">
            <div class="text-center">
                <div class="report-icon text-yellow-600 mb-4">
                    <i class="fas fa-trash-alt"></i>
                </div>
                <h5 class="text-lg font-semibold text-gray-800 mb-2">تتبع الهدر</h5>
                <p class="text-gray-600 text-sm">تحليل المواد المهدرة وتكلفتها</p>
            </div>
        </div>

        <div class="report-card bg-white rounded-lg shadow-lg p-6 hover:shadow-xl" onclick="generateReport('supplier_performance')">
            <div class="text-center">
                <div class="report-icon text-green-600 mb-4">
                    <i class="fas fa-truck"></i>
                </div>
                <h5 class="text-lg font-semibold text-gray-800 mb-2">أداء الموردين</h5>
                <p class="text-gray-600 text-sm">تقييم أداء الموردين والتسليم</p>
            </div>
        </div>
    </div>

    <!-- Restaurant Specific Reports -->
    <div class="report-category-header rounded-lg shadow-lg p-6 mb-6 text-white">
        <div class="flex items-center">
            <i class="fas fa-utensils text-3xl ml-3"></i>
            <div>
                <h3 class="text-xl font-bold mb-1">تقارير المطعم</h3>
                <p class="text-purple-100">تقارير خاصة بعمليات المطعم</p>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
        <div class="report-card bg-white rounded-lg shadow-lg p-6 hover:shadow-xl" onclick="generateReport('menu_items_report')">
            <div class="text-center">
                <div class="report-icon text-blue-600 mb-4">
                    <i class="fas fa-hamburger"></i>
                </div>
                <h5 class="text-lg font-semibold text-gray-800 mb-2">تقرير عناصر القائمة</h5>
                <p class="text-gray-600 text-sm">أداء عناصر القائمة والأسعار</p>
            </div>
        </div>

        <div class="report-card bg-white rounded-lg shadow-lg p-6 hover:shadow-xl" onclick="generateReport('meal_time_sales')">
            <div class="text-center">
                <div class="report-icon text-cyan-600 mb-4">
                    <i class="fas fa-clock"></i>
                </div>
                <h5 class="text-lg font-semibold text-gray-800 mb-2">مبيعات أوقات الوجبات</h5>
                <p class="text-gray-600 text-sm">تحليل المبيعات حسب أوقات الوجبات</p>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.tailwindcss.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.tailwindcss.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.tailwindcss.min.js"></script>

<!-- Export Buttons -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>

<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>

<script>
function generateReport(type) {
    // Show loading toast
    Swal.fire({
        title: 'جاري إنشاء التقرير...',
        text: 'يرجى الانتظار',
        icon: 'info',
        allowOutsideClick: false,
        showConfirmButton: false,
        willOpen: () => {
            Swal.showLoading();
        }
    });

    // Redirect to report generation page with type
    setTimeout(() => {
        window.location.href = `/reports/generate?type=${type}`;
    }, 1000);
}

$(document).ready(function() {
    $('#generateReportBtn').click(function() {
        Swal.fire({
            title: 'إنشاء تقرير جديد',
            text: 'سيتم توجيهك إلى صفحة إنشاء التقارير',
            icon: 'info',
            showCancelButton: true,
            confirmButtonText: 'متابعة',
            cancelButtonText: 'إلغاء',
            confirmButtonColor: '#3b82f6',
            cancelButtonColor: '#ef4444'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = '/reports/generate';
            }
        });
    });

    // Add hover effects to report cards
    $('.report-card').hover(
        function() {
            $(this).addClass('transform scale-105');
        },
        function() {
            $(this).removeClass('transform scale-105');
        }
    );

    // Show success message if redirected from report generation
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('generated') === 'true') {
        Swal.fire({
            title: 'تم إنشاء التقرير بنجاح!',
            text: 'يمكنك الآن عرض أو تحميل التقرير',
            icon: 'success',
            confirmButtonText: 'حسناً',
            confirmButtonColor: '#10b981'
        });
    }
});
</script>
@endpush
