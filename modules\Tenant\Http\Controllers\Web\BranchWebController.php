<?php

namespace Modules\Tenant\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Branch;
use App\Models\Tenant;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class BranchWebController extends Controller
{
    /**
     * Display a listing of branches.
     */
    public function index()
    {
        $tenants = Tenant::where('status', 'active')->get();
        
        return view('tenant::branches.index', compact('tenants'));
    }

    /**
     * Show the form for creating a new branch.
     */
    public function create()
    {
        $tenants = Tenant::where('status', 'active')->get();
        
        return view('tenant::branches.create', compact('tenants'));
    }

    /**
     * Store a newly created branch.
     */
    public function store(Request $request)
    {
        $request->validate([
            'tenant_id' => 'required|exists:tenants,id',
            'name' => 'required|string|max:255',
            'code' => 'nullable|string|unique:branches,code|max:50',
            'address' => 'required|string',
            'phone' => 'required|string|max:20',
            'email' => 'nullable|email',
            'manager_name' => 'required|string|max:255',
            'seating_capacity' => 'nullable|integer|min:1',
            'delivery_radius' => 'nullable|numeric|min:0',
            'timezone' => 'required|string|max:50',
            'is_delivery_enabled' => 'boolean',
            'is_takeaway_enabled' => 'boolean',
            'is_dine_in_enabled' => 'boolean',
            'is_online_ordering_enabled' => 'boolean',
        ]);

        try {
            // Generate unique branch code if not provided
            $data = $request->all();
            if (!$data['code']) {
                $data['code'] = $this->generateBranchCode($data['name'], $data['tenant_id']);
            }

            $branch = Branch::create($data);
            
            return redirect()->route('branches.index')
                ->with('success', 'تم إنشاء الفرع بنجاح');
        } catch (\Exception $e) {
            return back()->withInput()
                ->with('error', 'حدث خطأ أثناء إنشاء الفرع: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified branch.
     */
    public function show(Branch $branch)
    {
        // If it's an AJAX request, return JSON for modal
        if (request()->ajax()) {
            $branch->load(['tenant']);
            return response()->json([
                'id' => $branch->id,
                'name' => $branch->name,
                'code' => $branch->code,
                'address' => $branch->address,
                'phone' => $branch->phone,
                'email' => $branch->email,
                'manager_name' => $branch->manager_name,
                'status' => $branch->status,
                'tenant' => $branch->tenant,
                'created_at' => $branch->created_at,
                'seating_capacity' => $branch->seating_capacity,
                'delivery_radius' => $branch->delivery_radius,
                'timezone' => $branch->timezone,
                'is_delivery_enabled' => $branch->is_delivery_enabled,
                'is_takeaway_enabled' => $branch->is_takeaway_enabled,
                'is_dine_in_enabled' => $branch->is_dine_in_enabled,
                'is_online_ordering_enabled' => $branch->is_online_ordering_enabled,
            ]);
        }

        // For regular requests, load full view if it exists
        $branch->load(['tenant', 'users']);
        $statistics = $this->getStatistics($branch);
        
        return view('tenant::branches.show', compact('branch', 'statistics'));
    }

    /**
     * Show the form for editing the specified branch.
     */
    public function edit(Branch $branch)
    {
        // If it's an AJAX request, return JSON for modal
        if (request()->ajax()) {
            return response()->json([
                'id' => $branch->id,
                'tenant_id' => $branch->tenant_id,
                'name' => $branch->name,
                'code' => $branch->code,
                'address' => $branch->address,
                'phone' => $branch->phone,
                'email' => $branch->email,
                'manager_name' => $branch->manager_name,
                'status' => $branch->status,
                'seating_capacity' => $branch->seating_capacity,
                'delivery_radius' => $branch->delivery_radius,
                'timezone' => $branch->timezone,
                'is_delivery_enabled' => $branch->is_delivery_enabled,
                'is_takeaway_enabled' => $branch->is_takeaway_enabled,
                'is_dine_in_enabled' => $branch->is_dine_in_enabled,
                'is_online_ordering_enabled' => $branch->is_online_ordering_enabled,
            ]);
        }

        // For regular requests, load full view if it exists
        $tenants = Tenant::where('status', 'active')->get();
        
        return view('tenant::branches.edit', compact('branch', 'tenants'));
    }

    /**
     * Update the specified branch.
     */
    public function update(Request $request, Branch $branch)
    {
        $request->validate([
            'tenant_id' => 'required|exists:tenants,id',
            'name' => 'required|string|max:255',
            'code' => 'nullable|string|max:50|unique:branches,code,' . $branch->id,
            'address' => 'required|string',
            'phone' => 'required|string|max:20',
            'email' => 'nullable|email',
            'manager_name' => 'required|string|max:255',
            'seating_capacity' => 'nullable|integer|min:1',
            'delivery_radius' => 'nullable|numeric|min:0',
            'timezone' => 'required|string|max:50',
            'is_delivery_enabled' => 'boolean',
            'is_takeaway_enabled' => 'boolean',
            'is_dine_in_enabled' => 'boolean',
            'is_online_ordering_enabled' => 'boolean',
        ]);

        try {
            $branch->update($request->all());
            
            return redirect()->route('branches.show', $branch)
                ->with('success', 'تم تحديث بيانات الفرع بنجاح');
        } catch (\Exception $e) {
            return back()->withInput()
                ->with('error', 'حدث خطأ أثناء تحديث الفرع: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified branch.
     */
    public function destroy(Branch $branch)
    {
        try {
            // Check if branch has active orders or users
            if ($branch->orders()->count() > 0 || $branch->users()->count() > 0) {
                // If it's an AJAX request, return JSON response
                if (request()->ajax()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'لا يمكن حذف الفرع لوجود طلبات أو مستخدمين مرتبطين به'
                    ], 400);
                }
                return back()->with('error', 'لا يمكن حذف الفرع لوجود طلبات أو مستخدمين مرتبطين به');
            }

            $branch->delete();
            
            // If it's an AJAX request, return JSON response
            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'تم حذف الفرع بنجاح'
                ]);
            }
            
            return redirect()->route('branches.index')
                ->with('success', 'تم حذف الفرع بنجاح');
        } catch (\Exception $e) {
            // If it's an AJAX request, return JSON error response
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'حدث خطأ أثناء حذف الفرع: ' . $e->getMessage()
                ], 500);
            }
            
            return back()->with('error', 'حدث خطأ أثناء حذف الفرع: ' . $e->getMessage());
        }
    }

    /**
     * Activate branch.
     */
    public function activate(Branch $branch)
    {
        try {
            $branch->update(['status' => 'active']);
            
            return back()->with('success', 'تم تفعيل الفرع بنجاح');
        } catch (\Exception $e) {
            return back()->with('error', 'حدث خطأ أثناء تفعيل الفرع: ' . $e->getMessage());
        }
    }

    /**
     * Deactivate branch.
     */
    public function deactivate(Branch $branch)
    {
        try {
            $branch->update(['status' => 'inactive']);
            
            return back()->with('success', 'تم إلغاء تفعيل الفرع بنجاح');
        } catch (\Exception $e) {
            return back()->with('error', 'حدث خطأ أثناء إلغاء تفعيل الفرع: ' . $e->getMessage());
        }
    }

    /**
     * Update branch status (unified method for AJAX requests).
     */
    public function updateStatus(Request $request, Branch $branch)
    {
        $request->validate([
            'status' => 'required|in:active,inactive'
        ]);

        try {
            $branch->update(['status' => $request->status]);
            
            $message = $request->status === 'active' 
                ? 'تم تفعيل الفرع بنجاح' 
                : 'تم إلغاء تفعيل الفرع بنجاح';
            
            // If it's an AJAX request, return JSON response
            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'status' => $branch->status
                ]);
            }
            
            return back()->with('success', $message);
        } catch (\Exception $e) {
            $errorMessage = 'حدث خطأ أثناء تغيير حالة الفرع: ' . $e->getMessage();
            
            // If it's an AJAX request, return JSON error response
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => $errorMessage
                ], 500);
            }
            
            return back()->with('error', $errorMessage);
        }
    }

    /**
     * Get branches data for DataTable.
     */
public function getBranchesData(Request $request)
{
    $query = Branch::with(['tenant'])
        ->select(['id', 'tenant_id', 'name', 'code', 'address', 'phone', 'manager_name', 'status', 'created_at']);

    // Apply filters
    if ($request->filled('status')) {
        $query->where('status', $request->status);
    }

    if ($request->filled('tenant_id')) {
        $query->where('tenant_id', $request->tenant_id);
    }

    return DataTables::of($query)
        ->addIndexColumn()
        ->addColumn('tenant_name', function ($branch) {
            return $branch->tenant ? $branch->tenant->name : '-';
        })
        ->addColumn('contact_info', function ($branch) {
            return '<div class="space-y-1">' .
                   '<div class="flex items-center text-sm text-gray-600"><i class="fa fa-phone mr-2"></i> ' . $branch->phone . '</div>' .
                   ($branch->email ? '<div class="flex items-center text-sm text-gray-600"><i class="fa fa-envelope mr-2"></i> ' . $branch->email . '</div>' : '') .
                   '</div>';
        })
        ->addColumn('services', function ($branch) {
            $services = [];
            if ($branch->is_dine_in_enabled) $services[] = '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">تناول في المكان</span>';
            if ($branch->is_takeaway_enabled) $services[] = '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-cyan-100 text-cyan-800">استلام</span>';
            if ($branch->is_delivery_enabled) $services[] = '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">توصيل</span>';
            if ($branch->is_online_ordering_enabled) $services[] = '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">طلب أونلاين</span>';
            
            return '<div class="flex flex-wrap gap-1">' . implode(' ', $services) . '</div>';
        })
        ->addColumn('status_badge', function ($branch) {
            $badges = [
                'active' => '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">نشط</span>',
                'inactive' => '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">غير نشط</span>',
            ];
            return $badges[$branch->status] ?? $branch->status;
        })
        ->addColumn('users_count', function ($branch) {
            return $branch->users()->count();
        })
        ->addColumn('orders_count', function ($branch) {
            return $branch->orders()->count();
        })
        ->addColumn('action', function ($branch) {
            $actions = '<div class="flex space-x-1 rtl:space-x-reverse">';
            $actions .= '<a href="' . route('branches.show', $branch) . '" class="inline-flex items-center px-2 py-1 text-xs font-medium text-white bg-blue-600 border border-transparent rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2" title="عرض"><i class="fa fa-eye"></i></a>';
            $actions .= '<a href="' . route('branches.edit', $branch) . '" class="inline-flex items-center px-2 py-1 text-xs font-medium text-white bg-indigo-600 border border-transparent rounded hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2" title="تعديل"><i class="fa fa-edit"></i></a>';
            
            if ($branch->status === 'active') {
                $actions .= '<button type="button" class="inline-flex items-center px-2 py-1 text-xs font-medium text-white bg-yellow-600 border border-transparent rounded hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2" onclick="changeStatus(' . $branch->id . ', \'deactivate\')" title="إلغاء تفعيل"><i class="fa fa-pause"></i></button>';
            } else {
                $actions .= '<button type="button" class="inline-flex items-center px-2 py-1 text-xs font-medium text-white bg-green-600 border border-transparent rounded hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2" onclick="changeStatus(' . $branch->id . ', \'activate\')" title="تفعيل"><i class="fa fa-play"></i></button>';
            }
            
            $actions .= '<button type="button" class="inline-flex items-center px-2 py-1 text-xs font-medium text-white bg-red-600 border border-transparent rounded hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2" onclick="deleteBranch(' . $branch->id . ')" title="حذف"><i class="fa fa-trash"></i></button>';
            $actions .= '</div>';
            
            return $actions;
        })
        ->rawColumns(['contact_info', 'services', 'status_badge', 'action'])
        ->make(true);
}

    /**
     * Get branch statistics.
     */
    public function getStatistics(Branch $branch)
    {
        return [
            'users_count' => $branch->users()->count(),
            'active_users' => $branch->users()->where('is_active', true)->count(),
            'orders_count' => $branch->orders()->count(),
            'today_orders' => $branch->orders()->whereDate('created_at', today())->count(),
            'total_revenue' => $branch->orders()->where('status', 'completed')->sum('total_amount'),
            'tables_count' => $branch->tables()->count(),
        ];
    }

    /**
     * Show branch settings page.
     */
    public function settings(Branch $branch)
    {
        return view('tenant::branches.settings', compact('branch'));
    }

    /**
     * Update branch settings.
     */
    public function updateSettings(Request $request, Branch $branch)
    {
        // Implementation for updating branch-specific settings
        return back()->with('success', 'تم تحديث إعدادات الفرع بنجاح');
    }

    /**
     * Show branch users page.
     */
    public function users(Branch $branch)
    {
        return view('tenant::branches.users', compact('branch'));
    }

    /**
     * Get branch users data for DataTable.
     */
    public function getUsersData(Request $request, Branch $branch)
    {
        $query = User::where('branch_id', $branch->id)
            ->with(['roles'])
            ->select(['id', 'name', 'email', 'position', 'department', 'is_active', 'created_at']);

        return DataTables::of($query)
            ->addIndexColumn()
            ->addColumn('roles', function ($user) {
                return $user->roles->pluck('name')->implode(', ');
            })
            ->addColumn('status_badge', function ($user) {
                return $user->is_active 
                    ? '<span class="badge badge-success">نشط</span>'
                    : '<span class="badge badge-secondary">غير نشط</span>';
            })
            ->rawColumns(['status_badge'])
            ->make(true);
    }

    /**
     * Switch user's current branch.
     */
    public function switch(Request $request, Branch $branch)
    {
        try {
            $user = $request->user();
            
            // Verify that the branch belongs to the user's tenant
            if ($branch->tenant_id !== $user->tenant_id) {
                return back()->with('error', 'لا يمكنك التبديل إلى هذا الفرع');
            }
            
            // Verify that the branch is active
            if ($branch->status !== 'active') {
                return back()->with('error', 'هذا الفرع غير نشط حالياً');
            }
            
            // Update user's branch
            $user->update(['branch_id' => $branch->id]);
            
            // Store branch info in session for quick access
            session(['current_branch' => [
                'id' => $branch->id,
                'name' => $branch->name,
                'code' => $branch->code
            ]]);
            
            return back()->with('success', 'تم التبديل إلى فرع "' . $branch->name . '" بنجاح');
            
        } catch (\Exception $e) {
            return back()->with('error', 'حدث خطأ أثناء التبديل إلى الفرع: ' . $e->getMessage());
        }
    }

    /**
     * Generate unique branch code.
     */
    private function generateBranchCode(string $name, int $tenantId): string
    {
        $prefix = strtoupper(substr($name, 0, 3));
        $suffix = str_pad($tenantId, 3, '0', STR_PAD_LEFT);
        $counter = 1;
        
        do {
            $code = $prefix . $suffix . str_pad($counter, 2, '0', STR_PAD_LEFT);
            $exists = Branch::where('code', $code)->exists();
            $counter++;
        } while ($exists && $counter <= 99);
        
        return $code;
    }
}
