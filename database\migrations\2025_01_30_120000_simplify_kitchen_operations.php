<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Remove operating_hours from kitchens table
        Schema::table('kitchens', function (Blueprint $table) {
            $table->dropColumn('operating_hours');
        });

        // Add default_kitchen_id to branches table
        Schema::table('branches', function (Blueprint $table) {
            $table->foreignId('default_kitchen_id')->nullable()->constrained('kitchens')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Add back operating_hours to kitchens table
        Schema::table('kitchens', function (Blueprint $table) {
            $table->json('operating_hours')->nullable();
        });

        // Remove default_kitchen_id from branches table
        Schema::table('branches', function (Blueprint $table) {
            $table->dropForeign(['default_kitchen_id']);
            $table->dropColumn('default_kitchen_id');
        });
    }
};