<?php

namespace Modules\Inventory\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\PurchaseOrder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Modules\Inventory\Http\Requests\StorePurchaseOrderRequest;
use Modules\Inventory\Http\Requests\UpdatePurchaseOrderRequest;
use Modules\Inventory\Services\PurchaseOrderService;

class PurchaseOrderController extends Controller
{
    protected $purchaseOrderService;

    public function __construct(PurchaseOrderService $purchaseOrderService)
    {
        $this->purchaseOrderService = $purchaseOrderService;
    }

    /**
     * Display a listing of purchase orders.
     */
    public function index(Request $request)
    {
        $orders = $this->purchaseOrderService->getAllPurchaseOrders($request->all());
        return response()->json($orders);
    }

    /**
     * Get purchase orders data for DataTable using Yajra DataTables
     */
    public function datatable(Request $request)
    {
        $user = Auth::user();

        if (!$user->branch_id) {
            return response()->json([
                'error' => 'User is not assigned to any branch'
            ], 400);
        }

        $query = PurchaseOrder::with(['supplier', 'branch'])
            ->where('branch_id', $user->branch_id);

        // Apply filters
        if ($request->filled('po_number')) {
            $query->where('po_number', 'like', "%{$request->po_number}%");
        }

        if ($request->filled('supplier_id')) {
            $query->where('supplier_id', $request->supplier_id);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('order_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('order_date', '<=', $request->date_to);
        }

        return \Yajra\DataTables\Facades\DataTables::of($query)
            ->addIndexColumn()
            ->addColumn('supplier_name', function ($row) {
                return $row->supplier->name ?? 'N/A';
            })
            ->addColumn('status_badge', function ($row) {
                $statusMap = [
                    'draft' => ['مسودة', 'badge-secondary'],
                    'pending' => ['معلق', 'badge-warning'],
                    'approved' => ['موافق عليه', 'badge-info'],
                    'ordered' => ['تم الطلب', 'badge-primary'],
                    'received' => ['تم الاستلام', 'badge-success'],
                    'cancelled' => ['ملغي', 'badge-danger'],
                ];

                $status = $statusMap[$row->status] ?? ['غير معروف', 'badge-dark'];
                return "<span class='badge {$status[1]}'>{$status[0]}</span>";
            })
            ->addColumn('total_amount', function ($row) {
                return number_format($row->total_amount, 2) . ' ر.س';
            })
            ->addColumn('order_date', function ($row) {
                return $row->order_date ? $row->order_date->format('Y-m-d') : 'N/A';
            })
            ->addColumn('expected_date', function ($row) {
                return $row->expected_delivery_date ? $row->expected_delivery_date->format('Y-m-d') : 'N/A';
            })
            ->addColumn('actions', function ($row) {
                return "
                    <div class='btn-group' role='group'>
                        <button type='button' class='btn btn-sm btn-info' onclick='viewPurchaseOrder({$row->id})' title='عرض'>
                            <i class='fas fa-eye'></i>
                        </button>
                        <button type='button' class='btn btn-sm btn-warning' onclick='editPurchaseOrder({$row->id})' title='تعديل'>
                            <i class='fas fa-edit'></i>
                        </button>
                        <button type='button' class='btn btn-sm btn-success' onclick='receivePurchaseOrder({$row->id})' title='استلام'>
                            <i class='fas fa-check'></i>
                        </button>
                        <button type='button' class='btn btn-sm btn-danger' onclick='cancelPurchaseOrder({$row->id})' title='إلغاء'>
                            <i class='fas fa-times'></i>
                        </button>
                    </div>
                ";
            })
            ->rawColumns(['status_badge', 'actions'])
            ->make(true);
    }

    /**
     * Store a newly created purchase order.
     */
    public function store(StorePurchaseOrderRequest $request)
    {
        $order = $this->purchaseOrderService->createPurchaseOrder($request->validated());
        return response()->json($order, 201);
    }

    /**
     * Display the specified purchase order.
     */
    public function show(string $id)
    {
        $order = $this->purchaseOrderService->getPurchaseOrderById($id);
        return response()->json($order);
    }

    /**
     * Update the specified purchase order.
     */
    public function update(UpdatePurchaseOrderRequest $request, string $id)
    {
        $order = $this->purchaseOrderService->updatePurchaseOrder($id, $request->validated());
        return response()->json($order);
    }

    /**
     * Remove the specified purchase order.
     */
    public function destroy(string $id)
    {
        $this->purchaseOrderService->deletePurchaseOrder($id);
        return response()->json(null, 204);
    }

    /**
     * Approve purchase order
     */
    public function approve(string $id)
    {
        $order = $this->purchaseOrderService->approvePurchaseOrder($id);
        return response()->json($order);
    }

    /**
     * Reject purchase order
     */
    public function reject(Request $request, string $id)
    {
        $request->validate([
            'reason' => 'required|string|max:500'
        ]);

        $order = $this->purchaseOrderService->rejectPurchaseOrder($id, $request->reason);
        return response()->json($order);
    }

    /**
     * Mark purchase order as received
     */
    public function receive(Request $request, string $id)
    {
        $request->validate([
            'items' => 'required|array',
            'items.*.id' => 'required|exists:purchase_order_items,id',
            'items.*.received_quantity' => 'required|numeric|min:0',
            'items.*.unit_cost' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string|max:1000'
        ]);

        $order = $this->purchaseOrderService->receivePurchaseOrder($id, $request->validated());
        return response()->json($order);
    }

    /**
     * Cancel purchase order
     */
    public function cancel(Request $request, string $id)
    {
        $request->validate([
            'reason' => 'required|string|max:500'
        ]);

        $order = $this->purchaseOrderService->cancelPurchaseOrder($id, $request->reason);
        return response()->json($order);
    }

    /**
     * Get purchase order items
     */
    public function getItems(string $id)
    {
        $items = $this->purchaseOrderService->getPurchaseOrderItems($id);
        return response()->json($items);
    }

    /**
     * Add item to purchase order
     */
    public function addItem(Request $request, string $id)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|numeric|min:0.01',
            'unit_cost' => 'required|numeric|min:0',
            'notes' => 'nullable|string|max:255'
        ]);

        $item = $this->purchaseOrderService->addItemToPurchaseOrder($id, $request->validated());
        return response()->json($item, 201);
    }

    /**
     * Update purchase order item
     */
    public function updateItem(Request $request, string $orderId, string $itemId)
    {
        $request->validate([
            'quantity' => 'sometimes|required|numeric|min:0.01',
            'unit_cost' => 'sometimes|required|numeric|min:0',
            'notes' => 'nullable|string|max:255'
        ]);

        $item = $this->purchaseOrderService->updatePurchaseOrderItem($itemId, $request->validated());
        return response()->json($item);
    }

    /**
     * Remove item from purchase order
     */
    public function removeItem(string $orderId, string $itemId)
    {
        $this->purchaseOrderService->removeItemFromPurchaseOrder($itemId);
        return response()->json(null, 204);
    }

    /**
     * Generate purchase order PDF
     */
    public function generatePDF(string $id)
    {
        $pdf = $this->purchaseOrderService->generatePurchaseOrderPDF($id);
        return response($pdf, 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'attachment; filename="purchase-order-' . $id . '.pdf"'
        ]);
    }
}