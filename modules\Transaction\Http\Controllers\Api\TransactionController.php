<?php

namespace Modules\Transaction\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Helpers\ResponseHelper;
use Modules\Transaction\Services\TransactionService;
use Modules\Transaction\Http\Requests\StoreTransactionRequest;
use Modules\Transaction\Http\Requests\UpdateTransactionRequest;
use Modules\Transaction\Http\Resources\TransactionResource;
use Modules\Transaction\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Yajra\DataTables\Facades\DataTables;

class TransactionController extends Controller
{
    protected TransactionService $transactionService;

    public function __construct(TransactionService $transactionService)
    {
        $this->transactionService = $transactionService;
    }

    /**
     * Display a listing of transactions.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            // Check if this is a DataTables request
            if ($request->has('draw')) {
                return $this->getDataTablesData($request);
            }

            $filters = $request->only([
                'status', 'order_id', 'date_from', 'date_to', 'search',
                'sort_by', 'sort_direction', 'per_page'
            ]);

            $transactions = $this->transactionService->getAllTransactions($filters);

            return ResponseHelper::success([
                'transactions' => TransactionResource::collection($transactions->items()),
                'meta' => [
                    'current_page' => $transactions->currentPage(),
                    'last_page' => $transactions->lastPage(),
                    'per_page' => $transactions->perPage(),
                    'total' => $transactions->total(),
                ],
            ], 'Transactions retrieved successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve transactions', 500);
        }
    }

    /**
     * Get transactions data for DataTables using Yajra DataTables.
     */
    public function getDataTablesData(Request $request)
    {
        try {
            $query = Transaction::with(['order', 'createdBy', 'updatedBy'])
                ->select('transactions.*');

            // Apply filters
            if ($request->filled('status')) {
                $query->where('status', $request->get('status'));
            }

            if ($request->filled('date_from')) {
                $query->whereDate('created_at', '>=', $request->get('date_from'));
            }

            if ($request->filled('date_to')) {
                $query->whereDate('created_at', '<=', $request->get('date_to'));
            }

            return DataTables::of($query)
                ->addIndexColumn()
                ->addColumn('id', function ($row) {
                    return $row->id;
                })
                ->addColumn('transaction_number', function ($row) {
                    return $row->transaction_number;
                })
                ->addColumn('order_number', function ($row) {
                    return $row->order ? $row->order->order_number : 'N/A';
                })
                ->addColumn('status', function ($row) {
                    return $row->status;
                })
                ->addColumn('status_badge', function ($row) {
                    $statusClass = match($row->status) {
                        'paid' => 'bg-green-100 text-green-800',
                        'partially_paid' => 'bg-yellow-100 text-yellow-800',
                        'due' => 'bg-red-100 text-red-800',
                        'voided' => 'bg-gray-100 text-gray-800',
                        default => 'bg-blue-100 text-blue-800'
                    };
                    
                    return '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ' . $statusClass . '">' 
                           . ucfirst(str_replace('_', ' ', $row->status)) . '</span>';
                })
                ->addColumn('total_amount', function ($row) {
                    return $row->total_amount;
                })
                ->addColumn('paid_amount', function ($row) {
                    return $row->paid_amount;
                })
                ->addColumn('due_amount', function ($row) {
                    return $row->due_amount;
                })
                ->addColumn('formatted_total', function ($row) {
                    return '$' . number_format($row->total_amount, 2);
                })
                ->addColumn('formatted_paid', function ($row) {
                    return '$' . number_format($row->paid_amount, 2);
                })
                ->addColumn('formatted_due', function ($row) {
                    return '$' . number_format($row->due_amount, 2);
                })
                ->addColumn('formatted_date', function ($row) {
                    return $row->created_at->format('M d, Y H:i');
                })
                ->addColumn('action', function ($row) {
                    return '
                        <div class="flex space-x-2">
                            <button class="btn-view inline-flex items-center px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded transition-colors duration-200" 
                                    data-transaction-id="' . $row->id . '" title="View">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn-edit inline-flex items-center px-2 py-1 bg-yellow-600 hover:bg-yellow-700 text-white text-xs font-medium rounded transition-colors duration-200" 
                                    data-transaction-id="' . $row->id . '" title="Edit">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn-delete inline-flex items-center px-2 py-1 bg-red-600 hover:bg-red-700 text-white text-xs font-medium rounded transition-colors duration-200" 
                                    data-transaction-id="' . $row->id . '" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    ';
                })
                ->filterColumn('transaction_number', function($query, $keyword) {
                    $query->where('transaction_number', 'like', "%{$keyword}%");
                })
                ->filterColumn('order_number', function($query, $keyword) {
                    $query->whereHas('order', function($q) use ($keyword) {
                        $q->where('order_number', 'like', "%{$keyword}%");
                    });
                })
                ->orderColumn('order_number', function ($query, $order) {
                    $query->leftJoin('orders', 'transactions.order_id', '=', 'orders.id')
                          ->orderBy('orders.order_number', $order);
                })
                ->rawColumns(['status_badge', 'action'])
                ->make(true);
        } catch (\Exception $e) {
            return response()->json([
                'draw' => intval($request->get('draw', 1)),
                'recordsTotal' => 0,
                'recordsFiltered' => 0,
                'data' => [],
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Store a newly created transaction.
     */
    public function store(StoreTransactionRequest $request): JsonResponse
    {
        try {
            $validatedData = $request->validated();
            
            // Check if this is an order-based transaction or standalone
            if (!empty($validatedData['order_id'])) {
                // Order-based transaction
                $order = \App\Models\Order::findOrFail($validatedData['order_id']);
                $transaction = $this->transactionService->createTransactionFromOrder($order);
            } else {
                // Standalone transaction
                $transaction = $this->transactionService->createStandaloneTransaction($validatedData);
            }

            return ResponseHelper::success(
                new TransactionResource($transaction),
                'Transaction created successfully',
                201
            );
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to create transaction', 500);
        }
    }

    /**
     * Display the specified transaction.
     */
    public function show(int $id): JsonResponse
    {
        try {
            $transaction = $this->transactionService->getTransactionById($id);

            if (!$transaction) {
                return ResponseHelper::notFound('Transaction not found');
            }

            return ResponseHelper::success(
                new TransactionResource($transaction),
                'Transaction retrieved successfully'
            );
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve transaction', 500);
        }
    }

    /**
     * Update the specified transaction.
     */
    public function update(UpdateTransactionRequest $request, int $id): JsonResponse
    {
        try {
            $transaction = $this->transactionService->getTransactionById($id);

            if (!$transaction) {
                return response()->json([
                    'success' => false,
                    'message' => 'Transaction not found',
                ], Response::HTTP_NOT_FOUND);
            }

            // Update transaction notes if provided
            if ($request->has('notes')) {
                $transaction->update([
                    'notes' => $request->notes,
                    'updated_by' => auth()->id(),
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Transaction updated successfully',
                'data' => new TransactionResource($transaction->fresh()),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update transaction',
                'error' => $e->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Remove the specified transaction (void).
     */
    public function destroy(int $id, Request $request): JsonResponse
    {
        try {
            $transaction = $this->transactionService->getTransactionById($id);

            if (!$transaction) {
                return response()->json([
                    'success' => false,
                    'message' => 'Transaction not found',
                ], Response::HTTP_NOT_FOUND);
            }

            $reason = $request->input('reason', 'Transaction voided by user');
            $voidedTransaction = $this->transactionService->voidTransaction($transaction, $reason);

            return response()->json([
                'success' => true,
                'message' => 'Transaction voided successfully',
                'data' => new TransactionResource($voidedTransaction),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to void transaction',
                'error' => $e->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get transaction statistics.
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $filters = $request->only(['date_from', 'date_to']);
            $statistics = $this->transactionService->getTransactionStatistics($filters);

            return response()->json([
                'success' => true,
                'data' => $statistics,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve statistics',
                'error' => $e->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get due transactions.
     */
    public function due(Request $request): JsonResponse
    {
        try {
            $filters = $request->only(['limit']);
            $dueTransactions = $this->transactionService->getDueTransactions($filters);

            return response()->json([
                'success' => true,
                'data' => TransactionResource::collection($dueTransactions),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve due transactions',
                'error' => $e->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Update transaction status.
     */
    public function updateStatus(int $id): JsonResponse
    {
        try {
            $transaction = $this->transactionService->getTransactionById($id);

            if (!$transaction) {
                return response()->json([
                    'success' => false,
                    'message' => 'Transaction not found',
                ], Response::HTTP_NOT_FOUND);
            }

            $updatedTransaction = $this->transactionService->updateTransactionStatus($transaction);

            return response()->json([
                'success' => true,
                'message' => 'Transaction status updated successfully',
                'data' => new TransactionResource($updatedTransaction),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update transaction status',
                'error' => $e->getMessage(),
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
