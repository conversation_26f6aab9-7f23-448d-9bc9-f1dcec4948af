<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Custom DataTable</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f9fafb;
            color: #374151;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .datatable-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .datatable-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            background: #f9fafb;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .table-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #111827;
        }

        .controls-wrapper {
            display: flex;
            gap: 0.75rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-container {
            position: relative;
        }

        .search-input {
            padding: 0.5rem 0.75rem 0.5rem 2.5rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.875rem;
            width: 250px;
            background: white;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .search-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .search-icon {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6b7280;
            font-size: 0.875rem;
        }

        .filter-select, .per-page-select {
            padding: 0.5rem 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.875rem;
            background: white;
            cursor: pointer;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .filter-select:focus, .per-page-select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .table-container {
            overflow-x: auto;
        }

        .datatable {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.875rem;
        }

        .datatable thead {
            background: #f9fafb;
            border-bottom: 1px solid #e5e7eb;
        }

        .datatable th {
            padding: 0.75rem;
            text-align: left;
            font-weight: 600;
            color: #374151;
            user-select: none;
            cursor: pointer;
            position: relative;
            white-space: nowrap;
        }

        .datatable th:hover {
            background: #f3f4f6;
        }

        .datatable th.sortable::after {
            content: '';
            position: absolute;
            right: 0.5rem;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-bottom: 4px solid #9ca3af;
            opacity: 0.5;
        }

        .datatable th.sort-asc::after {
            border-bottom: 4px solid #374151;
            border-top: none;
            opacity: 1;
        }

        .datatable th.sort-desc::after {
            border-top: 4px solid #374151;
            border-bottom: none;
            opacity: 1;
        }

        .datatable tbody tr {
            border-bottom: 1px solid #f3f4f6;
            transition: background-color 0.15s ease-in-out;
        }

        .datatable tbody tr:hover {
            background: #f9fafb;
        }

        .datatable tbody tr:last-child {
            border-bottom: none;
        }

        .datatable td {
            padding: 0.75rem;
            vertical-align: middle;
            color: #374151;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: capitalize;
        }

        .status-active {
            background: #dcfce7;
            color: #166534;
        }

        .status-inactive {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        .table-footer {
            padding: 1rem 1.5rem;
            border-top: 1px solid #e5e7eb;
            background: #f9fafb;
            display: flex;
            justify-content: between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .table-info {
            font-size: 0.875rem;
            color: #6b7280;
        }

        .pagination {
            display: flex;
            gap: 0.25rem;
            align-items: center;
        }

        .pagination-btn {
            padding: 0.5rem 0.75rem;
            border: 1px solid #d1d5db;
            background: white;
            color: #374151;
            cursor: pointer;
            font-size: 0.875rem;
            transition: all 0.15s ease-in-out;
            min-width: 2.5rem;
            text-align: center;
            border-radius: 6px;
        }

        .pagination-btn:hover:not(:disabled) {
            background: #f3f4f6;
            border-color: #d1d5db;
        }

        .pagination-btn.active {
            background: #3b82f6;
            border-color: #3b82f6;
            color: white;
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            background: #f9fafb;
        }

        .pagination-ellipsis {
            padding: 0.5rem 0.25rem;
            color: #6b7280;
            font-size: 0.875rem;
        }

        .no-results {
            text-align: center;
            padding: 3rem 1.5rem;
            color: #6b7280;
        }

        .no-results-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 2rem;
            color: #6b7280;
        }

        .loading.show {
            display: block;
        }

        .spinner {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid #f3f4f6;
            border-top: 2px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 0.5rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                align-items: stretch;
            }

            .controls-wrapper {
                justify-content: space-between;
            }

            .search-input {
                width: 100%;
                max-width: 250px;
            }

            .table-footer {
                flex-direction: column;
                align-items: center;
                gap: 1rem;
            }

            .datatable th,
            .datatable td {
                padding: 0.5rem;
                font-size: 0.8rem;
            }
        }

        /* Custom scrollbar */
        .table-container::-webkit-scrollbar {
            height: 6px;
        }

        .table-container::-webkit-scrollbar-track {
            background: #f1f5f9;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        .table-container::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="datatable-card">
            <div class="datatable-header">
                <div class="header-content">
                    <h2 class="table-title">Users</h2>
                    <div class="controls-wrapper">
                        <div class="search-container">
                            <span class="search-icon">🔍</span>
                            <input type="text" class="search-input" placeholder="Search users..." id="searchInput">
                        </div>
                        <select class="filter-select" id="roleFilter">
                            <option value="">All Roles</option>
                            <option value="Admin">Admin</option>
                            <option value="Editor">Editor</option>
                            <option value="Viewer">Viewer</option>
                            <option value="Manager">Manager</option>
                        </select>
                        <select class="filter-select" id="statusFilter">
                            <option value="">All Status</option>
                            <option value="Active">Active</option>
                            <option value="Inactive">Inactive</option>
                            <option value="Pending">Pending</option>
                        </select>
                        <select class="per-page-select" id="perPageSelect">
                            <option value="10">10 per page</option>
                            <option value="25">25 per page</option>
                            <option value="50">50 per page</option>
                            <option value="100">100 per page</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                Loading...
            </div>

            <div class="table-container">
                <table class="datatable" id="dataTable">
                    <thead>
                        <tr>
                            <th class="sortable" data-column="id">ID</th>
                            <th class="sortable" data-column="name">Name</th>
                            <th class="sortable" data-column="email">Email</th>
                            <th class="sortable" data-column="role">Role</th>
                            <th class="sortable" data-column="department">Department</th>
                            <th class="sortable" data-column="status">Status</th>
                            <th class="sortable" data-column="lastLogin">Last Login</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                    </tbody>
                </table>
            </div>

            <div class="no-results" id="noResults" style="display: none;">
                <div class="no-results-icon">📂</div>
                <h3>No users found</h3>
                <p>Try adjusting your search or filter criteria</p>
            </div>

            <div class="table-footer">
                <div class="table-info" id="tableInfo"></div>
                <div class="pagination" id="pagination"></div>
            </div>
        </div>
    </div>

    <script>
        class PracticalDataTable {
            constructor() {
                this.data = [];
                this.filteredData = [];
                this.currentPage = 1;
                this.perPage = 10;
                this.sortColumn = '';
                this.sortDirection = 'asc';
                this.searchTerm = '';
                this.roleFilter = '';
                this.statusFilter = '';
                
                this.init();
                this.generateData();
                this.render();
                this.bindEvents();
            }

            init() {
                this.tableBody = document.getElementById('tableBody');
                this.searchInput = document.getElementById('searchInput');
                this.roleFilter = document.getElementById('roleFilter');
                this.statusFilter = document.getElementById('statusFilter');
                this.perPageSelect = document.getElementById('perPageSelect');
                this.tableInfo = document.getElementById('tableInfo');
                this.pagination = document.getElementById('pagination');
                this.noResults = document.getElementById('noResults');
                this.loading = document.getElementById('loading');
                this.tableContainer = document.querySelector('.table-container');
            }

            generateData() {
                const firstNames = ['John', 'Jane', 'Michael', 'Sarah', 'David', 'Lisa', 'Robert', 'Emily', 'Daniel', 'Jessica', 'Christopher', 'Ashley', 'Matthew', 'Amanda', 'Anthony', 'Stephanie', 'Mark', 'Jennifer', 'Steven', 'Elizabeth'];
                const lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez', 'Wilson', 'Anderson', 'Thomas', 'Taylor', 'Moore', 'Jackson', 'Martin'];
                const roles = ['Admin', 'Editor', 'Viewer', 'Manager'];
                const departments = ['Engineering', 'Marketing', 'Sales', 'HR', 'Finance', 'Operations'];
                const statuses = ['Active', 'Inactive', 'Pending'];

                for (let i = 1; i <= 100; i++) {
                    const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
                    const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
                    const name = `${firstName} ${lastName}`;
                    const email = `${firstName.toLowerCase()}.${lastName.toLowerCase()}@company.com`;
                    const role = roles[Math.floor(Math.random() * roles.length)];
                    const department = departments[Math.floor(Math.random() * departments.length)];
                    const status = statuses[Math.floor(Math.random() * statuses.length)];
                    const lastLogin = new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toLocaleDateString();

                    this.data.push({
                        id: i,
                        name,
                        email,
                        role,
                        department,
                        status,
                        lastLogin
                    });
                }

                this.filteredData = [...this.data];
            }

            bindEvents() {
                this.searchInput.addEventListener('input', (e) => {
                    this.searchTerm = e.target.value.toLowerCase();
                    this.debounceFilter();
                });

                this.roleFilter.addEventListener('change', (e) => {
                    this.roleFilter = e.target.value;
                    this.applyFilters();
                });

                this.statusFilter.addEventListener('change', (e) => {
                    this.statusFilter = e.target.value;
                    this.applyFilters();
                });

                this.perPageSelect.addEventListener('change', (e) => {
                    this.perPage = parseInt(e.target.value);
                    this.currentPage = 1;
                    this.render();
                });

                document.querySelectorAll('.sortable').forEach(th => {
                    th.addEventListener('click', () => {
                        const column = th.dataset.column;
                        this.sort(column);
                    });
                });
            }

            debounceFilter() {
                clearTimeout(this.debounceTimer);
                this.debounceTimer = setTimeout(() => {
                    this.applyFilters();
                }, 300);
            }

            applyFilters() {
                this.showLoading();
                
                setTimeout(() => {
                    this.filteredData = this.data.filter(row => {
                        const matchesSearch = !this.searchTerm || 
                            Object.values(row).some(value => 
                                value.toString().toLowerCase().includes(this.searchTerm)
                            );
                        
                        const matchesRole = !this.roleFilter || row.role === this.roleFilter;
                        const matchesStatus = !this.statusFilter || row.status === this.statusFilter;

                        return matchesSearch && matchesRole && matchesStatus;
                    });

                    this.currentPage = 1;
                    this.hideLoading();
                    this.render();
                }, 200);
            }

            sort(column) {
                if (this.sortColumn === column) {
                    this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
                } else {
                    this.sortColumn = column;
                    this.sortDirection = 'asc';
                }

                this.filteredData.sort((a, b) => {
                    let aVal = a[column];
                    let bVal = b[column];

                    if (column === 'id') {
                        aVal = parseInt(aVal);
                        bVal = parseInt(bVal);
                    }

                    if (aVal < bVal) return this.sortDirection === 'asc' ? -1 : 1;
                    if (aVal > bVal) return this.sortDirection === 'asc' ? 1 : -1;
                    return 0;
                });

                this.updateSortHeaders();
                this.render();
            }

            updateSortHeaders() {
                document.querySelectorAll('.sortable').forEach(th => {
                    th.classList.remove('sort-asc', 'sort-desc');
                    if (th.dataset.column === this.sortColumn) {
                        th.classList.add(`sort-${this.sortDirection}`);
                    }
                });
            }

            render() {
                const startIndex = (this.currentPage - 1) * this.perPage;
                const endIndex = startIndex + this.perPage;
                const pageData = this.filteredData.slice(startIndex, endIndex);

                if (this.filteredData.length === 0) {
                    this.noResults.style.display = 'block';
                    this.tableContainer.style.display = 'none';
                } else {
                    this.noResults.style.display = 'none';
                    this.tableContainer.style.display = 'block';
                    this.renderTable(pageData);
                }

                this.renderFooter();
            }

            renderTable(data) {
                this.tableBody.innerHTML = data.map(row => `
                    <tr>
                        <td>${row.id}</td>
                        <td>${row.name}</td>
                        <td>${row.email}</td>
                        <td>${row.role}</td>
                        <td>${row.department}</td>
                        <td><span class="status-badge status-${row.status.toLowerCase()}">${row.status}</span></td>
                        <td>${row.lastLogin}</td>
                        <td>
                            <button onclick="editUser(${row.id})" style="color: #3b82f6; background: none; border: none; cursor: pointer; margin-right: 0.5rem;">Edit</button>
                            <button onclick="deleteUser(${row.id})" style="color: #ef4444; background: none; border: none; cursor: pointer;">Delete</button>
                        </td>
                    </tr>
                `).join('');
            }

            renderFooter() {
                const totalItems = this.filteredData.length;
                const startItem = totalItems === 0 ? 0 : (this.currentPage - 1) * this.perPage + 1;
                const endItem = Math.min(this.currentPage * this.perPage, totalItems);

                this.tableInfo.innerHTML = `Showing ${startItem} to ${endItem} of ${totalItems} entries`;

                this.renderPagination();
            }

            renderPagination() {
                const totalPages = Math.ceil(this.filteredData.length / this.perPage);
                
                if (totalPages <= 1) {
                    this.pagination.innerHTML = '';
                    return;
                }

                let html = '';
                
                // Previous button
                html += `<button class="pagination-btn" ${this.currentPage === 1 ? 'disabled' : ''} 
                         onclick="dataTable.goToPage(${this.currentPage - 1})">Previous</button>`;

                // Page numbers
                const startPage = Math.max(1, this.currentPage - 2);
                const endPage = Math.min(totalPages, this.currentPage + 2);

                if (startPage > 1) {
                    html += `<button class="pagination-btn" onclick="dataTable.goToPage(1)">1</button>`;
                    if (startPage > 2) {
                        html += `<span class="pagination-ellipsis">...</span>`;
                    }
                }

                for (let i = startPage; i <= endPage; i++) {
                    html += `<button class="pagination-btn ${i === this.currentPage ? 'active' : ''}" 
                             onclick="dataTable.goToPage(${i})">${i}</button>`;
                }

                if (endPage < totalPages) {
                    if (endPage < totalPages - 1) {
                        html += `<span class="pagination-ellipsis">...</span>`;
                    }
                    html += `<button class="pagination-btn" onclick="dataTable.goToPage(${totalPages})">${totalPages}</button>`;
                }

                // Next button
                html += `<button class="pagination-btn" ${this.currentPage === totalPages ? 'disabled' : ''} 
                         onclick="dataTable.goToPage(${this.currentPage + 1})">Next</button>`;

                this.pagination.innerHTML = html;
            }

            goToPage(page) {
                this.currentPage = page;
                this.render();
            }

            showLoading() {
                this.loading.classList.add('show');
            }

            hideLoading() {
                this.loading.classList.remove('show');
            }
        }

        // Initialize DataTable
        const dataTable = new PracticalDataTable();

        // Example action functions
        function editUser(id) {
            alert(`Edit user with ID: ${id}`);
        }

        function deleteUser(id) {
            if (confirm(`Delete user with ID: ${id}?`)) {
                alert(`User ${id} deleted`);
            }
        }
    </script>
</body>
</html>