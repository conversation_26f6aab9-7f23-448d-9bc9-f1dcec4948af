<?php

namespace Modules\Reservation\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateWaiterRequestRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'table_id' => 'required|exists:tables,id',
            'branch_id' => 'required|exists:branches,id',
            'waiter_id' => 'nullable|exists:users,id',
            'request_type' => 'nullable|string|in:service,bill,assistance,complaint',
            'notes' => 'nullable|string|max:500',
            'tenant_id' => 'required|integer',
            'status' => 'required|string|in:pending,in_progress,completed,cancelled',
        ];
    }

    /**
     * Get custom error messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'table_id.required' => 'Table is required.',
            'table_id.exists' => 'Selected table does not exist.',
            'branch_id.required' => 'Branch is required.',
            'branch_id.exists' => 'Selected branch does not exist.',
            'waiter_id.exists' => 'Selected waiter does not exist.',
            'request_type.in' => 'Request type must be one of: service, bill, assistance, complaint.',
            'notes.max' => 'Notes cannot exceed 500 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'table_id' => 'table',
            'branch_id' => 'branch',
            'waiter_id' => 'waiter',
            'request_type' => 'request type',
            'notes' => 'notes',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'status' => 'pending', // Default status
            'tenant_id' => auth()->user()->tenant_id,
        ]);
    }
}