{"endpoints": [{"name": "List Menus", "method": "GET", "url": "/api/menus", "query_params": {"page": "integer (optional)", "per_page": "integer (optional)"}, "request_body": null, "expected_response": {"status": 200, "body": {"data": [{"id": 1, "name": "<PERSON><PERSON>", "description": "Lunch specials", "created_at": "2025-07-23T12:00:00Z", "updated_at": "2025-07-23T12:00:00Z"}], "meta": {"current_page": 1, "last_page": 5, "per_page": 10, "total": 50}}}}, {"name": "Show Menu", "method": "GET", "url": "/api/menus/{id}", "request_body": null, "expected_response": {"status": 200, "body": {"id": 1, "name": "<PERSON><PERSON>", "description": "Lunch specials", "created_at": "2025-07-23T12:00:00Z", "updated_at": "2025-07-23T12:00:00Z"}}}, {"name": "Create Menu", "method": "POST", "url": "/api/menus", "request_body": {"name": "string (required)", "description": "string (optional)"}, "expected_response": {"status": 201, "body": {"id": 2, "name": "Dinner Menu", "description": "Evening specials", "created_at": "2025-07-23T12:05:00Z", "updated_at": "2025-07-23T12:05:00Z"}}}, {"name": "Update Menu", "method": "PUT", "url": "/api/menus/{id}", "request_body": {"name": "string (optional)", "description": "string (optional)"}, "expected_response": {"status": 200, "body": {"id": 1, "name": "Updated Menu Name", "description": "Updated description", "created_at": "2025-07-23T12:00:00Z", "updated_at": "2025-07-23T12:10:00Z"}}}, {"name": "Delete Menu", "method": "DELETE", "url": "/api/menus/{id}", "request_body": null, "expected_response": {"status": 204, "body": null}}]}