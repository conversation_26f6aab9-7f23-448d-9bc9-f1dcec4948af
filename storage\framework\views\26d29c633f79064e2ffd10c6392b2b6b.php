

<?php $__env->startSection('title', 'Create Payment'); ?>

<?php $__env->startPush('styles'); ?>
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

<style>
    /* Select2 styling */
    .select2-container--default .select2-selection--single {
        @apply border border-gray-300 rounded-md h-10;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        @apply leading-8 text-sm;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-plus mr-3"></i>
                Create Payment
            </h1>
            <p class="text-sm text-gray-600 mt-1">Process a new payment for a transaction</p>
        </div>
        <div class="mt-4 sm:mt-0">
            <a href="<?php echo e(route('payments.index')); ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Payments
            </a>
        </div>
    </div>

    <!-- Create Payment Form -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Payment Information</h3>
        </div>
        
        <form action="<?php echo e(route('payments.store')); ?>" method="POST" class="p-6">
            <?php echo csrf_field(); ?>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Transaction Selection -->
                <div class="md:col-span-2">
                    <label for="transaction_id" class="block text-sm font-medium text-gray-700 mb-2">
                        Transaction <span class="text-red-500">*</span>
                    </label>
                    <select id="transaction_id" name="transaction_id" required class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Select a transaction</option>
                        <?php if($transaction): ?>
                            <option value="<?php echo e($transaction->id); ?>" selected>
                                <?php echo e($transaction->transaction_number); ?> - $<?php echo e(number_format($transaction->due_amount, 2)); ?> due
                            </option>
                        <?php endif; ?>
                        <?php $__currentLoopData = $dueTransactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $dueTransaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if(!$transaction || $dueTransaction->id !== $transaction->id): ?>
                                <option value="<?php echo e($dueTransaction->id); ?>">
                                    <?php echo e($dueTransaction->transaction_number); ?> - $<?php echo e(number_format($dueTransaction->due_amount, 2)); ?> due
                                </option>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                    <?php $__errorArgs = ['transaction_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Payment Method -->
                <div>
                    <label for="payment_method_id" class="block text-sm font-medium text-gray-700 mb-2">
                        Payment Method <span class="text-red-500">*</span>
                    </label>
                    <select id="payment_method_id" name="payment_method_id" required class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Select payment method</option>
                        <?php $__currentLoopData = $paymentMethods; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $method): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($method->id); ?>" <?php echo e(old('payment_method_id') == $method->id ? 'selected' : ''); ?>>
                                <?php echo e($method->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                    <?php $__errorArgs = ['payment_method_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Payment Amount -->
                <div>
                    <label for="amount" class="block text-sm font-medium text-gray-700 mb-2">
                        Payment Amount <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span class="text-gray-500 sm:text-sm">$</span>
                        </div>
                        <input type="number" id="amount" name="amount" step="0.01" min="0" required 
                               value="<?php echo e(old('amount', $transaction ? $transaction->due_amount : '')); ?>"
                               class="w-full pl-7 border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                               placeholder="0.00">
                    </div>
                    <?php $__errorArgs = ['amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    <p class="mt-1 text-xs text-gray-500">Maximum amount will be set based on transaction due amount</p>
                </div>

                <!-- Reference Number -->
                <div>
                    <label for="reference_number" class="block text-sm font-medium text-gray-700 mb-2">
                        Reference Number
                    </label>
                    <input type="text" id="reference_number" name="reference_number" 
                           value="<?php echo e(old('reference_number')); ?>"
                           class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                           placeholder="Enter reference number">
                    <?php $__errorArgs = ['reference_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Change Amount -->
                <div>
                    <label for="change_amount" class="block text-sm font-medium text-gray-700 mb-2">
                        Change Amount
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span class="text-gray-500 sm:text-sm">$</span>
                        </div>
                        <input type="number" id="change_amount" name="change_amount" step="0.01" min="0" 
                               value="<?php echo e(old('change_amount', '0.00')); ?>"
                               class="w-full pl-7 border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                               placeholder="0.00">
                    </div>
                    <?php $__errorArgs = ['change_amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Notes -->
                <div class="md:col-span-2">
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">
                        Notes
                    </label>
                    <textarea id="notes" name="notes" rows="3" 
                              class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                              placeholder="Enter any additional notes..."><?php echo e(old('notes')); ?></textarea>
                    <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>

            <!-- Transaction Details (if selected) -->
            <div id="transaction-details" class="mt-6 hidden">
                <div class="border-t border-gray-200 pt-6">
                    <h4 class="text-md font-medium text-gray-900 mb-4">Transaction Details</h4>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div>
                                <span class="font-medium text-gray-700">Transaction #:</span>
                                <span id="detail-transaction-number" class="text-gray-900"></span>
                            </div>
                            <div>
                                <span class="font-medium text-gray-700">Total Amount:</span>
                                <span id="detail-total-amount" class="text-gray-900"></span>
                            </div>
                            <div>
                                <span class="font-medium text-gray-700">Paid Amount:</span>
                                <span id="detail-paid-amount" class="text-gray-900"></span>
                            </div>
                            <div>
                                <span class="font-medium text-gray-700">Due Amount:</span>
                                <span id="detail-due-amount" class="text-gray-900 font-semibold"></span>
                            </div>
                            <div>
                                <span class="font-medium text-gray-700">Status:</span>
                                <span id="detail-status" class="text-gray-900"></span>
                            </div>
                            <div>
                                <span class="font-medium text-gray-700">Order #:</span>
                                <span id="detail-order-number" class="text-gray-900"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="mt-6 flex justify-end space-x-3">
                <a href="<?php echo e(route('payments.index')); ?>" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Cancel
                </a>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="fas fa-credit-card mr-2"></i>
                    Process Payment
                </button>
            </div>
        </form>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize Select2
    $('#transaction_id').select2({
        placeholder: 'Select a transaction',
        allowClear: true
    });

    $('#payment_method_id').select2({
        placeholder: 'Select payment method',
        allowClear: true
    });

    // Transaction selection change handler
    $('#transaction_id').on('change', function() {
        var transactionId = $(this).val();
        
        if (transactionId) {
            // Load transaction details
            $.get('<?php echo e(route("transactions.show", ":id")); ?>'.replace(':id', transactionId))
                .done(function(response) {
                    // Populate transaction details
                    $('#detail-transaction-number').text(response.transaction_number);
                    $('#detail-total-amount').text('$' + parseFloat(response.total_amount).toFixed(2));
                    $('#detail-paid-amount').text('$' + parseFloat(response.paid_amount).toFixed(2));
                    $('#detail-due-amount').text('$' + parseFloat(response.due_amount).toFixed(2));
                    $('#detail-status').text(response.status);
                    $('#detail-order-number').text(response.order ? response.order.order_number : 'N/A');
                    
                    // Set payment amount to due amount
                    $('#amount').val(parseFloat(response.due_amount).toFixed(2));
                    $('#amount').attr('max', response.due_amount);
                    
                    // Show transaction details
                    $('#transaction-details').removeClass('hidden');
                })
                .fail(function() {
                    Swal.fire('Error', 'Failed to load transaction details', 'error');
                });
        } else {
            // Hide transaction details
            $('#transaction-details').addClass('hidden');
            $('#amount').val('').removeAttr('max');
        }
    });

    // Amount validation
    $('#amount').on('input', function() {
        var amount = parseFloat($(this).val());
        var maxAmount = parseFloat($(this).attr('max'));
        
        if (maxAmount && amount > maxAmount) {
            $(this).val(maxAmount.toFixed(2));
            Swal.fire('Warning', 'Payment amount cannot exceed the due amount', 'warning');
        }
    });

    // Form validation
    $('form').on('submit', function(e) {
        var transactionId = $('#transaction_id').val();
        var paymentMethodId = $('#payment_method_id').val();
        var amount = parseFloat($('#amount').val());

        if (!transactionId) {
            e.preventDefault();
            Swal.fire('Error', 'Please select a transaction', 'error');
            return;
        }

        if (!paymentMethodId) {
            e.preventDefault();
            Swal.fire('Error', 'Please select a payment method', 'error');
            return;
        }

        if (!amount || amount <= 0) {
            e.preventDefault();
            Swal.fire('Error', 'Please enter a valid payment amount', 'error');
            return;
        }

        // Show loading state
        $(this).find('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Processing...');
    });

    // Auto-load transaction details if transaction is pre-selected
    <?php if($transaction): ?>
        $('#transaction_id').trigger('change');
    <?php endif; ?>
});
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Transaction\Providers/../resources/views/payments/create.blade.php ENDPATH**/ ?>