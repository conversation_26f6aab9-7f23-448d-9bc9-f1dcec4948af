@extends('layouts.master')

@section('title', 'KOT Details - ' . $kotOrder->kot_number)

@push('styles')
<style>
    .kot-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 0.5rem;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .status-badge {
        font-size: 0.875rem;
        font-weight: 600;
        padding: 0.5rem 1rem;
        border-radius: 9999px;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }
    
    .priority-badge {
        font-size: 0.75rem;
        font-weight: 600;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }
    
    .info-card {
        background: white;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    .item-card {
        background: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 0.375rem;
        padding: 1rem;
        margin-bottom: 0.75rem;
    }
    
    .time-indicator {
        font-size: 1.25rem;
        font-weight: 700;
    }
    
    .overdue {
        color: #dc2626;
    }
    
    .warning {
        color: #d97706;
    }
    
    .normal {
        color: #059669;
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- KOT Header -->
    <div class="kot-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="h2 mb-2">{{ $kotOrder->kot_number }}</h1>
                <p class="mb-0 opacity-75">Order #{{ $kotOrder->order?->order_number ?? 'N/A' }}</p>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="d-flex flex-column align-items-md-end gap-2">
                    @php
                        $statusClasses = [
                            'pending' => 'bg-warning text-dark',
                            'preparing' => 'bg-info text-white',
                            'ready' => 'bg-success text-white',
                            'completed' => 'bg-primary text-white',
                            'cancelled' => 'bg-danger text-white',
                        ];
                        $priorityClasses = [
                            'low' => 'bg-secondary text-white',
                            'normal' => 'bg-primary text-white',
                            'high' => 'bg-warning text-dark',
                            'urgent' => 'bg-danger text-white',
                        ];
                    @endphp
                    <span class="status-badge {{ $statusClasses[$kotOrder->status] ?? 'bg-secondary text-white' }}">
                        {{ ucfirst($kotOrder->status) }}
                    </span>
                    <span class="priority-badge {{ $priorityClasses[$kotOrder->priority] ?? 'bg-secondary text-white' }}">
                        {{ ucfirst($kotOrder->priority) }} Priority
                    </span>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Left Column -->
        <div class="col-lg-8">
            <!-- KOT Items -->
            <div class="info-card">
                <h5 class="mb-4">
                    <i class="fas fa-utensils text-primary me-2"></i>
                    Order Items ({{ $kotOrder->kotOrderItems->count() }})
                </h5>
                
                @forelse($kotOrder->kotOrderItems as $item)
                    <div class="item-card">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h6 class="mb-1">{{ $item->menuItem?->name ?? 'Unknown Item' }}</h6>
                                <p class="text-muted mb-0 small">
                                    Quantity: {{ $item->quantity }}
                                    @if($item->special_instructions)
                                        <br><strong>Instructions:</strong> {{ $item->special_instructions }}
                                    @endif
                                </p>
                            </div>
                            <div class="col-md-3">
                                @php
                                    $itemStatusClasses = [
                                        'pending' => 'bg-warning text-dark',
                                        'preparing' => 'bg-info text-white',
                                        'ready' => 'bg-success text-white',
                                        'completed' => 'bg-primary text-white',
                                        'cancelled' => 'bg-danger text-white',
                                    ];
                                @endphp
                                <span class="badge {{ $itemStatusClasses[$item->status] ?? 'bg-secondary text-white' }}">
                                    {{ ucfirst($item->status) }}
                                </span>
                            </div>
                            <div class="col-md-3 text-end">
                                @if($item->prep_time_minutes)
                                    <small class="text-muted">Prep: {{ $item->prep_time_minutes }}m</small>
                                @endif
                                @if($item->started_at)
                                    <br><small class="text-muted">Started: {{ $item->started_at->format('H:i') }}</small>
                                @endif
                                @if($item->completed_at)
                                    <br><small class="text-muted">Completed: {{ $item->completed_at->format('H:i') }}</small>
                                @endif
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="text-center py-4">
                        <i class="fas fa-utensils text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">No items found</p>
                    </div>
                @endforelse
            </div>

            <!-- Special Instructions -->
            @if($kotOrder->special_instructions)
                <div class="info-card">
                    <h5 class="mb-3">
                        <i class="fas fa-sticky-note text-warning me-2"></i>
                        Special Instructions
                    </h5>
                    <div class="alert alert-warning mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        {{ $kotOrder->special_instructions }}
                    </div>
                </div>
            @endif
        </div>

        <!-- Right Column -->
        <div class="col-lg-4">
            <!-- Time Information -->
            <div class="info-card">
                <h5 class="mb-4">
                    <i class="fas fa-clock text-info me-2"></i>
                    Time Information
                </h5>
                
                <div class="row g-3">
                    <div class="col-12">
                        <label class="form-label small text-muted">Created</label>
                        <div class="fw-bold">{{ $kotOrder->created_at->format('M d, Y H:i') }}</div>
                    </div>
                    
                    @if($kotOrder->started_at)
                        <div class="col-12">
                            <label class="form-label small text-muted">Started</label>
                            <div class="fw-bold">{{ $kotOrder->started_at->format('M d, Y H:i') }}</div>
                        </div>
                    @endif
                    
                    @if($kotOrder->completed_at)
                        <div class="col-12">
                            <label class="form-label small text-muted">Completed</label>
                            <div class="fw-bold">{{ $kotOrder->completed_at->format('M d, Y H:i') }}</div>
                        </div>
                    @endif
                    
                    <div class="col-12">
                        <label class="form-label small text-muted">Elapsed Time</label>
                        @php
                            $elapsedMinutes = $kotOrder->getElapsedTimeMinutes();
                            $remainingMinutes = $kotOrder->getRemainingTimeMinutes();
                            $isOverdue = $kotOrder->isOverdue();
                            
                            $timeClass = 'normal';
                            if ($isOverdue) {
                                $timeClass = 'overdue';
                            } elseif ($remainingMinutes !== null && $remainingMinutes <= 5) {
                                $timeClass = 'warning';
                            }
                        @endphp
                        <div class="time-indicator {{ $timeClass }}">
                            {{ $elapsedMinutes }} minutes
                            @if($isOverdue)
                                <i class="fas fa-exclamation-triangle ms-1"></i>
                            @endif
                        </div>
                    </div>
                    
                    @if($kotOrder->estimated_prep_time_minutes && $kotOrder->status !== 'completed')
                        <div class="col-12">
                            <label class="form-label small text-muted">Remaining Time</label>
                            <div class="time-indicator {{ $timeClass }}">
                                @if($remainingMinutes !== null)
                                    {{ max(0, $remainingMinutes) }} minutes
                                @else
                                    N/A
                                @endif
                            </div>
                        </div>
                    @endif
                    
                    @if($kotOrder->actual_prep_time_minutes)
                        <div class="col-12">
                            <label class="form-label small text-muted">Actual Prep Time</label>
                            <div class="fw-bold text-success">{{ $kotOrder->actual_prep_time_minutes }} minutes</div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Kitchen Information -->
            <div class="info-card">
                <h5 class="mb-4">
                    <i class="fas fa-kitchen-set text-success me-2"></i>
                    Kitchen Information
                </h5>
                
                <div class="row g-3">
                    <div class="col-12">
                        <label class="form-label small text-muted">Kitchen</label>
                        <div class="fw-bold">{{ $kotOrder->kitchen?->name ?? 'N/A' }}</div>
                        @if($kotOrder->kitchen?->station_type)
                            <small class="text-muted">{{ ucfirst($kotOrder->kitchen->station_type) }} Station</small>
                        @endif
                    </div>
                    
                    @if($kotOrder->assignedTo)
                        <div class="col-12">
                            <label class="form-label small text-muted">Assigned To</label>
                            <div class="fw-bold">{{ $kotOrder->assignedTo->name }}</div>
                        </div>
                    @endif
                    
                    @if($kotOrder->creator)
                        <div class="col-12">
                            <label class="form-label small text-muted">Created By</label>
                            <div class="fw-bold">{{ $kotOrder->creator->name }}</div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Customer Information -->
            @if($kotOrder->order?->customer)
                <div class="info-card">
                    <h5 class="mb-4">
                        <i class="fas fa-user text-primary me-2"></i>
                        Customer Information
                    </h5>
                    
                    <div class="row g-3">
                        <div class="col-12">
                            <label class="form-label small text-muted">Name</label>
                            <div class="fw-bold">{{ $kotOrder->order->customer->name }}</div>
                        </div>
                        
                        @if($kotOrder->order->customer->phone)
                            <div class="col-12">
                                <label class="form-label small text-muted">Phone</label>
                                <div class="fw-bold">{{ $kotOrder->order->customer->phone }}</div>
                            </div>
                        @endif
                        
                        @if($kotOrder->order->table_number)
                            <div class="col-12">
                                <label class="form-label small text-muted">Table</label>
                                <div class="fw-bold">Table {{ $kotOrder->order->table_number }}</div>
                            </div>
                        @endif
                    </div>
                </div>
            @endif

            <!-- Actions -->
            <div class="info-card">
                <h5 class="mb-4">
                    <i class="fas fa-cogs text-secondary me-2"></i>
                    Actions
                </h5>
                
                <div class="d-grid gap-2">
                    @include('kitchen::partials.kot-actions', ['kotOrder' => $kotOrder])
                    
                    <a href="{{ route('kitchen.kot-orders.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to KOT List
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Auto-refresh every 30 seconds if KOT is not completed
    @if($kotOrder->status !== 'completed' && $kotOrder->status !== 'cancelled')
        setInterval(function() {
            location.reload();
        }, 30000);
    @endif
});
</script>
@endpush