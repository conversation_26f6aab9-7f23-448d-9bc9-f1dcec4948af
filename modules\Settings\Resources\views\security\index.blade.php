@extends('layouts.app')

@section('title', 'إعدادات الأمان')

@section('content')
<div class="container-fluid" dir="rtl">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('settings.index') }}">الإعدادات</a></li>
                        <li class="breadcrumb-item active">الأمان</li>
                    </ol>
                </div>
                <h4 class="page-title">إعدادات الأمان</h4>
            </div>
        </div>
    </div>

    <form id="security-settings-form">
        <div class="row">
            <!-- Password Policy -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-body">
                        <h4 class="header-title mb-3">سياسة كلمات المرور</h4>
                        
                        <div class="mb-3">
                            <label for="password_min_length" class="form-label">الحد الأدنى لطول كلمة المرور</label>
                            <input type="number" class="form-control" id="password_min_length" name="password_min_length" 
                                   value="{{ $securitySetting->password_min_length }}" min="6" max="50">
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="password_require_uppercase" 
                                       name="password_require_uppercase" value="1" 
                                       {{ $securitySetting->password_require_uppercase ? 'checked' : '' }}>
                                <label class="form-check-label" for="password_require_uppercase">
                                    يجب أن تحتوي على أحرف كبيرة
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="password_require_lowercase" 
                                       name="password_require_lowercase" value="1" 
                                       {{ $securitySetting->password_require_lowercase ? 'checked' : '' }}>
                                <label class="form-check-label" for="password_require_lowercase">
                                    يجب أن تحتوي على أحرف صغيرة
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="password_require_numbers" 
                                       name="password_require_numbers" value="1" 
                                       {{ $securitySetting->password_require_numbers ? 'checked' : '' }}>
                                <label class="form-check-label" for="password_require_numbers">
                                    يجب أن تحتوي على أرقام
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="password_require_symbols" 
                                       name="password_require_symbols" value="1" 
                                       {{ $securitySetting->password_require_symbols ? 'checked' : '' }}>
                                <label class="form-check-label" for="password_require_symbols">
                                    يجب أن تحتوي على رموز خاصة
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="password_expiry_days" class="form-label">انتهاء صلاحية كلمة المرور (أيام)</label>
                            <input type="number" class="form-control" id="password_expiry_days" name="password_expiry_days" 
                                   value="{{ $securitySetting->password_expiry_days }}" min="0" max="365">
                            <small class="form-text text-muted">0 = لا تنتهي الصلاحية</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Login Security -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-body">
                        <h4 class="header-title mb-3">أمان تسجيل الدخول</h4>
                        
                        <div class="mb-3">
                            <label for="max_login_attempts" class="form-label">الحد الأقصى لمحاولات تسجيل الدخول</label>
                            <input type="number" class="form-control" id="max_login_attempts" name="max_login_attempts" 
                                   value="{{ $securitySetting->max_login_attempts }}" min="1" max="20">
                        </div>

                        <div class="mb-3">
                            <label for="lockout_duration" class="form-label">مدة الحظر (دقائق)</label>
                            <input type="number" class="form-control" id="lockout_duration" name="lockout_duration" 
                                   value="{{ $securitySetting->lockout_duration }}" min="1" max="1440">
                        </div>

                        <div class="mb-3">
                            <label for="session_timeout" class="form-label">انتهاء مهلة الجلسة (دقائق)</label>
                            <input type="number" class="form-control" id="session_timeout" name="session_timeout" 
                                   value="{{ $securitySetting->session_timeout }}" min="5" max="480">
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="two_factor_enabled" 
                                       name="two_factor_enabled" value="1" 
                                       {{ $securitySetting->two_factor_enabled ? 'checked' : '' }}>
                                <label class="form-check-label" for="two_factor_enabled">
                                    تفعيل المصادقة الثنائية
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- IP Whitelist -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-body">
                        <h4 class="header-title mb-3">القائمة البيضاء لعناوين IP</h4>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="ip_whitelist_enabled" 
                                       name="ip_whitelist_enabled" value="1" 
                                       {{ $securitySetting->ip_whitelist_enabled ? 'checked' : '' }}>
                                <label class="form-check-label" for="ip_whitelist_enabled">
                                    تفعيل القائمة البيضاء لعناوين IP
                                </label>
                            </div>
                        </div>

                        <div id="ip-whitelist-section" style="{{ $securitySetting->ip_whitelist_enabled ? '' : 'display: none;' }}">
                            <div class="mb-3">
                                <label class="form-label">عناوين IP المسموحة</label>
                                <div id="ip-whitelist-container">
                                    @if($securitySetting->ip_whitelist)
                                        @foreach($securitySetting->ip_whitelist as $ip)
                                            <div class="input-group mb-2 ip-entry">
                                                <input type="text" class="form-control" value="{{ $ip['ip'] }}" readonly>
                                                <span class="input-group-text">{{ $ip['description'] ?? '' }}</span>
                                                <button class="btn btn-outline-danger remove-ip" type="button" data-ip="{{ $ip['ip'] }}">
                                                    <i class="mdi mdi-delete"></i>
                                                </button>
                                            </div>
                                        @endforeach
                                    @endif
                                </div>
                                
                                <div class="input-group">
                                    <input type="text" class="form-control" id="new-ip" placeholder="***********">
                                    <input type="text" class="form-control" id="new-ip-description" placeholder="وصف (اختياري)">
                                    <button class="btn btn-outline-primary" type="button" id="add-ip">
                                        <i class="mdi mdi-plus"></i> إضافة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Audit Log -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-body">
                        <h4 class="header-title mb-3">سجل المراجعة</h4>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="audit_log_enabled" 
                                       name="audit_log_enabled" value="1" 
                                       {{ $securitySetting->audit_log_enabled ? 'checked' : '' }}>
                                <label class="form-check-label" for="audit_log_enabled">
                                    تفعيل سجل المراجعة
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="audit_log_retention_days" class="form-label">مدة الاحتفاظ بالسجل (أيام)</label>
                            <input type="number" class="form-control" id="audit_log_retention_days" name="audit_log_retention_days" 
                                   value="{{ $securitySetting->audit_log_retention_days }}" min="1" max="3650">
                        </div>

                        <div class="mt-3">
                            <button type="button" class="btn btn-info btn-sm" id="view-audit-log">
                                <i class="mdi mdi-eye me-1"></i> عرض سجل المراجعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="text-end">
                            <button type="button" class="btn btn-secondary me-2" id="reset-form">
                                <i class="mdi mdi-refresh me-1"></i> إعادة تعيين
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="mdi mdi-content-save me-1"></i> حفظ الإعدادات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Toggle IP whitelist section
    $('#ip_whitelist_enabled').change(function() {
        if ($(this).is(':checked')) {
            $('#ip-whitelist-section').show();
        } else {
            $('#ip-whitelist-section').hide();
        }
    });

    // Add IP to whitelist
    $('#add-ip').click(function() {
        const ip = $('#new-ip').val().trim();
        const description = $('#new-ip-description').val().trim();
        
        if (!ip) {
            alert('يرجى إدخال عنوان IP');
            return;
        }

        // Validate IP format
        const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        if (!ipRegex.test(ip)) {
            alert('عنوان IP غير صحيح');
            return;
        }

        $.ajax({
            url: '/api/settings/security/ip-whitelist/add',
            method: 'POST',
            headers: {
                'Authorization': 'Bearer ' + localStorage.getItem('auth_token'),
                'Content-Type': 'application/json'
            },
            data: JSON.stringify({
                ip_address: ip,
                description: description
            }),
            success: function(response) {
                if (response.success) {
                    const newEntry = `
                        <div class="input-group mb-2 ip-entry">
                            <input type="text" class="form-control" value="${ip}" readonly>
                            <span class="input-group-text">${description}</span>
                            <button class="btn btn-outline-danger remove-ip" type="button" data-ip="${ip}">
                                <i class="mdi mdi-delete"></i>
                            </button>
                        </div>
                    `;
                    $('#ip-whitelist-container').append(newEntry);
                    $('#new-ip').val('');
                    $('#new-ip-description').val('');
                } else {
                    alert(response.message || 'فشل في إضافة عنوان IP');
                }
            },
            error: function() {
                alert('حدث خطأ أثناء إضافة عنوان IP');
            }
        });
    });

    // Remove IP from whitelist
    $(document).on('click', '.remove-ip', function() {
        const ip = $(this).data('ip');
        const entry = $(this).closest('.ip-entry');
        
        if (confirm('هل أنت متأكد من حذف هذا العنوان؟')) {
            $.ajax({
                url: '/api/settings/security/ip-whitelist/remove',
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + localStorage.getItem('auth_token'),
                    'Content-Type': 'application/json'
                },
                data: JSON.stringify({
                    ip_address: ip
                }),
                success: function(response) {
                    if (response.success) {
                        entry.remove();
                    } else {
                        alert(response.message || 'فشل في حذف عنوان IP');
                    }
                },
                error: function() {
                    alert('حدث خطأ أثناء حذف عنوان IP');
                }
            });
        }
    });

    // Save security settings
    $('#security-settings-form').submit(function(e) {
        e.preventDefault();
        
        const formData = {
            password_min_length: parseInt($('#password_min_length').val()),
            password_require_uppercase: $('#password_require_uppercase').is(':checked'),
            password_require_lowercase: $('#password_require_lowercase').is(':checked'),
            password_require_numbers: $('#password_require_numbers').is(':checked'),
            password_require_symbols: $('#password_require_symbols').is(':checked'),
            password_expiry_days: parseInt($('#password_expiry_days').val()) || null,
            max_login_attempts: parseInt($('#max_login_attempts').val()),
            lockout_duration: parseInt($('#lockout_duration').val()),
            session_timeout: parseInt($('#session_timeout').val()),
            two_factor_enabled: $('#two_factor_enabled').is(':checked'),
            ip_whitelist_enabled: $('#ip_whitelist_enabled').is(':checked'),
            audit_log_enabled: $('#audit_log_enabled').is(':checked'),
            audit_log_retention_days: parseInt($('#audit_log_retention_days').val())
        };

        $.ajax({
            url: '/api/settings/security',
            method: 'PUT',
            headers: {
                'Authorization': 'Bearer ' + localStorage.getItem('auth_token'),
                'Content-Type': 'application/json'
            },
            data: JSON.stringify(formData),
            success: function(response) {
                if (response.success) {
                    alert('تم حفظ إعدادات الأمان بنجاح');
                } else {
                    alert(response.message || 'فشل في حفظ الإعدادات');
                }
            },
            error: function() {
                alert('حدث خطأ أثناء حفظ الإعدادات');
            }
        });
    });

    // Reset form
    $('#reset-form').click(function() {
        if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟')) {
            location.reload();
        }
    });
});
</script>
@endsection
