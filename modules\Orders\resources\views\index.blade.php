@extends('layouts.master')

@section('title', 'Orders Management')

@push('styles')
<meta name="csrf-token" content="{{ csrf_token() }}">
<!-- DataTables CSS for Tailwind -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.tailwindcss.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.tailwindcss.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.tailwindcss.min.css">
<!-- SweetAlert2 -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
@endpush



@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- <PERSON>er -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Orders Management</h1>
                    <p class="mt-2 text-sm text-gray-600">Manage and track all restaurant orders</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('orders.create') }}" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-lg font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                        <i class="mdi mdi-plus mr-2"></i> New Order
                    </a>
                    <a href="{{ route('pos.create') }}" class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-lg font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                        <i class="mdi mdi-cash-register mr-2"></i> POS System
                    </a>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl shadow-lg overflow-hidden">
                <div class="p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-blue-100 text-sm font-medium">Today's Orders</p>
                            <p class="text-white text-3xl font-bold" id="total-orders-today">0</p>
                        </div>
                        <div class="bg-blue-400 bg-opacity-30 rounded-full p-3">
                            <i class="fas fa-shopping-cart text-white text-2xl"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-gradient-to-r from-red-500 to-red-600 rounded-xl shadow-lg overflow-hidden">
                <div class="p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-red-100 text-sm font-medium">Pending Orders</p>
                            <p class="text-white text-3xl font-bold" id="pending-orders">0</p>
                        </div>
                        <div class="bg-red-400 bg-opacity-30 rounded-full p-3">
                            <i class="fas fa-clock text-white text-2xl"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-xl shadow-lg overflow-hidden">
                <div class="p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-green-100 text-sm font-medium">Completed Orders</p>
                            <p class="text-white text-3xl font-bold" id="completed-orders">0</p>
                        </div>
                        <div class="bg-green-400 bg-opacity-30 rounded-full p-3">
                            <i class="fas fa-check-circle text-white text-2xl"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-xl shadow-lg overflow-hidden">
                <div class="p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-yellow-100 text-sm font-medium">Today's Sales</p>
                            <p class="text-white text-3xl font-bold" id="total-sales-today">$0</p>
                        </div>
                        <div class="bg-yellow-400 bg-opacity-30 rounded-full p-3">
                            <i class="fas fa-dollar-sign text-white text-2xl"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Filters</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label for="status-filter" class="block text-sm font-medium text-gray-700 mb-2">Order Status:</label>
                        <select id="status-filter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">All Statuses</option>
                            <option value="pending">Pending</option>
                            <option value="confirmed">Confirmed</option>
                            <option value="preparing">Preparing</option>
                            <option value="ready">Ready</option>
                            <option value="served">Served</option>
                            <option value="completed">Completed</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>
                    <div>
                        <label for="type-filter" class="block text-sm font-medium text-gray-700 mb-2">Order Type:</label>
                        <select id="type-filter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">All Types</option>
                            <option value="dine_in">Dine In</option>
                            <option value="takeaway">Takeaway</option>
                            <option value="delivery">Delivery</option>
                            <option value="online">Online</option>
                        </select>
                    </div>
                    <div>
                        <label for="date-filter" class="block text-sm font-medium text-gray-700 mb-2">Date:</label>
                        <input type="date" id="date-filter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div class="flex items-end space-x-2">
                        <button id="clear-filters" class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition duration-150 ease-in-out">
                            Clear
                        </button>
                        <button id="refresh-orders" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-150 ease-in-out">
                            Refresh
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- View Toggle -->
        <div class="mb-6">
            <div class="inline-flex rounded-lg border border-gray-200 bg-white p-1">
                <button type="button" class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-150 ease-in-out" id="table-view-btn">
                    <i class="mdi mdi-table mr-2"></i> Table View
                </button>
                <button type="button" class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-150 ease-in-out" id="cards-view-btn">
                    <i class="mdi mdi-view-grid mr-2"></i> Cards View
                </button>
            </div>
        </div>

        <!-- Orders Table View -->
        <div id="table-view">
            <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Orders Table</h3>
                </div>
                <div class="p-6">
                    <div class="overflow-x-auto">
                        <table id="orders-table" class="min-w-full divide-y divide-gray-200" data-page-length='50'>
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order Number</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Table</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Orders Cards View -->
        <div id="cards-view" style="display: none;">
            <div id="orders-cards-container">
                <!-- Cards will be loaded here -->
            </div>
            <!-- Pagination for cards -->
            <div class="flex justify-center mt-6">
                <nav aria-label="Orders pagination">
                    <ul class="flex space-x-1" id="cards-pagination">
                        <!-- Pagination will be loaded here -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.tailwindcss.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.tailwindcss.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.tailwindcss.min.js"></script>
<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
$(document).ready(function() {
    // Add CSRF token to all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // View toggle functionality
    let currentView = 'table';
    let currentPage = 1;
    let table;

    // Initialize DataTable
    function initializeDataTable() {
        table = $('#orders-table').DataTable({
            processing: true,
            serverSide: true,
            responsive: true,
            ajax: {
                url: '{{ route("orders.data") }}',
                data: function(d) {
                    d.status = $('#status-filter').val();
                    d.order_type = $('#type-filter').val();
                    d.date = $('#date-filter').val();
                }
            },
            columns: [
                { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
                { data: 'order_number', name: 'order_number' },
                { data: 'customer_name', name: 'customer_name', orderable: false },
                { data: 'table_name', name: 'table_name', orderable: false },
                { data: 'type_badge', name: 'order_type' },
                { data: 'status_badge', name: 'status' },
                { data: 'formatted_total', name: 'total_amount' },
                { data: 'formatted_date', name: 'created_at' },
                { data: 'action', name: 'action', orderable: false, searchable: false }
            ],
            dom: '<"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4"<"flex items-center space-x-2"B><"flex items-center"f>>rtip',
            buttons: {
                dom: {
                    button: {
                        className: 'inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                    }
                },
                buttons: [
                    {
                        extend: 'copy',
                        text: '<i class="fas fa-copy mr-1"></i> Copy'
                    },
                    {
                        extend: 'csv',
                        text: '<i class="fas fa-file-csv mr-1"></i> CSV'
                    },
                    {
                        extend: 'excel',
                        text: '<i class="fas fa-file-excel mr-1"></i> Excel'
                    },
                    {
                        extend: 'pdf',
                        text: '<i class="fas fa-file-pdf mr-1"></i> PDF'
                    },
                    {
                        extend: 'print',
                        text: '<i class="fas fa-print mr-1"></i> Print'
                    }
                ]
            },
            order: [[7, 'desc']],
            language: {
                processing: '<div class="flex items-center justify-center"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div></div>',
                emptyTable: '<div class="text-center py-4 text-gray-500">No orders found</div>',
                zeroRecords: '<div class="text-center py-4 text-gray-500">No matching orders found</div>'
            }
        });
    }

    // View toggle handlers
    $('#table-view-btn').on('click', function() {
        if (currentView !== 'table') {
            currentView = 'table';
            $(this).removeClass('text-gray-700 bg-white hover:bg-gray-50').addClass('text-white bg-blue-600 hover:bg-blue-700');
            $('#cards-view-btn').removeClass('text-white bg-blue-600 hover:bg-blue-700').addClass('text-gray-700 bg-white hover:bg-gray-50');
            $('#table-view').show();
            $('#cards-view').hide();

            if (!table) {
                initializeDataTable();
            } else {
                table.draw();
            }
        }
    });

    $('#cards-view-btn').on('click', function() {
        if (currentView !== 'cards') {
            currentView = 'cards';
            $(this).removeClass('text-gray-700 bg-white hover:bg-gray-50').addClass('text-white bg-blue-600 hover:bg-blue-700');
            $('#table-view-btn').removeClass('text-white bg-blue-600 hover:bg-blue-700').addClass('text-gray-700 bg-white hover:bg-gray-50');
            $('#table-view').hide();
            $('#cards-view').show();
            loadCardsData();
        }
    });

    // Load cards data
    function loadCardsData(page = 1) {
        currentPage = page;
        $('#orders-cards-container').html('<div class="flex items-center justify-center py-12"><div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div><span class="ml-3 text-gray-600">Loading...</span></div>');

        $.ajax({
            url: '{{ route("orders.cards") }}',
            method: 'GET',
            data: {
                page: page,
                per_page: 12,
                status: $('#status-filter').val(),
                order_type: $('#type-filter').val(),
                date: $('#date-filter').val()
            },
            success: function(response) {
                if (response.success && response.data) {
                    renderCards(response.data);
                    renderPagination(response.data);
                } else {
                    $('#orders-cards-container').html('<div class="text-center py-12"><i class="mdi mdi-cart-off text-6xl text-gray-400 mb-4"></i><h5 class="text-xl text-gray-600">No orders found</h5></div>');
                }
            },
            error: function(xhr) {
                console.error('Error loading cards data:', xhr);
                $('#orders-cards-container').html('<div class="text-center py-12"><i class="mdi mdi-alert text-6xl text-red-400 mb-4"></i><h5 class="text-xl text-red-600">Error loading data</h5></div>');
            }
        });
    }

    // Render cards
    function renderCards(data) {
        let cardsHtml = '';

        if (data.data && data.data.length > 0) {
            cardsHtml = '<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">';

            data.data.forEach(function(order) {
                cardsHtml += generateOrderCard(order);
            });

            cardsHtml += '</div>';
        } else {
            cardsHtml = '<div class="text-center py-12"><i class="mdi mdi-cart-off text-6xl text-gray-400 mb-4"></i><h5 class="text-xl text-gray-600">No orders found</h5></div>';
        }

        $('#orders-cards-container').html(cardsHtml);
    }

    // Generate individual order card
    function generateOrderCard(order) {
        const statusTexts = {
            'pending': 'Pending',
            'confirmed': 'Confirmed',
            'preparing': 'Preparing',
            'ready': 'Ready',
            'served': 'Served',
            'completed': 'Completed',
            'cancelled': 'Cancelled'
        };

        const typeTexts = {
            'dine_in': 'Dine In',
            'takeaway': 'Takeaway',
            'delivery': 'Delivery',
            'online': 'Online'
        };

        const statusClasses = {
            'pending': 'bg-yellow-100 text-yellow-800',
            'confirmed': 'bg-blue-100 text-blue-800',
            'preparing': 'bg-purple-100 text-purple-800',
            'ready': 'bg-green-100 text-green-800',
            'served': 'bg-indigo-100 text-indigo-800',
            'completed': 'bg-teal-100 text-teal-800',
            'cancelled': 'bg-red-100 text-red-800'
        };

        const typeClasses = {
            'dine_in': 'bg-green-100 text-green-800',
            'takeaway': 'bg-orange-100 text-orange-800',
            'delivery': 'bg-blue-100 text-blue-800',
            'online': 'bg-purple-100 text-purple-800'
        };

        const customerName = order.customer ? order.customer.name : 'Unknown Customer';
        const tableName = order.table ? order.table.name : 'Not Assigned';
        const statusText = statusTexts[order.status] || order.status;
        const typeText = typeTexts[order.order_type] || order.order_type;
        const statusClass = statusClasses[order.status] || 'bg-gray-100 text-gray-800';
        const typeClass = typeClasses[order.order_type] || 'bg-gray-100 text-gray-800';
        const orderDate = new Date(order.created_at).toLocaleString();

        return `
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">#${order.order_number}</h3>
                        <span class="px-2 py-1 text-xs font-semibold rounded-full ${statusClass}">${statusText}</span>
                    </div>

                    <div class="space-y-3 mb-4">
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-500">Customer:</span>
                            <span class="text-sm text-gray-900">${customerName}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-500">Table:</span>
                            <span class="text-sm text-gray-900">${tableName}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-500">Type:</span>
                            <span class="px-2 py-1 text-xs font-semibold rounded-full ${typeClass}">${typeText}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-500">Date:</span>
                            <span class="text-sm text-gray-900">${orderDate}</span>
                        </div>
                    </div>

                    <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                        <div class="text-lg font-semibold text-green-600">$${parseFloat(order.total_amount).toFixed(2)}</div>
                        <div class="flex space-x-2">
                            ${order.status === 'pending' ? `
                                <button class="inline-flex items-center px-2 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700 confirm-order-btn" data-order-id="${order.id}" title="Confirm Order">
                                    <i class="mdi mdi-check"></i>
                                </button>
                            ` : ''}
                            <a href="/orders/${order.id}" class="inline-flex items-center px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700" title="View">
                                <i class="mdi mdi-eye"></i>
                            </a>
                            <a href="/orders/${order.id}/edit" class="inline-flex items-center px-2 py-1 bg-yellow-600 text-white text-xs rounded hover:bg-yellow-700" title="Edit">
                                <i class="mdi mdi-pencil"></i>
                            </a>
                            <button class="inline-flex items-center px-2 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700 delete-order" data-id="${order.id}" title="Delete">
                                <i class="mdi mdi-delete"></i>
                            </button>
                            ${order.status !== 'cancelled' ? `
                                <a href="/pos/orders/${order.id}/kot" class="inline-flex items-center px-2 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700" title="Print KOT">
                                    <i class="mdi mdi-printer"></i>
                                </a>
                            ` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Render pagination
    function renderPagination(data) {
        let paginationHtml = '';

        if (data.last_page > 1) {
            paginationHtml = '<li class="' + (data.current_page === 1 ? 'opacity-50 cursor-not-allowed' : '') + '">';
            paginationHtml += '<a class="px-3 py-2 text-sm leading-tight text-gray-500 bg-white border border-gray-300 rounded-l-lg hover:bg-gray-100 hover:text-gray-700" href="#" data-page="' + (data.current_page - 1) + '">Previous</a></li>';

            for (let i = 1; i <= data.last_page; i++) {
                const isActive = data.current_page === i;
                paginationHtml += '<li>';
                paginationHtml += '<a class="px-3 py-2 text-sm leading-tight ' + (isActive ? 'text-blue-600 bg-blue-50 border border-blue-300' : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700') + '" href="#" data-page="' + i + '">' + i + '</a></li>';
            }

            paginationHtml += '<li class="' + (data.current_page === data.last_page ? 'opacity-50 cursor-not-allowed' : '') + '">';
            paginationHtml += '<a class="px-3 py-2 text-sm leading-tight text-gray-500 bg-white border border-gray-300 rounded-r-lg hover:bg-gray-100 hover:text-gray-700" href="#" data-page="' + (data.current_page + 1) + '">Next</a></li>';
        }

        $('#cards-pagination').html(paginationHtml);
    }

    // Pagination click handler
    $(document).on('click', '#cards-pagination .page-link', function(e) {
        e.preventDefault();
        const page = $(this).data('page');
        if (page && page !== currentPage) {
            loadCardsData(page);
        }
    });

    // Initialize table view by default
    initializeDataTable();

    // Filter functionality
    $('#status-filter, #type-filter, #date-filter').on('change', function() {
        if (currentView === 'table') {
            table.draw();
        } else {
            loadCardsData(1);
        }
    });

    $('#clear-filters').on('click', function() {
        $('#status-filter, #type-filter, #date-filter').val('');
        if (currentView === 'table') {
            table.draw();
        } else {
            loadCardsData(1);
        }
    });

    $('#refresh-orders').on('click', function() {
        if (currentView === 'table') {
            table.draw();
        } else {
            loadCardsData(currentPage);
        }
        loadStatistics();
    });

    // Delete order functionality
    $(document).on('click', '.delete-order', function() {
        var orderId = $(this).data('id');

        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
            $.ajax({
                url: `/orders/${orderId}`,
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function() {
                    Swal.fire('Deleted!', 'Order has been deleted successfully.', 'success');
                    if (currentView === 'table') {
                        table.draw();
                    } else {
                        loadCardsData(currentPage);
                    }
                    loadStatistics();
                },
                error: function(xhr) {
                    Swal.fire('Error!', 'An error occurred while deleting the order.', 'error');
                }
            });
            }
        });
    });

    // Order confirmation functionality
    $(document).on('click', '.confirm-order-btn', function() {
        const orderId = $(this).data('order-id');
        const button = $(this);
        const originalText = button.html();
        
        // Show loading state
        button.prop('disabled', true).html('<i class="mdi mdi-loading mdi-spin"></i> Confirming...');
        
        $.ajax({
            url: `/api/orders/${orderId}/confirm`,
            method: 'POST',
            success: function(response) {
                if (response.success) {
                    // Extract order number and KOT information
                    const orderNumber = response.data.order.order_number || orderId;
                    let kotNumber = null;
                    
                    // Check if KOT was created successfully
                    if (response.data.kot_orders && response.data.kot_orders.length > 0) {
                        kotNumber = response.data.kot_orders[0].kot_number || response.data.kot_orders[0].id;
                    }
                    
                    // Show toast notification
                    showOrderConfirmationToast(orderNumber, kotNumber);
                } else {
                    showOrderErrorToast(response.message || "Error confirming order");
                }
                
                // Refresh the current view
                if (currentView === 'table') {
                    table.draw();
                } else {
                    loadCardsData(currentPage);
                }
                loadStatistics();
                
                // Restore button state
                button.prop('disabled', false).html(originalText);
            },
            error: function(xhr) {
                console.error('Error confirming order:', xhr);
                let errorMessage = "Error confirming order";
                
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                
                showOrderErrorToast(errorMessage);
                
                // Restore button
                button.prop('disabled', false).html(originalText);
            }
        });
    });

    // Load statistics
    function loadStatistics() {
        $.ajax({
            url: '{{ route("orders.statistics") }}',
            method: 'GET',
            success: function(response) {
                if (response.statistics) {
                    $('#total-orders-today').text(response.statistics.total_today || 0);
                    $('#pending-orders').text(response.statistics.pending || 0);
                    $('#completed-orders').text(response.statistics.completed || 0);
                    $('#total-sales-today').text('$' + (response.statistics.sales_today || 0).toFixed(2));
                }
            },
            error: function(xhr) {
                console.error('Error loading statistics:', xhr);
            }
        });
    }

    // Initial statistics load
    loadStatistics();
});
</script>
@endpush