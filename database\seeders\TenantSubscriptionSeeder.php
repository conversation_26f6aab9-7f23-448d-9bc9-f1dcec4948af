<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\TenantSubscription;

class TenantSubscriptionSeeder extends Seeder
{
    public function run()
    {
        if (class_exists(\Database\Factories\TenantSubscriptionFactory::class)) {
            TenantSubscription::factory()->count(5)->create();
        } else {
            TenantSubscription::create([
                // Add required fields here
            ]);
        }
    }
} 