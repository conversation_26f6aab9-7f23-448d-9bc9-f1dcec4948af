<?php

namespace Modules\Inventory\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Recipe;
use App\Models\RecipeIngredient;
use App\Models\Product;
use App\Models\Unit;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class RecipeController extends Controller
{
    public function index()
    {
        return view('Inventory::recipes.index');
    }

    public function datatable(Request $request)
    {
        $recipes = Recipe::with(['tenant'])
            ->where('tenant_id', auth()->user()->tenant_id);

        return DataTables::of($recipes)
            ->addColumn('ingredients_count', function ($recipe) {
                return $recipe->ingredients()->count();
            })
            ->addColumn('total_cost', function ($recipe) {
                $totalCost = $recipe->ingredients()->sum(\DB::raw('quantity * cost_per_unit'));
                return number_format($totalCost, 2) . ' ر.س';
            })
            ->addColumn('status', function ($recipe) {
                return $recipe->is_active 
                    ? '<span class="badge bg-success">نشط</span>' 
                    : '<span class="badge bg-danger">غير نشط</span>';
            })
            ->addColumn('actions', function ($recipe) {
                return '
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-info view-recipe" 
                                data-id="' . $recipe->id . '">
                            <i class="fe fe-eye"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-primary edit-recipe" 
                                data-id="' . $recipe->id . '">
                            <i class="fe fe-edit"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-danger delete-recipe" 
                                data-id="' . $recipe->id . '">
                            <i class="fe fe-trash"></i>
                        </button>
                    </div>
                ';
            })
            ->rawColumns(['status', 'actions'])
            ->make(true);
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'ingredients' => 'required|array|min:1',
            'ingredients.*.product_id' => 'required|exists:products,id',
            'ingredients.*.unit_id' => 'required|exists:units,id',
            'ingredients.*.quantity' => 'required|numeric|min:0.001',
            'ingredients.*.cost_per_unit' => 'required|numeric|min:0',
        ]);

        \DB::beginTransaction();
        try {
            $recipe = Recipe::create([
                'tenant_id' => auth()->user()->tenant_id,
                'name' => $request->name,
                'description' => $request->description,
                'is_active' => $request->has('is_active'),
            ]);

            foreach ($request->ingredients as $ingredient) {
                RecipeIngredient::create([
                    'recipe_id' => $recipe->id,
                    'product_id' => $ingredient['product_id'],
                    'unit_id' => $ingredient['unit_id'],
                    'quantity' => $ingredient['quantity'],
                    'cost_per_unit' => $ingredient['cost_per_unit'],
                ]);
            }

            \DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء الوصفة بنجاح',
                'data' => $recipe
            ]);
        } catch (\Exception $e) {
            \DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء الوصفة'
            ], 500);
        }
    }

    public function show($id)
    {
        $recipe = Recipe::with(['ingredients.product', 'ingredients.unit'])->findOrFail($id);
        return response()->json($recipe);
    }

    public function update(Request $request, $id)
    {
        $recipe = Recipe::findOrFail($id);
        
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'ingredients' => 'required|array|min:1',
            'ingredients.*.product_id' => 'required|exists:products,id',
            'ingredients.*.unit_id' => 'required|exists:units,id',
            'ingredients.*.quantity' => 'required|numeric|min:0.001',
            'ingredients.*.cost_per_unit' => 'required|numeric|min:0',
        ]);

        \DB::beginTransaction();
        try {
            $recipe->update([
                'name' => $request->name,
                'description' => $request->description,
                'is_active' => $request->has('is_active'),
            ]);

            // Delete existing ingredients
            $recipe->ingredients()->delete();

            // Add new ingredients
            foreach ($request->ingredients as $ingredient) {
                RecipeIngredient::create([
                    'recipe_id' => $recipe->id,
                    'product_id' => $ingredient['product_id'],
                    'unit_id' => $ingredient['unit_id'],
                    'quantity' => $ingredient['quantity'],
                    'cost_per_unit' => $ingredient['cost_per_unit'],
                ]);
            }

            \DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث الوصفة بنجاح',
                'data' => $recipe
            ]);
        } catch (\Exception $e) {
            \DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث الوصفة'
            ], 500);
        }
    }

    public function destroy($id)
    {
        $recipe = Recipe::findOrFail($id);
        
        // Check if recipe is being used by menu items
        if ($recipe->menuItems()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن حذف الوصفة لأنها مستخدمة في عناصر القائمة'
            ], 422);
        }

        \DB::beginTransaction();
        try {
            $recipe->ingredients()->delete();
            $recipe->delete();
            \DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم حذف الوصفة بنجاح'
            ]);
        } catch (\Exception $e) {
            \DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف الوصفة'
            ], 500);
        }
    }

    public function getRecipes()
    {
        $recipes = Recipe::where('tenant_id', auth()->user()->tenant_id)
            ->where('is_active', true)
            ->orderBy('name')
            ->get(['id', 'name', 'description']);

        return response()->json($recipes);
    }

    public function getProducts()
    {
        $products = Product::where('tenant_id', auth()->user()->tenant_id)
            ->where('is_active', true)
            ->with('unit')
            ->orderBy('name')
            ->get(['id', 'name', 'unit_id', 'standard_cost']);

        return response()->json($products);
    }
}