@extends('layouts.master')

@section('title', 'أوامر الشراء')

@section('breadcrumb')
<div class="bg-white rounded-lg shadow-sm border border-gray-200 px-6 py-4 mb-6">
    <nav class="flex" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3 rtl:space-x-reverse">
            <li class="inline-flex items-center">
                <a href="{{ route('dashboard') }}" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                    <i class="fas fa-home ml-2"></i>
                    الرئيسية
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <i class="fas fa-chevron-left text-gray-400 mx-2"></i>
                    <a href="{{ route('inventory.dashboard') }}" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">المخزون</a>
                </div>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <i class="fas fa-chevron-left text-gray-400 mx-2"></i>
                    <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">أوامر الشراء</span>
                </div>
            </li>
        </ol>
    </nav>
</div>
@endsection

@section('content')
<div class="space-y-6">
    <!-- Page Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 flex items-center gap-3">
                        <div class="w-10 h-10 bg-gradient-to-r from-indigo-600 to-indigo-700 rounded-lg flex items-center justify-center">
                            <i class="fas fa-shopping-cart text-white"></i>
                        </div>
                        أوامر الشراء
                    </h1>
                    <p class="mt-1 text-sm text-gray-600">إدارة ومتابعة أوامر الشراء والموردين</p>
                </div>
                <div class="flex gap-2">
                    <button type="button" onclick="exportPurchaseOrders()" class="px-4 py-2 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center gap-2">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                    <button type="button" onclick="generatePOReport()" class="px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2">
                        <i class="fas fa-chart-line"></i>
                        تقرير الطلبات
                    </button>
                    <button type="button" onclick="createPurchaseOrder()" class="px-4 py-2 bg-indigo-600 text-white font-medium rounded-lg hover:bg-indigo-700 transition-colors duration-200 flex items-center gap-2">
                        <i class="fas fa-plus"></i>
                        إنشاء أمر شراء جديد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Purchase Order Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Orders Card -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-shopping-cart text-indigo-600 text-xl"></i>
                    </div>
                </div>
                <div class="mr-4 flex-1 text-right">
                    <p class="text-sm font-medium text-gray-600">إجمالي الطلبات</p>
                    <p class="text-2xl font-bold text-indigo-600">{{ $analytics['total_orders'] ?? 0 }}</p>
                </div>
            </div>
        </div>

        <!-- Pending Orders Card -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-clock text-yellow-600 text-xl"></i>
                    </div>
                </div>
                <div class="mr-4 flex-1 text-right">
                    <p class="text-sm font-medium text-gray-600">في الانتظار</p>
                    <p class="text-2xl font-bold text-yellow-600">{{ $analytics['pending_orders'] ?? 0 }}</p>
                </div>
            </div>
        </div>

        <!-- Completed Orders Card -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                </div>
                <div class="mr-4 flex-1 text-right">
                    <p class="text-sm font-medium text-gray-600">مكتملة</p>
                    <p class="text-2xl font-bold text-green-600">{{ $analytics['completed_orders'] ?? 0 }}</p>
                </div>
            </div>
        </div>

        <!-- Total Amount Card -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-dollar-sign text-blue-600 text-xl"></i>
                    </div>
                </div>
                <div class="mr-4 flex-1 text-right">
                    <p class="text-sm font-medium text-gray-600">إجمالي القيمة (ريال)</p>
                    <p class="text-2xl font-bold text-blue-600">{{ number_format($analytics['total_amount'] ?? 0, 2) }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <i class="fas fa-filter text-indigo-600"></i>
            فلاتر البحث
        </h3>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">رقم الطلب</label>
                <input type="text" id="po-number-filter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500" placeholder="رقم الطلب...">
            </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>المورد</label>
                        <select id="supplier-filter" class="form-control select2">
                            <option value="">جميع الموردين</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label>الحالة</label>
                        <select id="status-filter" class="form-control select2">
                            <option value="">جميع الحالات</option>
                            <option value="draft">مسودة</option>
                            <option value="pending">في الانتظار</option>
                            <option value="approved">معتمد</option>
                            <option value="ordered">مطلوب</option>
                            <option value="received">مستلم</option>
                            <option value="cancelled">ملغي</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label>من تاريخ</label>
                        <input type="date" id="date-from" class="form-control">
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label>إلى تاريخ</label>
                        <input type="date" id="date-to" class="form-control">
                    </div>
                </div>
                <div class="col-md-1">
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <div class="d-flex flex-column">
                            <button type="button" class="btn btn-primary btn-sm mb-1" onclick="applyPOFilters()">
                                <i class="mdi mdi-filter"></i>
                            </button>
                            <button type="button" class="btn btn-secondary btn-sm" onclick="clearPOFilters()">
                                <i class="mdi mdi-filter-remove"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Purchase Orders Table -->
<div class="row">
    <div class="col-xl-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">قائمة أوامر الشراء</h4>
                    <div class="card-options">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="refreshPOTable()">
                                <i class="mdi mdi-refresh"></i> تحديث
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-info" onclick="togglePOCardView()">
                                <i class="mdi mdi-view-grid"></i> عرض البطاقات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Table View -->
                <div id="table-view">
                    <div class="table-responsive">
                        <table id="purchase-orders-table" class="table table-striped table-bordered text-md-nowrap">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>رقم الطلب</th>
                                    <th>المورد</th>
                                    <th>تاريخ الطلب</th>
                                    <th>تاريخ التسليم المتوقع</th>
                                    <th>عدد المواد</th>
                                    <th>إجمالي المبلغ</th>
                                    <th>الحالة</th>
                                    <th>المنشئ</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Card View -->
                <div id="card-view" style="display: none;">
                    <div id="po-cards" class="row">
                        <!-- Cards will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modals -->
@include('inventory::purchase-orders.modals')

@endsection

@push('inventory-scripts')
<script>
let purchaseOrdersTable;
let cardView = false;

$(document).ready(function() {
    initializePOTable();
    loadSuppliersForFilter();
});

function initializePOTable() {
    purchaseOrdersTable = $('#purchase-orders-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("inventory.api.purchase-orders.datatable") }}',
            data: function(d) {
                d.po_number = $('#po-number-filter').val();
                d.supplier_id = $('#supplier-filter').val();
                d.status = $('#status-filter').val();
                d.date_from = $('#date-from').val();
                d.date_to = $('#date-to').val();
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'po_number', name: 'po_number' },
            { data: 'supplier.name', name: 'supplier.name' },
            { 
                data: 'order_date', 
                name: 'order_date',
                render: function(data) {
                    return InventoryModule.formatDate(data);
                }
            },
            { 
                data: 'expected_delivery_date', 
                name: 'expected_delivery_date',
                render: function(data) {
                    return data ? InventoryModule.formatDate(data) : '-';
                }
            },
            { data: 'items_count', name: 'items_count' },
            { 
                data: 'total_amount', 
                name: 'total_amount',
                render: function(data) {
                    return InventoryModule.formatCurrency(data);
                }
            },
            { 
                data: 'status', 
                name: 'status',
                render: function(data) {
                    return getPOStatusBadge(data);
                }
            },
            { data: 'created_by.name', name: 'created_by.name' },
            { 
                data: 'actions', 
                name: 'actions', 
                orderable: false, 
                searchable: false,
                render: function(data, type, row) {
                    return `
                        <div class="table-actions">
                            <button type="button" class="btn btn-sm btn-info btn-action" onclick="viewPO(${row.id})" title="عرض">
                                <i class="mdi mdi-eye"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-primary btn-action" onclick="editPO(${row.id})" title="تعديل">
                                <i class="mdi mdi-pencil"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-success btn-action" onclick="printPO(${row.id})" title="طباعة">
                                <i class="mdi mdi-printer"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-warning btn-action" onclick="duplicatePO(${row.id})" title="نسخ">
                                <i class="mdi mdi-content-copy"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-danger btn-action" onclick="deletePO(${row.id})" title="حذف">
                                <i class="mdi mdi-delete"></i>
                            </button>
                        </div>
                    `;
                }
            }
        ],
        order: [[3, 'desc']],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
        },
        responsive: true,
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'excel',
                text: 'تصدير Excel',
                className: 'btn btn-success btn-sm'
            },
            {
                extend: 'pdf',
                text: 'تصدير PDF',
                className: 'btn btn-danger btn-sm'
            }
        ]
    });
}

function loadSuppliersForFilter() {
    $.get('{{ route("inventory.api.suppliers") }}')
        .done(function(suppliers) {
            const options = suppliers.map(supplier => 
                `<option value="${supplier.id}">${supplier.name}</option>`
            ).join('');
            
            $('#supplier-filter').html('<option value="">جميع الموردين</option>' + options);
        })
        .fail(function() {
            console.error('Failed to load suppliers for filter');
        });
}

function getPOStatusBadge(status) {
    const badges = {
        'draft': '<span class="po-status-badge status-draft">مسودة</span>',
        'pending': '<span class="po-status-badge status-pending">في الانتظار</span>',
        'approved': '<span class="po-status-badge status-approved">معتمد</span>',
        'ordered': '<span class="po-status-badge status-ordered">مطلوب</span>',
        'received': '<span class="po-status-badge status-received">مستلم</span>',
        'cancelled': '<span class="po-status-badge status-cancelled">ملغي</span>'
    };
    
    return badges[status] || status;
}

function applyPOFilters() {
    if (cardView) {
        loadPOCards();
    } else {
        purchaseOrdersTable.ajax.reload();
    }
}

function clearPOFilters() {
    $('#po-number-filter').val('');
    $('#supplier-filter').val('').trigger('change');
    $('#status-filter').val('').trigger('change');
    $('#date-from').val('');
    $('#date-to').val('');
    
    if (cardView) {
        loadPOCards();
    } else {
        purchaseOrdersTable.ajax.reload();
    }
}

function refreshPOTable() {
    if (cardView) {
        loadPOCards();
    } else {
        purchaseOrdersTable.ajax.reload();
    }
}

function togglePOCardView() {
    cardView = !cardView;
    
    if (cardView) {
        $('#table-view').hide();
        $('#card-view').show();
        loadPOCards();
    } else {
        $('#card-view').hide();
        $('#table-view').show();
    }
}

function loadPOCards() {
    const filters = {
        po_number: $('#po-number-filter').val(),
        supplier_id: $('#supplier-filter').val(),
        status: $('#status-filter').val(),
        date_from: $('#date-from').val(),
        date_to: $('#date-to').val()
    };
    
    $.get('{{ route("inventory.api.purchase-orders.cards") }}', filters)
        .done(function(orders) {
            renderPOCards(orders);
        })
        .fail(function() {
            InventoryModule.showError('حدث خطأ أثناء جلب بيانات أوامر الشراء');
        });
}

function renderPOCards(orders) {
    let cardsHtml = '';
    
    orders.forEach(function(order) {
        cardsHtml += `
            <div class="col-xl-4 col-lg-6 col-md-6 col-sm-12">
                <div class="card po-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <div>
                                <h5 class="mb-1">${order.po_number}</h5>
                                <small class="text-muted">${order.supplier.name}</small>
                            </div>
                            <div>
                                ${getPOStatusBadge(order.status)}
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="d-flex justify-content-between mb-1">
                                <small class="text-muted">تاريخ الطلب:</small>
                                <small>${InventoryModule.formatDate(order.order_date)}</small>
                            </div>
                            <div class="d-flex justify-content-between mb-1">
                                <small class="text-muted">عدد المواد:</small>
                                <small>${order.items_count}</small>
                            </div>
                            <div class="d-flex justify-content-between mb-1">
                                <small class="text-muted">إجمالي المبلغ:</small>
                                <small class="font-weight-bold">${InventoryModule.formatCurrency(order.total_amount)}</small>
                            </div>
                            <div class="d-flex justify-content-between">
                                <small class="text-muted">المنشئ:</small>
                                <small>${order.created_by.name}</small>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-sm btn-info" onclick="viewPO(${order.id})">
                                <i class="mdi mdi-eye"></i> عرض
                            </button>
                            <button type="button" class="btn btn-sm btn-primary" onclick="editPO(${order.id})">
                                <i class="mdi mdi-pencil"></i> تعديل
                            </button>
                            <button type="button" class="btn btn-sm btn-success" onclick="printPO(${order.id})">
                                <i class="mdi mdi-printer"></i> طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    $('#po-cards').html(cardsHtml || '<div class="col-12 text-center text-muted py-4">لا توجد أوامر شراء</div>');
}

function createPurchaseOrder() {
    window.location.href = '{{ route("inventory.purchase-orders.create") }}';
}

function viewPO(id) {
    window.location.href = `{{ route('inventory.purchase-orders.show', '') }}/${id}`;
}

function editPO(id) {
    window.location.href = `{{ route('inventory.purchase-orders.edit', '') }}/${id}`;
}

function printPO(id) {
    window.open(`{{ route('inventory.purchase-orders.print', '') }}/${id}`, '_blank');
}

function duplicatePO(id) {
    InventoryModule.confirm('هل تريد إنشاء نسخة من هذا الطلب؟', function() {
        $.post(`{{ route('inventory.api.purchase-orders.duplicate', '') }}/${id}`, {
            _token: $('meta[name="csrf-token"]').attr('content')
        })
        .done(function(response) {
            InventoryModule.showSuccess('تم إنشاء نسخة من الطلب بنجاح');
            refreshPOTable();
        })
        .fail(function() {
            InventoryModule.showError('حدث خطأ أثناء نسخ الطلب');
        });
    });
}

function deletePO(id) {
    InventoryModule.confirm('هل أنت متأكد من حذف هذا الطلب؟', function() {
        $.ajax({
            url: `{{ route('inventory.api.purchase-orders.destroy', '') }}/${id}`,
            type: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function() {
                InventoryModule.showSuccess('تم حذف الطلب بنجاح');
                refreshPOTable();
            },
            error: function(xhr) {
                const message = xhr.responseJSON?.message || 'حدث خطأ أثناء حذف الطلب';
                InventoryModule.showError(message);
            }
        });
    });
}

function exportPurchaseOrders() {
    const filters = {
        po_number: $('#po-number-filter').val(),
        supplier_id: $('#supplier-filter').val(),
        status: $('#status-filter').val(),
        date_from: $('#date-from').val(),
        date_to: $('#date-to').val(),
        format: 'excel'
    };
    
    const queryString = new URLSearchParams(filters).toString();
    window.location.href = `{{ route("inventory.api.purchase-orders.export") }}?${queryString}`;
}

function generatePOReport() {
    const filters = {
        po_number: $('#po-number-filter').val(),
        supplier_id: $('#supplier-filter').val(),
        status: $('#status-filter').val(),
        date_from: $('#date-from').val(),
        date_to: $('#date-to').val()
    };
    
    const queryString = new URLSearchParams(filters).toString();
    window.location.href = `{{ route("inventory.api.purchase-orders.report") }}?${queryString}`;
}
</script>
@endpush
