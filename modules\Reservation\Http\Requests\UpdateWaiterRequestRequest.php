<?php

namespace Modules\Reservation\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateWaiterRequestRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'table_id' => 'sometimes|exists:tables,id',
            'status' => 'sometimes|in:pending,completed,cancelled',
            'waiter_id' => 'nullable|exists:users,id',
        ];
    }

    /**
     * Get custom error messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'table_id.exists' => 'Selected table does not exist.',
            'status.in' => 'Status must be one of: pending, completed, cancelled.',
            'waiter_id.exists' => 'Selected waiter does not exist.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'table_id' => 'table',
            'waiter_id' => 'waiter',
        ];
    }
}