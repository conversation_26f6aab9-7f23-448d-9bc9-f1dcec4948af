<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('menu_item_addons', function (Blueprint $table) {
            $table->id();
            $table->foreignId('branch_id')->nullable()->constrained('branches')->onDelete('cascade');
            $table->foreignId('menu_item_id')->nullable()->constrained('menu_items')->onDelete('cascade');
            $table->string('addon_group_name', 100)->nullable()->comment('For grouping related addons');
            $table->string('name', 100)->nullable();
            $table->string('code', 50)->nullable();
            $table->decimal('price', 10, 2)->nullable()->default(0);
            $table->decimal('cost', 10, 2)->nullable()->default(0);
            $table->boolean('is_required')->nullable()->default(false);
            $table->integer('max_quantity')->nullable()->default(1);
            $table->integer('sort_order')->nullable()->default(0);
            $table->timestamps();
            
            $table->unique(['menu_item_id', 'code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('menu_item_addons');
    }
};