<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('events', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained()->onDelete('cascade');
            $table->foreignId('branch_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string('code')->unique();
            $table->text('description')->nullable();
            $table->text('short_description')->nullable();
            $table->string('event_type')->default('special'); // special, holiday, promotion, seasonal
            $table->json('image_urls')->nullable();
            $table->string('banner_image')->nullable();
            $table->datetime('start_date');
            $table->datetime('end_date');
            $table->time('start_time')->nullable();
            $table->time('end_time')->nullable();
            $table->json('available_days')->nullable(); // [0,1,2,3,4,5,6] for days of week
            $table->string('location')->nullable();
            $table->decimal('price', 10, 2)->nullable();
            $table->integer('max_participants')->nullable();
            $table->integer('current_participants')->default(0);
            $table->boolean('requires_reservation')->default(false);
            $table->json('menu_items')->nullable(); // Associated menu items for the event
            $table->json('special_menu')->nullable(); // Special menu for the event
            $table->decimal('discount_percentage', 5, 2)->nullable();
            $table->decimal('discount_amount', 10, 2)->nullable();
            $table->string('discount_type')->nullable(); // percentage, fixed_amount
            $table->json('terms_conditions')->nullable();
            $table->string('contact_info')->nullable();
            $table->string('booking_url')->nullable();
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_active')->default(true);
            $table->boolean('is_recurring')->default(false);
            $table->string('recurrence_pattern')->nullable(); // daily, weekly, monthly, yearly
            $table->integer('sort_order')->default(0);
            $table->json('metadata')->nullable(); // Additional event data
            $table->timestamps();

            // Indexes
            $table->index(['tenant_id', 'branch_id']);
            $table->index(['start_date', 'end_date']);
            $table->index(['is_active', 'is_featured']);
            $table->index('event_type');
            $table->index('code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('events');
    }
};