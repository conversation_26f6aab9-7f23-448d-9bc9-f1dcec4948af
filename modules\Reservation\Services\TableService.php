<?php

namespace Modules\Reservation\Services;

use App\Models\Table;
use App\Models\Reservation;
use Modules\Reservation\Contracts\TableServiceInterface;
use Modules\Reservation\Contracts\QRCodeServiceInterface;
use Illuminate\Database\Eloquent\Collection;
use Carbon\Carbon;
use Exception;

class TableService implements TableServiceInterface
{
    protected QRCodeServiceInterface $qrCodeService;

    public function __construct(QRCodeServiceInterface $qrCodeService)
    {
        $this->qrCodeService = $qrCodeService;
    }

    /**
     * Get all tables with optional filters.
     */
    public function getAllTables(array $filters = []): Collection
    {
        $query = Table::with(['branch', 'area', 'reservations' => function ($q) {
            $q->whereIn('reservation_status_id', function ($subQ) {
                $subQ->select('id')
                    ->from('reservation_statuses')
                    ->whereIn('name', ['confirmed', 'seated']);
            })->orderBy('reservation_datetime');
        }]);

        if (!empty($filters['branch_id'])) {
            $query->where('branch_id', $filters['branch_id']);
        }

        if (!empty($filters['area_id'])) {
            $query->where('area_id', $filters['area_id']);
        }

        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (!empty($filters['section'])) {
            $query->where('section', $filters['section']);
        }

        if (!empty($filters['min_capacity'])) {
            $query->where('seating_capacity', '>=', $filters['min_capacity']);
        }

        if (!empty($filters['max_capacity'])) {
            $query->where('seating_capacity', '<=', $filters['max_capacity']);
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        return $query->orderBy('table_number')->get();
    }

    /**
     * Create a new table.
     */
    public function createTable(array $data): Table
    {
        // Generate QR code if not provided
        if (empty($data['qr_code'])) {
            $data['qr_code'] = $this->generateUniqueQRCode();
        }

        $table = Table::create($data);
        
        return $table->load(['branch', 'area']);
    }

    /**
     * Get table by ID.
     */
    public function getTableById(int $id): ?Table
    {
        return Table::with([
            'branch',
            'area',
            'reservations' => function ($query) {
                $query->with(['customer', 'reservationStatus'])
                    ->orderBy('reservation_datetime');
            },
            'orders' => function ($query) {
                $query->with(['customer'])
                    ->whereIn('status', ['pending', 'confirmed', 'preparing', 'ready'])
                    ->orderBy('created_at', 'desc');
            }
        ])->find($id);
    }

    /**
     * Update table.
     */
    public function updateTable(int $id, array $data): Table
    {
        $table = Table::findOrFail($id);
        
        // If table number is being changed, check for uniqueness within branch
        if (isset($data['table_number']) && $data['table_number'] !== $table->table_number) {
            $exists = Table::where('branch_id', $table->branch_id)
                ->where('table_number', $data['table_number'])
                ->where('id', '!=', $id)
                ->exists();
                
            if ($exists) {
                throw new Exception('Table number already exists in this branch.');
            }
        }
        
        $table->update($data);
        
        return $table->load(['branch', 'area']);
    }

    /**
     * Delete table.
     */
    public function deleteTable(int $id): bool
    {
        $table = Table::findOrFail($id);
        
        // Check if table has active reservations
        $activeReservations = $table->reservations()
            ->whereIn('reservation_status_id', function ($q) {
                $q->select('id')
                    ->from('reservation_statuses')
                    ->whereIn('name', ['confirmed', 'seated']);
            })
            ->count();
            
        if ($activeReservations > 0) {
            throw new Exception('Cannot delete table with active reservations.');
        }
        
        // Check if table has active orders
        $activeOrders = $table->orders()
            ->whereIn('status', ['pending', 'confirmed', 'preparing', 'ready'])
            ->count();
            
        if ($activeOrders > 0) {
            throw new Exception('Cannot delete table with active orders.');
        }
        
        return $table->delete();
    }

    /**
     * Get available tables for given criteria.
     */
    public function getAvailableTables(int $branchId, string $datetime, int $partySize, int $duration = 120): Collection
    {
        $startTime = Carbon::parse($datetime);
        $endTime = $startTime->copy()->addMinutes($duration);
        $bufferMinutes = config('reservation.table_turnover_buffer', 30);
        
        // Get all tables that can accommodate the party size
        $tables = Table::where('branch_id', $branchId)
            ->where('seating_capacity', '>=', $partySize)
            ->where('is_active', true)
            ->where('status', '!=', 'out_of_order')
            ->with(['area'])
            ->get();
        
        // Filter out tables with conflicting reservations
        $availableTables = $tables->filter(function ($table) use ($startTime, $endTime, $bufferMinutes) {
            $conflictingReservations = Reservation::where('table_id', $table->id)
                ->whereIn('reservation_status_id', function ($q) {
                    $q->select('id')
                        ->from('reservation_statuses')
                        ->whereIn('name', ['confirmed', 'seated']);
                })
                ->where(function ($q) use ($startTime, $endTime, $bufferMinutes) {
                    $q->where(function ($subQ) use ($startTime, $endTime, $bufferMinutes) {
                        $subQ->where('reservation_datetime', '<', $endTime->copy()->addMinutes($bufferMinutes))
                            ->whereRaw('DATE_ADD(reservation_datetime, INTERVAL duration_minutes + ? MINUTE) > ?', 
                                [$bufferMinutes, $startTime->copy()->subMinutes($bufferMinutes)]);
                    });
                })
                ->count();
                
            return $conflictingReservations === 0;
        });
        
        return $availableTables->values();
    }

    /**
     * Update table status.
     */
    public function updateTableStatus(int $id, string $status): Table
    {
        $validStatuses = ['available', 'occupied', 'reserved', 'cleaning', 'out_of_order'];
        
        if (!in_array($status, $validStatuses)) {
            throw new Exception('Invalid table status.');
        }
        
        $table = Table::findOrFail($id);
        $table->update(['status' => $status]);
        
        return $table->load(['branch', 'area']);
    }

    /**
     * Generate QR code for table.
     */
    public function generateTableQR(int $id): string
    {
        $table = Table::findOrFail($id);
        
        if (!$table->qr_code) {
            $qrCode = $this->generateUniqueQRCode();
            $table->update(['qr_code' => $qrCode]);
        }
        
        return $this->qrCodeService->generateTableQR($id);
    }

    /**
     * Get table by QR code.
     */
    public function getTableByQR(string $qrCode): ?Table
    {
        return Table::where('qr_code', $qrCode)
            ->with(['branch', 'area'])
            ->first();
    }

    /**
     * Get table occupancy status.
     */
    public function getTableOccupancy(int $branchId = null, string $date = null): array
    {
        $query = Table::query();
        
        if ($branchId) {
            $query->where('branch_id', $branchId);
        }
        
        $tables = $query->with(['area'])->get();
        
        $occupancy = [
            'total_tables' => $tables->count(),
            'total_capacity' => $tables->sum('seating_capacity'),
            'by_status' => $tables->groupBy('status')->map->count(),
            'by_area' => $tables->groupBy('area.name')->map(function ($areaTables) {
                return [
                    'total' => $areaTables->count(),
                    'capacity' => $areaTables->sum('seating_capacity'),
                    'by_status' => $areaTables->groupBy('status')->map->count(),
                ];
            }),
        ];
        
        // Calculate occupancy rate
        $occupiedTables = $occupancy['by_status']['occupied'] ?? 0;
        $occupancy['occupancy_rate'] = $occupancy['total_tables'] > 0 
            ? round(($occupiedTables / $occupancy['total_tables']) * 100, 1)
            : 0;
        
        return $occupancy;
    }

    /**
     * Generate unique QR code.
     */
    private function generateUniqueQRCode(): string
    {
        do {
            $qrCode = 'TBL_' . strtoupper(uniqid());
        } while (Table::where('qr_code', $qrCode)->exists());
        
        return $qrCode;
    }

    /**
     * Get table by ID for a specific branch.
     */
    public function getTableByIdForBranch(int $id, int $branchId): ?Table
    {
        return Table::with([
            'branch',
            'area',
            'reservations' => function ($query) {
                $query->with(['customer', 'reservationStatus'])
                    ->orderBy('reservation_datetime');
            },
            'orders' => function ($query) {
                $query->with(['customer'])
                    ->whereIn('status', ['pending', 'confirmed', 'preparing', 'ready'])
                    ->orderBy('created_at', 'desc');
            }
        ])->where('branch_id', $branchId)->find($id);
    }

    /**
     * Update table for a specific branch.
     */
    public function updateTableForBranch(int $id, array $data, int $branchId): ?Table
    {
        $table = Table::where('branch_id', $branchId)->find($id);
        
        if (!$table) {
            return null;
        }
        
        // If table number is being changed, check for uniqueness within branch
        if (isset($data['table_number']) && $data['table_number'] !== $table->table_number) {
            $exists = Table::where('branch_id', $branchId)
                ->where('table_number', $data['table_number'])
                ->where('id', '!=', $id)
                ->exists();
                
            if ($exists) {
                throw new Exception('Table number already exists in this branch.');
            }
        }
        
        $table->update($data);
        
        return $table->load(['branch', 'area']);
    }

    /**
     * Delete table for a specific branch.
     */
    public function deleteTableForBranch(int $id, int $branchId): bool
    {
        $table = Table::where('branch_id', $branchId)->find($id);
        
        if (!$table) {
            return false;
        }
        
        // Check if table has active reservations
        $activeReservations = $table->reservations()
            ->whereIn('reservation_status_id', function ($q) {
                $q->select('id')
                    ->from('reservation_statuses')
                    ->whereIn('name', ['confirmed', 'seated']);
            })
            ->count();
            
        if ($activeReservations > 0) {
            throw new Exception('Cannot delete table with active reservations.');
        }
        
        // Check if table has active orders
        $activeOrders = $table->orders()
            ->whereIn('status', ['pending', 'confirmed', 'preparing', 'ready'])
            ->count();
            
        if ($activeOrders > 0) {
            throw new Exception('Cannot delete table with active orders.');
        }
        
        return $table->delete();
    }
}