<?php

/**
 * Comprehensive Menu Module API Test Script
 * Tests all API endpoints in the Menu module with proper authentication
 */

// Configuration
$baseUrl = 'http://127.0.0.1:8000/api';
$authToken = null;

// Test credentials (from RolesAndPermissionsSeeder)
$testCredentials = [
    'email' => '<EMAIL>',
    'password' => 'password'
];

// Colors for output
class Colors {
    const GREEN = "\033[32m";
    const RED = "\033[31m";
    const YELLOW = "\033[33m";
    const BLUE = "\033[34m";
    const RESET = "\033[0m";
}

/**
 * Make HTTP request with optional authentication
 */
function makeRequest($method, $url, $data = null, $useAuth = true) {
    global $authToken;
    
    $ch = curl_init();
    
    $headers = [
        'Content-Type: application/json',
        'Accept: application/json'
    ];
    
    if ($useAuth && $authToken) {
        $headers[] = 'Authorization: Bearer ' . $authToken;
    }
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_CUSTOMREQUEST => $method,
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_TIMEOUT => 30
    ]);
    
    if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        return ['error' => $error, 'http_code' => 0];
    }
    
    return [
        'response' => json_decode($response, true),
        'http_code' => $httpCode,
        'raw_response' => $response
    ];
}

/**
 * Print test result
 */
function printResult($testName, $success, $details = '') {
    $color = $success ? Colors::GREEN : Colors::RED;
    $status = $success ? 'PASS' : 'FAIL';
    echo $color . "[{$status}] {$testName}" . Colors::RESET . "\n";
    if ($details) {
        echo "       {$details}\n";
    }
}

/**
 * Print section header
 */
function printSection($title) {
    echo "\n" . Colors::BLUE . "=== {$title} ===" . Colors::RESET . "\n";
}

/**
 * Authenticate and get token
 */
function authenticate() {
    global $baseUrl, $testCredentials, $authToken;
    
    printSection("AUTHENTICATION");
    
    $result = makeRequest('POST', $baseUrl . '/auth/login', $testCredentials, false);
    
    if ($result['http_code'] === 200 && isset($result['response']['data']['token'])) {
        $authToken = $result['response']['data']['token'];
        printResult("Login", true, "Token obtained successfully");
        return true;
    } else {
        printResult("Login", false, "HTTP {$result['http_code']}: " . ($result['response']['message'] ?? 'Unknown error'));
        return false;
    }
}

/**
 * Test Categories API
 */
function testCategoriesAPI() {
    global $baseUrl;
    
    printSection("CATEGORIES API");
    
    // Test GET /categories
    $result = makeRequest('GET', $baseUrl . '/menu/categories');
    printResult("GET /menu/categories", $result['http_code'] === 200, "HTTP {$result['http_code']}");
    
    // First create a menu to use for category creation
    $menuData = [
        'name' => 'Test Menu for Category',
        'code' => 'TEST_MENU_CAT_' . time(),
        'description' => 'A test menu for category testing',
        'is_active' => true
    ];
    
    $menuResult = makeRequest('POST', $baseUrl . '/menu/menus', $menuData);
    $menuId = null;
    if ($menuResult['http_code'] === 201 && isset($menuResult['response']['data']['id'])) {
        $menuId = $menuResult['response']['data']['id'];
    }
    
    // Test POST /categories (Create)
    $categoryData = [
        'menu_id' => $menuId, // Required field
        'name' => 'Test Category',
        'code' => 'TEST_CAT_' . time(), // Required field
        'description' => 'A test category for API testing',
        'is_active' => true,
        'sort_order' => 1
    ];
    
    $result = makeRequest('POST', $baseUrl . '/menu/categories', $categoryData);
    $categoryId = null;
    if ($result['http_code'] === 201 && isset($result['response']['data']['id'])) {
        $categoryId = $result['response']['data']['id'];
        printResult("POST /menu/categories", true, "Category created with ID: {$categoryId}");
    } else {
        printResult("POST /menu/categories", false, "HTTP {$result['http_code']}: " . ($result['response']['message'] ?? 'Unknown error'));
    }
    
    if ($categoryId) {
        // Test GET /categories/{id}
        $result = makeRequest('GET', $baseUrl . "/menu/categories/{$categoryId}");
        printResult("GET /menu/categories/{id}", $result['http_code'] === 200, "HTTP {$result['http_code']}");
        
        // Test PUT /categories/{id}
        $updateData = [
            'name' => 'Updated Test Category',
            'description' => 'Updated description'
        ];
        $result = makeRequest('PUT', $baseUrl . "/menu/categories/{$categoryId}", $updateData);
        printResult("PUT /menu/categories/{id}", $result['http_code'] === 200, "HTTP {$result['http_code']}");
        
        // Test DELETE /categories/{id}
        $result = makeRequest('DELETE', $baseUrl . "/menu/categories/{$categoryId}");
        printResult("DELETE /menu/categories/{id}", $result['http_code'] === 200, "HTTP {$result['http_code']}");
    }
}

/**
 * Test Menus API
 */
function testMenusAPI() {
    global $baseUrl;
    
    printSection("MENUS API");
    
    // Test GET /menus
    $result = makeRequest('GET', $baseUrl . '/menu/menus');
    printResult("GET /menu/menus", $result['http_code'] === 200, "HTTP {$result['http_code']}");
    
    // Test POST /menus (Create)
    $menuData = [
        'name' => 'Test Menu',
        'code' => 'TEST_MENU_' . time(), // Required field
        'description' => 'A test menu for API testing',
        'is_active' => true,
        'start_time' => '09:00',
        'end_time' => '22:00'
    ];
    
    $result = makeRequest('POST', $baseUrl . '/menu/menus', $menuData);
    $menuId = null;
    if ($result['http_code'] === 201 && isset($result['response']['data']['id'])) {
        $menuId = $result['response']['data']['id'];
        printResult("POST /menu/menus", true, "Menu created with ID: {$menuId}");
    } else {
        printResult("POST /menu/menus", false, "HTTP {$result['http_code']}: " . ($result['response']['message'] ?? 'Unknown error'));
    }
    
    if ($menuId) {
        // Test GET /menus/{id}
        $result = makeRequest('GET', $baseUrl . "/menu/menus/{$menuId}");
        printResult("GET /menu/menus/{id}", $result['http_code'] === 200, "HTTP {$result['http_code']}");
        
        // Test PUT /menus/{id}
        $updateData = [
            'name' => 'Updated Test Menu',
            'description' => 'Updated description'
        ];
        $result = makeRequest('PUT', $baseUrl . "/menu/menus/{$menuId}", $updateData);
        printResult("PUT /menu/menus/{id}", $result['http_code'] === 200, "HTTP {$result['http_code']}");
        
        // Test DELETE /menus/{id}
        $result = makeRequest('DELETE', $baseUrl . "/menu/menus/{$menuId}");
        printResult("DELETE /menu/menus/{id}", $result['http_code'] === 200, "HTTP {$result['http_code']}");
    }
}

/**
 * Test Menu Items API
 */
function testMenuItemsAPI() {
    global $baseUrl;
    
    printSection("MENU ITEMS API");
    
    // Test GET /menu-items
    $result = makeRequest('GET', $baseUrl . '/menu/menu-items');
    printResult("GET /menu/menu-items", $result['http_code'] === 200, "HTTP {$result['http_code']}");
    
    // First create a menu to use for menu item creation
    $menuData = [
        'name' => 'Test Menu for Items',
        'code' => 'TEST_MENU_ITEMS_' . time(),
        'description' => 'A test menu for menu item testing',
        'is_active' => true
    ];
    
    $menuResult = makeRequest('POST', $baseUrl . '/menu/menus', $menuData);
    $menuId = null;
    if ($menuResult['http_code'] === 201 && isset($menuResult['response']['data']['id'])) {
        $menuId = $menuResult['response']['data']['id'];
    }
    
    // Test POST /menu-items (Create)
    $menuItemData = [
        'menu_id' => $menuId, // Required field
        'name' => 'Test Menu Item',
        'code' => 'TEST_ITEM_' . time(), // Required field
        'description' => 'A test menu item for API testing',
        'base_price' => 15.99, // Required field (not just 'price')
        'is_active' => true,
        'prep_time_minutes' => 15
    ];
    
    $result = makeRequest('POST', $baseUrl . '/menu/menu-items', $menuItemData);
    $menuItemId = null;
    if ($result['http_code'] === 201 && isset($result['response']['data']['id'])) {
        $menuItemId = $result['response']['data']['id'];
        printResult("POST /menu/menu-items", true, "Menu item created with ID: {$menuItemId}");
    } else {
        printResult("POST /menu/menu-items", false, "HTTP {$result['http_code']}: " . ($result['response']['message'] ?? 'Unknown error'));
    }
    
    if ($menuItemId) {
        // Test GET /menu-items/{id}
        $result = makeRequest('GET', $baseUrl . "/menu/menu-items/{$menuItemId}");
        printResult("GET /menu/menu-items/{id}", $result['http_code'] === 200, "HTTP {$result['http_code']}");
        
        // Test PUT /menu-items/{id}
        $updateData = [
            'name' => 'Updated Test Menu Item',
            'price' => 18.99
        ];
        $result = makeRequest('PUT', $baseUrl . "/menu/menu-items/{$menuItemId}", $updateData);
        printResult("PUT /menu/menu-items/{id}", $result['http_code'] === 200, "HTTP {$result['http_code']}");
        
        // Test DELETE /menu-items/{id}
        $result = makeRequest('DELETE', $baseUrl . "/menu/menu-items/{$menuItemId}");
        printResult("DELETE /menu/menu-items/{id}", $result['http_code'] === 200, "HTTP {$result['http_code']}");
    }
}

/**
 * Test Addons API
 */
function testAddonsAPI() {
    global $baseUrl;
    
    printSection("ADDONS API");
    
    // Test GET /addons
    $result = makeRequest('GET', $baseUrl . '/menu/addons');
    printResult("GET /menu/addons", $result['http_code'] === 200, "HTTP {$result['http_code']}");
    
    // First create a menu and menu item to use for addon creation
    $menuData = [
        'name' => 'Test Menu for Addons',
        'code' => 'TEST_MENU_ADDONS_' . time(),
        'description' => 'A test menu for addon testing',
        'is_active' => true
    ];
    
    $menuResult = makeRequest('POST', $baseUrl . '/menu/menus', $menuData);
    $menuId = null;
    if ($menuResult['http_code'] === 201 && isset($menuResult['response']['data']['id'])) {
        $menuId = $menuResult['response']['data']['id'];
    }
    
    $menuItemData = [
        'menu_id' => $menuId,
        'name' => 'Test Menu Item for Addons',
        'code' => 'TEST_ITEM_ADDONS_' . time(),
        'description' => 'A test menu item for addon testing',
        'base_price' => 15.99,
        'is_active' => true
    ];
    
    $menuItemResult = makeRequest('POST', $baseUrl . '/menu/menu-items', $menuItemData);
    $menuItemId = null;
    if ($menuItemResult['http_code'] === 201 && isset($menuItemResult['response']['data']['id'])) {
        $menuItemId = $menuItemResult['response']['data']['id'];
    }
    
    // Test POST /addons (Create)
    $addonData = [
        'menu_item_id' => $menuItemId, // Required field
        'name' => 'Test Addon',
        'code' => 'TEST_ADDON_' . time(), // Required field
        'price' => 2.99, // Required field
        'is_required' => false,
        'max_quantity' => 5
    ];
    
    $result = makeRequest('POST', $baseUrl . '/menu/addons', $addonData);
    $addonId = null;
    if ($result['http_code'] === 201 && isset($result['response']['data']['id'])) {
        $addonId = $result['response']['data']['id'];
        printResult("POST /menu/addons", true, "Addon created with ID: {$addonId}");
    } else {
        printResult("POST /menu/addons", false, "HTTP {$result['http_code']}: " . ($result['response']['message'] ?? 'Unknown error'));
    }
    
    if ($addonId) {
        // Test GET /addons/{id}
        $result = makeRequest('GET', $baseUrl . "/menu/addons/{$addonId}");
        printResult("GET /menu/addons/{id}", $result['http_code'] === 200, "HTTP {$result['http_code']}");
        
        // Test PUT /addons/{id}
        $updateData = [
            'name' => 'Updated Test Addon',
            'price' => 3.99
        ];
        $result = makeRequest('PUT', $baseUrl . "/menu/addons/{$addonId}", $updateData);
        printResult("PUT /menu/addons/{id}", $result['http_code'] === 200, "HTTP {$result['http_code']}");
        
        // Test DELETE /addons/{id}
        $result = makeRequest('DELETE', $baseUrl . "/menu/addons/{$addonId}");
        printResult("DELETE /menu/addons/{id}", $result['http_code'] === 200, "HTTP {$result['http_code']}");
    }
}

/**
 * Test Variants API
 */
function testVariantsAPI() {
    global $baseUrl;
    
    printSection("VARIANTS API");
    
    // Test GET /variants
    $result = makeRequest('GET', $baseUrl . '/menu/variants');
    printResult("GET /menu/variants", $result['http_code'] === 200, "HTTP {$result['http_code']}");
    
    // First create a menu and menu item to use for variant creation
    $menuData = [
        'name' => 'Test Menu for Variants',
        'code' => 'TEST_MENU_VARIANTS_' . time(),
        'description' => 'A test menu for variant testing',
        'is_active' => true
    ];
    
    $menuResult = makeRequest('POST', $baseUrl . '/menu/menus', $menuData);
    $menuId = null;
    if ($menuResult['http_code'] === 201 && isset($menuResult['response']['data']['id'])) {
        $menuId = $menuResult['response']['data']['id'];
    }
    
    $menuItemData = [
        'menu_id' => $menuId,
        'name' => 'Test Menu Item for Variants',
        'code' => 'TEST_ITEM_VARIANTS_' . time(),
        'description' => 'A test menu item for variant testing',
        'base_price' => 15.99,
        'is_active' => true
    ];
    
    $menuItemResult = makeRequest('POST', $baseUrl . '/menu/menu-items', $menuItemData);
    $menuItemId = null;
    if ($menuItemResult['http_code'] === 201 && isset($menuItemResult['response']['data']['id'])) {
        $menuItemId = $menuItemResult['response']['data']['id'];
    }
    
    // Test POST /variants (Create)
    $variantData = [
        'menu_item_id' => $menuItemId, // Required field
        'name' => 'Test Variant',
        'code' => 'TEST_VARIANT_' . time(), // Required field
        'price_modifier' => 1.50, // Required field
        'is_default' => false
    ];
    
    $result = makeRequest('POST', $baseUrl . '/menu/variants', $variantData);
    $variantId = null;
    if ($result['http_code'] === 201 && isset($result['response']['data']['id'])) {
        $variantId = $result['response']['data']['id'];
        printResult("POST /menu/variants", true, "Variant created with ID: {$variantId}");
    } else {
        printResult("POST /menu/variants", false, "HTTP {$result['http_code']}: " . ($result['response']['message'] ?? 'Unknown error'));
    }
    
    if ($variantId) {
        // Test GET /variants/{id}
        $result = makeRequest('GET', $baseUrl . "/menu/variants/{$variantId}");
        printResult("GET /menu/variants/{id}", $result['http_code'] === 200, "HTTP {$result['http_code']}");
        
        // Test PUT /variants/{id}
        $updateData = [
            'name' => 'Updated Test Variant',
            'price_modifier' => 2.00
        ];
        $result = makeRequest('PUT', $baseUrl . "/menu/variants/{$variantId}", $updateData);
        printResult("PUT /menu/variants/{id}", $result['http_code'] === 200, "HTTP {$result['http_code']}");
        
        // Test DELETE /variants/{id}
        $result = makeRequest('DELETE', $baseUrl . "/menu/variants/{$variantId}");
        printResult("DELETE /menu/variants/{id}", $result['http_code'] === 200, "HTTP {$result['http_code']}");
    }
}

/**
 * Test Banners API
 */
function testBannersAPI() {
    global $baseUrl;
    
    printSection("BANNERS API");
    
    // Test GET /banners
    $result = makeRequest('GET', $baseUrl . '/menu/banners');
    printResult("GET /menu/banners", $result['http_code'] === 200, "HTTP {$result['http_code']}");
    
    // Test GET /banners/display
    $result = makeRequest('GET', $baseUrl . '/menu/banners/display');
    printResult("GET /menu/banners/display", $result['http_code'] === 200, "HTTP {$result['http_code']}");
    
    // Test GET /banners/featured
    $result = makeRequest('GET', $baseUrl . '/menu/banners/featured');
    printResult("GET /menu/banners/featured", $result['http_code'] === 200, "HTTP {$result['http_code']}");
    
    // Test POST /banners (Create)
    $bannerData = [
        'tenant_id' => 1,
        'branch_id' => 1,
        'title' => 'Test Banner',
        'description' => 'A test banner for API testing',
        'banner_type' => 'promotional',
        'position' => 'top',
        'display_location' => 'homepage',
        'image_url' => 'https://example.com/banner.jpg',
        'start_date' => date('Y-m-d', strtotime('+1 day')),
        'end_date' => date('Y-m-d', strtotime('+7 days')),
        'priority' => 'medium',
        'is_active' => true,
        'is_featured' => false
    ];
    
    $result = makeRequest('POST', $baseUrl . '/menu/banners', $bannerData);
    $bannerId = null;
    if ($result['http_code'] === 201 && isset($result['response']['data']['id'])) {
        $bannerId = $result['response']['data']['id'];
        printResult("POST /menu/banners", true, "Banner created with ID: {$bannerId}");
    } else {
        printResult("POST /menu/banners", false, "HTTP {$result['http_code']}: " . ($result['response']['message'] ?? 'Unknown error'));
    }
    
    if ($bannerId) {
        // Test GET /banners/{id}
        $result = makeRequest('GET', $baseUrl . "/menu/banners/{$bannerId}");
        printResult("GET /menu/banners/{id}", $result['http_code'] === 200, "HTTP {$result['http_code']}");
        
        // Test PUT /banners/{id}
        $updateData = [
            'title' => 'Updated Test Banner',
            'is_featured' => true
        ];
        $result = makeRequest('PUT', $baseUrl . "/menu/banners/{$bannerId}", $updateData);
        printResult("PUT /menu/banners/{id}", $result['http_code'] === 200, "HTTP {$result['http_code']}");
        
        // Test DELETE /banners/{id}
        $result = makeRequest('DELETE', $baseUrl . "/menu/banners/{$bannerId}");
        printResult("DELETE /menu/banners/{id}", $result['http_code'] === 200, "HTTP {$result['http_code']}");
    }
}

/**
 * Test Events API
 */
function testEventsAPI() {
    global $baseUrl;
    
    printSection("EVENTS API");
    
    // Test GET /events
    $result = makeRequest('GET', $baseUrl . '/menu/events');
    printResult("GET /menu/events", $result['http_code'] === 200, "HTTP {$result['http_code']}");
    
    // Test GET /events/featured
    $result = makeRequest('GET', $baseUrl . '/menu/events/featured');
    printResult("GET /menu/events/featured", $result['http_code'] === 200, "HTTP {$result['http_code']}");
    
    // Test GET /events/upcoming
    $result = makeRequest('GET', $baseUrl . '/menu/events/upcoming');
    printResult("GET /menu/events/upcoming", $result['http_code'] === 200, "HTTP {$result['http_code']}");
    
    // Test POST /events (Create)
     $eventData = [
         'tenant_id' => 1,
         'branch_id' => 1,
         'name' => 'Test Event',
         'description' => 'A test event for API testing',
         'event_type' => 'dining',
         'start_date' => date('Y-m-d', strtotime('+1 day')),
         'end_date' => date('Y-m-d', strtotime('+2 days')),
        'is_active' => true,
        'is_featured' => false
    ];
    
    $result = makeRequest('POST', $baseUrl . '/menu/events', $eventData);
    $eventId = null;
    if ($result['http_code'] === 201 && isset($result['response']['data']['id'])) {
        $eventId = $result['response']['data']['id'];
        printResult("POST /menu/events", true, "Event created with ID: {$eventId}");
    } else {
        printResult("POST /menu/events", false, "HTTP {$result['http_code']}: " . ($result['response']['message'] ?? 'Unknown error'));
    }
    
    if ($eventId) {
        // Test GET /events/{id}
        $result = makeRequest('GET', $baseUrl . "/menu/events/{$eventId}");
        printResult("GET /menu/events/{id}", $result['http_code'] === 200, "HTTP {$result['http_code']}");
        
        // Test POST /events/{id}/register
        $result = makeRequest('POST', $baseUrl . "/menu/events/{$eventId}/register");
        printResult("POST /menu/events/{id}/register", in_array($result['http_code'], [200, 201, 422]), "HTTP {$result['http_code']}");
        
        // Test PUT /events/{id}
        $updateData = [
            'title' => 'Updated Test Event',
            'is_featured' => true
        ];
        $result = makeRequest('PUT', $baseUrl . "/menu/events/{$eventId}", $updateData);
        printResult("PUT /menu/events/{id}", $result['http_code'] === 200, "HTTP {$result['http_code']}");
        
        // Test DELETE /events/{id}
        $result = makeRequest('DELETE', $baseUrl . "/menu/events/{$eventId}");
        printResult("DELETE /menu/events/{id}", $result['http_code'] === 200, "HTTP {$result['http_code']}");
    }
}

/**
 * Test Offers API
 */
function testOffersAPI() {
    global $baseUrl;
    
    printSection("OFFERS API");
    
    // Test GET /offers
    $result = makeRequest('GET', $baseUrl . '/menu/offers');
    printResult("GET /menu/offers", $result['http_code'] === 200, "HTTP {$result['http_code']}");
    
    // Test GET /offers/featured
    $result = makeRequest('GET', $baseUrl . '/menu/offers/featured');
    printResult("GET /menu/offers/featured", $result['http_code'] === 200, "HTTP {$result['http_code']}");
    
    // Test POST /offers (Create)
     $offerData = [
          'tenant_id' => 1,
          'branch_id' => 1,
          'name' => 'Test Offer',
          'title' => 'Test Offer Title',
          'code' => 'TEST_OFFER_' . time(),
          'description' => 'A test offer for API testing',
          'type' => 'offer',
          'offer_type' => 'percentage',
          'discount_type' => 'percentage',
          'discount_value' => 10.00,
          'max_discount_amount' => 50.00,
          'start_date' => date('Y-m-d'),
          'end_date' => date('Y-m-d', strtotime('+7 days')),
          'priority' => 'medium',
        'is_active' => true,
        'is_featured' => false
    ];
    
    $result = makeRequest('POST', $baseUrl . '/menu/offers', $offerData);
    $offerId = null;
    if ($result['http_code'] === 201 && isset($result['response']['data']['id'])) {
        $offerId = $result['response']['data']['id'];
        printResult("POST /menu/offers", true, "Offer created with ID: {$offerId}");
    } else {
        printResult("POST /menu/offers", false, "HTTP {$result['http_code']}: " . ($result['response']['message'] ?? 'Unknown error'));
    }
    
    if ($offerId) {
        // Test GET /offers/{id}
        $result = makeRequest('GET', $baseUrl . "/menu/offers/{$offerId}");
        printResult("GET /menu/offers/{id}", $result['http_code'] === 200, "HTTP {$result['http_code']}");
        
        // Test POST /offers/{id}/validate
        $validateData = ['order_total' => 50.00];
        $result = makeRequest('POST', $baseUrl . "/menu/offers/{$offerId}/validate", $validateData);
        printResult("POST /menu/offers/{id}/validate", in_array($result['http_code'], [200, 422]), "HTTP {$result['http_code']}");
        
        // Test POST /offers/{id}/apply
        $applyData = ['order_total' => 50.00];
        $result = makeRequest('POST', $baseUrl . "/menu/offers/{$offerId}/apply", $applyData);
        printResult("POST /menu/offers/{id}/apply", in_array($result['http_code'], [200, 422]), "HTTP {$result['http_code']}");
        
        // Test PUT /offers/{id}
        $updateData = [
            'title' => 'Updated Test Offer',
            'discount_value' => 15.00
        ];
        $result = makeRequest('PUT', $baseUrl . "/menu/offers/{$offerId}", $updateData);
        printResult("PUT /menu/offers/{id}", $result['http_code'] === 200, "HTTP {$result['http_code']}");
        
        // Test DELETE /offers/{id}
        $result = makeRequest('DELETE', $baseUrl . "/menu/offers/{$offerId}");
        printResult("DELETE /menu/offers/{id}", $result['http_code'] === 200, "HTTP {$result['http_code']}");
    }
}

/**
 * Test Public APIs (no authentication required)
 */
function testPublicAPIs() {
    global $baseUrl;
    
    printSection("PUBLIC APIs (No Authentication)");
    
    // Test public endpoints without authentication
    $publicEndpoints = [
        'GET /menu/categories/public' => '/menu/categories/public',
        'GET /menu/menus/public' => '/menu/menus/public',
        'GET /menu/menu-items/public' => '/menu/menu-items/public',
        'GET /menu/banners/display' => '/menu/banners/display',
        'GET /menu/events/upcoming' => '/menu/events/upcoming',
        'GET /menu/offers/featured' => '/menu/offers/featured'
    ];
    
    foreach ($publicEndpoints as $name => $endpoint) {
        $result = makeRequest('GET', $baseUrl . $endpoint, null, false);
        printResult($name, in_array($result['http_code'], [200, 404]), "HTTP {$result['http_code']}");
    }
}

/**
 * Main execution
 */
function main() {
    echo Colors::BLUE . "Menu Module API Test Suite" . Colors::RESET . "\n";
    echo "Testing all API endpoints in the Menu module\n";
    echo "Base URL: " . $GLOBALS['baseUrl'] . "\n";
    
    // Step 1: Authenticate
    if (!authenticate()) {
        echo Colors::RED . "\nAuthentication failed. Cannot proceed with API tests." . Colors::RESET . "\n";
        return;
    }
    
    // Step 2: Test all API endpoints
    testCategoriesAPI();
    testMenusAPI();
    testMenuItemsAPI();
    testAddonsAPI();
    testVariantsAPI();
    testBannersAPI();
    testEventsAPI();
    testOffersAPI();
    testPublicAPIs();
    
    // Summary
    printSection("TEST COMPLETE");
    echo Colors::GREEN . "All Menu module API tests have been executed." . Colors::RESET . "\n";
    echo Colors::YELLOW . "Note: Some tests may fail if required database tables or relationships are missing." . Colors::RESET . "\n";
    echo Colors::YELLOW . "Check the individual test results above for specific issues." . Colors::RESET . "\n";
}

// Run the tests
main();

?>