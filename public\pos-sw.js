/**
 * POS Service Worker - Handles offline functionality and caching
 * Provides cache-first strategy for static assets and network-first for API calls
 */

const CACHE_NAME = 'pos-cache-v1';
const STATIC_CACHE_NAME = 'pos-static-v1';
const DYNAMIC_CACHE_NAME = 'pos-dynamic-v1';

// Static assets to cache immediately
const STATIC_ASSETS = [
    '/assets/css/pos.css',
    '/assets/js/pos/pos-core.js',
    '/assets/js/pos/pos-storage.js',
    '/assets/js/pos/pos-sync.js',
    '/assets/js/pos/pos-ui.js',
    '/pos-manifest.json',
    // Add other static assets as needed
];

// API routes that should be cached
const API_CACHE_PATTERNS = [
    /\/api\/menu-items/,
    /\/api\/customers/,
    /\/api\/tables/,
    /\/api\/delivery-personnel/
];

// Routes that should never be cached
const NEVER_CACHE_PATTERNS = [
    /\/orders\/store/,
    /\/api\/orders/,
    /\/logout/,
    /\/login/
];

/**
 * Service Worker Install Event
 */
self.addEventListener('install', event => {
    console.log('POS Service Worker installing...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE_NAME)
            .then(cache => {
                console.log('Caching static assets...');
                return cache.addAll(STATIC_ASSETS);
            })
            .then(() => {
                console.log('Static assets cached successfully');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('Failed to cache static assets:', error);
            })
    );
});

/**
 * Service Worker Activate Event
 */
self.addEventListener('activate', event => {
    console.log('POS Service Worker activating...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        // Delete old caches
                        if (cacheName !== STATIC_CACHE_NAME && 
                            cacheName !== DYNAMIC_CACHE_NAME &&
                            cacheName.startsWith('pos-')) {
                            console.log('Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('POS Service Worker activated');
                return self.clients.claim();
            })
    );
});

/**
 * Service Worker Fetch Event
 */
self.addEventListener('fetch', event => {
    const request = event.request;
    const url = new URL(request.url);
    
    // Skip non-GET requests for caching
    if (request.method !== 'GET') {
        return;
    }
    
    // Skip chrome-extension and other non-http requests
    if (!request.url.startsWith('http')) {
        return;
    }
    
    // Check if this route should never be cached
    if (NEVER_CACHE_PATTERNS.some(pattern => pattern.test(url.pathname))) {
        return;
    }
    
    // Handle different types of requests
    if (isStaticAsset(url)) {
        event.respondWith(handleStaticAsset(request));
    } else if (isAPIRequest(url)) {
        event.respondWith(handleAPIRequest(request));
    } else if (isPageRequest(request)) {
        event.respondWith(handlePageRequest(request));
    }
});

/**
 * Handle static asset requests (cache-first strategy)
 */
function handleStaticAsset(request) {
    return caches.match(request)
        .then(cachedResponse => {
            if (cachedResponse) {
                return cachedResponse;
            }
            
            return fetch(request)
                .then(response => {
                    // Cache successful responses
                    if (response.status === 200) {
                        const responseClone = response.clone();
                        caches.open(STATIC_CACHE_NAME)
                            .then(cache => cache.put(request, responseClone));
                    }
                    return response;
                })
                .catch(() => {
                    // Return offline fallback for images
                    if (request.destination === 'image') {
                        return new Response(
                            '<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200"><rect width="200" height="200" fill="#f0f0f0"/><text x="100" y="100" text-anchor="middle" dy=".3em" fill="#999">Image Offline</text></svg>',
                            { headers: { 'Content-Type': 'image/svg+xml' } }
                        );
                    }
                    throw error;
                });
        });
}

/**
 * Handle API requests (network-first with cache fallback)
 */
function handleAPIRequest(request) {
    return fetch(request)
        .then(response => {
            // Cache successful GET responses
            if (response.status === 200 && request.method === 'GET') {
                const responseClone = response.clone();
                caches.open(DYNAMIC_CACHE_NAME)
                    .then(cache => cache.put(request, responseClone));
            }
            return response;
        })
        .catch(() => {
            // Try to serve from cache
            return caches.match(request)
                .then(cachedResponse => {
                    if (cachedResponse) {
                        // Add a header to indicate this is from cache
                        const response = cachedResponse.clone();
                        response.headers.set('X-Served-From', 'cache');
                        return response;
                    }
                    
                    // Return offline response for API calls
                    return new Response(
                        JSON.stringify({
                            error: 'Offline',
                            message: 'This data is not available offline'
                        }),
                        {
                            status: 503,
                            headers: { 'Content-Type': 'application/json' }
                        }
                    );
                });
        });
}

/**
 * Handle page requests (network-first with cache fallback)
 */
function handlePageRequest(request) {
    return fetch(request)
        .then(response => {
            // Cache successful page responses
            if (response.status === 200) {
                const responseClone = response.clone();
                caches.open(DYNAMIC_CACHE_NAME)
                    .then(cache => cache.put(request, responseClone));
            }
            return response;
        })
        .catch(() => {
            // Try to serve from cache
            return caches.match(request)
                .then(cachedResponse => {
                    if (cachedResponse) {
                        return cachedResponse;
                    }
                    
                    // Return offline page
                    return caches.match('/offline.html')
                        .then(offlinePage => {
                            return offlinePage || new Response(
                                '<!DOCTYPE html><html><head><title>Offline</title></head><body><h1>You are offline</h1><p>Please check your connection and try again.</p></body></html>',
                                { headers: { 'Content-Type': 'text/html' } }
                            );
                        });
                });
        });
}

/**
 * Background Sync Event
 */
self.addEventListener('sync', event => {
    console.log('Background sync triggered:', event.tag);
    
    if (event.tag === 'pos-sync') {
        event.waitUntil(syncPOSData());
    }
});

/**
 * Sync POS data in background
 */
async function syncPOSData() {
    try {
        console.log('Starting background sync...');
        
        // Get all clients (open tabs)
        const clients = await self.clients.matchAll();
        
        if (clients.length > 0) {
            // Send message to main app to trigger sync
            clients[0].postMessage({
                type: 'BACKGROUND_SYNC',
                action: 'sync-all'
            });
        }
        
        console.log('Background sync completed');
    } catch (error) {
        console.error('Background sync failed:', error);
        throw error;
    }
}

/**
 * Push Event (for future notifications)
 */
self.addEventListener('push', event => {
    console.log('Push event received:', event);
    
    const options = {
        body: event.data ? event.data.text() : 'New notification',
        icon: '/assets/images/pos-icon-192.png',
        badge: '/assets/images/pos-badge-72.png',
        vibrate: [100, 50, 100],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1
        },
        actions: [
            {
                action: 'explore',
                title: 'View',
                icon: '/assets/images/checkmark.png'
            },
            {
                action: 'close',
                title: 'Close',
                icon: '/assets/images/xmark.png'
            }
        ]
    };
    
    event.waitUntil(
        self.registration.showNotification('POS System', options)
    );
});

/**
 * Notification Click Event
 */
self.addEventListener('notificationclick', event => {
    console.log('Notification clicked:', event);
    
    event.notification.close();
    
    if (event.action === 'explore') {
        // Open the app
        event.waitUntil(
            clients.openWindow('/pos/create')
        );
    }
});

/**
 * Message Event (communication with main app)
 */
self.addEventListener('message', event => {
    console.log('Service Worker received message:', event.data);
    
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
});

/**
 * Utility Functions
 */

function isStaticAsset(url) {
    return url.pathname.includes('/assets/') || 
           url.pathname.includes('/css/') || 
           url.pathname.includes('/js/') ||
           url.pathname.includes('/images/') ||
           url.pathname.endsWith('.css') ||
           url.pathname.endsWith('.js') ||
           url.pathname.endsWith('.png') ||
           url.pathname.endsWith('.jpg') ||
           url.pathname.endsWith('.jpeg') ||
           url.pathname.endsWith('.gif') ||
           url.pathname.endsWith('.svg') ||
           url.pathname.endsWith('.woff') ||
           url.pathname.endsWith('.woff2') ||
           url.pathname.endsWith('.ttf');
}

function isAPIRequest(url) {
    return url.pathname.startsWith('/api/') ||
           API_CACHE_PATTERNS.some(pattern => pattern.test(url.pathname));
}

function isPageRequest(request) {
    return request.headers.get('accept') && 
           request.headers.get('accept').includes('text/html');
}
