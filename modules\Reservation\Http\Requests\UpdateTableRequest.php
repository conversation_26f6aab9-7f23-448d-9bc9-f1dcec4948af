<?php

namespace Modules\Reservation\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateTableRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $tableId = $this->route('table') ?? $this->route('id');
        
        return [
            'area_id' => 'nullable|integer|exists:areas,id',
            'table_number' => [
                'sometimes',
                'required',
                'string',
                'max:50',
                Rule::unique('tables')->where(function ($query) {
                    return $query->where('branch_id', $this->branch_id ?? null);
                })->ignore($tableId)
            ],
            'table_name' => 'nullable|string|max:255',
            'seating_capacity' => 'sometimes|required|integer|min:1|max:50',
            'section' => 'nullable|string|max:100',
            'status' => 'nullable|string|in:available,occupied,reserved,cleaning,out_of_order',
            'qr_code' => [
                'nullable',
                'string',
                'max:255',
                Rule::unique('tables')->ignore($tableId)
            ],
            'position_coordinates' => 'nullable|json',
            'notes' => 'nullable|string|max:1000',
            'is_active' => 'nullable|boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'area_id.exists' => 'Selected area does not exist.',
            'table_number.required' => 'Table number is required.',
            'table_number.unique' => 'This table number already exists in the branch.',
            'table_number.max' => 'Table number cannot exceed 50 characters.',
            'table_name.max' => 'Table name cannot exceed 255 characters.',
            'seating_capacity.required' => 'Seating capacity is required.',
            'seating_capacity.min' => 'Seating capacity must be at least 1.',
            'seating_capacity.max' => 'Seating capacity cannot exceed 50.',
            'section.max' => 'Section cannot exceed 100 characters.',
            'status.in' => 'Invalid table status.',
            'qr_code.unique' => 'This QR code is already in use.',
            'position_coordinates.json' => 'Position coordinates must be valid JSON.',
            'notes.max' => 'Notes cannot exceed 1000 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'area_id' => 'area',
            'table_number' => 'table number',
            'table_name' => 'table name',
            'seating_capacity' => 'seating capacity',
            'qr_code' => 'QR code',
            'position_coordinates' => 'position coordinates',
            'is_active' => 'active status',
        ];
    }
}