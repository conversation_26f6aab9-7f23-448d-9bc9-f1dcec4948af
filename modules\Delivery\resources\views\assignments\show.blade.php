@extends('layouts.master')

@section('content')
<!-- Page Header -->
<div class="mb-6">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="mb-4 sm:mb-0">
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-truck mr-3"></i>
                تفاصيل تعيين التوصيل #{{ $assignment->id }}
            </h1>
            <p class="text-sm text-gray-600 mt-1">عرض تفاصيل تعيين التوصيل والحالة الحالية</p>
        </div>
        <div>
            <a href="{{ route('delivery.assignments.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-md transition-colors duration-200">
                <i class="fas fa-arrow-left mr-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Assignment Details -->
    <div class="lg:col-span-2">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">معلومات التعيين</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-500">رقم الطلب:</span>
                            <span class="text-sm text-gray-900">{{ $assignment->order->order_number ?? 'غير متوفر' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-500">الحالة:</span>
                            <span>
                                @php
                                    $statusColors = [
                                        'pending' => 'bg-yellow-100 text-yellow-800',
                                        'assigned' => 'bg-blue-100 text-blue-800',
                                        'picked_up' => 'bg-indigo-100 text-indigo-800',
                                        'in_transit' => 'bg-purple-100 text-purple-800',
                                        'delivered' => 'bg-green-100 text-green-800',
                                        'cancelled' => 'bg-red-100 text-red-800',
                                        'failed' => 'bg-red-100 text-red-800'
                                    ];
                                    $statusLabels = [
                                        'pending' => 'في الانتظار',
                                        'assigned' => 'معين',
                                        'picked_up' => 'تم الاستلام',
                                        'in_transit' => 'في الطريق',
                                        'delivered' => 'تم التوصيل',
                                        'cancelled' => 'ملغي',
                                        'failed' => 'فشل'
                                    ];
                                    $color = $statusColors[$assignment->status] ?? 'bg-gray-100 text-gray-800';
                                    $label = $statusLabels[$assignment->status] ?? $assignment->status;
                                @endphp
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $color }}">
                                    {{ $label }}
                                </span>
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-500">تاريخ التعيين:</span>
                            <span class="text-sm text-gray-900">{{ $assignment->assigned_at ? $assignment->assigned_at->format('Y-m-d H:i') : 'غير معين' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-500">التوصيل المتوقع:</span>
                            <span class="text-sm text-gray-900">{{ $assignment->estimated_delivery_time ? $assignment->estimated_delivery_time->format('Y-m-d H:i') : 'غير محدد' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-500">التوصيل الفعلي:</span>
                            <span class="text-sm text-gray-900">{{ $assignment->delivered_at ? $assignment->delivered_at->format('Y-m-d H:i') : 'لم يتم التوصيل' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-500">رسوم التوصيل:</span>
                            <span class="text-sm text-gray-900">{{ number_format($assignment->delivery_fee ?? 0, 2) }} ريال</span>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-500">الفرع:</span>
                            <span class="text-sm text-gray-900">{{ $assignment->order->branch->name ?? 'غير متوفر' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-500">العميل:</span>
                            <span class="text-sm text-gray-900">{{ $assignment->order->customer->name ?? 'ضيف' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-500">هاتف العميل:</span>
                            <span class="text-sm text-gray-900">{{ $assignment->order->customer->phone ?? $assignment->order->customer_phone ?? 'غير متوفر' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-500">موظف التوصيل:</span>
                            <span class="text-sm text-gray-900">{{ $assignment->personnel->name ?? 'غير معين' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-500">هاتف الموظف:</span>
                            <span class="text-sm text-gray-900">{{ $assignment->personnel->phone ?? 'غير متوفر' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm font-medium text-gray-500">نوع المركبة:</span>
                            <span class="text-sm text-gray-900">{{ $assignment->personnel->vehicle_type ?? 'غير متوفر' }}</span>
                        </div>
                    </div>
                </div>

                @if($assignment->notes)
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <h4 class="text-sm font-medium text-gray-900 mb-2">ملاحظات:</h4>
                    <div class="bg-blue-50 border border-blue-200 rounded-md p-3">
                        <p class="text-sm text-blue-800">{{ $assignment->notes }}</p>
                    </div>
                </div>
                @endif
            </div>
        </div>

                    <!-- Delivery Address -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Delivery Address</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Address:</strong><br>
                                    {{ $assignment->delivery_address ?? $assignment->order->delivery_address ?? 'N/A' }}</p>
                                    
                                    @if($assignment->delivery_latitude && $assignment->delivery_longitude)
                                    <p><strong>Coordinates:</strong><br>
                                    Lat: {{ $assignment->delivery_latitude }}, Lng: {{ $assignment->delivery_longitude }}</p>
                                    @endif
                                </div>
                                <div class="col-md-6">
                                    <div id="deliveryMap" style="height: 200px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px;">
                                        <div class="d-flex align-items-center justify-content-center h-100">
                                            <div class="text-center">
                                                <i class="fas fa-map-marker-alt fa-2x text-muted mb-2"></i>
                                                <p class="text-muted">Map view</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Order Items -->
                    @if($assignment->order && $assignment->order->orderItems)
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Order Items</h3>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Item</th>
                                            <th>Quantity</th>
                                            <th>Unit Price</th>
                                            <th>Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($assignment->order->orderItems as $item)
                                        <tr>
                                            <td>{{ $item->menuItem->name ?? 'Unknown Item' }}</td>
                                            <td>{{ $item->quantity }}</td>
                                            <td>${{ number_format($item->unit_price, 2) }}</td>
                                            <td>${{ number_format($item->total_price, 2) }}</td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <th colspan="3">Subtotal:</th>
                                            <th>${{ number_format($assignment->order->subtotal ?? 0, 2) }}</th>
                                        </tr>
                                        <tr>
                                            <th colspan="3">Delivery Fee:</th>
                                            <th>${{ number_format($assignment->delivery_fee ?? 0, 2) }}</th>
                                        </tr>
                                        <tr>
                                            <th colspan="3">Total:</th>
                                            <th>${{ number_format(($assignment->order->total ?? 0) + ($assignment->delivery_fee ?? 0), 2) }}</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>

                <!-- Actions & Tracking -->
                <div class="col-md-4">
                    <!-- Quick Actions -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Quick Actions</h3>
                        </div>
                        <div class="card-body">
                            @if($assignment->status === 'pending')
                            <button type="button" class="btn btn-success btn-block assign-personnel" 
                                    data-assignment-id="{{ $assignment->id }}">
                                <i class="fas fa-user-plus"></i> Assign Personnel
                            </button>
                            @endif

                            @if(in_array($assignment->status, ['assigned', 'picked_up', 'in_transit']))
                            <button type="button" class="btn btn-primary btn-block update-status" 
                                    data-assignment-id="{{ $assignment->id }}">
                                <i class="fas fa-edit"></i> Update Status
                            </button>
                            
                            <button type="button" class="btn btn-info btn-block track-assignment" 
                                    data-assignment-id="{{ $assignment->id }}">
                                <i class="fas fa-map-marker-alt"></i> Track Delivery
                            </button>
                            @endif

                            @if($assignment->personnel)
                            <a href="tel:{{ $assignment->personnel->phone }}" class="btn btn-warning btn-block">
                                <i class="fas fa-phone"></i> Call Personnel
                            </a>
                            @endif

                            @if($assignment->order->customer)
                            <a href="tel:{{ $assignment->order->customer->phone ?? $assignment->order->customer_phone }}" 
                               class="btn btn-secondary btn-block">
                                <i class="fas fa-phone"></i> Call Customer
                            </a>
                            @endif
                        </div>
                    </div>

                    <!-- Tracking History -->
                    @if($assignment->tracking && $assignment->tracking->count() > 0)
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Tracking History</h3>
                        </div>
                        <div class="card-body">
                            <div class="timeline">
                                @foreach($assignment->tracking->sortByDesc('recorded_at') as $track)
                                <div class="time-label">
                                    <span class="bg-blue">{{ $track->recorded_at->format('M d, H:i') }}</span>
                                </div>
                                <div>
                                    <i class="fas fa-map-marker-alt bg-blue"></i>
                                    <div class="timeline-item">
                                        <div class="timeline-body">
                                            <strong>Location Update</strong><br>
                                            @if($track->latitude && $track->longitude)
                                            Lat: {{ $track->latitude }}, Lng: {{ $track->longitude }}<br>
                                            @endif
                                            @if($track->address)
                                            Address: {{ $track->address }}<br>
                                            @endif
                                            Speed: {{ $track->speed ?? 0 }} km/h
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Assignment Timeline -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Assignment Timeline</h3>
                        </div>
                        <div class="card-body">
                            <div class="timeline">
                                @if($assignment->assigned_at)
                                <div class="time-label">
                                    <span class="bg-green">{{ $assignment->assigned_at->format('M d, H:i') }}</span>
                                </div>
                                <div>
                                    <i class="fas fa-user-plus bg-green"></i>
                                    <div class="timeline-item">
                                        <div class="timeline-body">
                                            Assignment created and personnel assigned
                                        </div>
                                    </div>
                                </div>
                                @endif

                                @if($assignment->picked_up_at)
                                <div class="time-label">
                                    <span class="bg-yellow">{{ $assignment->picked_up_at->format('M d, H:i') }}</span>
                                </div>
                                <div>
                                    <i class="fas fa-box bg-yellow"></i>
                                    <div class="timeline-item">
                                        <div class="timeline-body">
                                            Order picked up from restaurant
                                        </div>
                                    </div>
                                </div>
                                @endif

                                @if($assignment->delivered_at)
                                <div class="time-label">
                                    <span class="bg-blue">{{ $assignment->delivered_at->format('M d, H:i') }}</span>
                                </div>
                                <div>
                                    <i class="fas fa-check-circle bg-blue"></i>
                                    <div class="timeline-item">
                                        <div class="timeline-body">
                                            Order delivered to customer
                                        </div>
                                    </div>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include modals from index page -->
@include('delivery::assignments.partials.modals')
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Assign Personnel Modal
    $(document).on('click', '.assign-personnel', function() {
        const assignmentId = $(this).data('assignment-id');
        $('#assignmentId').val(assignmentId);
        
        // Load available personnel
        $.get('{{ route("delivery.assignments.available-personnel") }}')
        .done(function(personnel) {
            const select = $('#personnelSelect');
            select.empty().append('<option value="">Choose personnel...</option>');
            
            personnel.forEach(function(person) {
                select.append(`<option value="${person.id}">${person.name} (${person.vehicle_type})</option>`);
            });
            
            $('#assignPersonnelModal').modal('show');
        })
        .fail(function() {
            toastr.error('Failed to load available personnel');
        });
    });

    // Confirm assignment
    $('#confirmAssign').on('click', function() {
        const assignmentId = $('#assignmentId').val();
        const personnelId = $('#personnelSelect').val();
        
        if (!personnelId) {
            toastr.error('Please select personnel');
            return;
        }

        $.post(`/delivery/assignments/${assignmentId}/assign-personnel`, {
            personnel_id: personnelId,
            _token: '{{ csrf_token() }}'
        })
        .done(function(response) {
            if (response.success) {
                toastr.success(response.message);
                $('#assignPersonnelModal').modal('hide');
                location.reload(); // Reload to show updated assignment
            } else {
                toastr.error(response.message);
            }
        })
        .fail(function() {
            toastr.error('Failed to assign personnel');
        });
    });

    // Update Status Modal
    $(document).on('click', '.update-status', function() {
        const assignmentId = $(this).data('assignment-id');
        $('#statusAssignmentId').val(assignmentId);
        $('#updateStatusModal').modal('show');
    });

    // Confirm status update
    $('#confirmStatusUpdate').on('click', function() {
        const assignmentId = $('#statusAssignmentId').val();
        const status = $('#statusSelect').val();
        const notes = $('#statusNotes').val();
        
        if (!status) {
            toastr.error('Please select a status');
            return;
        }

        $.post(`/delivery/assignments/${assignmentId}/update-status`, {
            status: status,
            notes: notes,
            _token: '{{ csrf_token() }}'
        })
        .done(function(response) {
            if (response.success) {
                toastr.success(response.message);
                $('#updateStatusModal').modal('hide');
                location.reload(); // Reload to show updated status
            } else {
                toastr.error(response.message);
            }
        })
        .fail(function() {
            toastr.error('Failed to update status');
        });
    });

    // Track Assignment Modal
    $(document).on('click', '.track-assignment', function() {
        const assignmentId = $(this).data('assignment-id');
        
        // Load tracking information
        $.get(`/delivery/tracking/assignment/${assignmentId}`)
        .done(function(response) {
            if (response.success) {
                const data = response.data;
                $('#trackingStatus').text(data.status || '-');
                $('#trackingPersonnel').text(data.personnel_name || '-');
                $('#trackingLastUpdate').text(data.last_update || '-');
                $('#trackingEstimated').text(data.estimated_delivery || '-');
                
                $('#trackAssignmentModal').modal('show');
            } else {
                toastr.error(response.message);
            }
        })
        .fail(function() {
            toastr.error('Failed to load tracking information');
        });
    });
});
</script>
@endpush