@extends('layouts.master')

@section('title', 'Business Settings')

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
@endpush

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- <PERSON> Header -->
    <div class="bg-gradient-to-r from-green-600 to-teal-600 rounded-lg shadow-lg p-6 mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                            <i class="fas fa-briefcase text-white text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <h1 class="text-2xl font-bold text-white">Business Settings</h1>
                        <p class="text-green-100">Configure your business operations and preferences</p>
                    </div>
                </div>
            </div>
            <div class="mt-4 md:mt-0">
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="inline-flex items-center space-x-1 md:space-x-3">
                        <li class="inline-flex items-center">
                            <a href="{{ route('dashboard') }}" class="text-green-100 hover:text-white transition-colors duration-200">
                                <i class="fas fa-home mr-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i class="fas fa-chevron-right text-green-200 mx-2"></i>
                                <a href="{{ route('settings.index') }}" class="text-green-100 hover:text-white">Settings</a>
                            </div>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i class="fas fa-chevron-right text-green-200 mx-2"></i>
                                <span class="text-white font-medium">Business Settings</span>
                            </div>
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Settings Form -->
    <div class="bg-white rounded-lg shadow-lg">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Business Configuration</h3>
            <p class="text-gray-600 mt-1">Manage your business operations, tax settings, and service preferences</p>
        </div>

        <form class="p-6 space-y-8">
            @csrf
            
            <!-- Business Type & Category -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="business_type" class="block text-sm font-medium text-gray-700 mb-2">
                        Business Type <span class="text-red-500">*</span>
                    </label>
                    <select id="business_type" name="business_type" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" required>
                        <option value="">Select business type</option>
                        <option value="restaurant">Restaurant</option>
                        <option value="cafe">Cafe</option>
                        <option value="fast_food">Fast Food</option>
                        <option value="bakery">Bakery</option>
                        <option value="bar">Bar</option>
                        <option value="food_truck">Food Truck</option>
                    </select>
                </div>

                <div>
                    <label for="cuisine_type" class="block text-sm font-medium text-gray-700 mb-2">
                        Cuisine Type
                    </label>
                    <select id="cuisine_type" name="cuisine_type" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                        <option value="">Select cuisine type</option>
                        <option value="italian">Italian</option>
                        <option value="chinese">Chinese</option>
                        <option value="indian">Indian</option>
                        <option value="mexican">Mexican</option>
                        <option value="american">American</option>
                        <option value="mediterranean">Mediterranean</option>
                        <option value="asian">Asian</option>
                        <option value="international">International</option>
                    </select>
                </div>
            </div>

            <!-- Tax Configuration -->
            <div class="border-t border-gray-200 pt-6">
                <h4 class="text-lg font-medium text-gray-900 mb-4">Tax Configuration</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label for="tax_rate" class="block text-sm font-medium text-gray-700 mb-2">
                            Default Tax Rate (%)
                        </label>
                        <input type="number" id="tax_rate" name="tax_rate" step="0.01" min="0" max="100"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                               placeholder="0.00">
                    </div>

                    <div>
                        <label for="service_charge" class="block text-sm font-medium text-gray-700 mb-2">
                            Service Charge (%)
                        </label>
                        <input type="number" id="service_charge" name="service_charge" step="0.01" min="0" max="100"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                               placeholder="0.00">
                    </div>

                    <div>
                        <label for="tax_number" class="block text-sm font-medium text-gray-700 mb-2">
                            Tax Registration Number
                        </label>
                        <input type="text" id="tax_number" name="tax_number" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                               placeholder="Enter tax number">
                    </div>
                </div>
            </div>

            <!-- Service Settings -->
            <div class="border-t border-gray-200 pt-6">
                <h4 class="text-lg font-medium text-gray-900 mb-4">Service Settings</h4>
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <label for="table_service" class="text-sm font-medium text-gray-700">Table Service</label>
                            <p class="text-sm text-gray-500">Enable table service for dine-in customers</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="table_service" name="table_service" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="takeaway_service" class="text-sm font-medium text-gray-700">Takeaway Service</label>
                            <p class="text-sm text-gray-500">Enable takeaway orders</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="takeaway_service" name="takeaway_service" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="delivery_service" class="text-sm font-medium text-gray-700">Delivery Service</label>
                            <p class="text-sm text-gray-500">Enable delivery orders</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="delivery_service" name="delivery_service" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="reservation_system" class="text-sm font-medium text-gray-700">Reservation System</label>
                            <p class="text-sm text-gray-500">Enable table reservations</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="reservation_system" name="reservation_system" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Business Hours -->
            <div class="border-t border-gray-200 pt-6">
                <h4 class="text-lg font-medium text-gray-900 mb-4">Business Hours</h4>
                <div class="space-y-4">
                    @foreach(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'] as $day)
                    <div class="flex items-center space-x-4">
                        <div class="w-24">
                            <label class="text-sm font-medium text-gray-700 capitalize">{{ $day }}</label>
                        </div>
                        <div class="flex items-center space-x-2">
                            <input type="checkbox" id="{{ $day }}_open" name="{{ $day }}_open" 
                                   class="rounded border-gray-300 text-green-600 focus:ring-green-500">
                            <label for="{{ $day }}_open" class="text-sm text-gray-600">Open</label>
                        </div>
                        <div class="flex items-center space-x-2">
                            <input type="time" id="{{ $day }}_start" name="{{ $day }}_start" 
                                   class="px-3 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-green-500 focus:border-green-500">
                            <span class="text-gray-500">to</span>
                            <input type="time" id="{{ $day }}_end" name="{{ $day }}_end" 
                                   class="px-3 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-green-500 focus:border-green-500">
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-green-500 focus:border-green-500">
                    Cancel
                </button>
                <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                    <i class="fas fa-save mr-2"></i>
                    Save Settings
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Toggle business hours inputs based on checkbox
    $('[id$="_open"]').on('change', function() {
        const day = $(this).attr('id').replace('_open', '');
        const startInput = $(`#${day}_start`);
        const endInput = $(`#${day}_end`);
        
        if ($(this).is(':checked')) {
            startInput.prop('disabled', false);
            endInput.prop('disabled', false);
        } else {
            startInput.prop('disabled', true).val('');
            endInput.prop('disabled', true).val('');
        }
    });

    // Form submission handler
    $('form').on('submit', function(e) {
        e.preventDefault();
        
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        submitBtn.html('<i class="fas fa-spinner fa-spin mr-2"></i>Saving...').prop('disabled', true);
        
        setTimeout(() => {
            submitBtn.html(originalText).prop('disabled', false);
            
            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: 'Business settings updated successfully.',
                timer: 2000,
                showConfirmButton: false
            });
        }, 1500);
    });
});
</script>
@endpush