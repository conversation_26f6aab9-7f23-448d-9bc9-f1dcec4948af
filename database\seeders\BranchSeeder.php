<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Branch;

class BranchSeeder extends Seeder
{
    public function run()
    {
        if (class_exists(\Database\Factories\BranchFactory::class)) {
            Branch::factory()->count(10)->create();
        } else {
            Branch::create([
                'name' => 'Main Branch',
                'code' => 'MB001',
                'address' => '123 Main St, Anytown, USA',
                'phone' => '************',
                'email' => '<EMAIL>',
                'manager_name' => 'Jane Do<PERSON>',
                'seating_capacity' => 100,
                'delivery_radius' => 5.0,
                'operating_hours' => json_encode(['Mon-Fri' => '9 AM - 9 PM']),
                'timezone' => 'UTC',
                'is_delivery_enabled' => true,
                'is_takeaway_enabled' => true,
                'is_dine_in_enabled' => true,
                'is_online_ordering_enabled' => true,
                'printer_configurations' => json_encode(['kitchen' => 'thermal_printer_1']),
                'pos_terminal_id' => 'POS001',
                'status' => 'active',
                'tenant_id' => 1,
            ]);
        }
    }
}