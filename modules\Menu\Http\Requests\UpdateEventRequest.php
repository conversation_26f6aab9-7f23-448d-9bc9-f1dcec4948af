<?php

namespace Modules\Menu\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * Update Event Request
 * 
 * Handles validation for updating existing events in the restaurant system.
 * Validates event information, scheduling, pricing, participant limits,
 * and special requirements with update-specific rules.
 * 
 * @package Modules\Menu\Http\Requests
 * <AUTHOR> POS Team
 * @version 1.0.0
 */
class UpdateEventRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $eventId = $this->route('event') ?? $this->route('id');

        return [
            'tenant_id' => 'sometimes|integer|exists:tenants,id',
            'branch_id' => 'sometimes|integer|exists:branches,id',
            'name' => 'sometimes|string|max:255',
            'code' => 'sometimes|string|max:50|unique:events,code,' . $eventId,
            'description' => 'nullable|string',
            'short_description' => 'nullable|string|max:500',
            'event_type' => 'sometimes|string|in:dining,entertainment,special_occasion,workshop,tasting,private_party,corporate,seasonal,holiday,promotional',
            'image_urls' => 'nullable|array',
            'image_urls.*' => 'string|url',
            'banner_image' => 'nullable|string|url',
            'start_date' => 'sometimes|date',
            'end_date' => 'sometimes|date|after_or_equal:start_date',
            'start_time' => 'nullable|date_format:H:i',
            'end_time' => 'nullable|date_format:H:i|after:start_time',
            'available_days' => 'nullable|array',
            'available_days.*' => 'integer|between:0,6',
            'location' => 'nullable|string|max:255',
            'price' => 'nullable|numeric|min:0',
            'max_participants' => 'nullable|integer|min:1',
            'current_participants' => 'sometimes|integer|min:0',
            'requires_reservation' => 'sometimes|boolean',
            'menu_items' => 'nullable|array',
            'menu_items.*' => 'integer|exists:menu_items,id',
            'special_menu' => 'nullable|array',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'discount_amount' => 'nullable|numeric|min:0',
            'discount_type' => 'nullable|string|in:percentage,fixed_amount,none',
            'terms_conditions' => 'nullable|array',
            'contact_info' => 'nullable|array',
            'booking_url' => 'nullable|string|url',
            'is_featured' => 'sometimes|boolean',
            'is_active' => 'sometimes|boolean',
            'is_recurring' => 'sometimes|boolean',
            'recurrence_pattern' => 'nullable|array',
            'sort_order' => 'nullable|integer|min:0',
            'metadata' => 'nullable|array',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'tenant_id.exists' => 'Selected tenant does not exist.',
            'branch_id.exists' => 'Selected branch does not exist.',
            'name.max' => 'Event name cannot exceed 255 characters.',
            'code.unique' => 'Event code must be unique.',
            'event_type.in' => 'Invalid event type selected.',
            'start_date.date' => 'Start date must be a valid date.',
            'end_date.date' => 'End date must be a valid date.',
            'end_date.after_or_equal' => 'End date must be after or equal to start date.',
            'start_time.date_format' => 'Start time must be in HH:MM format.',
            'end_time.date_format' => 'End time must be in HH:MM format.',
            'end_time.after' => 'End time must be after start time.',
            'available_days.*.between' => 'Available days must be between 0 (Sunday) and 6 (Saturday).',
            'price.numeric' => 'Price must be a valid number.',
            'price.min' => 'Price cannot be negative.',
            'max_participants.integer' => 'Maximum participants must be a valid number.',
            'max_participants.min' => 'Maximum participants must be at least 1.',
            'current_participants.integer' => 'Current participants must be a valid number.',
            'current_participants.min' => 'Current participants cannot be negative.',
            'menu_items.*.exists' => 'Selected menu item does not exist.',
            'discount_percentage.numeric' => 'Discount percentage must be a valid number.',
            'discount_percentage.max' => 'Discount percentage cannot exceed 100%.',
            'discount_amount.numeric' => 'Discount amount must be a valid number.',
            'discount_amount.min' => 'Discount amount cannot be negative.',
            'discount_type.in' => 'Invalid discount type selected.',
            'booking_url.url' => 'Booking URL must be a valid URL.',
            'sort_order.min' => 'Sort order cannot be negative.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'tenant_id' => 'tenant',
            'branch_id' => 'branch',
            'event_type' => 'event type',
            'start_date' => 'start date',
            'end_date' => 'end date',
            'start_time' => 'start time',
            'end_time' => 'end time',
            'available_days' => 'available days',
            'max_participants' => 'maximum participants',
            'current_participants' => 'current participants',
            'requires_reservation' => 'requires reservation',
            'menu_items' => 'menu items',
            'special_menu' => 'special menu',
            'discount_percentage' => 'discount percentage',
            'discount_amount' => 'discount amount',
            'discount_type' => 'discount type',
            'terms_conditions' => 'terms and conditions',
            'contact_info' => 'contact information',
            'booking_url' => 'booking URL',
            'is_featured' => 'featured status',
            'is_active' => 'active status',
            'is_recurring' => 'recurring status',
            'recurrence_pattern' => 'recurrence pattern',
            'sort_order' => 'sort order',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        if ($this->has('is_featured')) {
            $this->merge(['is_featured' => $this->boolean('is_featured')]);
        }

        if ($this->has('is_active')) {
            $this->merge(['is_active' => $this->boolean('is_active')]);
        }

        if ($this->has('is_recurring')) {
            $this->merge(['is_recurring' => $this->boolean('is_recurring')]);
        }

        if ($this->has('requires_reservation')) {
            $this->merge(['requires_reservation' => $this->boolean('requires_reservation')]);
        }
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Validate discount type consistency
            if ($this->has('discount_type')) {
                if ($this->discount_type === 'percentage' && !$this->discount_percentage) {
                    $validator->errors()->add('discount_percentage', 'Discount percentage is required when discount type is percentage.');
                }

                if ($this->discount_type === 'fixed_amount' && !$this->discount_amount) {
                    $validator->errors()->add('discount_amount', 'Discount amount is required when discount type is fixed amount.');
                }
            }

            // Validate time consistency
            if ($this->start_time && $this->end_time) {
                try {
                    $startTime = \Carbon\Carbon::createFromFormat('H:i', $this->start_time);
                    $endTime = \Carbon\Carbon::createFromFormat('H:i', $this->end_time);
                    
                    if ($endTime->lte($startTime)) {
                        $validator->errors()->add('end_time', 'End time must be after start time.');
                    }
                } catch (\Exception $e) {
                    // Time format validation will catch this
                }
            }

            // Validate recurring pattern
            if ($this->has('is_recurring') && $this->is_recurring && empty($this->recurrence_pattern)) {
                $validator->errors()->add('recurrence_pattern', 'Recurrence pattern is required for recurring events.');
            }

            // Validate reservation requirements
            if ($this->has('requires_reservation') && $this->requires_reservation && !$this->max_participants) {
                $validator->errors()->add('max_participants', 'Maximum participants is required when reservations are required.');
            }

            // Validate current participants doesn't exceed maximum
            if ($this->has('current_participants') && $this->has('max_participants')) {
                if ($this->current_participants > $this->max_participants) {
                    $validator->errors()->add('current_participants', 'Current participants cannot exceed maximum participants.');
                }
            }

            // Validate start date for active events (cannot be changed to past if event is active)
            if ($this->has('start_date') && $this->has('is_active') && $this->is_active) {
                $startDate = \Carbon\Carbon::parse($this->start_date);
                if ($startDate->isPast()) {
                    $validator->errors()->add('start_date', 'Cannot set start date to past for active events.');
                }
            }
        });
    }
}