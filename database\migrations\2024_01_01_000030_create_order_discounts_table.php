<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_discounts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained('tenants')->onDelete('cascade');
            $table->foreignId('order_id')->constrained('orders')->onDelete('cascade');
            $table->foreignId('discount_id')->nullable()->constrained('discounts');
            $table->string('discount_name')->comment('Snapshot of discount name');
            $table->string('discount_code')->nullable();
            $table->enum('discount_type', ['percentage', 'fixed_amount', 'buy_x_get_y']);
            $table->decimal('discount_value', 10, 2);
            $table->decimal('discount_amount', 10, 2)->comment('Actual discount applied');
            $table->foreignId('applied_by')->nullable()->constrained('users');
            $table->timestamps();
            
            // Performance indexes
            $table->index(['tenant_id', 'order_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_discounts');
    }
};