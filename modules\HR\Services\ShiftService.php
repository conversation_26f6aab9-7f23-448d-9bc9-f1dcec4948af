<?php

namespace Modules\HR\Services;

use App\Models\User;
use App\Models\Shift;
use App\Models\ShiftType;
use App\Models\ShiftAssignment;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class ShiftService
{
    /**
     * Create shift assignment
     */
    public function assignShift(int $tenantId, array $data): ShiftAssignment
    {
        $this->validateShiftAssignment($data);
        
        $data['tenant_id'] = $tenantId;
        $data['assigned_at'] = now();
        $data['status'] = 'pending';
        
        return ShiftAssignment::create($data);
    }

    /**
     * Accept shift assignment
     */
    public function acceptShiftAssignment(ShiftAssignment $assignment): void
    {
        if ($assignment->status !== 'pending') {
            throw new \InvalidArgumentException('Only pending assignments can be accepted');
        }

        $assignment->accept();
        
        $shift = $assignment->shift;
        $shift->increment('assigned_staff_count');
    }

    /**
     * Decline shift assignment
     */
    public function declineShiftAssignment(ShiftAssignment $assignment, string $reason = null): void
    {
        if ($assignment->status !== 'pending') {
            throw new \InvalidArgumentException('Only pending assignments can be declined');
        }

        $assignment->decline($reason);
    }

    /**
     * Create or update shift
     */
    public function createShift(int $tenantId, array $data): Shift
    {
        $this->validateShiftData($data);
        
        $data['tenant_id'] = $tenantId;
        $data['assigned_staff_count'] = 0;
        $data['status'] = 'active';
        
        return Shift::create($data);
    }

    /**
     * Update shift
     */
    public function updateShift(Shift $shift, array $data): Shift
    {
        $this->validateShiftData($data);
        
        $shift->update($data);
        return $shift->fresh();
    }

    /**
     * Get shifts with filters - Optimized to prevent N+1 queries
     */
    public function getShifts(int $tenantId, array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = Shift::where('tenant_id', $tenantId)
                     ->with([
                         'shiftType:id,name,description',
                         'branch:id,name,code',
                         'assignments' => function($q) {
                             $q->select('id', 'shift_id', 'user_id', 'status')
                               ->with('user:id,name,email,employee_id');
                         }
                     ]);

        $this->applyShiftFilters($query, $filters);

        return $query->orderBy('date', 'desc')
                    ->orderBy('start_time')
                    ->paginate($perPage);
    }

    /**
     * Get shift types
     */
    public function getShiftTypes(int $tenantId, ?int $branchId = null): Collection
    {
        $query = ShiftType::where('tenant_id', $tenantId)->active();
        
        if ($branchId) {
            $query->where(function ($q) use ($branchId) {
                $q->where('branch_id', $branchId)
                  ->orWhereNull('branch_id');
            });
        }
        
        return $query->orderBy('name')->get();
    }

    /**
     * Create shift type
     */
    public function createShiftType(int $tenantId, array $data): ShiftType
    {
        $this->validateShiftTypeData($data);
        
        $data['tenant_id'] = $tenantId;
        return ShiftType::create($data);
    }

    /**
     * Update shift type
     */
    public function updateShiftType(ShiftType $shiftType, array $data): ShiftType
    {
        $this->validateShiftTypeData($data);
        
        $shiftType->update($data);
        return $shiftType->fresh();
    }

    /**
     * Get staff schedule for a date range
     */
    public function getStaffSchedule(int $tenantId, string $startDate, string $endDate, ?int $branchId = null): Collection
    {
        $query = ShiftAssignment::where('tenant_id', $tenantId)
                               ->with(['user', 'shift', 'shiftType'])
                               ->whereHas('shift', function ($q) use ($startDate, $endDate) {
                                   $q->whereBetween('date', [$startDate, $endDate]);
                               })
                               ->accepted();

        if ($branchId) {
            $query->where('branch_id', $branchId);
        }

        return $query->get();
    }

    /**
     * Request shift replacement
     */
    public function requestShiftReplacement(ShiftAssignment $assignment, string $reason): void
    {
        if ($assignment->status !== 'accepted') {
            throw new \InvalidArgumentException('Only accepted assignments can request replacement');
        }

        $assignment->requestReplacement($reason);
    }

    /**
     * Get understaffed shifts
     */
    public function getUnderstaffedShifts(int $tenantId, ?int $branchId = null): Collection
    {
        $query = Shift::where('tenant_id', $tenantId)
                     ->underStaffed()
                     ->active()
                     ->with(['shiftType', 'branch', 'assignments.user']);

        if ($branchId) {
            $query->where('branch_id', $branchId);
        }

        return $query->orderBy('date')
                    ->orderBy('start_time')
                    ->get();
    }

    /**
     * Get available staff for shift
     */
    public function getAvailableStaffForShift(int $tenantId, int $shiftId): Collection
    {
        $shift = Shift::find($shiftId);
        
        if (!$shift) {
            return collect();
        }

        $assignedUserIds = ShiftAssignment::where('shift_id', $shiftId)
                                         ->pluck('user_id')
                                         ->toArray();

        $query = User::where('tenant_id', $tenantId)
                    ->whereNotIn('id', $assignedUserIds)
                    ->active();

        if ($shift->branch_id) {
            $query->where('branch_id', $shift->branch_id);
        }

        return $query->with('roles')->get();
    }

    /**
     * Apply filters to shift query
     */
    private function applyShiftFilters($query, array $filters): void
    {
        if (!empty($filters['branch_id'])) {
            $query->where('branch_id', $filters['branch_id']);
        }

        if (!empty($filters['date_from'])) {
            $query->where('date', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->where('date', '<=', $filters['date_to']);
        }

        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (!empty($filters['shift_type_id'])) {
            $query->where('shift_type_id', $filters['shift_type_id']);
        }
    }

    /**
     * Validate shift assignment data
     */
    private function validateShiftAssignment(array $data): void
    {
        if (empty($data['shift_id']) || empty($data['user_id'])) {
            throw new \InvalidArgumentException('Shift ID and User ID are required');
        }

        // Check for conflicts
        $existingAssignment = ShiftAssignment::where('shift_id', $data['shift_id'])
                                           ->where('user_id', $data['user_id'])
                                           ->whereIn('status', ['pending', 'accepted'])
                                           ->exists();

        if ($existingAssignment) {
            throw new \InvalidArgumentException('User is already assigned to this shift');
        }
    }

    /**
     * Validate shift data
     */
    private function validateShiftData(array $data): void
    {
        if (empty($data['date']) || empty($data['start_time']) || empty($data['end_time'])) {
            throw new \InvalidArgumentException('Date, start time, and end time are required');
        }

        if ($data['start_time'] >= $data['end_time']) {
            throw new \InvalidArgumentException('End time must be after start time');
        }
    }

    /**
     * Validate shift type data
     */
    private function validateShiftTypeData(array $data): void
    {
        if (empty($data['name'])) {
            throw new \InvalidArgumentException('Shift type name is required');
        }
    }
}