@extends('layouts.master')

@section('title', 'Edit Transaction')

@push('styles')
<style>
    .form-group {
        margin-bottom: 1rem;
    }
    
    .form-label {
        display: block;
        font-size: 0.875rem;
        font-weight: 500;
        color: #374151;
        margin-bottom: 0.5rem;
    }
    
    .form-control {
        width: 100%;
        padding: 0.5rem 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }
    
    .form-control:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    .form-control.is-invalid {
        border-color: #ef4444;
    }
    
    .form-control:disabled {
        background-color: #f9fafb;
        color: #6b7280;
    }
    
    .invalid-feedback {
        display: block;
        width: 100%;
        margin-top: 0.25rem;
        font-size: 0.75rem;
        color: #ef4444;
    }
    
    .transaction-status {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
    }
    .status-paid { background-color: #d1fae5; color: #065f46; }
    .status-due { background-color: #fee2e2; color: #991b1b; }
    .status-partially_paid { background-color: #fef3c7; color: #92400e; }
    .status-cancelled { background-color: #f3f4f6; color: #374151; }
    .status-overpaid { background-color: #dbeafe; color: #1e40af; }
</style>
@endpush

@section('content')
<!-- Page Header -->
<div class="mb-6">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="mb-4 sm:mb-0">
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-edit mr-3"></i>
                Edit Transaction
            </h1>
            <p class="text-sm text-gray-600 mt-1">Update transaction #{{ $transaction->transaction_number }}</p>
        </div>
        <div class="flex gap-2">
            <a href="{{ route('transactions.show', $transaction->id) }}" class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-md transition-colors duration-200">
                <i class="fas fa-eye mr-2"></i>
                View Details
            </a>
            <a href="{{ route('transactions.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-md transition-colors duration-200">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to List
            </a>
        </div>
    </div>
</div>

<!-- Current Status -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">Current Status</h3>
            <span class="transaction-status status-{{ $transaction->status }}">
                {{ ucfirst(str_replace('_', ' ', $transaction->status)) }}
            </span>
        </div>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
            <div class="text-center">
                <div class="text-2xl font-bold text-gray-900">${{ number_format($transaction->total_amount, 2) }}</div>
                <div class="text-gray-600">Total Amount</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-green-600">${{ number_format($transaction->paid_amount, 2) }}</div>
                <div class="text-gray-600">Paid Amount</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-red-600">${{ number_format($transaction->due_amount, 2) }}</div>
                <div class="text-gray-600">Due Amount</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-blue-600">{{ number_format($transaction->getPaymentProgress(), 0) }}%</div>
                <div class="text-gray-600">Payment Progress</div>
            </div>
        </div>
    </div>
</div>

<!-- Order Information (if applicable) -->
@if($transaction->order)
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Associated Order</h3>
        <p class="text-sm text-gray-600 mt-1">This transaction is linked to an order</p>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div class="flex justify-between">
                <span class="text-gray-600">Order Number:</span>
                <span class="font-medium">{{ $transaction->order->order_number }}</span>
            </div>
            <div class="flex justify-between">
                <span class="text-gray-600">Order Date:</span>
                <span class="font-medium">{{ $transaction->order->created_at->format('M d, Y H:i') }}</span>
            </div>
            <div class="flex justify-between">
                <span class="text-gray-600">Order Status:</span>
                <span class="font-medium">{{ ucfirst($transaction->order->status) }}</span>
            </div>
            @if($transaction->order->customer)
            <div class="flex justify-between">
                <span class="text-gray-600">Customer:</span>
                <span class="font-medium">{{ $transaction->order->customer->name }}</span>
            </div>
            @endif
            @if($transaction->order->table)
            <div class="flex justify-between">
                <span class="text-gray-600">Table:</span>
                <span class="font-medium">{{ $transaction->order->table->table_number }}</span>
            </div>
            @endif
        </div>
    </div>
</div>
@endif

<!-- Edit Form -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Transaction Details</h3>
        <p class="text-sm text-gray-600 mt-1">Update the transaction information</p>
    </div>
    
    <form id="edit-transaction-form" action="{{ route('transactions.update', $transaction->id) }}" method="POST">
        @csrf
        @method('PUT')
        
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Left Column -->
                <div>
                    <div class="form-group">
                        <label for="transaction_number" class="form-label">Transaction Number</label>
                        <input type="text" id="transaction_number" name="transaction_number" class="form-control" value="{{ $transaction->transaction_number }}" disabled>
                        <small class="text-gray-500">Transaction number cannot be changed</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="total_amount" class="form-label">Total Amount *</label>
                        <input type="number" step="0.01" min="0" id="total_amount" name="total_amount" class="form-control" value="{{ $transaction->total_amount }}" required>
                        @error('total_amount')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="paid_amount" class="form-label">Paid Amount</label>
                        <input type="number" step="0.01" min="0" id="paid_amount" name="paid_amount" class="form-control" value="{{ $transaction->paid_amount }}" disabled>
                        <small class="text-gray-500">Paid amount is calculated from payments and cannot be directly edited</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="tax_amount" class="form-label">Tax Amount</label>
                        <input type="number" step="0.01" min="0" id="tax_amount" name="tax_amount" class="form-control" value="{{ $transaction->tax_amount }}">
                        @error('tax_amount')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                
                <!-- Right Column -->
                <div>
                    <div class="form-group">
                        <label for="discount_amount" class="form-label">Discount Amount</label>
                        <input type="number" step="0.01" min="0" id="discount_amount" name="discount_amount" class="form-control" value="{{ $transaction->discount_amount }}">
                        @error('discount_amount')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="service_charge" class="form-label">Service Charge</label>
                        <input type="number" step="0.01" min="0" id="service_charge" name="service_charge" class="form-control" value="{{ $transaction->service_charge }}">
                        @error('service_charge')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="status" class="form-label">Status</label>
                        <select id="status" name="status" class="form-control">
                            <option value="due" {{ $transaction->status === 'due' ? 'selected' : '' }}>Due</option>
                            <option value="paid" {{ $transaction->status === 'paid' ? 'selected' : '' }}>Paid</option>
                            <option value="partially_paid" {{ $transaction->status === 'partially_paid' ? 'selected' : '' }}>Partially Paid</option>
                            <option value="cancelled" {{ $transaction->status === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                            <option value="overpaid" {{ $transaction->status === 'overpaid' ? 'selected' : '' }}>Overpaid</option>
                        </select>
                        @error('status')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="text-gray-500">Status is usually automatically calculated based on payments</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="due_amount" class="form-label">Due Amount</label>
                        <input type="number" step="0.01" id="due_amount" name="due_amount" class="form-control" value="{{ $transaction->due_amount }}" disabled>
                        <small class="text-gray-500">Due amount is automatically calculated (Total - Paid)</small>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="notes" class="form-label">Notes</label>
                <textarea id="notes" name="notes" rows="3" class="form-control" placeholder="Optional notes about this transaction...">{{ $transaction->notes }}</textarea>
                @error('notes')
                <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
            
            <!-- Amount Calculation Summary -->
            <div class="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
                <h4 class="font-semibold text-gray-900 mb-3">Amount Summary</h4>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Subtotal:</span>
                        <span id="subtotal-display" class="font-medium">$0.00</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Tax:</span>
                        <span id="tax-display" class="font-medium">$0.00</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Service Charge:</span>
                        <span id="service-display" class="font-medium">$0.00</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Discount:</span>
                        <span id="discount-display" class="font-medium">-$0.00</span>
                    </div>
                    <div class="flex justify-between font-semibold text-lg border-t pt-2">
                        <span class="text-gray-900">Total:</span>
                        <span id="total-display" class="text-gray-900">$0.00</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Paid:</span>
                        <span id="paid-display" class="font-medium">$0.00</span>
                    </div>
                    <div class="flex justify-between font-semibold">
                        <span class="text-gray-900">Due:</span>
                        <span id="due-display" class="text-gray-900">$0.00</span>
                    </div>
                </div>
            </div>
            
            <!-- Warning for status changes -->
            <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-yellow-800">Important Notes</h3>
                        <div class="mt-2 text-sm text-yellow-700">
                            <ul class="list-disc pl-5 space-y-1">
                                <li>The paid amount is automatically calculated from associated payments and cannot be directly edited.</li>
                                <li>The due amount is automatically calculated as Total Amount - Paid Amount.</li>
                                <li>The transaction status is usually automatically updated based on payment status.</li>
                                <li>Changing the total amount may affect the transaction status and due amount.</li>
                                <li>To add payments, use the "Add Payment" feature from the transaction details page.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="px-6 py-4 border-t border-gray-200 flex justify-end gap-2">
            <a href="{{ route('transactions.show', $transaction->id) }}" class="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md transition-colors duration-200">
                Cancel
            </a>
            <button type="submit" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors duration-200">
                <i class="fas fa-save mr-2"></i>
                Update Transaction
            </button>
        </div>
    </form>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    const originalPaidAmount = parseFloat($('#paid_amount').val()) || 0;
    
    // Amount calculation
    $('#total_amount, #tax_amount, #discount_amount, #service_charge').on('input', function() {
        updateAmountSummary();
    });
    
    function updateAmountSummary() {
        const total = parseFloat($('#total_amount').val()) || 0;
        const paid = originalPaidAmount; // Use original paid amount since it can't be edited
        const tax = parseFloat($('#tax_amount').val()) || 0;
        const discount = parseFloat($('#discount_amount').val()) || 0;
        const service = parseFloat($('#service_charge').val()) || 0;
        
        const subtotal = total - tax - service + discount;
        const due = total - paid;
        
        $('#subtotal-display').text('$' + subtotal.toFixed(2));
        $('#tax-display').text('$' + tax.toFixed(2));
        $('#service-display').text('$' + service.toFixed(2));
        $('#discount-display').text('-$' + discount.toFixed(2));
        $('#total-display').text('$' + total.toFixed(2));
        $('#paid-display').text('$' + paid.toFixed(2));
        $('#due-display').text('$' + due.toFixed(2));
        $('#due_amount').val(due.toFixed(2));
        
        // Update status suggestion based on payment
        if (paid >= total) {
            if (paid > total) {
                $('#status').val('overpaid');
            } else {
                $('#status').val('paid');
            }
        } else if (paid > 0) {
            $('#status').val('partially_paid');
        } else {
            $('#status').val('due');
        }
    }
    
    // Form submission confirmation
    $('#edit-transaction-form').on('submit', function(e) {
        const currentTotal = parseFloat($('#total_amount').val()) || 0;
        const originalTotal = {{ $transaction->total_amount }};
        
        if (Math.abs(currentTotal - originalTotal) > 0.01) {
            e.preventDefault();
            Swal.fire({
                title: 'Confirm Total Amount Change',
                text: `You are changing the total amount from $${originalTotal.toFixed(2)} to $${currentTotal.toFixed(2)}. This may affect the transaction status and due amount. Are you sure?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, update it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    $('#edit-transaction-form')[0].submit();
                }
            });
        }
    });
    
    // Initialize amount summary
    updateAmountSummary();
});
</script>
@endpush