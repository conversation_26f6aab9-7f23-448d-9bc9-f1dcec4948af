<?php

namespace Modules\Menu\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MenuItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'menu_id' => $this->menu_id,
            'category_id' => $this->category_id,
            'name' => $this->name,
            'code' => $this->code,
            'description' => $this->description,
            'short_description' => $this->short_description,
            'base_price' => $this->base_price,
            'cost_price' => $this->cost_price,
            'image_urls' => $this->image_urls,
            'prep_time_minutes' => $this->prep_time_minutes,
            'calories' => $this->calories,
            'nutritional_info' => $this->nutritional_info,
            'allergens' => $this->allergens,
            'dietary_info' => $this->dietary_info,
            'recipe_id' => $this->recipe_id,
            'barcode' => $this->barcode,
            'sku' => $this->sku,
            'is_active' => $this->is_active,
            'is_featured' => $this->is_featured,
            'is_spicy' => $this->is_spicy,
            'spice_level' => $this->spice_level,
            'sort_order' => $this->sort_order,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'deleted_at' => $this->deleted_at,
            
            // Computed attributes
            'first_image' => $this->first_image,
            'formatted_price' => $this->formatted_price,
            'formatted_cost_price' => $this->formatted_cost_price,
            
            // Relationships
            'menu' => $this->whenLoaded('menu'),
            'category' => new CategoryResource($this->whenLoaded('category')),
            'recipe' => $this->whenLoaded('recipe'),
            'variants' => VariantResource::collection($this->whenLoaded('variants')),
            'addons' => AddonResource::collection($this->whenLoaded('addons')),
            'branches' => $this->whenLoaded('branches'),
            'availability' => $this->whenLoaded('availability'),
            
            // Computed attributes
            'variants_count' => $this->whenCounted('variants'),
            'addons_count' => $this->whenCounted('addons'),
        ];
    }
}