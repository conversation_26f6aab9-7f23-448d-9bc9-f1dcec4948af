<?php

namespace Modules\Menu\Services;

use App\Models\MenuCategory;
use Illuminate\Pagination\LengthAwarePaginator;

class CategoryService
{
    protected $codeGenerator;

    public function __construct(CodeGeneratorService $codeGenerator)
    {
        $this->codeGenerator = $codeGenerator;
    }
    /**
     * Get categories for a specific branch with pagination and filtering.
     */
    public function getCategoriesForBranch(int $branchId, array $filters = []): LengthAwarePaginator
    {
        $query = MenuCategory::with(['menu', 'parentCategory', 'childCategories'])
            ->withCount(['menuItems', 'childCategories'])
            ->whereHas('menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            });

        // Apply filters
        if (isset($filters['menu_id'])) {
            $query->where('menu_id', $filters['menu_id']);
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        if (isset($filters['parent_category_id'])) {
            if ($filters['parent_category_id'] === 'null') {
                $query->whereNull('parent_category_id');
            } else {
                $query->where('parent_category_id', $filters['parent_category_id']);
            }
        }

        if (isset($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'sort_order';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortBy, $sortDirection);

        // Pagination
        $perPage = $filters['per_page'] ?? 15;
        
        return $query->paginate($perPage);
    }

    /**
     * Get category by ID with relationships.
     */
    public function getCategoryById(int $id): ?MenuCategory
    {
        return MenuCategory::with(['menu', 'parentCategory', 'childCategories', 'menuItems'])
            ->withCount(['menuItems', 'childCategories'])
            ->find($id);
    }

    /**
     * Create a new category.
     */
    public function createCategory(array $data): MenuCategory
    {
        // Generate code automatically if not provided
        if (empty($data['code'])) {
            $data['code'] = $this->codeGenerator->generateCategoryCode(
                $data['name'], 
                $data['menu_id'] ?? null
            );
        }

        return MenuCategory::create($data);
    }

    /**
     * Update an existing category.
     */
    public function updateCategory(int $id, array $data): ?MenuCategory
    {
        $category = MenuCategory::find($id);
        
        if (!$category) {
            return null;
        }

        $category->update($data);
        
        return $category->fresh(['menu', 'parentCategory', 'childCategories']);
    }

    /**
     * Delete a category.
     */
    public function deleteCategory(int $id): bool
    {
        $category = MenuCategory::find($id);
        
        if (!$category) {
            return false;
        }

        // Check if category has child categories or menu items
        if ($category->childCategories()->exists() || $category->menuItems()->exists()) {
            throw new \Exception('Cannot delete category that has child categories or menu items');
        }

        return $category->delete();
    }

    /**
     * Get active categories for a menu.
     */
    public function getActiveCategoriesForMenu(int $menuId): \Illuminate\Database\Eloquent\Collection
    {
        return MenuCategory::where('menu_id', $menuId)
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * Get root categories for a menu (categories without parent).
     */
    public function getRootCategoriesForMenu(int $menuId): \Illuminate\Database\Eloquent\Collection
    {
        return MenuCategory::where('menu_id', $menuId)
            ->whereNull('parent_category_id')
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * Get category by ID for a specific branch.
     */
    public function getCategoryByIdForBranch(int $id, int $branchId): ?MenuCategory
    {
        return MenuCategory::with(['menu', 'parentCategory', 'childCategories', 'menuItems'])
            ->withCount(['menuItems', 'childCategories'])
            ->whereHas('menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })
            ->find($id);
    }

    /**
     * Update category for a specific branch.
     */
    public function updateCategoryForBranch(int $id, array $data, int $branchId): ?MenuCategory
    {
        $category = MenuCategory::whereHas('menu', function ($q) use ($branchId) {
            $q->where('branch_id', $branchId);
        })->find($id);
        
        if (!$category) {
            return null;
        }

        $category->update($data);
        
        return $category->fresh(['menu', 'parentCategory', 'childCategories']);
    }

    /**
     * Delete category for a specific branch.
     */
    public function deleteCategoryForBranch(int $id, int $branchId): bool
    {
        $category = MenuCategory::whereHas('menu', function ($q) use ($branchId) {
            $q->where('branch_id', $branchId);
        })->find($id);
        
        if (!$category) {
            return false;
        }

        // Check if category has child categories or menu items
        if ($category->childCategories()->exists() || $category->menuItems()->exists()) {
            throw new \Exception('Cannot delete category that has child categories or menu items');
        }

        return $category->delete();
    }

    /**
     * Create category for web interface.
     */
    public function createCategoryForWeb(array $data): MenuCategory
    {
        return $this->createCategory($data);
    }

    /**
     * Update category for web interface.
     */
    public function updateCategoryForWeb(int $id, array $data, int $branchId): ?MenuCategory
    {
        return $this->updateCategoryForBranch($id, $data, $branchId);
    }

    /**
     * Get categories for DataTable display.
     */
    public function getCategoriesForDataTable(int $branchId, $request)
    {
        $query = MenuCategory::with(['menu', 'parentCategory'])
            ->withCount(['menuItems', 'childCategories'])
            ->whereHas('menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })
            ->select('id', 'menu_id', 'parent_category_id', 'name', 'code', 'description', 'is_active', 'is_featured', 'sort_order', 'created_at');

        // Apply search filter only if search value exists and is not empty
        if ($request->has('search') && !empty($request->search['value'])) {
            $search = $request->search['value'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Apply menu filter only if menu_id is provided and not empty
        if ($request->has('menu_id') && !empty($request->menu_id) && $request->menu_id !== '') {
            $query->where('menu_id', $request->menu_id);
        }

        // Apply status filter only if status is provided and not empty string
        if ($request->has('status') && $request->status !== '' && $request->status !== null) {
            $query->where('is_active', (bool)$request->status);
        }

        // Apply featured filter only if featured is provided and not empty string
        if ($request->has('featured') && $request->featured !== '' && $request->featured !== null) {
            $query->where('is_featured', (bool)$request->featured);
        }

        return datatables($query)
            ->addIndexColumn()
            ->addColumn('actions', function ($category) {
                return $this->getActionButtons($category);
            })
            ->editColumn('status', function ($category) {
                return $category->is_active 
                    ? '<span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">نشط</span>' 
                    : '<span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded">غير نشط</span>';
            })
            ->addColumn('featured', function ($category) {
                return $category->is_featured 
                    ? '<span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">مميز</span>' 
                    : '<span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">عادي</span>';
            })
            ->addColumn('menu_name', function ($category) {
                return $category->menu ? $category->menu->name : '-';
            })
            ->editColumn('description', function ($category) {
                return $category->description ? \Str::limit($category->description, 50) : '-';
            })
            ->editColumn('created_at', function ($category) {
                return $category->created_at ? $category->created_at->format('Y-m-d H:i:s') : '-';
            })
            ->rawColumns(['actions', 'status', 'featured'])
            ->make(true);
    }

    /**
     * Get action buttons for DataTable.
     */
    private function getActionButtons($category)
    {
        return '
            <div class="flex space-x-2 justify-center">
                <button type="button" 
                        data-id="' . $category->id . '"
                        class="show-category inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 bg-blue-100 rounded-lg hover:bg-blue-200 focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 transition-colors duration-200"
                        title="عرض التفاصيل">
                    <i class="fas fa-eye"></i>
                </button>
                <button type="button" 
                        data-id="' . $category->id . '"
                        class="edit-category inline-flex items-center px-2 py-1 text-xs font-medium text-yellow-600 bg-yellow-100 rounded-lg hover:bg-yellow-200 focus:ring-2 focus:ring-yellow-500 focus:ring-offset-1 transition-colors duration-200"
                        title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button type="button" 
                        data-id="' . $category->id . '"
                        class="delete-category inline-flex items-center px-2 py-1 text-xs font-medium text-red-600 bg-red-100 rounded-lg hover:bg-red-200 focus:ring-2 focus:ring-red-500 focus:ring-offset-1 transition-colors duration-200"
                        title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </div>';
    }

    /**
     * Get categories list for dropdowns.
     */
    public function getCategoriesListForBranch(int $branchId, ?int $menuId = null): \Illuminate\Database\Eloquent\Collection
    {
        $query = MenuCategory::select('id', 'name', 'menu_id', 'parent_category_id')
            ->whereHas('menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })
            ->where('is_active', true)
            ->orderBy('sort_order');

        if ($menuId) {
            $query->where('menu_id', $menuId);
        }

        return $query->get();
    }

    /**
     * Get categories query for custom pagination.
     */
    public function getCategoriesQuery(int $branchId)
    {
        $query = MenuCategory::with(['menu', 'parentCategory'])
            ->withCount(['menuItems', 'childCategories']);
            
        // If branchId is provided and valid, filter by branch
        if ($branchId && $branchId > 0) {
            $query->whereHas('menu', function ($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            });
        }
        
        return $query;
    }
}