<?php

namespace Modules\Delivery\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Modules\Delivery\Models\DeliveryAssignment;

class CreateDeliveryReviewRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Check if the user is authenticated
        // if (!Auth::check()) {
        //     return false;
        // }

        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'assignment_id' => 'required|exists:delivery_assignments,id',
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'nullable|string|max:500',
            'is_anonymous' => 'nullable|boolean',
            'review_categories' => 'nullable|array',
            'review_categories.speed' => 'nullable|integer|min:1|max:5',
            'review_categories.politeness' => 'nullable|integer|min:1|max:5',
            'review_categories.food_condition' => 'nullable|integer|min:1|max:5',
            'review_categories.communication' => 'nullable|integer|min:1|max:5',
            'review_categories.professionalism' => 'nullable|integer|min:1|max:5',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'assignment_id.required' => 'The delivery assignment ID is required.',
            'assignment_id.exists' => 'The selected delivery assignment does not exist.',
            'rating.required' => 'Please provide a rating for this delivery.',
            'rating.integer' => 'The rating must be a number.',
            'rating.min' => 'The rating must be at least 1.',
            'rating.max' => 'The rating cannot be greater than 5.',
            'comment.max' => 'The comment cannot exceed 500 characters.',
            'review_categories.array' => 'Review categories must be provided as an array.',
        ];
    }
}