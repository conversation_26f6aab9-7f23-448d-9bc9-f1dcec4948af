<?php

namespace Modules\Kitchen\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AssignMenuItemRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization should be handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'menu_item_id' => ['required', 'integer', 'exists:menu_items,id'],
            'prep_time_minutes' => ['nullable', 'integer', 'min:1', 'max:300'],
            'priority_level' => ['nullable', 'integer', 'min:1', 'max:10'],
            'special_instructions' => ['nullable', 'string', 'max:1000'],
            'is_active' => ['boolean'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'menu_item_id.required' => 'Please select a menu item.',
            'menu_item_id.exists' => 'The selected menu item does not exist.',
            'prep_time_minutes.min' => 'Prep time must be at least 1 minute.',
            'prep_time_minutes.max' => 'Prep time cannot exceed 300 minutes.',
            'priority_level.min' => 'Priority level must be at least 1.',
            'priority_level.max' => 'Priority level cannot exceed 10.',
            'special_instructions.max' => 'Special instructions cannot exceed 1000 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'menu_item_id' => 'menu item',
            'prep_time_minutes' => 'prep time',
            'priority_level' => 'priority level',
            'special_instructions' => 'special instructions',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default values
        if (!$this->has('is_active')) {
            $this->merge(['is_active' => true]);
        }

        if (!$this->has('priority_level')) {
            $this->merge(['priority_level' => 5]);
        }
    }
}
