@extends('layouts.master')

@section('title', 'Branch Settings')

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
@endpush

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-emerald-600 to-teal-600 rounded-lg shadow-lg p-6 mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                            <i class="fas fa-store text-white text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <h1 class="text-2xl font-bold text-white">Branch Settings</h1>
                        <p class="text-emerald-100">Configure individual branch settings and preferences</p>
                    </div>
                </div>
            </div>
            <div class="mt-4 md:mt-0 flex space-x-3">
                <button id="sync-settings-btn" class="px-4 py-2 bg-white text-emerald-600 rounded-lg hover:bg-emerald-50 transition-colors duration-200 font-medium">
                    <i class="fas fa-sync mr-2"></i>
                    Sync Settings
                </button>
                <button id="clone-settings-btn" class="px-4 py-2 bg-emerald-500 text-white rounded-lg hover:bg-emerald-400 transition-colors duration-200 font-medium">
                    <i class="fas fa-clone mr-2"></i>
                    Clone Settings
                </button>
            </div>
        </div>
    </div>

    <!-- Branch Selection -->
    <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fas fa-map-marker-alt mr-2 text-emerald-600"></i>
            Select Branch
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <label for="branch_select" class="block text-sm font-medium text-gray-700 mb-2">
                    Current Branch
                </label>
                <select id="branch_select" name="branch_select" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                    <option value="main">Main Branch - Downtown</option>
                    <option value="north">North Branch - Mall Plaza</option>
                    <option value="south">South Branch - Shopping Center</option>
                    <option value="east">East Branch - Business District</option>
                </select>
            </div>
            
            <div>
                <label for="branch_status" class="block text-sm font-medium text-gray-700 mb-2">
                    Branch Status
                </label>
                <div class="flex items-center space-x-4">
                    <span class="px-3 py-2 text-sm font-medium text-green-700 bg-green-100 rounded-lg">
                        <i class="fas fa-circle text-green-500 mr-1"></i>
                        Active
                    </span>
                    <span class="text-sm text-gray-600">Last updated: 2 hours ago</span>
                </div>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Quick Actions
                </label>
                <div class="flex space-x-2">
                    <button class="px-3 py-2 text-sm font-medium text-emerald-700 bg-emerald-100 rounded-lg hover:bg-emerald-200">
                        <i class="fas fa-edit mr-1"></i>
                        Edit Info
                    </button>
                    <button class="px-3 py-2 text-sm font-medium text-blue-700 bg-blue-100 rounded-lg hover:bg-blue-200">
                        <i class="fas fa-chart-bar mr-1"></i>
                        View Stats
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Branch Information -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-info-circle mr-2 text-blue-600"></i>
                Branch Information
            </h3>
            
            <form class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="branch_name" class="block text-sm font-medium text-gray-700 mb-2">
                            Branch Name
                        </label>
                        <input type="text" id="branch_name" name="branch_name" value="Main Branch - Downtown" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label for="branch_code" class="block text-sm font-medium text-gray-700 mb-2">
                            Branch Code
                        </label>
                        <input type="text" id="branch_code" name="branch_code" value="MAIN001" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>

                <div>
                    <label for="branch_address" class="block text-sm font-medium text-gray-700 mb-2">
                        Address
                    </label>
                    <textarea id="branch_address" name="branch_address" rows="3" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">123 Main Street, Downtown District, City, State 12345</textarea>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="branch_phone" class="block text-sm font-medium text-gray-700 mb-2">
                            Phone Number
                        </label>
                        <input type="tel" id="branch_phone" name="branch_phone" value="+****************" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label for="branch_email" class="block text-sm font-medium text-gray-700 mb-2">
                            Email Address
                        </label>
                        <input type="email" id="branch_email" name="branch_email" value="<EMAIL>" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="branch_manager" class="block text-sm font-medium text-gray-700 mb-2">
                            Branch Manager
                        </label>
                        <select id="branch_manager" name="branch_manager" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="john_doe">John Doe</option>
                            <option value="jane_smith">Jane Smith</option>
                            <option value="mike_johnson">Mike Johnson</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="branch_timezone" class="block text-sm font-medium text-gray-700 mb-2">
                            Timezone
                        </label>
                        <select id="branch_timezone" name="branch_timezone" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="America/New_York">Eastern Time (ET)</option>
                            <option value="America/Chicago">Central Time (CT)</option>
                            <option value="America/Denver">Mountain Time (MT)</option>
                            <option value="America/Los_Angeles">Pacific Time (PT)</option>
                        </select>
                    </div>
                </div>

                <div class="pt-4">
                    <button type="submit" class="w-full px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700">
                        <i class="fas fa-save mr-2"></i>
                        Save Branch Information
                    </button>
                </div>
            </form>
        </div>

        <!-- Operating Hours -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-clock mr-2 text-orange-600"></i>
                Operating Hours
            </h3>
            
            <form class="space-y-4">
                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                        <div class="flex items-center">
                            <input type="checkbox" id="monday_open" name="monday_open" checked class="rounded border-gray-300 text-orange-600 focus:ring-orange-500">
                            <label for="monday_open" class="ml-2 text-sm font-medium text-gray-700">Monday</label>
                        </div>
                        <div class="flex items-center space-x-2">
                            <input type="time" id="monday_start" name="monday_start" value="09:00" class="px-2 py-1 border border-gray-300 rounded text-sm">
                            <span class="text-gray-500">to</span>
                            <input type="time" id="monday_end" name="monday_end" value="22:00" class="px-2 py-1 border border-gray-300 rounded text-sm">
                        </div>
                    </div>

                    <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                        <div class="flex items-center">
                            <input type="checkbox" id="tuesday_open" name="tuesday_open" checked class="rounded border-gray-300 text-orange-600 focus:ring-orange-500">
                            <label for="tuesday_open" class="ml-2 text-sm font-medium text-gray-700">Tuesday</label>
                        </div>
                        <div class="flex items-center space-x-2">
                            <input type="time" id="tuesday_start" name="tuesday_start" value="09:00" class="px-2 py-1 border border-gray-300 rounded text-sm">
                            <span class="text-gray-500">to</span>
                            <input type="time" id="tuesday_end" name="tuesday_end" value="22:00" class="px-2 py-1 border border-gray-300 rounded text-sm">
                        </div>
                    </div>

                    <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                        <div class="flex items-center">
                            <input type="checkbox" id="wednesday_open" name="wednesday_open" checked class="rounded border-gray-300 text-orange-600 focus:ring-orange-500">
                            <label for="wednesday_open" class="ml-2 text-sm font-medium text-gray-700">Wednesday</label>
                        </div>
                        <div class="flex items-center space-x-2">
                            <input type="time" id="wednesday_start" name="wednesday_start" value="09:00" class="px-2 py-1 border border-gray-300 rounded text-sm">
                            <span class="text-gray-500">to</span>
                            <input type="time" id="wednesday_end" name="wednesday_end" value="22:00" class="px-2 py-1 border border-gray-300 rounded text-sm">
                        </div>
                    </div>

                    <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                        <div class="flex items-center">
                            <input type="checkbox" id="thursday_open" name="thursday_open" checked class="rounded border-gray-300 text-orange-600 focus:ring-orange-500">
                            <label for="thursday_open" class="ml-2 text-sm font-medium text-gray-700">Thursday</label>
                        </div>
                        <div class="flex items-center space-x-2">
                            <input type="time" id="thursday_start" name="thursday_start" value="09:00" class="px-2 py-1 border border-gray-300 rounded text-sm">
                            <span class="text-gray-500">to</span>
                            <input type="time" id="thursday_end" name="thursday_end" value="22:00" class="px-2 py-1 border border-gray-300 rounded text-sm">
                        </div>
                    </div>

                    <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                        <div class="flex items-center">
                            <input type="checkbox" id="friday_open" name="friday_open" checked class="rounded border-gray-300 text-orange-600 focus:ring-orange-500">
                            <label for="friday_open" class="ml-2 text-sm font-medium text-gray-700">Friday</label>
                        </div>
                        <div class="flex items-center space-x-2">
                            <input type="time" id="friday_start" name="friday_start" value="09:00" class="px-2 py-1 border border-gray-300 rounded text-sm">
                            <span class="text-gray-500">to</span>
                            <input type="time" id="friday_end" name="friday_end" value="23:00" class="px-2 py-1 border border-gray-300 rounded text-sm">
                        </div>
                    </div>

                    <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                        <div class="flex items-center">
                            <input type="checkbox" id="saturday_open" name="saturday_open" checked class="rounded border-gray-300 text-orange-600 focus:ring-orange-500">
                            <label for="saturday_open" class="ml-2 text-sm font-medium text-gray-700">Saturday</label>
                        </div>
                        <div class="flex items-center space-x-2">
                            <input type="time" id="saturday_start" name="saturday_start" value="10:00" class="px-2 py-1 border border-gray-300 rounded text-sm">
                            <span class="text-gray-500">to</span>
                            <input type="time" id="saturday_end" name="saturday_end" value="23:00" class="px-2 py-1 border border-gray-300 rounded text-sm">
                        </div>
                    </div>

                    <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                        <div class="flex items-center">
                            <input type="checkbox" id="sunday_open" name="sunday_open" class="rounded border-gray-300 text-orange-600 focus:ring-orange-500">
                            <label for="sunday_open" class="ml-2 text-sm font-medium text-gray-700">Sunday</label>
                        </div>
                        <div class="flex items-center space-x-2">
                            <input type="time" id="sunday_start" name="sunday_start" value="11:00" class="px-2 py-1 border border-gray-300 rounded text-sm">
                            <span class="text-gray-500">to</span>
                            <input type="time" id="sunday_end" name="sunday_end" value="21:00" class="px-2 py-1 border border-gray-300 rounded text-sm">
                        </div>
                    </div>
                </div>

                <div class="flex space-x-2">
                    <button type="button" id="copy-hours-btn" class="flex-1 px-4 py-2 text-sm font-medium text-orange-700 bg-orange-100 border border-orange-300 rounded-lg hover:bg-orange-200">
                        <i class="fas fa-copy mr-2"></i>
                        Copy to All Days
                    </button>
                    <button type="submit" class="flex-1 px-4 py-2 text-sm font-medium text-white bg-orange-600 border border-transparent rounded-lg hover:bg-orange-700">
                        <i class="fas fa-save mr-2"></i>
                        Save Hours
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Branch-Specific Settings -->
    <div class="mt-6 grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- POS Configuration -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-cash-register mr-2 text-green-600"></i>
                POS Configuration
            </h3>
            
            <form class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="default_table" class="block text-sm font-medium text-gray-700 mb-2">
                            Default Table
                        </label>
                        <select id="default_table" name="default_table" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                            <option value="takeout">Takeout</option>
                            <option value="table_1">Table 1</option>
                            <option value="table_2">Table 2</option>
                            <option value="counter">Counter</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="order_prefix" class="block text-sm font-medium text-gray-700 mb-2">
                            Order Prefix
                        </label>
                        <input type="text" id="order_prefix" name="order_prefix" value="MAIN" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="tax_rate" class="block text-sm font-medium text-gray-700 mb-2">
                            Tax Rate (%)
                        </label>
                        <input type="number" id="tax_rate" name="tax_rate" value="8.25" step="0.01" min="0" max="100"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                    </div>
                    
                    <div>
                        <label for="service_charge" class="block text-sm font-medium text-gray-700 mb-2">
                            Service Charge (%)
                        </label>
                        <input type="number" id="service_charge" name="service_charge" value="10" step="0.01" min="0" max="100"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                    </div>
                </div>

                <div>
                    <label for="receipt_footer" class="block text-sm font-medium text-gray-700 mb-2">
                        Receipt Footer Message
                    </label>
                    <textarea id="receipt_footer" name="receipt_footer" rows="3" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">Thank you for dining with us! Visit us again soon.</textarea>
                </div>

                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div>
                            <label for="auto_print_receipt" class="text-sm font-medium text-gray-700">Auto Print Receipt</label>
                            <p class="text-sm text-gray-500">Automatically print receipt after payment</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="auto_print_receipt" name="auto_print_receipt" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="customer_display" class="text-sm font-medium text-gray-700">Customer Display</label>
                            <p class="text-sm text-gray-500">Show order details to customer</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="customer_display" name="customer_display" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="require_customer_info" class="text-sm font-medium text-gray-700">Require Customer Info</label>
                            <p class="text-sm text-gray-500">Mandatory customer details for orders</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="require_customer_info" name="require_customer_info" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                        </label>
                    </div>
                </div>

                <div class="pt-4">
                    <button type="submit" class="w-full px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-lg hover:bg-green-700">
                        <i class="fas fa-save mr-2"></i>
                        Save POS Settings
                    </button>
                </div>
            </form>
        </div>

        <!-- Kitchen & Service Settings -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-utensils mr-2 text-red-600"></i>
                Kitchen & Service Settings
            </h3>
            
            <form class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="kitchen_display_mode" class="block text-sm font-medium text-gray-700 mb-2">
                            Kitchen Display Mode
                        </label>
                        <select id="kitchen_display_mode" name="kitchen_display_mode" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500">
                            <option value="order_time">Order Time</option>
                            <option value="preparation_time">Preparation Time</option>
                            <option value="priority">Priority Based</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="order_timeout" class="block text-sm font-medium text-gray-700 mb-2">
                            Order Timeout (minutes)
                        </label>
                        <input type="number" id="order_timeout" name="order_timeout" value="30" min="5" max="120"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="avg_preparation_time" class="block text-sm font-medium text-gray-700 mb-2">
                            Avg. Preparation Time (minutes)
                        </label>
                        <input type="number" id="avg_preparation_time" name="avg_preparation_time" value="15" min="1" max="60"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500">
                    </div>
                    
                    <div>
                        <label for="max_concurrent_orders" class="block text-sm font-medium text-gray-700 mb-2">
                            Max Concurrent Orders
                        </label>
                        <input type="number" id="max_concurrent_orders" name="max_concurrent_orders" value="20" min="1" max="100"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500">
                    </div>
                </div>

                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div>
                            <label for="auto_print_kitchen" class="text-sm font-medium text-gray-700">Auto Print Kitchen Orders</label>
                            <p class="text-sm text-gray-500">Automatically print orders to kitchen</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="auto_print_kitchen" name="auto_print_kitchen" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="sound_notifications" class="text-sm font-medium text-gray-700">Sound Notifications</label>
                            <p class="text-sm text-gray-500">Play sound for new orders</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="sound_notifications" name="sound_notifications" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="show_order_notes" class="text-sm font-medium text-gray-700">Show Order Notes</label>
                            <p class="text-sm text-gray-500">Display customer notes on kitchen display</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="show_order_notes" name="show_order_notes" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="auto_refresh_display" class="text-sm font-medium text-gray-700">Auto Refresh Display</label>
                            <p class="text-sm text-gray-500">Automatically refresh kitchen display</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="auto_refresh_display" name="auto_refresh_display" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                        </label>
                    </div>
                </div>

                <div class="pt-4">
                    <button type="submit" class="w-full px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-lg hover:bg-red-700">
                        <i class="fas fa-save mr-2"></i>
                        Save Kitchen Settings
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Staff & Permissions -->
    <div class="mt-6 bg-white rounded-lg shadow-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fas fa-users mr-2 text-purple-600"></i>
            Staff & Permissions
        </h3>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div class="space-y-4">
                <h4 class="font-medium text-gray-900">Branch Staff</h4>
                
                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-user text-purple-600"></i>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-900">John Doe</h5>
                                <p class="text-sm text-gray-500">Branch Manager</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="px-2 py-1 text-xs font-medium text-green-700 bg-green-100 rounded">Active</span>
                            <button class="px-3 py-1 text-xs font-medium text-purple-700 bg-purple-100 rounded hover:bg-purple-200">
                                Edit
                            </button>
                        </div>
                    </div>

                    <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-user text-blue-600"></i>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-900">Jane Smith</h5>
                                <p class="text-sm text-gray-500">Cashier</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="px-2 py-1 text-xs font-medium text-green-700 bg-green-100 rounded">Active</span>
                            <button class="px-3 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded hover:bg-blue-200">
                                Edit
                            </button>
                        </div>
                    </div>

                    <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-user text-orange-600"></i>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-900">Mike Johnson</h5>
                                <p class="text-sm text-gray-500">Kitchen Staff</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="px-2 py-1 text-xs font-medium text-yellow-700 bg-yellow-100 rounded">On Break</span>
                            <button class="px-3 py-1 text-xs font-medium text-orange-700 bg-orange-100 rounded hover:bg-orange-200">
                                Edit
                            </button>
                        </div>
                    </div>

                    <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-user text-green-600"></i>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-900">Sarah Wilson</h5>
                                <p class="text-sm text-gray-500">Server</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="px-2 py-1 text-xs font-medium text-green-700 bg-green-100 rounded">Active</span>
                            <button class="px-3 py-1 text-xs font-medium text-green-700 bg-green-100 rounded hover:bg-green-200">
                                Edit
                            </button>
                        </div>
                    </div>
                </div>

                <button class="w-full px-4 py-2 text-sm font-medium text-purple-700 bg-purple-100 border border-purple-300 rounded-lg hover:bg-purple-200">
                    <i class="fas fa-plus mr-2"></i>
                    Add New Staff Member
                </button>
            </div>

            <div class="space-y-4">
                <h4 class="font-medium text-gray-900">Branch Permissions</h4>
                
                <form class="space-y-3">
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <div>
                                <label for="allow_discounts" class="text-sm font-medium text-gray-700">Allow Discounts</label>
                                <p class="text-sm text-gray-500">Staff can apply discounts to orders</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" id="allow_discounts" name="allow_discounts" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                            </label>
                        </div>

                        <div class="flex items-center justify-between">
                            <div>
                                <label for="allow_refunds" class="text-sm font-medium text-gray-700">Allow Refunds</label>
                                <p class="text-sm text-gray-500">Staff can process refunds</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" id="allow_refunds" name="allow_refunds" class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                            </label>
                        </div>

                        <div class="flex items-center justify-between">
                            <div>
                                <label for="allow_void_orders" class="text-sm font-medium text-gray-700">Allow Void Orders</label>
                                <p class="text-sm text-gray-500">Staff can void orders</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" id="allow_void_orders" name="allow_void_orders" class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                            </label>
                        </div>

                        <div class="flex items-center justify-between">
                            <div>
                                <label for="allow_price_override" class="text-sm font-medium text-gray-700">Allow Price Override</label>
                                <p class="text-sm text-gray-500">Staff can modify item prices</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" id="allow_price_override" name="allow_price_override" class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                            </label>
                        </div>

                        <div class="flex items-center justify-between">
                            <div>
                                <label for="require_manager_approval" class="text-sm font-medium text-gray-700">Require Manager Approval</label>
                                <p class="text-sm text-gray-500">Manager approval for sensitive actions</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" id="require_manager_approval" name="require_manager_approval" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                            </label>
                        </div>

                        <div class="flex items-center justify-between">
                            <div>
                                <label for="access_reports" class="text-sm font-medium text-gray-700">Access Reports</label>
                                <p class="text-sm text-gray-500">Staff can view branch reports</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" id="access_reports" name="access_reports" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                            </label>
                        </div>
                    </div>

                    <div class="pt-4">
                        <button type="submit" class="w-full px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-lg hover:bg-purple-700">
                            <i class="fas fa-save mr-2"></i>
                            Save Permissions
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Form submission handlers
    $('form').on('submit', function(e) {
        e.preventDefault();
        
        Swal.fire({
            icon: 'success',
            title: 'Success!',
            text: 'Branch settings updated successfully.',
            timer: 2000,
            showConfirmButton: false
        });
    });

    // Branch selection handler
    $('#branch_select').on('change', function() {
        const selectedBranch = $(this).find('option:selected').text();
        
        Swal.fire({
            title: 'Switch Branch',
            text: `Are you sure you want to switch to ${selectedBranch}?`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Switch',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                // Simulate loading new branch data
                Swal.fire({
                    title: 'Loading...',
                    text: 'Loading branch settings...',
                    icon: 'info',
                    showConfirmButton: false,
                    timer: 2000
                }).then(() => {
                    Swal.fire({
                        icon: 'success',
                        title: 'Branch Switched',
                        text: `Successfully switched to ${selectedBranch}`,
                        timer: 1500,
                        showConfirmButton: false
                    });
                });
            }
        });
    });

    // Sync settings handler
    $('#sync-settings-btn').on('click', function() {
        Swal.fire({
            title: 'Sync Settings',
            text: 'This will sync settings from the main branch. Continue?',
            icon: 'info',
            showCancelButton: true,
            confirmButtonText: 'Sync',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                Swal.fire({
                    title: 'Syncing...',
                    text: 'Synchronizing settings...',
                    icon: 'info',
                    showConfirmButton: false,
                    timer: 3000
                }).then(() => {
                    Swal.fire({
                        icon: 'success',
                        title: 'Settings Synced',
                        text: 'Branch settings have been synchronized successfully.',
                        confirmButtonText: 'OK'
                    });
                });
            }
        });
    });

    // Clone settings handler
    $('#clone-settings-btn').on('click', function() {
        Swal.fire({
            title: 'Clone Settings',
            input: 'select',
            inputOptions: {
                'north': 'North Branch - Mall Plaza',
                'south': 'South Branch - Shopping Center',
                'east': 'East Branch - Business District'
            },
            inputPlaceholder: 'Select target branch',
            showCancelButton: true,
            confirmButtonText: 'Clone',
            inputValidator: (value) => {
                if (!value) {
                    return 'Please select a target branch!';
                }
            }
        }).then((result) => {
            if (result.isConfirmed) {
                const targetBranch = result.value;
                
                Swal.fire({
                    title: 'Cloning Settings...',
                    text: 'Copying settings to target branch...',
                    icon: 'info',
                    showConfirmButton: false,
                    timer: 3000
                }).then(() => {
                    Swal.fire({
                        icon: 'success',
                        title: 'Settings Cloned',
                        text: `Settings have been cloned to the selected branch successfully.`,
                        confirmButtonText: 'OK'
                    });
                });
            }
        });
    });

    // Copy hours to all days
    $('#copy-hours-btn').on('click', function() {
        const mondayStart = $('#monday_start').val();
        const mondayEnd = $('#monday_end').val();
        
        if (mondayStart && mondayEnd) {
            Swal.fire({
                title: 'Copy Hours',
                text: `Copy Monday's hours (${mondayStart} - ${mondayEnd}) to all days?`,
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'Copy',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Copy Monday's hours to all other days
                    const days = ['tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
                    days.forEach(day => {
                        $(`#${day}_start`).val(mondayStart);
                        $(`#${day}_end`).val(mondayEnd);
                        $(`#${day}_open`).prop('checked', true);
                    });
                    
                    Swal.fire({
                        icon: 'success',
                        title: 'Hours Copied',
                        text: 'Monday hours have been copied to all days.',
                        timer: 1500,
                        showConfirmButton: false
                    });
                }
            });
        } else {
            Swal.fire({
                icon: 'warning',
                title: 'Missing Hours',
                text: 'Please set Monday hours first.',
                confirmButtonText: 'OK'
            });
        }
    });

    // Operating hours toggle
    $('input[type="checkbox"][id$="_open"]').on('change', function() {
        const day = $(this).attr('id').replace('_open', '');
        const startInput = $(`#${day}_start`);
        const endInput = $(`#${day}_end`);
        
        if ($(this).is(':checked')) {
            startInput.prop('disabled', false);
            endInput.prop('disabled', false);
        } else {
            startInput.prop('disabled', true);
            endInput.prop('disabled', true);
        }
    });

    // Staff edit handlers
    $('button:contains("Edit")').on('click', function() {
        const staffName = $(this).closest('.flex').find('h5').text();
        const staffRole = $(this).closest('.flex').find('p').text();
        
        Swal.fire({
            title: `Edit ${staffName}`,
            html: `
                <div class="text-left space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Name</label>
                        <input type="text" id="edit_name" value="${staffName}" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Role</label>
                        <select id="edit_role" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                            <option value="manager" ${staffRole === 'Branch Manager' ? 'selected' : ''}>Branch Manager</option>
                            <option value="cashier" ${staffRole === 'Cashier' ? 'selected' : ''}>Cashier</option>
                            <option value="kitchen" ${staffRole === 'Kitchen Staff' ? 'selected' : ''}>Kitchen Staff</option>
                            <option value="server" ${staffRole === 'Server' ? 'selected' : ''}>Server</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <select id="edit_status" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                            <option value="active">Active</option>
                            <option value="break">On Break</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'Save Changes',
            cancelButtonText: 'Cancel',
            preConfirm: () => {
                return {
                    name: document.getElementById('edit_name').value,
                    role: document.getElementById('edit_role').value,
                    status: document.getElementById('edit_status').value
                };
            }
        }).then((result) => {
            if (result.isConfirmed) {
                Swal.fire({
                    icon: 'success',
                    title: 'Staff Updated',
                    text: 'Staff member information has been updated.',
                    timer: 1500,
                    showConfirmButton: false
                });
            }
        });
    });

    // Add new staff member
    $('button:contains("Add New Staff Member")').on('click', function() {
        Swal.fire({
            title: 'Add New Staff Member',
            html: `
                <div class="text-left space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Name</label>
                        <input type="text" id="new_name" placeholder="Enter staff name" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <input type="email" id="new_email" placeholder="Enter email address" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Role</label>
                        <select id="new_role" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                            <option value="">Select role</option>
                            <option value="manager">Branch Manager</option>
                            <option value="cashier">Cashier</option>
                            <option value="kitchen">Kitchen Staff</option>
                            <option value="server">Server</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                        <input type="tel" id="new_phone" placeholder="Enter phone number" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'Add Staff',
            cancelButtonText: 'Cancel',
            preConfirm: () => {
                const name = document.getElementById('new_name').value;
                const email = document.getElementById('new_email').value;
                const role = document.getElementById('new_role').value;
                const phone = document.getElementById('new_phone').value;
                
                if (!name || !email || !role) {
                    Swal.showValidationMessage('Please fill in all required fields');
                    return false;
                }
                
                return { name, email, role, phone };
            }
        }).then((result) => {
            if (result.isConfirmed) {
                Swal.fire({
                    icon: 'success',
                    title: 'Staff Added',
                    text: 'New staff member has been added successfully.',
                    timer: 1500,
                    showConfirmButton: false
                });
            }
        });
    });

    // Quick action handlers
    $('button:contains("Edit Info")').on('click', function() {
        Swal.fire({
            title: 'Edit Branch Information',
            text: 'This will open the branch information form for editing.',
            icon: 'info',
            confirmButtonText: 'OK'
        });
    });

    $('button:contains("View Stats")').on('click', function() {
        Swal.fire({
            title: 'Branch Statistics',
            html: `
                <div class="text-left space-y-3">
                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center p-3 bg-blue-50 rounded">
                            <div class="text-2xl font-bold text-blue-600">156</div>
                            <div class="text-sm text-blue-800">Orders Today</div>
                        </div>
                        <div class="text-center p-3 bg-green-50 rounded">
                            <div class="text-2xl font-bold text-green-600">$2,847</div>
                            <div class="text-sm text-green-800">Revenue Today</div>
                        </div>
                        <div class="text-center p-3 bg-orange-50 rounded">
                            <div class="text-2xl font-bold text-orange-600">4.8</div>
                            <div class="text-sm text-orange-800">Avg Rating</div>
                        </div>
                        <div class="text-center p-3 bg-purple-50 rounded">
                            <div class="text-2xl font-bold text-purple-600">12</div>
                            <div class="text-sm text-purple-800">Staff Online</div>
                        </div>
                    </div>
                </div>
            `,
            confirmButtonText: 'Close'
        });
    });

    // Initialize disabled state for closed days
    $('input[type="checkbox"][id$="_open"]').each(function() {
        const day = $(this).attr('id').replace('_open', '');
        const startInput = $(`#${day}_start`);
        const endInput = $(`#${day}_end`);
        
        if (!$(this).is(':checked')) {
            startInput.prop('disabled', true);
            endInput.prop('disabled', true);
        }
    });
});
</script>
@endpush