<?php

namespace Modules\Delivery\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Modules\Delivery\Models\DeliveryAssignment;
use Modules\Delivery\Models\DeliveryPersonnel;
use Modules\Orders\Models\Order;
use App\Models\User;
use App\Models\Branch;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Facades\Auth;

class DeliveryAssignmentWebController extends Controller
{
    /**
     * Display delivery assignments listing page
     */
    public function index(): View
    {
        $branches = Branch::where('tenant_id', Auth::user()->tenant_id)->get();
        $personnel = DeliveryPersonnel::where('tenant_id', Auth::user()->tenant_id)->get();
        
        return view('delivery::assignments.index', compact('branches', 'personnel'));
    }

    /**
     * Show specific delivery assignment details
     */
    public function show(DeliveryAssignment $assignment): View
    {
        $assignment->load(['order.customer', 'personnel', 'tracking']);
        
        return view('Delivery::assignments.show', compact('assignment'));
    }

    /**
     * Get assignments data for DataTables
     */
    public function getAssignmentsData(Request $request): JsonResponse
    {
        $query = DeliveryAssignment::with([
            'order.customer', 
            'personnel', 
            'order.branch',
            'latestTracking'
        ])
        ->whereHas('order', function($q) {
            $q->where('tenant_id', Auth::user()->tenant_id);
        });

        // Apply filters
        if ($request->filled('personnel_id')) {
            $query->where('delivery_personnel_id', $request->personnel_id);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('branch_id')) {
            $query->whereHas('order', function($q) use ($request) {
                $q->where('branch_id', $request->branch_id);
            });
        }

        if ($request->filled('date_from')) {
            $query->whereDate('assigned_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('assigned_at', '<=', $request->date_to);
        }

        return DataTables::of($query)
            ->addColumn('order_number', function ($assignment) {
                return $assignment->order->order_number ?? 'N/A';
            })
            ->addColumn('customer_name', function ($assignment) {
                return $assignment->order->customer->name ?? 'Guest';
            })
            ->addColumn('customer_phone', function ($assignment) {
                return $assignment->order->customer->phone ?? $assignment->order->customer_phone ?? 'N/A';
            })
            ->addColumn('personnel_name', function ($assignment) {
                return $assignment->personnel->name ?? 'Unassigned';
            })
            ->addColumn('branch_name', function ($assignment) {
                return $assignment->order->branch->name ?? 'N/A';
            })
            ->addColumn('delivery_address', function ($assignment) {
                return $assignment->delivery_address ?? $assignment->order->delivery_address ?? 'N/A';
            })
            ->addColumn('status_badge', function ($assignment) {
                $statusColors = [
                    'pending' => 'warning',
                    'assigned' => 'info',
                    'picked_up' => 'primary',
                    'in_transit' => 'secondary',
                    'delivered' => 'success',
                    'cancelled' => 'danger',
                    'failed' => 'danger'
                ];
                
                $color = $statusColors[$assignment->status] ?? 'secondary';
                return '<span class="badge bg-' . $color . '">' . ucfirst(str_replace('_', ' ', $assignment->status)) . '</span>';
            })
            ->addColumn('estimated_delivery', function ($assignment) {
                return $assignment->estimated_delivery_time ? 
                    $assignment->estimated_delivery_time->format('M d, Y H:i') : 'N/A';
            })
            ->addColumn('actual_delivery', function ($assignment) {
                return $assignment->delivered_at ? 
                    $assignment->delivered_at->format('M d, Y H:i') : 'N/A';
            })
            ->addColumn('delivery_fee', function ($assignment) {
                return $assignment->delivery_fee ? '$' . number_format($assignment->delivery_fee, 2) : 'N/A';
            })
            ->addColumn('actions', function ($assignment) {
                $actions = '<div class="btn-group" role="group">';
                
                // View button
                $actions .= '<a href="' . route('delivery.assignments.show', $assignment->id) . '" 
                            class="btn btn-sm btn-outline-primary" title="View Details">
                            <i class="fas fa-eye"></i>
                        </a>';
                
                // Track button (if in transit)
                if (in_array($assignment->status, ['assigned', 'picked_up', 'in_transit'])) {
                    $actions .= '<button type="button" class="btn btn-sm btn-outline-info track-assignment" 
                                data-assignment-id="' . $assignment->id . '" title="Track Delivery">
                                <i class="fas fa-map-marker-alt"></i>
                            </button>';
                }
                
                // Status update buttons
                if ($assignment->status === 'pending') {
                    $actions .= '<button type="button" class="btn btn-sm btn-outline-success assign-personnel" 
                                data-assignment-id="' . $assignment->id . '" title="Assign Personnel">
                                <i class="fas fa-user-plus"></i>
                            </button>';
                }
                
                if (in_array($assignment->status, ['assigned', 'picked_up', 'in_transit'])) {
                    $actions .= '<button type="button" class="btn btn-sm btn-outline-warning update-status" 
                                data-assignment-id="' . $assignment->id . '" title="Update Status">
                                <i class="fas fa-edit"></i>
                            </button>';
                }
                
                $actions .= '</div>';
                
                return $actions;
            })
            ->rawColumns(['status_badge', 'actions'])
            ->make(true);
    }

    /**
     * Assign personnel to delivery
     */
    public function assignPersonnel(Request $request, DeliveryAssignment $assignment): JsonResponse
    {
        $request->validate([
            'personnel_id' => 'required|exists:delivery_personnel,id'
        ]);

        try {
            $assignment->update([
                'delivery_personnel_id' => $request->personnel_id,
                'status' => 'assigned',
                'assigned_at' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Personnel assigned successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to assign personnel: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update delivery status
     */
    public function updateStatus(Request $request, DeliveryAssignment $assignment): JsonResponse
    {
        $request->validate([
            'status' => 'required|in:pending,assigned,picked_up,in_transit,delivered,cancelled,failed',
            'notes' => 'nullable|string|max:500'
        ]);

        try {
            $updateData = ['status' => $request->status];
            
            if ($request->status === 'delivered') {
                $updateData['delivered_at'] = now();
            }
            
            if ($request->filled('notes')) {
                $updateData['notes'] = $request->notes;
            }

            $assignment->update($updateData);

            return response()->json([
                'success' => true,
                'message' => 'Status updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get assignment statistics
     */
    public function getStatistics(Request $request): JsonResponse
    {
        $query = DeliveryAssignment::whereHas('order', function($q) {
            $q->where('tenant_id', Auth::user()->tenant_id);
        });

        // Apply date filters if provided
        if ($request->filled('date_from')) {
            $query->whereDate('assigned_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('assigned_at', '<=', $request->date_to);
        }

        $totalAssignments = $query->count();
        $pendingAssignments = (clone $query)->where('status', 'pending')->count();
        $inTransitAssignments = (clone $query)->whereIn('status', ['assigned', 'picked_up', 'in_transit'])->count();
        $deliveredAssignments = (clone $query)->where('status', 'delivered')->count();
        $cancelledAssignments = (clone $query)->whereIn('status', ['cancelled', 'failed'])->count();

        $avgDeliveryTime = (clone $query)
            ->where('status', 'delivered')
            ->whereNotNull('delivered_at')
            ->whereNotNull('assigned_at')
            ->selectRaw('AVG(TIMESTAMPDIFF(MINUTE, assigned_at, delivered_at)) as avg_time')
            ->value('avg_time');

        return response()->json([
            'total_assignments' => $totalAssignments,
            'pending_assignments' => $pendingAssignments,
            'in_transit_assignments' => $inTransitAssignments,
            'delivered_assignments' => $deliveredAssignments,
            'cancelled_assignments' => $cancelledAssignments,
            'completion_rate' => $totalAssignments > 0 ? round(($deliveredAssignments / $totalAssignments) * 100, 2) : 0,
            'average_delivery_time' => $avgDeliveryTime ? round($avgDeliveryTime, 2) : 0
        ]);
    }

    /**
     * Get available personnel for assignment
     */
    public function getAvailablePersonnel(): JsonResponse
    {
        $personnel = DeliveryPersonnel::where('tenant_id', Auth::user()->tenant_id)
            ->where('status', 'active')
            ->where('is_available', true)
            ->select('id', 'name', 'phone', 'vehicle_type')
            ->get();

        return response()->json($personnel);
    }

    /**
     * Get available orders for delivery assignment
     */
    public function getAvailableOrders(): JsonResponse
    {
        $orders = Order::where('tenant_id', Auth::user()->tenant_id)
            ->where('status', 'ready_for_delivery')
            ->whereDoesntHave('deliveryAssignment')
            ->with(['customer', 'branch'])
            ->select('id', 'order_number', 'customer_id', 'branch_id', 'delivery_address', 'total')
            ->get();

        return response()->json($orders);
    }

    /**
     * Store a new delivery assignment
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'order_id' => 'required|exists:orders,id',
            'delivery_personnel_id' => 'nullable|exists:delivery_personnel,id',
            'delivery_address' => 'required|string|max:500',
            'delivery_fee' => 'nullable|numeric|min:0',
            'estimated_delivery_time' => 'nullable|date',
            'delivery_latitude' => 'nullable|numeric',
            'delivery_longitude' => 'nullable|numeric',
            'priority' => 'nullable|in:normal,high,urgent',
            'notes' => 'nullable|string|max:1000'
        ]);

        try {
            // Check if order already has an assignment
            $existingAssignment = DeliveryAssignment::where('order_id', $request->order_id)->first();
            if ($existingAssignment) {
                return response()->json([
                    'success' => false,
                    'message' => 'This order already has a delivery assignment'
                ], 400);
            }

            $assignment = DeliveryAssignment::create([
                'order_id' => $request->order_id,
                'delivery_personnel_id' => $request->delivery_personnel_id,
                'delivery_address' => $request->delivery_address,
                'delivery_fee' => $request->delivery_fee ?? 0,
                'estimated_delivery_time' => $request->estimated_delivery_time,
                'delivery_latitude' => $request->delivery_latitude,
                'delivery_longitude' => $request->delivery_longitude,
                'priority' => $request->priority ?? 'normal',
                'notes' => $request->notes,
                'status' => $request->delivery_personnel_id ? 'assigned' : 'pending',
                'assigned_at' => $request->delivery_personnel_id ? now() : null
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Assignment created successfully',
                'assignment' => $assignment
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create assignment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get assignment data for editing
     */
    public function edit(DeliveryAssignment $assignment): JsonResponse
    {
        $assignment->load(['order', 'personnel']);
        
        return response()->json([
            'success' => true,
            'assignment' => $assignment
        ]);
    }

    /**
     * Update delivery assignment
     */
    public function update(Request $request, DeliveryAssignment $assignment): JsonResponse
    {
        $request->validate([
            'delivery_personnel_id' => 'nullable|exists:delivery_personnel,id',
            'delivery_address' => 'required|string|max:500',
            'delivery_fee' => 'nullable|numeric|min:0',
            'estimated_delivery_time' => 'nullable|date',
            'delivery_latitude' => 'nullable|numeric',
            'delivery_longitude' => 'nullable|numeric',
            'priority' => 'nullable|in:normal,high,urgent',
            'status' => 'required|in:pending,assigned,picked_up,in_transit,delivered,cancelled,failed',
            'notes' => 'nullable|string|max:1000'
        ]);

        try {
            $updateData = $request->only([
                'delivery_personnel_id',
                'delivery_address',
                'delivery_fee',
                'estimated_delivery_time',
                'delivery_latitude',
                'delivery_longitude',
                'priority',
                'status',
                'notes'
            ]);

            // Update assigned_at if personnel is being assigned
            if ($request->delivery_personnel_id && !$assignment->delivery_personnel_id) {
                $updateData['assigned_at'] = now();
            }

            // Update delivered_at if status is delivered
            if ($request->status === 'delivered' && $assignment->status !== 'delivered') {
                $updateData['delivered_at'] = now();
            }

            $assignment->update($updateData);

            return response()->json([
                'success' => true,
                'message' => 'Assignment updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update assignment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete delivery assignment
     */
    public function destroy(DeliveryAssignment $assignment): JsonResponse
    {
        try {
            // Check if assignment can be deleted (not delivered)
            if ($assignment->status === 'delivered') {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete delivered assignments'
                ], 400);
            }

            $assignment->delete();

            return response()->json([
                'success' => true,
                'message' => 'Assignment deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete assignment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle bulk actions
     */
    public function bulkAction(Request $request): JsonResponse
    {
        $request->validate([
            'action' => 'required|in:assign_personnel,update_status,delete,export',
            'assignment_ids' => 'required|array',
            'assignment_ids.*' => 'exists:delivery_assignments,id',
            'personnel_id' => 'required_if:action,assign_personnel|exists:delivery_personnel,id',
            'status' => 'required_if:action,update_status|in:pending,assigned,picked_up,in_transit,delivered,cancelled,failed',
            'notes' => 'nullable|string|max:1000'
        ]);

        try {
            $assignments = DeliveryAssignment::whereIn('id', $request->assignment_ids)
                ->whereHas('order', function($q) {
                    $q->where('tenant_id', Auth::user()->tenant_id);
                })
                ->get();

            $successCount = 0;
            $errors = [];

            foreach ($assignments as $assignment) {
                try {
                    switch ($request->action) {
                        case 'assign_personnel':
                            $assignment->update([
                                'delivery_personnel_id' => $request->personnel_id,
                                'status' => 'assigned',
                                'assigned_at' => now(),
                                'notes' => $request->notes
                            ]);
                            $successCount++;
                            break;

                        case 'update_status':
                            $updateData = ['status' => $request->status];
                            if ($request->status === 'delivered') {
                                $updateData['delivered_at'] = now();
                            }
                            if ($request->notes) {
                                $updateData['notes'] = $request->notes;
                            }
                            $assignment->update($updateData);
                            $successCount++;
                            break;

                        case 'delete':
                            if ($assignment->status !== 'delivered') {
                                $assignment->delete();
                                $successCount++;
                            } else {
                                $errors[] = "Assignment #{$assignment->id} cannot be deleted (delivered)";
                            }
                            break;

                        case 'export':
                            // Export functionality would be implemented here
                            $successCount++;
                            break;
                    }
                } catch (\Exception $e) {
                    $errors[] = "Assignment #{$assignment->id}: " . $e->getMessage();
                }
            }

            $message = "{$successCount} assignments processed successfully";
            if (!empty($errors)) {
                $message .= ". Errors: " . implode(', ', $errors);
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'processed_count' => $successCount,
                'errors' => $errors
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Bulk action failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get assignment details for modal view
     */
    public function getDetails(DeliveryAssignment $assignment): JsonResponse
    {
        $assignment->load([
            'order.customer',
            'order.branch',
            'order.orderItems.menuItem',
            'personnel',
            'tracking'
        ]);

        return response()->json([
            'success' => true,
            'assignment' => $assignment
        ]);
    }
}