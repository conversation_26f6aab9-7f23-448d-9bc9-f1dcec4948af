<?php

namespace Modules\Menu\Services;

use App\Models\Event;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * Event Service
 * 
 * Handles business logic for event management including creation,
 * updates, reservations, and event analytics.
 * 
 * @package Modules\Menu\Services
 * <AUTHOR> POS Team
 * @version 1.0.0
 */
class EventService
{
    /**
     * Get all events with optional filtering
     *
     * @param array $filters
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getAllEvents(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = Event::query();

        // Apply filters
        if (isset($filters['branch_id'])) {
            $query->forBranch($filters['branch_id']);
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        if (isset($filters['is_featured'])) {
            $query->featured();
        }

        if (isset($filters['event_type'])) {
            $query->byType($filters['event_type']);
        }

        if (isset($filters['status'])) {
            switch ($filters['status']) {
                case 'current':
                    $query->current();
                    break;
                case 'upcoming':
                    $query->upcoming();
                    break;
                case 'ended':
                    $query->ended();
                    break;
            }
        }

        if (isset($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('description', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('location', 'like', '%' . $filters['search'] . '%');
            });
        }

        return $query->with(['tenant', 'branch'])
                    ->orderBy('start_date', 'desc')
                    ->paginate($perPage);
    }

    /**
     * Get event by ID
     *
     * @param int $id
     * @return Event|null
     */
    public function getEventById(int $id): ?Event
    {
        return Event::with(['tenant', 'branch'])->find($id);
    }

    /**
     * Create a new event
     *
     * @param array $data
     * @return Event
     * @throws Exception
     */
    public function createEvent(array $data): Event
    {
        try {
            DB::beginTransaction();

            // Generate unique code if not provided
            if (!isset($data['code'])) {
                $data['code'] = $this->generateEventCode($data['name']);
            }

            // Set default values
            $data['current_participants'] = 0;
            $data['sort_order'] = $data['sort_order'] ?? 0;

            $event = Event::create($data);

            DB::commit();

            Log::info('Event created successfully', ['event_id' => $event->id]);

            return $event;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Failed to create event', ['error' => $e->getMessage(), 'data' => $data]);
            throw $e;
        }
    }

    /**
     * Update an existing event
     *
     * @param int $id
     * @param array $data
     * @return Event
     * @throws Exception
     */
    public function updateEvent(int $id, array $data): Event
    {
        try {
            DB::beginTransaction();

            $event = Event::findOrFail($id);
            $event->update($data);

            DB::commit();

            Log::info('Event updated successfully', ['event_id' => $event->id]);

            return $event->fresh();
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Failed to update event', ['error' => $e->getMessage(), 'event_id' => $id]);
            throw $e;
        }
    }

    /**
     * Delete an event
     *
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function deleteEvent(int $id): bool
    {
        try {
            DB::beginTransaction();

            $event = Event::findOrFail($id);
            
            // Check if event has participants
            if ($event->current_participants > 0) {
                throw new Exception('Cannot delete event with existing participants');
            }

            $event->delete();

            DB::commit();

            Log::info('Event deleted successfully', ['event_id' => $id]);

            return true;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Failed to delete event', ['error' => $e->getMessage(), 'event_id' => $id]);
            throw $e;
        }
    }

    /**
     * Get featured events
     *
     * @param int|null $branchId
     * @param int $limit
     * @return Collection
     */
    public function getFeaturedEvents(?int $branchId = null, int $limit = 5): Collection
    {
        $query = Event::featured()->active()->current();

        if ($branchId) {
            $query->forBranch($branchId);
        }

        return $query->orderBy('sort_order')
                    ->limit($limit)
                    ->get();
    }

    /**
     * Get upcoming events
     *
     * @param int|null $branchId
     * @param int $limit
     * @return Collection
     */
    public function getUpcomingEvents(?int $branchId = null, int $limit = 10): Collection
    {
        $query = Event::upcoming()->active();

        if ($branchId) {
            $query->forBranch($branchId);
        }

        return $query->orderBy('start_date')
                    ->limit($limit)
                    ->get();
    }

    /**
     * Register for an event
     *
     * @param int $eventId
     * @param array $participantData
     * @return bool
     * @throws Exception
     */
    public function registerForEvent(int $eventId, array $participantData): bool
    {
        try {
            DB::beginTransaction();

            $event = Event::findOrFail($eventId);

            // Check if event is active and not ended
            if (!$event->isActive() || $event->hasEnded()) {
                throw new Exception('Event is not available for registration');
            }

            // Check if event requires reservation
            if (!$event->requires_reservation) {
                throw new Exception('This event does not require reservation');
            }

            // Check if there are available spots
            if (!$event->hasAvailableSpots()) {
                throw new Exception('No available spots for this event');
            }

            // Increment participant count
            $event->increment('current_participants');

            // Here you would typically create a reservation record
            // For now, we'll just log the registration
            Log::info('Event registration successful', [
                'event_id' => $eventId,
                'participant' => $participantData
            ]);

            DB::commit();

            return true;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Failed to register for event', [
                'error' => $e->getMessage(),
                'event_id' => $eventId,
                'participant' => $participantData
            ]);
            throw $e;
        }
    }

    /**
     * Get event analytics
     *
     * @param int $eventId
     * @return array
     */
    public function getEventAnalytics(int $eventId): array
    {
        $event = Event::findOrFail($eventId);

        return [
            'event_id' => $event->id,
            'name' => $event->name,
            'status' => $event->getStatus(),
            'participants' => [
                'current' => $event->current_participants,
                'max' => $event->max_participants,
                'percentage' => $event->max_participants ? 
                    round(($event->current_participants / $event->max_participants) * 100, 2) : 0,
                'available_spots' => $event->getAvailableSpots(),
            ],
            'duration' => $event->getFormattedDuration(),
            'revenue' => [
                'potential' => $event->price * $event->max_participants,
                'current' => $event->price * $event->current_participants,
            ],
            'metadata' => $event->metadata ?? [],
        ];
    }

    /**
     * Generate unique event code
     *
     * @param string $name
     * @return string
     */
    private function generateEventCode(string $name): string
    {
        $baseCode = strtoupper(substr(preg_replace('/[^A-Za-z0-9]/', '', $name), 0, 6));
        $counter = 1;
        $code = $baseCode;

        while (Event::where('code', $code)->exists()) {
            $code = $baseCode . str_pad($counter, 2, '0', STR_PAD_LEFT);
            $counter++;
        }

        return $code;
    }

    /**
     * Toggle event status
     *
     * @param int $id
     * @return Event
     * @throws Exception
     */
    public function toggleEventStatus(int $id): Event
    {
        try {
            $event = Event::findOrFail($id);
            $event->update(['is_active' => !$event->is_active]);

            Log::info('Event status toggled', [
                'event_id' => $id,
                'new_status' => $event->is_active ? 'active' : 'inactive'
            ]);

            return $event;
        } catch (Exception $e) {
            Log::error('Failed to toggle event status', ['error' => $e->getMessage(), 'event_id' => $id]);
            throw $e;
        }
    }

    /**
     * Duplicate an event
     *
     * @param int $id
     * @param array $overrides
     * @return Event
     * @throws Exception
     */
    public function duplicateEvent(int $id, array $overrides = []): Event
    {
        try {
            DB::beginTransaction();

            $originalEvent = Event::findOrFail($id);
            $eventData = $originalEvent->toArray();

            // Remove fields that shouldn't be duplicated
            unset($eventData['id'], $eventData['created_at'], $eventData['updated_at']);
            
            // Reset participant count
            $eventData['current_participants'] = 0;
            
            // Generate new code
            $eventData['code'] = $this->generateEventCode($eventData['name'] . ' Copy');
            
            // Apply overrides
            $eventData = array_merge($eventData, $overrides);

            $newEvent = Event::create($eventData);

            DB::commit();

            Log::info('Event duplicated successfully', [
                'original_id' => $id,
                'new_id' => $newEvent->id
            ]);

            return $newEvent;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Failed to duplicate event', ['error' => $e->getMessage(), 'event_id' => $id]);
            throw $e;
        }
    }
}