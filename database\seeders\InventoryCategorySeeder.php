<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\InventoryCategory;
use App\Models\Tenant;

class InventoryCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all tenants
        $tenants = Tenant::all();

        foreach ($tenants as $tenant) {
            // Main categories
            $categories = [
                [
                    'name' => 'المكونات الأساسية',
                    'description' => 'المكونات الأساسية للطبخ',
                    'sort_order' => 1,
                    'children' => [
                        ['name' => 'اللحوم', 'description' => 'جميع أنواع اللحوم', 'sort_order' => 1],
                        ['name' => 'الدواجن', 'description' => 'الدجاج والديك الرومي وغيرها', 'sort_order' => 2],
                        ['name' => 'الأسماك والمأكولات البحرية', 'description' => 'الأسماك والجمبري وغيرها', 'sort_order' => 3],
                        ['name' => 'الخضروات', 'description' => 'جميع أنواع الخضروات', 'sort_order' => 4],
                        ['name' => 'الفواكه', 'description' => 'جميع أنواع الفواكه', 'sort_order' => 5],
                        ['name' => 'الحبوب والبقوليات', 'description' => 'الأرز والعدس والفاصوليا وغيرها', 'sort_order' => 6],
                        ['name' => 'منتجات الألبان', 'description' => 'الحليب والجبن والزبدة وغيرها', 'sort_order' => 7],
                    ]
                ],
                [
                    'name' => 'التوابل والبهارات',
                    'description' => 'جميع أنواع التوابل والبهارات',
                    'sort_order' => 2,
                    'children' => [
                        ['name' => 'البهارات المحلية', 'description' => 'البهارات التقليدية', 'sort_order' => 1],
                        ['name' => 'البهارات العالمية', 'description' => 'البهارات من مختلف البلدان', 'sort_order' => 2],
                        ['name' => 'الأعشاب الطازجة', 'description' => 'البقدونس والنعناع وغيرها', 'sort_order' => 3],
                        ['name' => 'الأعشاب المجففة', 'description' => 'الأعشاب المجففة والمحفوظة', 'sort_order' => 4],
                    ]
                ],
                [
                    'name' => 'المشروبات',
                    'description' => 'جميع أنواع المشروبات',
                    'sort_order' => 3,
                    'children' => [
                        ['name' => 'المشروبات الغازية', 'description' => 'الكولا والسبرايت وغيرها', 'sort_order' => 1],
                        ['name' => 'العصائر الطبيعية', 'description' => 'عصائر الفواكه الطبيعية', 'sort_order' => 2],
                        ['name' => 'المشروبات الساخنة', 'description' => 'الشاي والقهوة وغيرها', 'sort_order' => 3],
                        ['name' => 'المياه', 'description' => 'المياه المعدنية والعادية', 'sort_order' => 4],
                    ]
                ],
                [
                    'name' => 'مواد التعبئة والتغليف',
                    'description' => 'مواد التعبئة والتغليف للطعام',
                    'sort_order' => 4,
                    'children' => [
                        ['name' => 'الأكياس البلاستيكية', 'description' => 'أكياس التعبئة البلاستيكية', 'sort_order' => 1],
                        ['name' => 'العلب الورقية', 'description' => 'علب الطعام الورقية', 'sort_order' => 2],
                        ['name' => 'الأكواب والأطباق', 'description' => 'أكواب وأطباق التقديم', 'sort_order' => 3],
                        ['name' => 'أدوات المائدة', 'description' => 'الملاعق والشوك والسكاكين', 'sort_order' => 4],
                    ]
                ],
                [
                    'name' => 'مواد التنظيف',
                    'description' => 'مواد التنظيف والتطهير',
                    'sort_order' => 5,
                    'children' => [
                        ['name' => 'منظفات الأطباق', 'description' => 'سوائل وبودرة غسيل الأطباق', 'sort_order' => 1],
                        ['name' => 'منظفات الأرضيات', 'description' => 'منظفات ومطهرات الأرضيات', 'sort_order' => 2],
                        ['name' => 'منظفات الأسطح', 'description' => 'منظفات الطاولات والأسطح', 'sort_order' => 3],
                        ['name' => 'مواد التطهير', 'description' => 'الكحول والمطهرات', 'sort_order' => 4],
                    ]
                ],
                [
                    'name' => 'المعدات والأدوات',
                    'description' => 'المعدات والأدوات المطبخية',
                    'sort_order' => 6,
                    'children' => [
                        ['name' => 'أدوات الطبخ', 'description' => 'المقالي والأواني', 'sort_order' => 1],
                        ['name' => 'أدوات التقطيع', 'description' => 'السكاكين وألواح التقطيع', 'sort_order' => 2],
                        ['name' => 'أدوات القياس', 'description' => 'الكؤوس والملاعق المعيارية', 'sort_order' => 3],
                        ['name' => 'المعدات الكهربائية', 'description' => 'الخلاطات والمحضرات', 'sort_order' => 4],
                    ]
                ]
            ];

            foreach ($categories as $categoryData) {
                $children = $categoryData['children'] ?? [];
                unset($categoryData['children']);
                
                $categoryData['tenant_id'] = $tenant->id;
                $categoryData['is_active'] = true;
                
                $parentCategory = InventoryCategory::create($categoryData);

                // Create child categories
                foreach ($children as $childData) {
                    $childData['parent_id'] = $parentCategory->id;
                    $childData['tenant_id'] = $tenant->id;
                    $childData['is_active'] = true;
                    
                    InventoryCategory::create($childData);
                }
            }
        }
    }
}