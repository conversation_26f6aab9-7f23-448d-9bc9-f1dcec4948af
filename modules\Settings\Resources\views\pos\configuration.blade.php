@extends('layouts.master')

@section('title', 'POS Configuration')

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
@endpush

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-emerald-600 to-teal-600 rounded-lg shadow-lg p-6 mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                            <i class="fas fa-cash-register text-white text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <h1 class="text-2xl font-bold text-white">POS Configuration</h1>
                        <p class="text-emerald-100">Configure Point of Sale system settings and behavior</p>
                    </div>
                </div>
            </div>
            <div class="mt-4 md:mt-0">
                <button class="px-4 py-2 bg-white text-emerald-600 rounded-lg hover:bg-emerald-50 transition-colors duration-200 font-medium">
                    <i class="fas fa-sync mr-2"></i>
                    Reset to Defaults
                </button>
            </div>
        </div>
    </div>

    <!-- POS Status Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-desktop text-green-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Active Terminals</p>
                    <p class="text-2xl font-bold text-gray-900">8</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-receipt text-blue-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Today's Orders</p>
                    <p class="text-2xl font-bold text-gray-900">142</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-clock text-yellow-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Avg. Order Time</p>
                    <p class="text-2xl font-bold text-gray-900">3.2m</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-dollar-sign text-purple-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Today's Revenue</p>
                    <p class="text-2xl font-bold text-gray-900">$2,847</p>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- General POS Settings -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-cog mr-2 text-emerald-600"></i>
                General POS Settings
            </h3>
            
            <form class="space-y-4">
                <div>
                    <label for="default_table" class="block text-sm font-medium text-gray-700 mb-2">
                        Default Table Selection
                    </label>
                    <select id="default_table" name="default_table" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                        <option value="none">No Default</option>
                        <option value="takeaway" selected>Takeaway</option>
                        <option value="table_1">Table 1</option>
                        <option value="delivery">Delivery</option>
                    </select>
                </div>

                <div>
                    <label for="order_prefix" class="block text-sm font-medium text-gray-700 mb-2">
                        Order Number Prefix
                    </label>
                    <input type="text" id="order_prefix" name="order_prefix" value="ORD-" maxlength="10"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                </div>

                <div>
                    <label for="receipt_footer" class="block text-sm font-medium text-gray-700 mb-2">
                        Receipt Footer Text
                    </label>
                    <textarea id="receipt_footer" name="receipt_footer" rows="3" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                              placeholder="Thank you for dining with us!">Thank you for dining with us!
Visit us again soon.</textarea>
                </div>

                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div>
                            <label for="auto_print_receipt" class="text-sm font-medium text-gray-700">Auto Print Receipt</label>
                            <p class="text-sm text-gray-500">Automatically print receipt after order</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="auto_print_receipt" name="auto_print_receipt" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-emerald-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-emerald-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="show_customer_display" class="text-sm font-medium text-gray-700">Customer Display</label>
                            <p class="text-sm text-gray-500">Show order details to customer</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="show_customer_display" name="show_customer_display" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-emerald-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-emerald-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="require_customer_info" class="text-sm font-medium text-gray-700">Require Customer Info</label>
                            <p class="text-sm text-gray-500">Mandatory customer details for orders</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="require_customer_info" name="require_customer_info" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-emerald-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-emerald-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="enable_discounts" class="text-sm font-medium text-gray-700">Enable Discounts</label>
                            <p class="text-sm text-gray-500">Allow staff to apply discounts</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="enable_discounts" name="enable_discounts" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-emerald-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-emerald-600"></div>
                        </label>
                    </div>
                </div>

                <div class="pt-4">
                    <button type="submit" class="w-full px-4 py-2 text-sm font-medium text-white bg-emerald-600 border border-transparent rounded-lg hover:bg-emerald-700">
                        <i class="fas fa-save mr-2"></i>
                        Save General Settings
                    </button>
                </div>
            </form>
        </div>

        <!-- Kitchen Display Settings -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-utensils mr-2 text-orange-600"></i>
                Kitchen Display Settings
            </h3>
            
            <form class="space-y-4">
                <div>
                    <label for="kitchen_display_mode" class="block text-sm font-medium text-gray-700 mb-2">
                        Display Mode
                    </label>
                    <select id="kitchen_display_mode" name="kitchen_display_mode" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                        <option value="grid" selected>Grid View</option>
                        <option value="list">List View</option>
                        <option value="timeline">Timeline View</option>
                    </select>
                </div>

                <div>
                    <label for="order_timeout" class="block text-sm font-medium text-gray-700 mb-2">
                        Order Timeout (minutes)
                    </label>
                    <input type="number" id="order_timeout" name="order_timeout" value="15" min="5" max="60"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                </div>

                <div>
                    <label for="preparation_time" class="block text-sm font-medium text-gray-700 mb-2">
                        Default Preparation Time (minutes)
                    </label>
                    <input type="number" id="preparation_time" name="preparation_time" value="10" min="1" max="120"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                </div>

                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div>
                            <label for="auto_print_kitchen" class="text-sm font-medium text-gray-700">Auto Print Kitchen Orders</label>
                            <p class="text-sm text-gray-500">Automatically print to kitchen printer</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="auto_print_kitchen" name="auto_print_kitchen" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="sound_notifications" class="text-sm font-medium text-gray-700">Sound Notifications</label>
                            <p class="text-sm text-gray-500">Play sound for new orders</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="sound_notifications" name="sound_notifications" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="show_order_notes" class="text-sm font-medium text-gray-700">Show Order Notes</label>
                            <p class="text-sm text-gray-500">Display special instructions</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="show_order_notes" name="show_order_notes" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="auto_refresh" class="text-sm font-medium text-gray-700">Auto Refresh Display</label>
                            <p class="text-sm text-gray-500">Automatically update kitchen display</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="auto_refresh" name="auto_refresh" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                        </label>
                    </div>
                </div>

                <div class="pt-4">
                    <button type="submit" class="w-full px-4 py-2 text-sm font-medium text-white bg-orange-600 border border-transparent rounded-lg hover:bg-orange-700">
                        <i class="fas fa-save mr-2"></i>
                        Save Kitchen Settings
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Tax & Pricing Settings -->
    <div class="mt-6 bg-white rounded-lg shadow-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fas fa-percentage mr-2 text-blue-600"></i>
            Tax & Pricing Settings
        </h3>

        <form class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div class="space-y-4">
                <div>
                    <label for="default_tax_rate" class="block text-sm font-medium text-gray-700 mb-2">
                        Default Tax Rate (%)
                    </label>
                    <input type="number" id="default_tax_rate" name="default_tax_rate" value="15" step="0.01" min="0" max="100"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label for="service_charge" class="block text-sm font-medium text-gray-700 mb-2">
                        Service Charge (%)
                    </label>
                    <input type="number" id="service_charge" name="service_charge" value="10" step="0.01" min="0" max="50"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label for="currency_symbol" class="block text-sm font-medium text-gray-700 mb-2">
                        Currency Symbol
                    </label>
                    <select id="currency_symbol" name="currency_symbol" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="$">$ (USD)</option>
                        <option value="€">€ (EUR)</option>
                        <option value="£">£ (GBP)</option>
                        <option value="ر.س" selected>ر.س (SAR)</option>
                        <option value="¥">¥ (JPY)</option>
                    </select>
                </div>

                <div>
                    <label for="decimal_places" class="block text-sm font-medium text-gray-700 mb-2">
                        Decimal Places
                    </label>
                    <select id="decimal_places" name="decimal_places" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="0">0 (No decimals)</option>
                        <option value="2" selected>2 (0.00)</option>
                        <option value="3">3 (0.000)</option>
                    </select>
                </div>
            </div>

            <div class="space-y-4">
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div>
                            <label for="tax_inclusive" class="text-sm font-medium text-gray-700">Tax Inclusive Pricing</label>
                            <p class="text-sm text-gray-500">Prices include tax</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="tax_inclusive" name="tax_inclusive" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="auto_service_charge" class="text-sm font-medium text-gray-700">Auto Apply Service Charge</label>
                            <p class="text-sm text-gray-500">Automatically add service charge</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="auto_service_charge" name="auto_service_charge" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="round_totals" class="text-sm font-medium text-gray-700">Round Totals</label>
                            <p class="text-sm text-gray-500">Round final amounts to nearest currency unit</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="round_totals" name="round_totals" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="show_tax_breakdown" class="text-sm font-medium text-gray-700">Show Tax Breakdown</label>
                            <p class="text-sm text-gray-500">Display tax details on receipt</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="show_tax_breakdown" name="show_tax_breakdown" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                </div>

                <div class="pt-8">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-blue-900 mb-2">Tax Calculation Preview</h4>
                        <div class="space-y-1 text-sm text-blue-800">
                            <div class="flex justify-between">
                                <span>Subtotal:</span>
                                <span>100.00 ر.س</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Tax (15%):</span>
                                <span>15.00 ر.س</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Service Charge (10%):</span>
                                <span>10.00 ر.س</span>
                            </div>
                            <div class="flex justify-between font-medium border-t border-blue-300 pt-1">
                                <span>Total:</span>
                                <span>125.00 ر.س</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2 pt-4">
                <button type="submit" class="px-6 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700">
                    <i class="fas fa-save mr-2"></i>
                    Save Tax & Pricing Settings
                </button>
            </div>
        </form>
    </div>

    <!-- Hardware Configuration -->
    <div class="mt-6 bg-white rounded-lg shadow-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fas fa-print mr-2 text-purple-600"></i>
            Hardware Configuration
        </h3>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Receipt Printer -->
            <div class="border border-gray-200 rounded-lg p-4">
                <div class="flex items-center justify-between mb-3">
                    <h4 class="font-medium text-gray-900">Receipt Printer</h4>
                    <span class="px-2 py-1 text-xs font-medium text-green-800 bg-green-100 rounded-full">Connected</span>
                </div>
                <div class="space-y-2 text-sm text-gray-600">
                    <div>Model: Epson TM-T88V</div>
                    <div>Port: USB001</div>
                    <div>Paper: 80mm</div>
                </div>
                <button class="mt-3 w-full px-3 py-2 text-sm font-medium text-purple-700 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100">
                    Configure
                </button>
            </div>

            <!-- Kitchen Printer -->
            <div class="border border-gray-200 rounded-lg p-4">
                <div class="flex items-center justify-between mb-3">
                    <h4 class="font-medium text-gray-900">Kitchen Printer</h4>
                    <span class="px-2 py-1 text-xs font-medium text-green-800 bg-green-100 rounded-full">Connected</span>
                </div>
                <div class="space-y-2 text-sm text-gray-600">
                    <div>Model: Star TSP143</div>
                    <div>Port: LAN</div>
                    <div>Paper: 80mm</div>
                </div>
                <button class="mt-3 w-full px-3 py-2 text-sm font-medium text-purple-700 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100">
                    Configure
                </button>
            </div>

            <!-- Cash Drawer -->
            <div class="border border-gray-200 rounded-lg p-4">
                <div class="flex items-center justify-between mb-3">
                    <h4 class="font-medium text-gray-900">Cash Drawer</h4>
                    <span class="px-2 py-1 text-xs font-medium text-yellow-800 bg-yellow-100 rounded-full">Offline</span>
                </div>
                <div class="space-y-2 text-sm text-gray-600">
                    <div>Model: APG VB320</div>
                    <div>Port: COM1</div>
                    <div>Status: Disconnected</div>
                </div>
                <button class="mt-3 w-full px-3 py-2 text-sm font-medium text-purple-700 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100">
                    Configure
                </button>
            </div>
        </div>

        <div class="mt-6 flex justify-end space-x-4">
            <button class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                Test All Devices
            </button>
            <button class="px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-lg hover:bg-purple-700">
                <i class="fas fa-plus mr-2"></i>
                Add Device
            </button>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Form submission handlers
    $('form').on('submit', function(e) {
        e.preventDefault();
        
        Swal.fire({
            icon: 'success',
            title: 'Success!',
            text: 'POS settings updated successfully.',
            timer: 2000,
            showConfirmButton: false
        });
    });

    // Hardware configuration handlers
    $('button:contains("Configure")').on('click', function() {
        Swal.fire({
            title: 'Hardware Configuration',
            text: 'This will open the hardware configuration wizard.',
            icon: 'info',
            confirmButtonText: 'OK'
        });
    });

    // Test devices handler
    $('button:contains("Test All Devices")').on('click', function() {
        Swal.fire({
            title: 'Testing Hardware Devices',
            text: 'Testing all connected devices...',
            icon: 'info',
            showConfirmButton: false,
            timer: 3000
        }).then(() => {
            Swal.fire({
                icon: 'success',
                title: 'Hardware Test Complete',
                text: 'All devices tested successfully.',
                timer: 2000,
                showConfirmButton: false
            });
        });
    });

    // Update tax calculation preview when values change
    $('#default_tax_rate, #service_charge').on('input', function() {
        updateTaxPreview();
    });

    function updateTaxPreview() {
        const subtotal = 100;
        const taxRate = parseFloat($('#default_tax_rate').val()) || 0;
        const serviceRate = parseFloat($('#service_charge').val()) || 0;
        
        const tax = (subtotal * taxRate / 100);
        const service = (subtotal * serviceRate / 100);
        const total = subtotal + tax + service;
        
        $('.bg-blue-50 .space-y-1').html(`
            <div class="flex justify-between">
                <span>Subtotal:</span>
                <span>${subtotal.toFixed(2)} ر.س</span>
            </div>
            <div class="flex justify-between">
                <span>Tax (${taxRate}%):</span>
                <span>${tax.toFixed(2)} ر.س</span>
            </div>
            <div class="flex justify-between">
                <span>Service Charge (${serviceRate}%):</span>
                <span>${service.toFixed(2)} ر.س</span>
            </div>
            <div class="flex justify-between font-medium border-t border-blue-300 pt-1">
                <span>Total:</span>
                <span>${total.toFixed(2)} ر.س</span>
            </div>
        `);
    }
});
</script>
@endpush