<!-- Quick Create Purchase Order Modal -->
<div class="modal fade" id="quickCreatePOModal" tabindex="-1" role="dialog" aria-labelledby="quickCreatePOModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="quickCreatePOModalLabel">إنشاء أمر شراء سريع</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="quickCreatePOForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="quick_supplier">المورد <span class="text-danger">*</span></label>
                                <select class="form-control select2" id="quick_supplier" name="supplier_id" required>
                                    <option value="">اختر المورد</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="quick_expected_delivery">تاريخ التسليم المتوقع</label>
                                <input type="date" class="form-control" id="quick_expected_delivery" name="expected_delivery_date">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="quick_notes">ملاحظات</label>
                        <textarea class="form-control" id="quick_notes" name="notes" rows="2"></textarea>
                    </div>
                    
                    <hr>
                    
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">مواد الطلب</h6>
                        <button type="button" class="btn btn-sm btn-success" onclick="addQuickPOItem()">
                            <i class="mdi mdi-plus"></i> إضافة مادة
                        </button>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-bordered" id="quickPOItemsTable">
                            <thead>
                                <tr>
                                    <th>المادة</th>
                                    <th>الكمية</th>
                                    <th>سعر الوحدة</th>
                                    <th>الإجمالي</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="3" class="text-right">الإجمالي الكلي:</th>
                                    <th id="quickPOTotal">0.00 ريال</th>
                                    <th></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="mdi mdi-check"></i> إنشاء الطلب
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Update PO Status Modal -->
<div class="modal fade" id="updatePOStatusModal" tabindex="-1" role="dialog" aria-labelledby="updatePOStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="updatePOStatusModalLabel">تحديث حالة الطلب</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="updatePOStatusForm">
                <input type="hidden" id="status_po_id" name="po_id">
                <div class="modal-body">
                    <div class="form-group">
                        <label>رقم الطلب</label>
                        <input type="text" class="form-control" id="status_po_number" readonly>
                    </div>
                    
                    <div class="form-group">
                        <label>الحالة الحالية</label>
                        <input type="text" class="form-control" id="status_current_status" readonly>
                    </div>
                    
                    <div class="form-group">
                        <label for="new_status">الحالة الجديدة <span class="text-danger">*</span></label>
                        <select class="form-control" id="new_status" name="status" required>
                            <option value="">اختر الحالة الجديدة</option>
                            <option value="pending">في الانتظار</option>
                            <option value="approved">معتمد</option>
                            <option value="ordered">مطلوب</option>
                            <option value="received">مستلم</option>
                            <option value="cancelled">ملغي</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="status_notes">ملاحظات التحديث</label>
                        <textarea class="form-control" id="status_notes" name="notes" rows="3" placeholder="سبب تغيير الحالة (اختياري)"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="mdi mdi-check"></i> تحديث الحالة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Receive Items Modal -->
<div class="modal fade" id="receiveItemsModal" tabindex="-1" role="dialog" aria-labelledby="receiveItemsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="receiveItemsModalLabel">استلام المواد</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="receiveItemsForm">
                <input type="hidden" id="receive_po_id" name="po_id">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="mdi mdi-information"></i>
                        قم بتحديد الكميات المستلمة لكل مادة. سيتم تحديث المخزون تلقائياً.
                    </div>
                    
                    <div class="form-group">
                        <label>رقم الطلب</label>
                        <input type="text" class="form-control" id="receive_po_number" readonly>
                    </div>
                    
                    <div class="form-group">
                        <label for="receive_date">تاريخ الاستلام <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="receive_date" name="receive_date" required>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-bordered" id="receiveItemsTable">
                            <thead>
                                <tr>
                                    <th>المادة</th>
                                    <th>الكمية المطلوبة</th>
                                    <th>الكمية المستلمة سابقاً</th>
                                    <th>الكمية المستلمة الآن</th>
                                    <th>ملاحظات</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="form-group">
                        <label for="receive_notes">ملاحظات الاستلام</label>
                        <textarea class="form-control" id="receive_notes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">
                        <i class="mdi mdi-package-down"></i> تأكيد الاستلام
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- PO History Modal -->
<div class="modal fade" id="poHistoryModal" tabindex="-1" role="dialog" aria-labelledby="poHistoryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="poHistoryModalLabel">تاريخ الطلب</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>رقم الطلب</label>
                    <input type="text" class="form-control" id="history_po_number" readonly>
                </div>
                
                <div class="timeline" id="poHistoryTimeline">
                    <!-- Timeline items will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Actions Modal -->
<div class="modal fade" id="bulkActionsModal" tabindex="-1" role="dialog" aria-labelledby="bulkActionsModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkActionsModalLabel">إجراءات مجمعة</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="bulkActionsForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label>الطلبات المحددة</label>
                        <div id="selectedPOsList" class="border rounded p-2 bg-light">
                            <!-- Selected POs will be listed here -->
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="bulk_action">الإجراء <span class="text-danger">*</span></label>
                        <select class="form-control" id="bulk_action" name="action" required>
                            <option value="">اختر الإجراء</option>
                            <option value="approve">اعتماد</option>
                            <option value="cancel">إلغاء</option>
                            <option value="delete">حذف</option>
                            <option value="export">تصدير</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="bulk_notes">ملاحظات</label>
                        <textarea class="form-control" id="bulk_notes" name="notes" rows="3" placeholder="ملاحظات الإجراء المجمع (اختياري)"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="mdi mdi-check-all"></i> تطبيق الإجراء
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
let quickPOItemCounter = 0;

// Quick Create PO Form Handler
$('#quickCreatePOForm').on('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const items = [];
    
    $('#quickPOItemsTable tbody tr').each(function() {
        const row = $(this);
        items.push({
            item_id: row.find('.item-select').val(),
            quantity: row.find('.quantity-input').val(),
            unit_price: row.find('.price-input').val()
        });
    });
    
    formData.append('items', JSON.stringify(items));
    
    $.ajax({
        url: '{{ route("inventory.api.purchase-orders.store") }}',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            $('#quickCreatePOModal').modal('hide');
            $('#quickCreatePOForm')[0].reset();
            $('#quickPOItemsTable tbody').empty();
            updateQuickPOTotal();
            InventoryModule.showSuccess('تم إنشاء أمر الشراء بنجاح');
            refreshPOTable();
        },
        error: function(xhr) {
            const errors = xhr.responseJSON?.errors;
            if (errors) {
                let errorMessage = 'يرجى تصحيح الأخطاء التالية:\n';
                Object.keys(errors).forEach(key => {
                    errorMessage += `- ${errors[key][0]}\n`;
                });
                InventoryModule.showError(errorMessage);
            } else {
                InventoryModule.showError('حدث خطأ أثناء إنشاء أمر الشراء');
            }
        }
    });
});

// Update PO Status Form Handler
$('#updatePOStatusForm').on('submit', function(e) {
    e.preventDefault();
    
    const formData = $(this).serialize();
    const poId = $('#status_po_id').val();
    
    $.ajax({
        url: `{{ route('inventory.api.purchase-orders.update-status', ':id') }}`.replace(':id', poId),
        type: 'POST',
        data: formData,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            $('#updatePOStatusModal').modal('hide');
            InventoryModule.showSuccess('تم تحديث حالة الطلب بنجاح');
            refreshPOTable();
        },
        error: function(xhr) {
            const message = xhr.responseJSON?.message || 'حدث خطأ أثناء تحديث حالة الطلب';
            InventoryModule.showError(message);
        }
    });
});

// Receive Items Form Handler
$('#receiveItemsForm').on('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const items = [];
    
    $('#receiveItemsTable tbody tr').each(function() {
        const row = $(this);
        const receivedQty = row.find('.received-quantity').val();
        if (receivedQty && receivedQty > 0) {
            items.push({
                item_id: row.data('item-id'),
                received_quantity: receivedQty,
                notes: row.find('.item-notes').val()
            });
        }
    });
    
    formData.append('items', JSON.stringify(items));
    
    const poId = $('#receive_po_id').val();
    
    $.ajax({
        url: `{{ route('inventory.api.purchase-orders.receive', ':id') }}`.replace(':id', poId),
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            $('#receiveItemsModal').modal('hide');
            InventoryModule.showSuccess('تم تسجيل استلام المواد بنجاح');
            refreshPOTable();
        },
        error: function(xhr) {
            const message = xhr.responseJSON?.message || 'حدث خطأ أثناء تسجيل الاستلام';
            InventoryModule.showError(message);
        }
    });
});

// Bulk Actions Form Handler
$('#bulkActionsForm').on('submit', function(e) {
    e.preventDefault();
    
    const formData = $(this).serialize();
    const selectedPOs = getSelectedPOs();
    
    if (selectedPOs.length === 0) {
        InventoryModule.showError('يرجى تحديد طلبات للتطبيق عليها');
        return;
    }
    
    $.ajax({
        url: '{{ route("inventory.api.purchase-orders.bulk-action") }}',
        type: 'POST',
        data: formData + '&po_ids=' + selectedPOs.join(','),
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            $('#bulkActionsModal').modal('hide');
            InventoryModule.showSuccess('تم تطبيق الإجراء المجمع بنجاح');
            refreshPOTable();
            clearSelectedPOs();
        },
        error: function(xhr) {
            const message = xhr.responseJSON?.message || 'حدث خطأ أثناء تطبيق الإجراء المجمع';
            InventoryModule.showError(message);
        }
    });
});

function addQuickPOItem() {
    quickPOItemCounter++;
    
    const row = `
        <tr id="quickPOItem_${quickPOItemCounter}">
            <td>
                <select class="form-control select2 item-select" name="items[${quickPOItemCounter}][item_id]" required>
                    <option value="">اختر المادة</option>
                </select>
            </td>
            <td>
                <input type="number" class="form-control quantity-input" name="items[${quickPOItemCounter}][quantity]" 
                       min="0" step="0.01" required onchange="updateQuickPOItemTotal(${quickPOItemCounter})">
            </td>
            <td>
                <input type="number" class="form-control price-input" name="items[${quickPOItemCounter}][unit_price]" 
                       min="0" step="0.01" required onchange="updateQuickPOItemTotal(${quickPOItemCounter})">
            </td>
            <td>
                <span class="item-total">0.00 ريال</span>
            </td>
            <td>
                <button type="button" class="btn btn-sm btn-danger" onclick="removeQuickPOItem(${quickPOItemCounter})">
                    <i class="mdi mdi-delete"></i>
                </button>
            </td>
        </tr>
    `;
    
    $('#quickPOItemsTable tbody').append(row);
    
    // Load items for the new select
    loadItemsForSelect(`#quickPOItem_${quickPOItemCounter} .item-select`);
}

function removeQuickPOItem(itemId) {
    $(`#quickPOItem_${itemId}`).remove();
    updateQuickPOTotal();
}

function updateQuickPOItemTotal(itemId) {
    const row = $(`#quickPOItem_${itemId}`);
    const quantity = parseFloat(row.find('.quantity-input').val()) || 0;
    const price = parseFloat(row.find('.price-input').val()) || 0;
    const total = quantity * price;
    
    row.find('.item-total').text(InventoryModule.formatCurrency(total));
    updateQuickPOTotal();
}

function updateQuickPOTotal() {
    let total = 0;
    
    $('#quickPOItemsTable tbody tr').each(function() {
        const quantity = parseFloat($(this).find('.quantity-input').val()) || 0;
        const price = parseFloat($(this).find('.price-input').val()) || 0;
        total += quantity * price;
    });
    
    $('#quickPOTotal').text(InventoryModule.formatCurrency(total));
}

function loadItemsForSelect(selector) {
    $.get('{{ route("inventory.api.items") }}')
        .done(function(items) {
            const options = items.map(item => 
                `<option value="${item.id}">${item.product.name} (${item.product.sku})</option>`
            ).join('');
            
            $(selector).html('<option value="">اختر المادة</option>' + options);
        })
        .fail(function() {
            console.error('Failed to load items for select');
        });
}

function loadSuppliersForQuickPO() {
    $.get('{{ route("inventory.api.suppliers") }}')
        .done(function(suppliers) {
            const options = suppliers.map(supplier => 
                `<option value="${supplier.id}">${supplier.name}</option>`
            ).join('');
            
            $('#quick_supplier').html('<option value="">اختر المورد</option>' + options);
        })
        .fail(function() {
            console.error('Failed to load suppliers for quick PO');
        });
}

function showUpdatePOStatusModal(poId) {
    $.get(`{{ route('inventory.api.purchase-orders.show', ':id') }}`.replace(':id', poId))
        .done(function(po) {
            $('#status_po_id').val(po.id);
            $('#status_po_number').val(po.po_number);
            $('#status_current_status').val(getPOStatusText(po.status));
            $('#updatePOStatusModal').modal('show');
        })
        .fail(function() {
            InventoryModule.showError('حدث خطأ أثناء جلب بيانات الطلب');
        });
}

function showReceiveItemsModal(poId) {
    $.get(`{{ route('inventory.api.purchase-orders.items', ':id') }}`.replace(':id', poId))
        .done(function(data) {
            $('#receive_po_id').val(data.po.id);
            $('#receive_po_number').val(data.po.po_number);
            $('#receive_date').val(new Date().toISOString().split('T')[0]);
            
            let itemsHtml = '';
            data.items.forEach(function(item) {
                itemsHtml += `
                    <tr data-item-id="${item.id}">
                        <td>${item.product.name}</td>
                        <td>${item.quantity} ${item.product.unit?.symbol || ''}</td>
                        <td>${item.received_quantity || 0} ${item.product.unit?.symbol || ''}</td>
                        <td>
                            <input type="number" class="form-control received-quantity" 
                                   min="0" max="${item.quantity - (item.received_quantity || 0)}" step="0.01">
                        </td>
                        <td>
                            <input type="text" class="form-control item-notes" placeholder="ملاحظات">
                        </td>
                    </tr>
                `;
            });
            
            $('#receiveItemsTable tbody').html(itemsHtml);
            $('#receiveItemsModal').modal('show');
        })
        .fail(function() {
            InventoryModule.showError('حدث خطأ أثناء جلب مواد الطلب');
        });
}

function showPOHistoryModal(poId) {
    $.get(`{{ route('inventory.api.purchase-orders.history', ':id') }}`.replace(':id', poId))
        .done(function(data) {
            $('#history_po_number').val(data.po.po_number);
            
            let timelineHtml = '';
            data.history.forEach(function(event) {
                timelineHtml += `
                    <div class="timeline-item">
                        <div class="timeline-marker"></div>
                        <div class="timeline-content">
                            <h6>${event.action}</h6>
                            <p class="text-muted mb-1">${event.description}</p>
                            <small class="text-muted">
                                ${InventoryModule.formatDate(event.created_at)} - ${event.user.name}
                            </small>
                        </div>
                    </div>
                `;
            });
            
            $('#poHistoryTimeline').html(timelineHtml);
            $('#poHistoryModal').modal('show');
        })
        .fail(function() {
            InventoryModule.showError('حدث خطأ أثناء جلب تاريخ الطلب');
        });
}

function getPOStatusText(status) {
    const statusMap = {
        'draft': 'مسودة',
        'pending': 'في الانتظار',
        'approved': 'معتمد',
        'ordered': 'مطلوب',
        'received': 'مستلم',
        'cancelled': 'ملغي'
    };
    
    return statusMap[status] || status;
}

function getSelectedPOs() {
    const selected = [];
    $('.po-checkbox:checked').each(function() {
        selected.push($(this).val());
    });
    return selected;
}

function clearSelectedPOs() {
    $('.po-checkbox').prop('checked', false);
}

// Initialize modals when document is ready
$(document).ready(function() {
    loadSuppliersForQuickPO();
    
    // Set default receive date to today
    $('#receive_date').val(new Date().toISOString().split('T')[0]);
});
</script>
