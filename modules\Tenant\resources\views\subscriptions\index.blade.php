@extends('layouts.master')

@section('title', 'إدارة الاشتراكات')

@section('content')
<!-- Page Header -->
<div class="bg-white shadow-sm border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 flex items-center gap-3">
                    <div class="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-credit-card text-white text-sm"></i>
                    </div>
                    إدارة الاشتراكات
                </h1>
                <p class="mt-1 text-sm text-gray-500">إدارة اشتراكات المستأجرين والباقات</p>
            </div>
            <div class="flex items-center gap-3">
                <button onclick="openModal('createModal')" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                    <i class="fas fa-plus text-sm"></i>
                    إضافة اشتراك جديد
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Filters Section -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <i class="fas fa-filter text-purple-600"></i>
                فلاتر البحث
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label for="statusFilter" class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                    <select id="statusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="pending">معلق</option>
                        <option value="suspended">موقوف</option>
                        <option value="cancelled">ملغي</option>
                        <option value="expired">منتهي الصلاحية</option>
                    </select>
                </div>
                <div>
                    <label for="planFilter" class="block text-sm font-medium text-gray-700 mb-2">الباقة</label>
                    <select id="planFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        <option value="">جميع الباقات</option>
                        @foreach($plans as $plan)
                            <option value="{{ $plan->id }}">{{ $plan->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div>
                    <label for="tenantFilter" class="block text-sm font-medium text-gray-700 mb-2">المستأجر</label>
                    <select id="tenantFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        <option value="">جميع المستأجرين</option>
                        @foreach($tenants as $tenant)
                            <option value="{{ $tenant->id }}">{{ $tenant->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="flex items-end gap-2">
                    <button onclick="applyFilters()" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                        <i class="fas fa-search text-sm"></i>
                        بحث
                    </button>
                    <button onclick="clearFilters()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                        <i class="fas fa-times text-sm"></i>
                        مسح
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="p-6">
            <div class="overflow-x-auto">
                <table id="subscriptionsTable" class="w-full table-auto">
                    <thead>
                        <tr class="bg-gray-50">
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المستأجر</th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">معلومات التواصل</th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الباقة</th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المبلغ</th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التواريخ</th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- Data will be loaded via DataTables -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Create Modal -->
<div id="createModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">إضافة اشتراك جديد</h3>
                <button onclick="closeModal('createModal')" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="createForm" method="POST" action="{{ route('subscriptions-web.store') }}">
                @csrf
                <div class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="create_tenant_id" class="block text-sm font-medium text-gray-700 mb-2">
                                المستأجر <span class="text-red-500">*</span>
                            </label>
                            <select name="tenant_id" id="create_tenant_id" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                                <option value="">اختر المستأجر</option>
                                @foreach($tenants as $tenant)
                                    <option value="{{ $tenant->id }}">{{ $tenant->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        
                        <div>
                            <label for="create_plan_id" class="block text-sm font-medium text-gray-700 mb-2">
                                الباقة <span class="text-red-500">*</span>
                            </label>
                            <select name="plan_id" id="create_plan_id" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                                <option value="">اختر الباقة</option>
                                @foreach($plans as $plan)
                                    <option value="{{ $plan->id }}" data-price="{{ $plan->price }}">{{ $plan->name }} - {{ $plan->price }} {{ $plan->currency ?? 'USD' }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="create_start_date" class="block text-sm font-medium text-gray-700 mb-2">
                                تاريخ البداية <span class="text-red-500">*</span>
                            </label>
                            <input type="date" name="start_date" id="create_start_date" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        </div>
                        
                        <div>
                            <label for="create_end_date" class="block text-sm font-medium text-gray-700 mb-2">
                                تاريخ النهاية
                            </label>
                            <input type="date" name="end_date" id="create_end_date" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="create_amount" class="block text-sm font-medium text-gray-700 mb-2">
                                المبلغ <span class="text-red-500">*</span>
                            </label>
                            <input type="number" name="amount" id="create_amount" step="0.01" min="0" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        </div>
                        
                        <div>
                            <label for="create_status" class="block text-sm font-medium text-gray-700 mb-2">
                                الحالة <span class="text-red-500">*</span>
                            </label>
                            <select name="status" id="create_status" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                                <option value="active">نشط</option>
                                <option value="pending">معلق</option>
                                <option value="suspended">موقوف</option>
                                <option value="cancelled">ملغي</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="create_payment_method" class="block text-sm font-medium text-gray-700 mb-2">
                                طريقة الدفع
                            </label>
                            <input type="text" name="payment_method" id="create_payment_method" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" name="auto_renew" id="create_auto_renew" value="1" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                            <label for="create_auto_renew" class="mr-2 block text-sm text-gray-900">
                                تجديد تلقائي
                            </label>
                        </div>
                    </div>
                    
                    <div>
                        <label for="create_notes" class="block text-sm font-medium text-gray-700 mb-2">
                            ملاحظات
                        </label>
                        <textarea name="notes" id="create_notes" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"></textarea>
                    </div>
                </div>
                
                <div class="flex justify-end gap-3 mt-6">
                    <button type="button" onclick="closeModal('createModal')" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                        إلغاء
                    </button>
                    <button type="submit" class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                        حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Modal -->
<div id="editModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">تعديل الاشتراك</h3>
                <button onclick="closeModal('editModal')" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="editForm" method="POST">
                @csrf
                @method('PUT')
                <div class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="edit_tenant_id" class="block text-sm font-medium text-gray-700 mb-2">
                                المستأجر <span class="text-red-500">*</span>
                            </label>
                            <select name="tenant_id" id="edit_tenant_id" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                                <option value="">اختر المستأجر</option>
                                @foreach($tenants as $tenant)
                                    <option value="{{ $tenant->id }}">{{ $tenant->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        
                        <div>
                            <label for="edit_plan_id" class="block text-sm font-medium text-gray-700 mb-2">
                                الباقة <span class="text-red-500">*</span>
                            </label>
                            <select name="plan_id" id="edit_plan_id" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                                <option value="">اختر الباقة</option>
                                @foreach($plans as $plan)
                                    <option value="{{ $plan->id }}" data-price="{{ $plan->price }}">{{ $plan->name }} - {{ $plan->price }} {{ $plan->currency ?? 'USD' }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="edit_start_date" class="block text-sm font-medium text-gray-700 mb-2">
                                تاريخ البداية <span class="text-red-500">*</span>
                            </label>
                            <input type="date" name="start_date" id="edit_start_date" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        </div>
                        
                        <div>
                            <label for="edit_end_date" class="block text-sm font-medium text-gray-700 mb-2">
                                تاريخ النهاية
                            </label>
                            <input type="date" name="end_date" id="edit_end_date" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="edit_amount" class="block text-sm font-medium text-gray-700 mb-2">
                                المبلغ <span class="text-red-500">*</span>
                            </label>
                            <input type="number" name="amount" id="edit_amount" step="0.01" min="0" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        </div>
                        
                        <div>
                            <label for="edit_status" class="block text-sm font-medium text-gray-700 mb-2">
                                الحالة <span class="text-red-500">*</span>
                            </label>
                            <select name="status" id="edit_status" required class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                                <option value="active">نشط</option>
                                <option value="pending">معلق</option>
                                <option value="suspended">موقوف</option>
                                <option value="cancelled">ملغي</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="edit_payment_method" class="block text-sm font-medium text-gray-700 mb-2">
                                طريقة الدفع
                            </label>
                            <input type="text" name="payment_method" id="edit_payment_method" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" name="auto_renew" id="edit_auto_renew" value="1" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                            <label for="edit_auto_renew" class="mr-2 block text-sm text-gray-900">
                                تجديد تلقائي
                            </label>
                        </div>
                    </div>
                    
                    <div>
                        <label for="edit_notes" class="block text-sm font-medium text-gray-700 mb-2">
                            ملاحظات
                        </label>
                        <textarea name="notes" id="edit_notes" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"></textarea>
                    </div>
                </div>
                
                <div class="flex justify-end gap-3 mt-6">
                    <button type="button" onclick="closeModal('editModal')" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                        إلغاء
                    </button>
                    <button type="submit" class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                        تحديث
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View Modal -->
<div id="viewModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">تفاصيل الاشتراك</h3>
                <button onclick="closeModal('viewModal')" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div id="viewContent" class="space-y-4">
                <!-- Content will be loaded dynamically -->
            </div>
            
            <div class="flex justify-end mt-6">
                <button onclick="closeModal('viewModal')" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                    إغلاق
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Status Change Modal -->
<div id="statusModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 mb-4">
                <i class="fas fa-exclamation-triangle text-yellow-600"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">تأكيد العملية</h3>
            <p id="statusMessage" class="text-sm text-gray-500 mb-4"></p>
            <div class="flex justify-center gap-3">
                <button onclick="closeModal('statusModal')" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                    إلغاء
                </button>
                <button id="confirmStatusBtn" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                    تأكيد
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                <i class="fas fa-trash text-red-600"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">حذف الاشتراك</h3>
            <p class="text-sm text-gray-500 mb-4">هل أنت متأكد من حذف هذا الاشتراك؟ لا يمكن التراجع عن هذا الإجراء.</p>
            <div class="flex justify-center gap-3">
                <button onclick="closeModal('deleteModal')" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                    إلغاء
                </button>
                <button id="confirmDeleteBtn" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                    حذف
                </button>
            </div>
        </div>
    </div>
</div>

@endsection

@push('styles')
<meta name="csrf-token" content="{{ csrf_token() }}">
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
    /* Custom DataTables Tailwind Styling */
    .dataTables_wrapper {
        font-family: inherit;
    }
    
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_processing,
    .dataTables_wrapper .dataTables_paginate {
        color: #374151;
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }
    .dataTables_processing{
        margin: auto
    }
    .dataTables_wrapper .dataTables_length select,
    .dataTables_wrapper .dataTables_filter input {
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
    
    .dataTables_wrapper .dataTables_filter input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.5rem 0.75rem !important;
        margin: 0 0.125rem !important;
        border-radius: 0.375rem !important;
        border: 1px solid #d1d5db !important;
        background: white !important;
        color: #374151 !important;
        text-decoration: none !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background: #f3f4f6 !important;
        border-color: #9ca3af !important;
        color: #374151 !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background: #3b82f6 !important;
        border-color: #3b82f6 !important;
        color: white !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
        background: #f9fafb !important;
        color: #9ca3af !important;
        border-color: #e5e7eb !important;
    }
    
    table.dataTable {
        border-collapse: separate !important;
        border-spacing: 0 !important;
    }
    
    table.dataTable thead th {
        background-color: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
        font-weight: 600;
        color: #374151;
        padding: 0.75rem;
    }
    
    table.dataTable tbody td {
        padding: 0.75rem;
        border-bottom: 1px solid #f3f4f6;
    }
    
    table.dataTable tbody tr:hover {
        background-color: #f9fafb;
    }
    
    .select2-container--default .select2-selection--single {
        height: 42px;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        padding: 0.5rem;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 26px;
        padding-left: 0;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 40px;
    }
    
    .modal-backdrop {
        background-color: rgba(0, 0, 0, 0.5);
    }
    
    .animate-fadeIn {
        animation: fadeIn 0.3s ease-in-out;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: scale(0.95); }
        to { opacity: 1; transform: scale(1); }
    }
    
    .animate-slideDown {
        animation: slideDown 0.3s ease-in-out;
    }
    
    @keyframes slideDown {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>
@endpush

@push('scripts')
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.tailwindcss.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable
    window.table = $('#subscriptionsTable').DataTable({
        processing: true,
        serverSide: true,
        responsive: true,
        ajax: {
            url: '{{ route("subscriptions.data") }}',
            data: function(d) {
                d.status = $('#statusFilter').val();
                d.plan_id = $('#planFilter').val();
                d.tenant_id = $('#tenantFilter').val();
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'tenant_name', name: 'tenant.name' },
            { data: 'tenant_contact', name: 'tenant_contact', orderable: false, searchable: false },
            { data: 'plan_name', name: 'subscriptionPlan.name' },
            { data: 'amount_formatted', name: 'amount' },
            { data: 'status_badge', name: 'status' },
            { data: 'dates', name: 'dates', orderable: false, searchable: false },
            { data: 'action', name: 'action', orderable: false, searchable: false }
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json'
        },
        order: [[0, 'desc']],
        pageLength: 25,
        dom: '<"flex justify-between items-center mb-4"<"flex items-center gap-2"l><"flex items-center gap-2"f>>rtip'
    });

    // Auto-fill amount when plan is selected
    $('#create_plan_id, #edit_plan_id').on('change', function() {
        const selectedOption = $(this).find('option:selected');
        const price = selectedOption.data('price');
        const amountField = $(this).attr('id').includes('create') ? '#create_amount' : '#edit_amount';
        
        if (price) {
            $(amountField).val(price);
        }
    });
});

// Filter functions
function applyFilters() {
    table.draw();
}

function clearFilters() {
    $('#statusFilter, #planFilter, #tenantFilter').val('');
    table.draw();
}

// Modal functions
function openModal(modalId) {
    document.getElementById(modalId).classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
    document.body.style.overflow = 'auto';
    
    // Reset forms when closing modals
    if (modalId === 'createModal') {
        document.getElementById('createForm').reset();
    } else if (modalId === 'editModal') {
        document.getElementById('editForm').reset();
    }
}

// Close modal when clicking outside
window.addEventListener('click', function(event) {
    const modals = ['createModal', 'editModal', 'viewModal', 'statusModal', 'deleteModal'];
    modals.forEach(modalId => {
        const modal = document.getElementById(modalId);
        if (event.target === modal) {
            closeModal(modalId);
        }
    });
});

// CRUD Functions
let currentSubscriptionId = null;
let currentAction = null;

window.viewSubscription = function(subscriptionId) {
    fetch(`{{ route('subscriptions-web.index') }}/${subscriptionId}`, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        const content = `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">المستأجر</label>
                    <p class="mt-1 text-sm text-gray-900">${data.tenant ? data.tenant.name : 'N/A'}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">الباقة</label>
                    <p class="mt-1 text-sm text-gray-900">${data.subscription_plan ? data.subscription_plan.name : 'N/A'}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">المبلغ</label>
                    <p class="mt-1 text-sm text-gray-900">${data.amount} ${data.currency || 'USD'}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">الحالة</label>
                    <p class="mt-1 text-sm text-gray-900">${data.status}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">تاريخ البداية</label>
                    <p class="mt-1 text-sm text-gray-900">${data.start_date || 'N/A'}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">تاريخ النهاية</label>
                    <p class="mt-1 text-sm text-gray-900">${data.end_date || 'N/A'}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">طريقة الدفع</label>
                    <p class="mt-1 text-sm text-gray-900">${data.payment_method || 'N/A'}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">تجديد تلقائي</label>
                    <p class="mt-1 text-sm text-gray-900">${data.auto_renew ? 'نعم' : 'لا'}</p>
                </div>
            </div>
            ${data.notes ? `
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700">ملاحظات</label>
                    <p class="mt-1 text-sm text-gray-900">${data.notes}</p>
                </div>
            ` : ''}
        `;
        
        document.getElementById('viewContent').innerHTML = content;
        openModal('viewModal');
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'خطأ!',
            text: 'حدث خطأ في تحميل البيانات',
            confirmButtonText: 'موافق'
        });
    });
};

window.editSubscription = function(subscriptionId) {
    fetch(`{{ route('subscriptions-web.index') }}/${subscriptionId}`, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        // Fill the edit form
        document.getElementById('edit_tenant_id').value = data.tenant_id;
        document.getElementById('edit_plan_id').value = data.plan_id;
        document.getElementById('edit_start_date').value = data.start_date;
        document.getElementById('edit_end_date').value = data.end_date || '';
        document.getElementById('edit_amount').value = data.amount;
        document.getElementById('edit_status').value = data.status;
        document.getElementById('edit_payment_method').value = data.payment_method || '';
        document.getElementById('edit_auto_renew').checked = data.auto_renew;
        document.getElementById('edit_notes').value = data.notes || '';
        
        // Set form action
        document.getElementById('editForm').action = `{{ route('subscriptions-web.index') }}/${subscriptionId}`;
        
        openModal('editModal');
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'خطأ!',
            text: 'حدث خطأ في تحميل البيانات',
            confirmButtonText: 'موافق'
        });
    });
};

window.changeSubscriptionStatus = function(subscriptionId, action) {
    currentSubscriptionId = subscriptionId;
    currentAction = action;
    
    const messages = {
        'suspend': 'هل أنت متأكد من إيقاف هذا الاشتراك؟',
        'cancel': 'هل أنت متأكد من إلغاء هذا الاشتراك؟',
        'reactivate': 'هل أنت متأكد من إعادة تفعيل هذا الاشتراك؟'
    };
    
    document.getElementById('statusMessage').textContent = messages[action];
    openModal('statusModal');
};

window.deleteSubscription = function(subscriptionId) {
    currentSubscriptionId = subscriptionId;
    openModal('deleteModal');
};

// Confirm status change
document.getElementById('confirmStatusBtn').addEventListener('click', function() {
    if (currentSubscriptionId && currentAction) {
        const url = `{{ route('subscriptions-web.index') }}/${currentSubscriptionId}/${currentAction}`;
        
        fetch(url, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                table.draw();
                closeModal('statusModal');
                Swal.fire({
                    icon: 'success',
                    title: 'تم بنجاح!',
                    text: 'تم تنفيذ العملية بنجاح',
                    confirmButtonText: 'موافق'
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ!',
                    text: data.error || 'حدث خطأ',
                    confirmButtonText: 'موافق'
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'خطأ!',
                text: 'حدث خطأ في تنفيذ العملية',
                confirmButtonText: 'موافق'
            });
        });
    }
});

// Confirm delete
document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
    if (currentSubscriptionId) {
        const url = `{{ route('subscriptions-web.index') }}/${currentSubscriptionId}`;
        
        fetch(url, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                table.draw();
                closeModal('deleteModal');
                Swal.fire({
                    icon: 'success',
                    title: 'تم بنجاح!',
                    text: 'تم حذف الاشتراك بنجاح',
                    confirmButtonText: 'موافق'
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ!',
                    text: data.error || 'حدث خطأ في الحذف',
                    confirmButtonText: 'موافق'
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'خطأ!',
                text: 'حدث خطأ في حذف الاشتراك',
                confirmButtonText: 'موافق'
            });
        });
    }
});
</script>
@endpush