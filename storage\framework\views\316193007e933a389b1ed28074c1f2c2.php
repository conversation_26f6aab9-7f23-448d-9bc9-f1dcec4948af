<?php $__env->startSection('title', 'تفاصيل الفرع'); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h1 class="text-2xl font-bold text-gray-900 flex items-center gap-3">
                    <i class="fas fa-store text-blue-600"></i>
                    <?php echo e($branch->name); ?>

                </h1>
                <div class="flex gap-2">
                    <a href="<?php echo e(route('branches.edit', $branch)); ?>" 
                       class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                        <i class="fas fa-edit"></i>
                        تعديل
                    </a>
                    <a href="<?php echo e(route('branches.index')); ?>" 
                       class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                        <i class="fas fa-arrow-right"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Status and Quick Info -->
        <div class="px-6 py-4 bg-gray-50">
            <div class="flex flex-wrap items-center gap-4">
                <div class="flex items-center gap-2">
                    <span class="text-sm font-medium text-gray-700">الحالة:</span>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($branch->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'); ?>">
                        <i class="fas fa-circle text-xs mr-1"></i>
                        <?php echo e($branch->status === 'active' ? 'نشط' : 'غير نشط'); ?>

                    </span>
                </div>
                <?php if($branch->code): ?>
                <div class="flex items-center gap-2">
                    <span class="text-sm font-medium text-gray-700">الكود:</span>
                    <span class="text-sm text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded"><?php echo e($branch->code); ?></span>
                </div>
                <?php endif; ?>
                <div class="flex items-center gap-2">
                    <span class="text-sm font-medium text-gray-700">المدير:</span>
                    <span class="text-sm text-gray-900"><?php echo e($branch->manager_name); ?></span>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="fas fa-info-circle text-blue-600"></i>
                        المعلومات الأساسية
                    </h2>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">اسم الفرع</label>
                            <p class="text-gray-900 font-medium"><?php echo e($branch->name); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">المستأجر</label>
                            <p class="text-gray-900"><?php echo e($branch->tenant->name ?? 'غير محدد'); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">كود الفرع</label>
                            <p class="text-gray-900 font-mono"><?php echo e($branch->code ?: 'غير محدد'); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">مدير الفرع</label>
                            <p class="text-gray-900"><?php echo e($branch->manager_name); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="fas fa-address-book text-green-600"></i>
                        معلومات الاتصال
                    </h2>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">العنوان</label>
                            <p class="text-gray-900 leading-relaxed"><?php echo e($branch->address); ?></p>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">رقم الهاتف</label>
                                <p class="text-gray-900 font-mono"><?php echo e($branch->phone); ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">البريد الإلكتروني</label>
                                <p class="text-gray-900"><?php echo e($branch->email ?: 'غير محدد'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Business Settings -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="fas fa-cogs text-purple-600"></i>
                        إعدادات العمل
                    </h2>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">سعة الجلوس</label>
                            <p class="text-gray-900 font-medium"><?php echo e($branch->seating_capacity ? $branch->seating_capacity . ' مقعد' : 'غير محدد'); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">نطاق التوصيل</label>
                            <p class="text-gray-900 font-medium"><?php echo e($branch->delivery_radius ? $branch->delivery_radius . ' كم' : 'غير محدد'); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">المنطقة الزمنية</label>
                            <p class="text-gray-900 font-mono"><?php echo e($branch->timezone ?: 'غير محدد'); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Services -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="fas fa-concierge-bell text-orange-600"></i>
                        الخدمات المتاحة
                    </h2>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div class="flex items-center p-4 border border-gray-200 rounded-lg <?php echo e($branch->is_dine_in_enabled ? 'bg-blue-50 border-blue-200' : 'bg-gray-50'); ?>">
                            <i class="fas fa-utensils <?php echo e($branch->is_dine_in_enabled ? 'text-blue-600' : 'text-gray-400'); ?> mr-3"></i>
                            <div>
                                <p class="text-sm font-medium text-gray-900">تناول في المكان</p>
                                <p class="text-xs <?php echo e($branch->is_dine_in_enabled ? 'text-blue-600' : 'text-gray-500'); ?>">
                                    <?php echo e($branch->is_dine_in_enabled ? 'متاح' : 'غير متاح'); ?>

                                </p>
                            </div>
                        </div>
                        
                        <div class="flex items-center p-4 border border-gray-200 rounded-lg <?php echo e($branch->is_takeaway_enabled ? 'bg-cyan-50 border-cyan-200' : 'bg-gray-50'); ?>">
                            <i class="fas fa-shopping-bag <?php echo e($branch->is_takeaway_enabled ? 'text-cyan-600' : 'text-gray-400'); ?> mr-3"></i>
                            <div>
                                <p class="text-sm font-medium text-gray-900">استلام</p>
                                <p class="text-xs <?php echo e($branch->is_takeaway_enabled ? 'text-cyan-600' : 'text-gray-500'); ?>">
                                    <?php echo e($branch->is_takeaway_enabled ? 'متاح' : 'غير متاح'); ?>

                                </p>
                            </div>
                        </div>
                        
                        <div class="flex items-center p-4 border border-gray-200 rounded-lg <?php echo e($branch->is_delivery_enabled ? 'bg-green-50 border-green-200' : 'bg-gray-50'); ?>">
                            <i class="fas fa-truck <?php echo e($branch->is_delivery_enabled ? 'text-green-600' : 'text-gray-400'); ?> mr-3"></i>
                            <div>
                                <p class="text-sm font-medium text-gray-900">توصيل</p>
                                <p class="text-xs <?php echo e($branch->is_delivery_enabled ? 'text-green-600' : 'text-gray-500'); ?>">
                                    <?php echo e($branch->is_delivery_enabled ? 'متاح' : 'غير متاح'); ?>

                                </p>
                            </div>
                        </div>
                        
                        <div class="flex items-center p-4 border border-gray-200 rounded-lg <?php echo e($branch->is_online_ordering_enabled ? 'bg-yellow-50 border-yellow-200' : 'bg-gray-50'); ?>">
                            <i class="fas fa-globe <?php echo e($branch->is_online_ordering_enabled ? 'text-yellow-600' : 'text-gray-400'); ?> mr-3"></i>
                            <div>
                                <p class="text-sm font-medium text-gray-900">طلب أونلاين</p>
                                <p class="text-xs <?php echo e($branch->is_online_ordering_enabled ? 'text-yellow-600' : 'text-gray-500'); ?>">
                                    <?php echo e($branch->is_online_ordering_enabled ? 'متاح' : 'غير متاح'); ?>

                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Statistics -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="fas fa-chart-bar text-indigo-600"></i>
                        إحصائيات
                    </h2>
                </div>
                <div class="p-6 space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">تاريخ الإنشاء</span>
                        <span class="text-sm font-medium text-gray-900"><?php echo e($branch->created_at->format('Y-m-d')); ?></span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">آخر تحديث</span>
                        <span class="text-sm font-medium text-gray-900"><?php echo e($branch->updated_at->format('Y-m-d')); ?></span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">عدد الموظفين</span>
                        <span class="text-sm font-medium text-gray-900"><?php echo e($branch->users_count ?? 0); ?></span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">عدد الطاولات</span>
                        <span class="text-sm font-medium text-gray-900"><?php echo e($branch->tables_count ?? 0); ?></span>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="fas fa-tools text-red-600"></i>
                        الإجراءات
                    </h2>
                </div>
                <div class="p-6 space-y-3">
                    <?php if($branch->status === 'active'): ?>
                        <button onclick="updateBranchStatus('<?php echo e($branch->id); ?>', 'inactive')" 
                                class="w-full bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                            <i class="fas fa-pause"></i>
                            إلغاء تفعيل الفرع
                        </button>
                    <?php else: ?>
                        <button onclick="updateBranchStatus('<?php echo e($branch->id); ?>', 'active')" 
                                class="w-full bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                            <i class="fas fa-play"></i>
                            تفعيل الفرع
                        </button>
                    <?php endif; ?>
                    
                    <a href="<?php echo e(route('branches.edit', $branch)); ?>" 
                       class="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                        <i class="fas fa-edit"></i>
                        تعديل الفرع
                    </a>
                    
                    <button onclick="deleteBranch('<?php echo e($branch->id); ?>')" 
                            class="w-full bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                        <i class="fas fa-trash"></i>
                        حذف الفرع
                    </button>
                </div>
            </div>

            <!-- Quick Links -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="fas fa-link text-blue-600"></i>
                        روابط سريعة
                    </h2>
                </div>
                <div class="p-6 space-y-3">
                    <a href="#" class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                        <i class="fas fa-users"></i>
                        إدارة الموظفين
                    </a>
                    <a href="#" class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                        <i class="fas fa-table"></i>
                        إدارة الطاولات
                    </a>
                    <a href="#" class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                        <i class="fas fa-utensils"></i>
                        إدارة القائمة
                    </a>
                    <a href="#" class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                        <i class="fas fa-chart-line"></i>
                        التقارير
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="p-6">
            <div class="flex items-center mb-4">
                <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                    <i class="fas fa-exclamation-triangle text-red-600"></i>
                </div>
            </div>
            <div class="text-center">
                <h3 class="text-lg font-medium text-gray-900 mb-2">تأكيد حذف الفرع</h3>
                <p class="text-sm text-gray-500 mb-4">هل أنت متأكد من حذف هذا الفرع؟ لا يمكن التراجع عن هذا الإجراء.</p>
            </div>
        </div>
        <div class="bg-gray-50 px-6 py-3 flex justify-end gap-3">
            <button type="button" onclick="closeDeleteModal()" 
                    class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                إلغاء
            </button>
            <button type="button" onclick="confirmDelete()" 
                    class="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700">
                حذف
            </button>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
let branchToDelete = null;

function updateBranchStatus(branchId, status) {
    if (confirm('هل أنت متأكد من تغيير حالة الفرع؟')) {
        fetch(`/admin/branches/${branchId}/status`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ status: status })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء تحديث حالة الفرع');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء تحديث حالة الفرع');
        });
    }
}

function deleteBranch(branchId) {
    branchToDelete = branchId;
    document.getElementById('deleteModal').classList.remove('hidden');
    document.getElementById('deleteModal').classList.add('flex');
}

function closeDeleteModal() {
    branchToDelete = null;
    document.getElementById('deleteModal').classList.add('hidden');
    document.getElementById('deleteModal').classList.remove('flex');
}

function confirmDelete() {
    if (branchToDelete) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/branches/${branchToDelete}`;
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';
        
        form.appendChild(csrfToken);
        form.appendChild(methodField);
        document.body.appendChild(form);
        form.submit();
    }
}

// Close modal when clicking outside
document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDeleteModal();
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Tenant\Providers/../resources/views/branches/show.blade.php ENDPATH**/ ?>