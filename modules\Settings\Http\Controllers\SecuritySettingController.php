<?php

namespace Modules\Settings\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Settings\SecuritySetting;
use App\Services\SettingsService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class SecuritySettingController extends Controller
{
    protected $settingsService;

    public function __construct(SettingsService $settingsService)
    {
        $this->settingsService = $settingsService;
    }

    public function index(Request $request): JsonResponse
    {
        $tenantId = $request->user()->tenant_id;

        $securitySettings = $this->settingsService->getSecuritySettings($tenantId);

        return response()->json([
            'success' => true,
            'data' => $securitySettings
        ]);
    }

    public function show(Request $request): JsonResponse
    {
        $tenantId = $request->user()->tenant_id;
        
        $securitySetting = SecuritySetting::where('tenant_id', $tenantId)->first();

        if (!$securitySetting) {
            // Create default security settings if none exist
            $securitySetting = SecuritySetting::create([
                'tenant_id' => $tenantId,
                'password_min_length' => 8,
                'password_require_uppercase' => true,
                'password_require_lowercase' => true,
                'password_require_numbers' => true,
                'password_require_symbols' => false,
                'password_expiry_days' => 90,
                'max_login_attempts' => 5,
                'lockout_duration' => 30,
                'session_timeout' => 120,
                'two_factor_enabled' => false,
                'ip_whitelist_enabled' => false,
                'audit_log_enabled' => true,
                'audit_log_retention_days' => 365,
            ]);
        }

        return response()->json([
            'success' => true,
            'data' => $securitySetting
        ]);
    }

    public function update(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'password_min_length' => 'integer|min:6|max:50',
            'password_require_uppercase' => 'boolean',
            'password_require_lowercase' => 'boolean',
            'password_require_numbers' => 'boolean',
            'password_require_symbols' => 'boolean',
            'password_expiry_days' => 'nullable|integer|min:0|max:365',
            'max_login_attempts' => 'integer|min:1|max:20',
            'lockout_duration' => 'integer|min:1|max:1440',
            'session_timeout' => 'integer|min:5|max:480',
            'two_factor_enabled' => 'boolean',
            'ip_whitelist_enabled' => 'boolean',
            'ip_whitelist' => 'nullable|array',
            'ip_whitelist.*' => 'ip',
            'audit_log_enabled' => 'boolean',
            'audit_log_retention_days' => 'integer|min:1|max:3650',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $tenantId = $request->user()->tenant_id;
            
            $securitySetting = SecuritySetting::where('tenant_id', $tenantId)->first();

            if (!$securitySetting) {
                $securitySetting = new SecuritySetting(['tenant_id' => $tenantId]);
            }

            $securitySetting->fill($request->only([
                'password_min_length', 'password_require_uppercase', 'password_require_lowercase',
                'password_require_numbers', 'password_require_symbols', 'password_expiry_days',
                'max_login_attempts', 'lockout_duration', 'session_timeout', 'two_factor_enabled',
                'ip_whitelist_enabled', 'ip_whitelist', 'audit_log_enabled', 'audit_log_retention_days'
            ]));

            $securitySetting->save();

            return response()->json([
                'success' => true,
                'data' => $securitySetting,
                'message' => 'Security settings updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update security settings: ' . $e->getMessage()
            ], 500);
        }
    }

    public function addIpToWhitelist(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'ip_address' => 'required|ip',
            'description' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $tenantId = $request->user()->tenant_id;
            $ipAddress = $request->input('ip_address');
            $description = $request->input('description');
            
            $securitySetting = SecuritySetting::where('tenant_id', $tenantId)->first();

            if (!$securitySetting) {
                return response()->json([
                    'success' => false,
                    'message' => 'Security settings not found'
                ], 404);
            }

            $result = $securitySetting->addIpToWhitelist($ipAddress, $description);

            if ($result) {
                return response()->json([
                    'success' => true,
                    'data' => $securitySetting->ip_whitelist,
                    'message' => 'IP address added to whitelist successfully'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'IP address already exists in whitelist'
                ], 409);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to add IP to whitelist: ' . $e->getMessage()
            ], 500);
        }
    }

    public function removeIpFromWhitelist(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'ip_address' => 'required|ip',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $tenantId = $request->user()->tenant_id;
            $ipAddress = $request->input('ip_address');
            
            $securitySetting = SecuritySetting::where('tenant_id', $tenantId)->first();

            if (!$securitySetting) {
                return response()->json([
                    'success' => false,
                    'message' => 'Security settings not found'
                ], 404);
            }

            $result = $securitySetting->removeIpFromWhitelist($ipAddress);

            if ($result) {
                return response()->json([
                    'success' => true,
                    'data' => $securitySetting->ip_whitelist,
                    'message' => 'IP address removed from whitelist successfully'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'IP address not found in whitelist'
                ], 404);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to remove IP from whitelist: ' . $e->getMessage()
            ], 500);
        }
    }

    public function validatePassword(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $tenantId = $request->user()->tenant_id;
            $password = $request->input('password');
            
            $securitySetting = SecuritySetting::where('tenant_id', $tenantId)->first();

            if (!$securitySetting) {
                return response()->json([
                    'success' => false,
                    'message' => 'Security settings not found'
                ], 404);
            }

            $validation = $securitySetting->validatePassword($password);

            return response()->json([
                'success' => true,
                'data' => $validation
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to validate password: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getPasswordPolicy(Request $request): JsonResponse
    {
        try {
            $tenantId = $request->user()->tenant_id;
            
            $securitySetting = SecuritySetting::where('tenant_id', $tenantId)->first();

            if (!$securitySetting) {
                return response()->json([
                    'success' => false,
                    'message' => 'Security settings not found'
                ], 404);
            }

            $policy = $securitySetting->getPasswordPolicy();

            return response()->json([
                'success' => true,
                'data' => $policy
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get password policy: ' . $e->getMessage()
            ], 500);
        }
    }

    public function checkIpWhitelist(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'ip_address' => 'required|ip',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $tenantId = $request->user()->tenant_id;
            $ipAddress = $request->input('ip_address');
            
            $securitySetting = SecuritySetting::where('tenant_id', $tenantId)->first();

            if (!$securitySetting) {
                return response()->json([
                    'success' => false,
                    'message' => 'Security settings not found'
                ], 404);
            }

            $isAllowed = $securitySetting->isIpAllowed($ipAddress);

            return response()->json([
                'success' => true,
                'data' => [
                    'ip_address' => $ipAddress,
                    'is_allowed' => $isAllowed,
                    'whitelist_enabled' => $securitySetting->ip_whitelist_enabled
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to check IP whitelist: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getSecurityReport(Request $request): JsonResponse
    {
        try {
            $tenantId = $request->user()->tenant_id;
            
            $securitySetting = SecuritySetting::where('tenant_id', $tenantId)->first();

            if (!$securitySetting) {
                return response()->json([
                    'success' => false,
                    'message' => 'Security settings not found'
                ], 404);
            }

            $report = [
                'password_policy' => $securitySetting->getPasswordPolicy(),
                'login_security' => [
                    'max_attempts' => $securitySetting->max_login_attempts,
                    'lockout_duration' => $securitySetting->lockout_duration,
                    'session_timeout' => $securitySetting->session_timeout,
                ],
                'two_factor' => [
                    'enabled' => $securitySetting->two_factor_enabled,
                ],
                'ip_whitelist' => [
                    'enabled' => $securitySetting->ip_whitelist_enabled,
                    'count' => count($securitySetting->ip_whitelist ?? []),
                ],
                'audit_log' => [
                    'enabled' => $securitySetting->audit_log_enabled,
                    'retention_days' => $securitySetting->audit_log_retention_days,
                ],
            ];

            return response()->json([
                'success' => true,
                'data' => $report
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate security report: ' . $e->getMessage()
            ], 500);
        }
    }
}
