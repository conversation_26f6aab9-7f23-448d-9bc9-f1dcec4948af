<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Tenant;
use App\Models\SubscriptionPlan;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\TenantSubscription>
 */
class TenantSubscriptionFactory extends Factory
{
    public function definition(): array
    {
        return [
            'tenant_id' => Tenant::factory(),
            'plan_id' => SubscriptionPlan::factory(),
            'status' => $this->faker->randomElement(['trial', 'active', 'suspended', 'cancelled', 'expired']),
            'start_date' => now()->subDays($this->faker->numberBetween(1, 30)),
            'end_date' => now()->addDays($this->faker->numberBetween(30, 365)),
            'trial_days' => $this->faker->numberBetween(0, 30),
            'billing_cycle' => $this->faker->randomElement(['monthly', 'yearly']),
            'next_billing_date' => now()->addDays($this->faker->numberBetween(1, 30)),
            'auto_renew' => $this->faker->boolean(),
            'discount_percentage' => $this->faker->randomFloat(2, 0, 20),
            'total_amount' => $this->faker->randomFloat(2, 10, 500),
        ];
    }
}