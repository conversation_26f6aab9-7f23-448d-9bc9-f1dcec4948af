@extends('layouts.master')

@section('title', 'Backup & Recovery Settings')

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
@endpush

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-purple-600 to-indigo-600 rounded-lg shadow-lg p-6 mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                            <i class="fas fa-shield-alt text-white text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <h1 class="text-2xl font-bold text-white">Backup & Recovery Settings</h1>
                        <p class="text-purple-100">Configure automated backups and data recovery options</p>
                    </div>
                </div>
            </div>
            <div class="mt-4 md:mt-0 flex space-x-3">
                <button id="manual-backup-btn" class="px-4 py-2 bg-white text-purple-600 rounded-lg hover:bg-purple-50 transition-colors duration-200 font-medium">
                    <i class="fas fa-download mr-2"></i>
                    Manual Backup
                </button>
                <button id="test-restore-btn" class="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-400 transition-colors duration-200 font-medium">
                    <i class="fas fa-upload mr-2"></i>
                    Test Restore
                </button>
            </div>
        </div>
    </div>

    <!-- Backup Status Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-check-circle text-green-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Last Backup</p>
                    <p class="text-lg font-bold text-gray-900">2 hours ago</p>
                    <p class="text-xs text-green-600">Successful</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-database text-blue-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Backup Size</p>
                    <p class="text-lg font-bold text-gray-900">2.4 GB</p>
                    <p class="text-xs text-blue-600">Compressed</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-history text-purple-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Retention</p>
                    <p class="text-lg font-bold text-gray-900">30 days</p>
                    <p class="text-xs text-purple-600">Auto cleanup</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-cloud text-orange-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Storage Used</p>
                    <p class="text-lg font-bold text-gray-900">45.2 GB</p>
                    <p class="text-xs text-orange-600">of 100 GB</p>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Automated Backup Settings -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-robot mr-2 text-purple-600"></i>
                Automated Backup Settings
            </h3>
            
            <form class="space-y-4">
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div>
                            <label for="auto_backup" class="text-sm font-medium text-gray-700">Enable Auto Backup</label>
                            <p class="text-sm text-gray-500">Automatically backup data at scheduled intervals</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="auto_backup" name="auto_backup" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                        </label>
                    </div>
                </div>

                <div>
                    <label for="backup_frequency" class="block text-sm font-medium text-gray-700 mb-2">
                        Backup Frequency
                    </label>
                    <select id="backup_frequency" name="backup_frequency" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        <option value="hourly">Every Hour</option>
                        <option value="daily" selected>Daily</option>
                        <option value="weekly">Weekly</option>
                        <option value="monthly">Monthly</option>
                    </select>
                </div>

                <div>
                    <label for="backup_time" class="block text-sm font-medium text-gray-700 mb-2">
                        Backup Time
                    </label>
                    <input type="time" id="backup_time" name="backup_time" value="02:00"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                    <p class="text-xs text-gray-500 mt-1">Recommended: During low activity hours</p>
                </div>

                <div>
                    <label for="retention_days" class="block text-sm font-medium text-gray-700 mb-2">
                        Retention Period (days)
                    </label>
                    <input type="number" id="retention_days" name="retention_days" value="30" min="1" max="365"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                    <p class="text-xs text-gray-500 mt-1">Backups older than this will be automatically deleted</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Backup Components
                    </label>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" name="backup_database" checked class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                            <span class="ml-2 text-sm text-gray-700">Database</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="backup_files" checked class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                            <span class="ml-2 text-sm text-gray-700">Application Files</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="backup_uploads" checked class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                            <span class="ml-2 text-sm text-gray-700">User Uploads</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="backup_logs" class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                            <span class="ml-2 text-sm text-gray-700">System Logs</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="backup_config" checked class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                            <span class="ml-2 text-sm text-gray-700">Configuration Files</span>
                        </label>
                    </div>
                </div>

                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div>
                            <label for="compress_backup" class="text-sm font-medium text-gray-700">Compress Backups</label>
                            <p class="text-sm text-gray-500">Reduce backup file size</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="compress_backup" name="compress_backup" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="encrypt_backup" class="text-sm font-medium text-gray-700">Encrypt Backups</label>
                            <p class="text-sm text-gray-500">Secure backup files with encryption</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="encrypt_backup" name="encrypt_backup" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                        </label>
                    </div>
                </div>

                <div class="pt-4">
                    <button type="submit" class="w-full px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-lg hover:bg-purple-700">
                        <i class="fas fa-save mr-2"></i>
                        Save Backup Settings
                    </button>
                </div>
            </form>
        </div>

        <!-- Storage & Cloud Settings -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-cloud-upload-alt mr-2 text-blue-600"></i>
                Storage & Cloud Settings
            </h3>
            
            <form class="space-y-4">
                <div>
                    <label for="storage_location" class="block text-sm font-medium text-gray-700 mb-2">
                        Primary Storage Location
                    </label>
                    <select id="storage_location" name="storage_location" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="local" selected>Local Server</option>
                        <option value="aws_s3">Amazon S3</option>
                        <option value="google_drive">Google Drive</option>
                        <option value="dropbox">Dropbox</option>
                        <option value="azure">Microsoft Azure</option>
                        <option value="ftp">FTP Server</option>
                    </select>
                </div>

                <div id="local-storage-settings">
                    <label for="local_path" class="block text-sm font-medium text-gray-700 mb-2">
                        Local Storage Path
                    </label>
                    <input type="text" id="local_path" name="local_path" value="/var/backups/restaurant-pos"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div id="cloud-storage-settings" style="display: none;">
                    <div class="space-y-3">
                        <div>
                            <label for="cloud_access_key" class="block text-sm font-medium text-gray-700 mb-2">
                                Access Key / Client ID
                            </label>
                            <input type="password" id="cloud_access_key" name="cloud_access_key" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label for="cloud_secret_key" class="block text-sm font-medium text-gray-700 mb-2">
                                Secret Key / Client Secret
                            </label>
                            <input type="password" id="cloud_secret_key" name="cloud_secret_key" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label for="cloud_bucket" class="block text-sm font-medium text-gray-700 mb-2">
                                Bucket / Container Name
                            </label>
                            <input type="text" id="cloud_bucket" name="cloud_bucket" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label for="cloud_region" class="block text-sm font-medium text-gray-700 mb-2">
                                Region
                            </label>
                            <input type="text" id="cloud_region" name="cloud_region" placeholder="us-east-1"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                    </div>
                </div>

                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div>
                            <label for="offsite_backup" class="text-sm font-medium text-gray-700">Offsite Backup</label>
                            <p class="text-sm text-gray-500">Store backups in secondary location</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="offsite_backup" name="offsite_backup" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="verify_backup" class="text-sm font-medium text-gray-700">Verify Backup Integrity</label>
                            <p class="text-sm text-gray-500">Check backup files after creation</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="verify_backup" name="verify_backup" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                </div>

                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 class="text-sm font-medium text-blue-900 mb-2">Storage Usage</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between text-sm text-blue-800">
                            <span>Local Storage:</span>
                            <span>45.2 GB / 100 GB</span>
                        </div>
                        <div class="w-full bg-blue-200 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full" style="width: 45%"></div>
                        </div>
                        <div class="flex justify-between text-sm text-blue-800">
                            <span>Cloud Storage:</span>
                            <span>12.8 GB / 50 GB</span>
                        </div>
                        <div class="w-full bg-blue-200 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full" style="width: 26%"></div>
                        </div>
                    </div>
                </div>

                <div class="flex space-x-3">
                    <button type="button" id="test-connection-btn" class="flex-1 px-4 py-2 text-sm font-medium text-blue-700 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100">
                        <i class="fas fa-plug mr-2"></i>
                        Test Connection
                    </button>
                    <button type="submit" class="flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700">
                        <i class="fas fa-save mr-2"></i>
                        Save Storage Settings
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Recovery & Restore Settings -->
    <div class="mt-6 bg-white rounded-lg shadow-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fas fa-undo mr-2 text-green-600"></i>
            Recovery & Restore Settings
        </h3>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div class="space-y-4">
                <h4 class="font-medium text-gray-900">Recovery Options</h4>
                
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div>
                            <label for="point_in_time_recovery" class="text-sm font-medium text-gray-700">Point-in-Time Recovery</label>
                            <p class="text-sm text-gray-500">Restore to specific date and time</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="point_in_time_recovery" name="point_in_time_recovery" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="selective_restore" class="text-sm font-medium text-gray-700">Selective Restore</label>
                            <p class="text-sm text-gray-500">Restore specific tables or files only</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="selective_restore" name="selective_restore" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <label for="auto_rollback" class="text-sm font-medium text-gray-700">Auto Rollback on Failure</label>
                            <p class="text-sm text-gray-500">Automatically rollback failed restores</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="auto_rollback" name="auto_rollback" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
                        </label>
                    </div>
                </div>

                <div>
                    <label for="recovery_timeout" class="block text-sm font-medium text-gray-700 mb-2">
                        Recovery Timeout (minutes)
                    </label>
                    <input type="number" id="recovery_timeout" name="recovery_timeout" value="60" min="5" max="300"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                </div>

                <div>
                    <label for="notification_emails" class="block text-sm font-medium text-gray-700 mb-2">
                        Recovery Notification Emails
                    </label>
                    <textarea id="notification_emails" name="notification_emails" rows="3" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                              placeholder="<EMAIL>, <EMAIL>"><EMAIL>
<EMAIL></textarea>
                </div>
            </div>

            <div class="space-y-4">
                <h4 class="font-medium text-gray-900">Recent Backups</h4>
                
                <div class="space-y-3 max-h-64 overflow-y-auto">
                    <div class="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-600 mr-3"></i>
                            <div>
                                <p class="text-sm font-medium text-gray-900">Full Backup</p>
                                <p class="text-xs text-gray-500">Today, 2:00 AM - 2.4 GB</p>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 text-xs font-medium text-green-700 bg-green-100 rounded hover:bg-green-200">
                                Download
                            </button>
                            <button class="px-3 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded hover:bg-blue-200">
                                Restore
                            </button>
                        </div>
                    </div>

                    <div class="flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-gray-400 mr-3"></i>
                            <div>
                                <p class="text-sm font-medium text-gray-900">Full Backup</p>
                                <p class="text-xs text-gray-500">Yesterday, 2:00 AM - 2.3 GB</p>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 text-xs font-medium text-gray-700 bg-gray-100 rounded hover:bg-gray-200">
                                Download
                            </button>
                            <button class="px-3 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded hover:bg-blue-200">
                                Restore
                            </button>
                        </div>
                    </div>

                    <div class="flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-gray-400 mr-3"></i>
                            <div>
                                <p class="text-sm font-medium text-gray-900">Full Backup</p>
                                <p class="text-xs text-gray-500">2 days ago, 2:00 AM - 2.2 GB</p>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 text-xs font-medium text-gray-700 bg-gray-100 rounded hover:bg-gray-200">
                                Download
                            </button>
                            <button class="px-3 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded hover:bg-blue-200">
                                Restore
                            </button>
                        </div>
                    </div>

                    <div class="flex items-center justify-between p-3 bg-red-50 border border-red-200 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-triangle text-red-600 mr-3"></i>
                            <div>
                                <p class="text-sm font-medium text-gray-900">Full Backup</p>
                                <p class="text-xs text-gray-500">3 days ago, 2:00 AM - Failed</p>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 text-xs font-medium text-red-700 bg-red-100 rounded hover:bg-red-200">
                                View Error
                            </button>
                            <button class="px-3 py-1 text-xs font-medium text-gray-700 bg-gray-100 rounded hover:bg-gray-200">
                                Retry
                            </button>
                        </div>
                    </div>
                </div>

                <div class="pt-4">
                    <button class="w-full px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-lg hover:bg-green-700">
                        <i class="fas fa-save mr-2"></i>
                        Save Recovery Settings
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Form submission handlers
    $('form').on('submit', function(e) {
        e.preventDefault();
        
        Swal.fire({
            icon: 'success',
            title: 'Success!',
            text: 'Backup settings updated successfully.',
            timer: 2000,
            showConfirmButton: false
        });
    });

    // Manual backup handler
    $('#manual-backup-btn').on('click', function() {
        Swal.fire({
            title: 'Manual Backup',
            text: 'This will create a full backup of your system. This may take several minutes.',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Start Backup',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                // Show progress
                let timerInterval;
                Swal.fire({
                    title: 'Creating Backup...',
                    html: 'Progress: <b>0%</b>',
                    timer: 10000,
                    timerProgressBar: true,
                    didOpen: () => {
                        Swal.showLoading();
                        const b = Swal.getHtmlContainer().querySelector('b');
                        let progress = 0;
                        timerInterval = setInterval(() => {
                            progress += 10;
                            b.textContent = progress + '%';
                            if (progress >= 100) {
                                clearInterval(timerInterval);
                            }
                        }, 1000);
                    },
                    willClose: () => {
                        clearInterval(timerInterval);
                    }
                }).then(() => {
                    Swal.fire({
                        icon: 'success',
                        title: 'Backup Complete',
                        text: 'Manual backup created successfully.',
                        timer: 2000,
                        showConfirmButton: false
                    });
                });
            }
        });
    });

    // Test restore handler
    $('#test-restore-btn').on('click', function() {
        Swal.fire({
            title: 'Test Restore',
            text: 'This will test the restore process using the latest backup.',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Run Test',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                Swal.fire({
                    title: 'Testing Restore...',
                    text: 'Please wait while we test the restore process.',
                    icon: 'info',
                    showConfirmButton: false,
                    timer: 5000
                }).then(() => {
                    Swal.fire({
                        icon: 'success',
                        title: 'Test Successful',
                        text: 'Restore test completed successfully. Your backups are working properly.',
                        timer: 3000,
                        showConfirmButton: false
                    });
                });
            }
        });
    });

    // Storage location change handler
    $('#storage_location').on('change', function() {
        const location = $(this).val();
        if (location === 'local') {
            $('#local-storage-settings').show();
            $('#cloud-storage-settings').hide();
        } else {
            $('#local-storage-settings').hide();
            $('#cloud-storage-settings').show();
        }
    });

    // Test connection handler
    $('#test-connection-btn').on('click', function() {
        const location = $('#storage_location').val();
        
        Swal.fire({
            title: 'Testing Connection...',
            text: `Testing connection to ${location} storage.`,
            icon: 'info',
            showConfirmButton: false,
            timer: 3000
        }).then(() => {
            Swal.fire({
                icon: 'success',
                title: 'Connection Successful',
                text: 'Storage connection test passed.',
                timer: 2000,
                showConfirmButton: false
            });
        });
    });

    // Backup frequency change handler
    $('#backup_frequency').on('change', function() {
        const frequency = $(this).val();
        const timeField = $('#backup_time');
        
        if (frequency === 'hourly') {
            timeField.prop('disabled', true);
            timeField.val('');
        } else {
            timeField.prop('disabled', false);
            if (!timeField.val()) {
                timeField.val('02:00');
            }
        }
    });

    // Retention period validation
    $('#retention_days').on('input', function() {
        const days = parseInt($(this).val());
        if (days < 7) {
            Swal.fire({
                icon: 'warning',
                title: 'Low Retention Period',
                text: 'Retention period less than 7 days is not recommended.',
                timer: 3000,
                showConfirmButton: false
            });
        }
    });

    // Restore button handlers
    $('.bg-green-50 button:contains("Restore"), .bg-gray-50 button:contains("Restore")').on('click', function() {
        const backupInfo = $(this).closest('.flex').find('.text-xs').text();
        
        Swal.fire({
            title: 'Restore Backup',
            text: `Are you sure you want to restore from: ${backupInfo}? This will overwrite current data.`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Restore',
            cancelButtonText: 'Cancel',
            confirmButtonColor: '#dc2626'
        }).then((result) => {
            if (result.isConfirmed) {
                Swal.fire({
                    title: 'Restoring...',
                    text: 'Please wait while we restore your data.',
                    icon: 'info',
                    showConfirmButton: false,
                    timer: 8000
                }).then(() => {
                    Swal.fire({
                        icon: 'success',
                        title: 'Restore Complete',
                        text: 'Data restored successfully.',
                        timer: 2000,
                        showConfirmButton: false
                    });
                });
            }
        });
    });

    // Download button handlers
    $('button:contains("Download")').on('click', function() {
        Swal.fire({
            icon: 'info',
            title: 'Download Started',
            text: 'Backup download has started. Please check your downloads folder.',
            timer: 2000,
            showConfirmButton: false
        });
    });

    // View error button handler
    $('button:contains("View Error")').on('click', function() {
        Swal.fire({
            icon: 'error',
            title: 'Backup Error Details',
            text: 'Error: Insufficient disk space. Please free up space and try again.',
            confirmButtonText: 'OK'
        });
    });

    // Retry button handler
    $('button:contains("Retry")').on('click', function() {
        Swal.fire({
            title: 'Retry Backup',
            text: 'This will retry the failed backup operation.',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Retry',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                Swal.fire({
                    icon: 'success',
                    title: 'Backup Queued',
                    text: 'Backup has been queued for retry.',
                    timer: 2000,
                    showConfirmButton: false
                });
            }
        });
    });
});
</script>
@endpush