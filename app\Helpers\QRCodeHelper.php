<?php

namespace App\Helpers;

use App\Models\Table;

class QRCodeHelper
{
    /**
     * Generate a slug from a given name.
     * 
     * @param string $name
     * @return string
     */
    public static function generateSlug(string $name): string
    {
        // Convert to lowercase, replace spaces with hyphens, and remove special characters
        $slug = strtolower(preg_replace('/[^a-zA-Z0-9\s-]/', '', $name));
        $slug = str_replace(' ', '-', $slug);
        $slug = preg_replace('/-+/', '-', $slug); // Replace multiple hyphens with single hyphen
        $slug = trim($slug, '-'); // Remove leading/trailing hyphens
        
        return $slug ?: 'default';
    }

    /**
     * Generate a hash for a table based on branch name and table number.
     * 
     * @param Table $table
     * @return string
     */
    public static function generateTableHash(Table $table): string
    {
        // Create a hash based on branch name and table number
        $branchName = $table->branch->name ?? 'default';
        $tableNumber = $table->table_number;
        
        // Convert to lowercase and replace spaces with hyphens
        $hash = strtolower(str_replace(' ', '-', $branchName . '-' . $tableNumber));
        
        return $hash;
    }

    /**
     * Generate a unique QR code string for a table.
     * 
     * @param int $tableId
     * @param int $branchId
     * @return string
     */
    public static function generateUniqueQRString(int $tableId, int $branchId): string
    {
        do {
            $qrString = 'TBL_' . $branchId . '_' . $tableId . '_' . strtoupper(uniqid());
        } while (Table::where('qr_code', $qrString)->exists());
        
        return $qrString;
    }

    /**
     * Generate QR code URL for a table with specific action.
     * 
     * @param Table $table
     * @param string $action
     * @return string
     */
    public static function generateTableUrl(Table $table, string $action = ''): string
    {
        $baseUrl = config('app.url');
        $tenantSlug = $table->branch->tenant->code ?? self::generateSlug($table->branch->tenant->name);
        
        // Create the new URL format: domain/restaurant/{tenantSlug}/{qr_code}
        $tableUrl = $baseUrl . '/restaurant/' . $tenantSlug . '/' . $table->qr_code;
        
        if ($action) {
            $tableUrl .= '?action=' . $action;
        }
        
        return $tableUrl;
    }

    /**
     * Validate QR code data structure.
     * 
     * @param array $data
     * @return array
     */
    public static function validateQRData(array $data): array
    {
        if (!isset($data['type']) || $data['type'] !== 'table') {
            return [
                'valid' => false,
                'error' => 'Invalid QR code format or type'
            ];
        }
        
        if (!isset($data['table_id']) || !isset($data['qr_code'])) {
            return [
                'valid' => false,
                'error' => 'Missing required QR code data'
            ];
        }
        
        return [
            'valid' => true,
            'data' => $data
        ];
    }

    /**
     * Generate QR code content array for a table.
     * 
     * @param Table $table
     * @param string $type
     * @return array
     */
    public static function generateQRContent(Table $table, string $type = 'table'): array
    {
        $tenantSlug = $table->branch->tenant->code ?? self::generateSlug($table->branch->tenant->name);
        $hash = self::generateTableHash($table);
        $tableUrl = self::generateTableUrl($table);
        
        return [
            'type' => $type,
            'table_id' => $table->id,
            'table_number' => $table->table_number,
            'branch_id' => $table->branch_id,
            'branch_name' => $table->branch->name ?? '',
            'tenant_slug' => $tenantSlug,
            'qr_code' => $table->qr_code,
            'url' => $tableUrl,
            'menu_url' => $tableUrl . '?action=menu',
            'order_url' => $tableUrl . '?action=order',
            'hash' => $hash,
        ];
    }
}