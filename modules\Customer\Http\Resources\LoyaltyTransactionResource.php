<?php

namespace Modules\Customer\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LoyaltyTransactionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'customer_id' => $this->customer_id,
            'customer' => $this->whenLoaded('customer', function () {
                return [
                    'id' => $this->customer->id,
                    'full_name' => $this->customer->first_name . ' ' . $this->customer->last_name,
                    'email' => $this->customer->email,
                    'phone' => $this->customer->phone,
                ];
            }),
            'order_id' => $this->order_id,
            'order' => $this->whenLoaded('order', function () {
                return [
                    'id' => $this->order->id,
                    'order_number' => $this->order->order_number,
                    'total_amount' => (float) $this->order->total_amount,
                    'status' => $this->order->status,
                ];
            }),
            'type' => $this->type,
            'type_label' => $this->type === 'earned' ? 'Points Earned' : 'Points Redeemed',
            'points' => (float) $this->points,
            'points_display' => $this->type === 'earned' ? '+' . $this->points : '-' . $this->points,
            'balance_before' => (float) $this->balance_before,
            'balance_after' => (float) $this->balance_after,
            'reason' => $this->reason,
            'expires_at' => $this->expires_at?->format('Y-m-d H:i:s'),
            'is_expired' => $this->expires_at ? $this->expires_at->isPast() : false,
            'days_until_expiry' => $this->expires_at ? $this->expires_at->diffInDays(now(), false) : null,
            'tenant_id' => $this->tenant_id,
            'branch_id' => $this->branch_id,
            'branch' => $this->whenLoaded('branch', function () {
                return [
                    'id' => $this->branch->id,
                    'name' => $this->branch->name,
                ];
            }),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
        ];
    }
}