<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'branch_id',
        'customer_id',
        'table_id',
        'order_number',
        'order_type',
        'status',
        'pax', // Number of people (for dine-in orders)
        'subtotal',
        'tax_amount',
        'discount_amount',
        'service_charge',
        'delivery_fee',
        'total_amount',
        'customer_info',
        'delivery_address',
        'delivery_coordinates',
        'special_instructions',
        'waiter_id',
        'cashier_id',
        'order_time',
        'estimated_ready_time',
        'actual_ready_time',
        'served_time',
        'delivery_man_id',
        'notes', // General order notes
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'subtotal' => 'decimal:2',
            'tax_amount' => 'decimal:2',
            'discount_amount' => 'decimal:2',
            'service_charge' => 'decimal:2',
            'delivery_fee' => 'decimal:2',
            'total_amount' => 'decimal:2',
            'customer_info' => 'array',
            'delivery_coordinates' => 'array',
            'order_time' => 'datetime',
            'estimated_ready_time' => 'datetime',
            'actual_ready_time' => 'datetime',
            'served_time' => 'datetime',
        ];
    }

    // Relationships
    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function table()
    {
        return $this->belongsTo(Table::class);
    }

    public function server()
    {
        return $this->belongsTo(User::class, 'waiter_id');
    }

    public function cashier()
    {
        return $this->belongsTo(User::class, 'cashier_id');
    }

    public function items()
    {
        return $this->hasMany(OrderItem::class);
    }

    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }

    public function discounts()
    {
        return $this->hasMany(OrderDiscount::class);
    }

    public function transaction()
    {
        return $this->hasOne(\Modules\Transaction\Models\Transaction::class);
    }

    public function payments()
    {
        return $this->hasManyThrough(
            \Modules\Transaction\Models\Payment::class,
            \Modules\Transaction\Models\Transaction::class
        );
    }

    public function loyaltyTransactions()
    {
        return $this->hasMany(LoyaltyTransaction::class);
    }

    public function kitchenDisplayOrders()
    {
        return $this->hasMany(KitchenDisplayOrder::class);
    }

    /**
     * Get KOT orders for this order.
     */
    public function kotOrders()
    {
        return $this->hasMany(\Modules\Kitchen\Models\KotOrder::class);
    }

    /**
     * Get the active KOT for this order.
     */
    public function activeKot()
    {
        return $this->hasOne(\Modules\Kitchen\Models\KotOrder::class)
            ->whereIn('status', ['pending', 'preparing', 'ready']);
    }

    /**
     * Check if this order has an active KOT.
     */
    public function hasActiveKot(): bool
    {
        return $this->activeKot()->exists();
    }

    /**
     * Get the latest KOT for this order.
     */
    public function latestKot()
    {
        return $this->hasOne(\Modules\Kitchen\Models\KotOrder::class)
            ->latest();
    }

    /**
     * Create KOT orders for this order using the Kitchen Service.
     */
    public function createKotOrders(): int
    {
        $kitchenService = app(\Modules\Kitchen\Services\KitchenService::class);
        $kotOrders = $kitchenService->createKotFromOrder($this);
        return count($kotOrders);
    }
}