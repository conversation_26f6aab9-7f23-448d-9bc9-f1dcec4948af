<?php

namespace Modules\Reservation\Contracts;

interface TableServiceInterface
{
    /**
     * Get all tables with optional filters.
     */
    public function getAllTables(array $filters = []);

    /**
     * Create a new table.
     */
    public function createTable(array $data);

    /**
     * Get table by ID.
     */
    public function getTableById(int $id);

    /**
     * Update table.
     */
    public function updateTable(int $id, array $data);

    /**
     * Delete table.
     */
    public function deleteTable(int $id);

    /**
     * Get available tables for given criteria.
     */
    public function getAvailableTables(int $branchId, string $datetime, int $partySize, int $duration = 120);

    /**
     * Update table status.
     */
    public function updateTableStatus(int $id, string $status);

    /**
     * Generate QR code for table.
     */
    public function generateTableQR(int $id);

    /**
     * Get table by QR code.
     */
    public function getTableByQR(string $qrCode);

    /**
     * Get table occupancy status.
     */
    public function getTableOccupancy(int $branchId = null, string $date = null);
}