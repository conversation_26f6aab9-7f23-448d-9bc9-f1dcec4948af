<?php

use Illuminate\Support\Facades\Route;
use Modules\Settings\Http\Controllers\Web\SettingsWebController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::middleware(['auth', 'tenant'])->prefix('settings')->name('settings.')->group(function () {

    // Main Settings Dashboard
    Route::get('/', [SettingsWebController::class, 'index'])->name('index');

    // Printer Settings Routes
    Route::prefix('printer')->name('printer.')->group(function () {
        Route::get('/', [SettingsWebController::class, 'printerSettings'])->name('index');
        Route::get('/create', [SettingsWebController::class, 'createPrinter'])->name('create');
        Route::get('/{id}/edit', [SettingsWebController::class, 'editPrinter'])->name('edit');
    });

    // Payment Settings Routes
    Route::prefix('payment')->name('payment.')->group(function () {
        Route::get('/', [SettingsWebController::class, 'paymentSettings'])->name('index');
        Route::get('/create', [SettingsWebController::class, 'createPaymentSetting'])->name('create');
        Route::get('/{id}/edit', [SettingsWebController::class, 'editPaymentSetting'])->name('edit');
    });

    // Security Settings Routes
    Route::prefix('security')->name('security.')->group(function () {
        Route::get('/', [SettingsWebController::class, 'securitySettings'])->name('index');
    });

    // Branch Settings Routes
    Route::prefix('branch')->name('branch.')->group(function () {
        Route::get('/', [SettingsWebController::class, 'branchSettings'])->name('index');
        Route::get('/{branchId}', [SettingsWebController::class, 'branchSettings'])->name('show');
    });

    // System Settings Routes
    Route::prefix('system')->name('system.')->group(function () {
        Route::get('/', [SettingsWebController::class, 'systemSettings'])->name('index');
    });

    // Restaurant Settings Routes
    Route::prefix('restaurant')->name('restaurant.')->group(function () {
        Route::get('/', [SettingsWebController::class, 'restaurantSettings'])->name('index');
    });

    // User/Role Settings Routes
    Route::prefix('user')->name('user.')->group(function () {
        Route::get('/', [SettingsWebController::class, 'userSettings'])->name('index');
    });

    // Report Settings Routes
    Route::prefix('report')->name('report.')->group(function () {
        Route::get('/', [SettingsWebController::class, 'reportSettings'])->name('index');
    });

    // Inventory Settings Routes
    Route::prefix('inventory')->name('inventory.')->group(function () {
        Route::get('/', [SettingsWebController::class, 'inventorySettings'])->name('index');
    });

    // Backup Settings Routes
    Route::prefix('backup')->name('backup.')->group(function () {
        Route::get('/', [SettingsWebController::class, 'backupSettings'])->name('index');
    });

    // General Settings Routes
    Route::prefix('general')->name('general.')->group(function () {
        Route::get('/restaurant', [SettingsWebController::class, 'restaurantInfo'])->name('restaurant');
        Route::get('/business', [SettingsWebController::class, 'businessSettings'])->name('business');
        Route::get('/localization', [SettingsWebController::class, 'localizationSettings'])->name('localization');
        Route::get('/timezone', [SettingsWebController::class, 'timezoneSettings'])->name('timezone');
    });

    // POS Settings Routes
    Route::prefix('pos')->name('pos.')->group(function () {
        Route::get('/receipt', [SettingsWebController::class, 'receiptSettings'])->name('receipt');
        Route::get('/printer', [SettingsWebController::class, 'printerSettings'])->name('printer');
        Route::get('/display', [SettingsWebController::class, 'displaySettings'])->name('display');
        Route::get('/shortcuts', [SettingsWebController::class, 'shortcutSettings'])->name('shortcuts');
    });

    // Payment Settings Routes (Extended)
    Route::prefix('payment')->name('payment.')->group(function () {
        Route::get('/', [SettingsWebController::class, 'paymentSettings'])->name('index');
        Route::get('/methods', [SettingsWebController::class, 'paymentMethods'])->name('methods');
        Route::get('/gateways', [SettingsWebController::class, 'paymentGateways'])->name('gateways');
        Route::get('/tax', [SettingsWebController::class, 'taxConfiguration'])->name('tax');
        Route::get('/discounts', [SettingsWebController::class, 'discountsSettings'])->name('discounts');
        Route::get('/create', [SettingsWebController::class, 'createPaymentSetting'])->name('create');
        Route::get('/{id}/edit', [SettingsWebController::class, 'editPaymentSetting'])->name('edit');
    });

    // Notification Settings Routes
    Route::prefix('notifications')->name('notifications.')->group(function () {
        Route::get('/email', [SettingsWebController::class, 'emailNotifications'])->name('email');
        Route::get('/sms', [SettingsWebController::class, 'smsNotifications'])->name('sms');
        Route::get('/push', [SettingsWebController::class, 'pushNotifications'])->name('push');
        Route::get('/alerts', [SettingsWebController::class, 'systemAlerts'])->name('alerts');
    });

    // Security Settings Routes (Extended)
    Route::prefix('security')->name('security.')->group(function () {
        Route::get('/', [SettingsWebController::class, 'securitySettings'])->name('index');
        Route::get('/password-policy', [SettingsWebController::class, 'passwordPolicy'])->name('password-policy');
        Route::get('/two-factor', [SettingsWebController::class, 'twoFactorAuth'])->name('two-factor');
        Route::get('/session', [SettingsWebController::class, 'sessionManagement'])->name('session');
        Route::get('/audit-log', [SettingsWebController::class, 'auditLog'])->name('audit-log');
    });

    // System Settings Routes (Extended)
    Route::prefix('system')->name('system.')->group(function () {
        Route::get('/', [SettingsWebController::class, 'systemSettings'])->name('index');
        Route::get('/backup', [SettingsWebController::class, 'backupRestore'])->name('backup');
        Route::get('/maintenance', [SettingsWebController::class, 'maintenanceMode'])->name('maintenance');
        Route::get('/logs', [SettingsWebController::class, 'systemLogs'])->name('logs');
        Route::get('/cache', [SettingsWebController::class, 'cacheManagement'])->name('cache');
    });
});

Route::middleware('web')->group(function () {
    // Add your web routes here
});
