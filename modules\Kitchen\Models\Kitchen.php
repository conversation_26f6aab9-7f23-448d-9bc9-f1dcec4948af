<?php

namespace Modules\Kitchen\Models;

use App\Models\Branch;
use App\Models\MenuCategory;
use App\Models\MenuItem;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Kitchen extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'branch_id',
        'name',
        'code',
        'description',
        'station_type',
        'max_concurrent_orders',
        'average_prep_time_minutes',
        'is_active',
        'display_order',
        'manager_id',
        'equipment_list',
        'created_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'equipment_list' => 'array',
        ];
    }

    /**
     * Get the branch that owns the kitchen.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the manager of the kitchen.
     */
    public function manager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'manager_id');
    }

    /**
     * Get the user who created the kitchen.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the KOT orders for this kitchen.
     */
    public function kotOrders(): HasMany
    {
        return $this->hasMany(KotOrder::class);
    }

    /**
     * Get the menu items assigned to this kitchen.
     */
    public function menuItems(): BelongsToMany
    {
        return $this->belongsToMany(MenuItem::class, 'kitchen_menu_items')
            ->withPivot(['prep_time_minutes', 'priority_level', 'is_active', 'special_instructions', 'assigned_by', 'assigned_at'])
            ->withTimestamps();
    }

    /**
     * Get the kitchen menu item assignments.
     */
    public function kitchenMenuItems(): HasMany
    {
        return $this->hasMany(KitchenMenuItem::class);
    }

    /**
     * Get the kitchen display orders.
     */
    public function displayOrders(): HasMany
    {
        return $this->hasMany(KitchenDisplayOrder::class);
    }

    /**
     * Scope to get active kitchens.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get kitchens by station type.
     */
    public function scopeByStationType($query, string $stationType)
    {
        return $query->where('station_type', $stationType);
    }

    /**
     * Scope to order by display order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('display_order')->orderBy('name');
    }

    /**
     * Check if kitchen is currently operating.
     */
    public function isOperating(): bool
    {
        return $this->is_active;
    }

    /**
     * Get current workload (pending + preparing orders).
     */
    public function getCurrentWorkload(): int
    {
        return $this->kotOrders()
            ->whereIn('status', ['pending', 'preparing'])
            ->count();
    }

    /**
     * Check if kitchen can accept new orders.
     */
    public function canAcceptOrders(): bool
    {
        if (!$this->isOperating()) {
            return false;
        }

        if ($this->max_concurrent_orders > 0) {
            return $this->getCurrentWorkload() < $this->max_concurrent_orders;
        }

        return true;
    }
}
