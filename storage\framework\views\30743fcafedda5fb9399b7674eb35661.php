<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo e($restaurant['name']); ?> - QR Menu</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'frost': {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a'
                        },
                        'ice': {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e'
                        },
                        'winter': {
                            50: '#fafafa',
                            100: '#f4f4f5',
                            200: '#e4e4e7',
                            300: '#d4d4d8',
                            400: '#a1a1aa',
                            500: '#71717a',
                            600: '#52525b',
                            700: '#3f3f46',
                            800: '#27272a',
                            900: '#18181b'
                        }
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.3s ease-in-out',
                        'slide-up': 'slideUp 0.3s ease-out',
                        'pulse-soft': 'pulseSoft 2s infinite',
                        'float': 'float 3s ease-in-out infinite'
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' }
                        },
                        pulseSoft: {
                            '0%, 100%': { opacity: '1' },
                            '50%': { opacity: '0.7' }
                        },
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' }
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .card-hover {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .card-hover:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .carousel-shadow {
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
        
        .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }
    </style>
</head>
<body class="bg-frost-50 text-frost-800 font-sans">
    <!-- Navigation Bar -->
    <nav class="fixed top-0 w-full bg-white/80 backdrop-blur-md border-b border-frost-200 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16 sm:h-18">
                <!-- Logo -->
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-ice-500 rounded-xl flex items-center justify-center">
                        <i class="fas fa-utensils text-white text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-frost-900"><?php echo e($restaurant['name']); ?></h1>
                        <p class="text-xs text-frost-500 hidden sm:block">Digital Menu</p>
                    </div>
                </div>

                <!-- Controls -->
                <div class="flex items-center space-x-3">
                    <!-- Branch Selector Dropdown -->
                    <div class="relative">
                        <button id="branchDropdownBtn" onclick="toggleBranchDropdown()" class="flex items-center space-x-2 bg-white/80 hover:bg-white border border-frost-300 rounded-lg px-4 py-2 text-sm font-medium text-frost-700 focus:outline-none focus:ring-2 focus:ring-ice-500 focus:border-transparent transition-all duration-200 shadow-sm hover:shadow-md">
                            <i class="fas fa-map-marker-alt text-ice-500"></i>
                            <span id="selectedBranchName"><?php echo e($branches->firstWhere('id', $restaurant['branch_id'])->name ?? 'Select Branch'); ?></span>
                            <i class="fas fa-chevron-down text-frost-400 text-xs transition-transform duration-200" id="branchDropdownIcon"></i>
                        </button>
                        
                        <!-- Dropdown Menu -->
                        <div id="branchDropdownMenu" class="hidden absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-lg border border-frost-200 z-50 overflow-hidden">
                            <div class="py-2">
                                <?php $__currentLoopData = $branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <button onclick="selectBranch(<?php echo e($branch->id); ?>, '<?php echo e($branch->name); ?>', '<?php echo e($branch->address ?? ''); ?>')" class="w-full text-left px-4 py-3 hover:bg-frost-50 transition-colors duration-150 <?php echo e($branch->id == $restaurant['branch_id'] ? 'bg-ice-50 border-l-4 border-ice-500' : ''); ?>">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <div class="font-medium text-frost-900"><?php echo e($branch->name); ?></div>
                                            <?php if($branch->address): ?>
                                            <div class="text-xs text-frost-500 mt-1"><?php echo e($branch->address); ?></div>
                                            <?php endif; ?>
                                        </div>
                                        <?php if($branch->id == $restaurant['branch_id']): ?>
                                        <i class="fas fa-check text-ice-500 text-sm"></i>
                                        <?php endif; ?>
                                    </div>
                                </button>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>

                    <!-- Cart Icon -->
                    <button onclick="toggleCart()" class="relative bg-ice-500 hover:bg-ice-600 text-white p-3 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105">
                        <i class="fas fa-shopping-cart text-lg"></i>
                        <span id="cartCount" class="absolute -top-2 -right-2 bg-winter-800 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center animate-pulse-soft">0</span>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="pt-20 pb-24">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Restaurant Info Card -->
            <div class="bg-white rounded-2xl shadow-xl p-6 mb-8 border border-frost-200">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div class="mb-4 lg:mb-0">
                        <h2 class="text-2xl font-bold text-frost-900 mb-2"><?php echo e($restaurant['name']); ?></h2>
                        <div class="space-y-1 text-frost-600">
                            <p class="flex items-center"><i class="fas fa-map-marker-alt w-4 mr-2"></i><?php echo e($restaurant['business_address']); ?></p>
                            <p class="flex items-center"><i class="fas fa-phone w-4 mr-2"></i><?php echo e($restaurant['contact_phone']); ?></p>
                            <p class="flex items-center"><i class="fas fa-clock w-4 mr-2"></i><?php echo e($restaurant['business_hours'] ?? 'Contact for hours'); ?></p>
                        </div>
                    </div>
                    
                    <?php if(isset($restaurant['current_table'])): ?>
                    <div class="bg-ice-50 border border-ice-200 rounded-xl p-4 lg:max-w-sm">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-ice-500 rounded-xl flex items-center justify-center">
                                <i class="fas fa-chair text-white text-lg"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-frost-900">Table <?php echo e($restaurant['current_table']['table_number']); ?></h3>
                                <?php if($restaurant['current_table']['area_name']): ?>
                                <p class="text-sm text-frost-600"><?php echo e($restaurant['current_table']['area_name']); ?> Area</p>
                                <?php endif; ?>
                                <p class="text-xs text-ice-600 font-medium">QR Code Access</p>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Events Carousel -->
            <div class="mb-12">
                <h2 class="text-2xl font-bold text-frost-900 mb-6 flex items-center">
                    <i class="fas fa-star text-ice-500 mr-3"></i>
                    Special Events
                </h2>
                
                <div class="relative overflow-hidden rounded-2xl carousel-shadow">
                    <div class="flex transition-transform duration-500 ease-in-out" id="carouselInner">
                        <div class="min-w-full relative">
                            <img src="https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=800" alt="Wine Night" class="w-full h-64 lg:h-80 object-cover">
                            <div class="absolute inset-0 bg-black/40"></div>
                            <div class="absolute bottom-0 left-0 right-0 p-8 text-white">
                                <h3 class="text-2xl font-bold mb-2">Weekly Wine Night</h3>
                                <p class="text-lg opacity-90">Every Friday - 50% off on selected wines</p>
                            </div>
                        </div>
                        <div class="min-w-full relative">
                            <img src="https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=800" alt="Seafood Special" class="w-full h-64 lg:h-80 object-cover">
                            <div class="absolute inset-0 bg-black/40"></div>
                            <div class="absolute bottom-0 left-0 right-0 p-8 text-white">
                                <h3 class="text-2xl font-bold mb-2">Seafood Weekend</h3>
                                <p class="text-lg opacity-90">Fresh catch every Saturday & Sunday</p>
                            </div>
                        </div>
                        <div class="min-w-full relative">
                            <img src="https://images.unsplash.com/photo-1551782450-a2132b4ba21d?w=800" alt="Live Music" class="w-full h-64 lg:h-80 object-cover">
                            <div class="absolute inset-0 bg-black/40"></div>
                            <div class="absolute bottom-0 left-0 right-0 p-8 text-white">
                                <h3 class="text-2xl font-bold mb-2">Live Jazz Nights</h3>
                                <p class="text-lg opacity-90">Every Thursday from 7 PM</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Carousel Controls -->
                    <button onclick="prevSlide()" class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white text-frost-700 w-12 h-12 rounded-full flex items-center justify-center transition-all duration-200 shadow-lg hover:shadow-xl">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button onclick="nextSlide()" class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white text-frost-700 w-12 h-12 rounded-full flex items-center justify-center transition-all duration-200 shadow-lg hover:shadow-xl">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                    
                    <!-- Carousel Indicators -->
                    <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                        <div class="w-3 h-3 bg-white rounded-full opacity-80" id="indicator-0"></div>
                        <div class="w-3 h-3 bg-white/50 rounded-full" id="indicator-1"></div>
                        <div class="w-3 h-3 bg-white/50 rounded-full" id="indicator-2"></div>
                    </div>
                </div>
            </div>

            <!-- Category Slider -->
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-frost-900 mb-6 flex items-center">
                    <i class="fas fa-list text-ice-500 mr-3"></i>
                    Categories
                </h2>
                
                <div class="flex overflow-x-auto space-x-4 pb-2 scrollbar-hide" id="categorySlider">
                    <button class="category-item bg-ice-500 text-white px-6 py-3 rounded-xl font-medium whitespace-nowrap transition-all duration-200 shadow-lg active" data-category="all">
                        All Items
                    </button>
                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <button class="category-item bg-white hover:bg-frost-50 text-frost-700 border border-frost-200 px-6 py-3 rounded-xl font-medium whitespace-nowrap transition-all duration-200 shadow-lg hover:shadow-xl" data-category="<?php echo e($category->id); ?>">
                        <?php echo e($category->name); ?>

                    </button>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>

            <!-- Menu Items Grid -->
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-frost-900 mb-6 flex items-center">
                    <i class="fas fa-utensils text-ice-500 mr-3"></i>
                    Our Menu
                </h2>
                
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4" id="menuGrid">
                    <?php $__currentLoopData = $menuItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="menu-item bg-white rounded-xl overflow-hidden shadow-lg card-hover border border-frost-200" data-category="<?php echo e($item->category->id); ?>">
                        <div class="aspect-square relative overflow-hidden">
                            <img src="https://images.pexels.com/photos/376464/pexels-photo-376464.jpeg" alt="<?php echo e($item->name); ?>" class="w-full h-full object-cover" onerror="this.src='https://images.pexels.com/photos/376464/pexels-photo-376464.jpeg'">
                            <div class="absolute top-2 left-2">
                                <span class="bg-ice-500 text-white text-xs px-2 py-1 rounded-lg font-medium"><?php echo e($item->category->name); ?></span>
                            </div>
                        </div>
                        
                        <div class="p-4">
                            <div class="mb-2">
                                <h3 class="font-semibold text-frost-900 text-sm mb-1 line-clamp-2"><?php echo e($item->name); ?></h3>
                                <p class="text-frost-600 text-xs line-clamp-2 mb-2"><?php echo e($item->description ?? 'Delicious menu item'); ?></p>
                            </div>
                            
                            <div class="flex items-center justify-between mb-3">
                                <span class="text-ice-600 font-bold text-lg">$<?php echo e(number_format($item->getBranchPrice($restaurant['branch_id']), 2)); ?></span>
                            </div>
                            
                            <button onclick="addToCart(<?php echo e($item->id); ?>, '<?php echo e($item->name); ?>', <?php echo e($item->getBranchPrice($restaurant['branch_id'])); ?>, '<?php echo e($item->category->name); ?>')" class="w-full bg-ice-500 hover:bg-ice-600 text-white py-2 px-4 rounded-lg font-medium transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl">
                                Add to Cart
                            </button>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    </main>

    <!-- Floating Waiter Button -->
    <button onclick="callWaiter()" class="fixed bottom-6 right-6 bg-winter-800 hover:bg-winter-700 text-white p-4 rounded-full shadow-2xl hover:shadow-3xl transition-all duration-200 transform hover:scale-110 animate-float z-40">
        <i class="fas fa-bell text-xl"></i>
    </button>

    <!-- Cart Modal -->
    <div id="cartModal" class="hidden fixed inset-0 bg-black/50 backdrop-blur-sm z-50 animate-fade-in">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-hidden animate-slide-up">
                <!-- Modal Header -->
                <div class="flex items-center justify-between p-6 border-b border-frost-200">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-ice-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-shopping-cart text-white text-sm"></i>
                        </div>
                        <h2 class="text-xl font-bold text-frost-900">Your Cart</h2>
                    </div>
                    <button onclick="toggleCart()" class="text-frost-400 hover:text-frost-600 transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                
                <!-- Cart Items -->
                <div class="p-6 max-h-96 overflow-y-auto" id="cartItems">
                    <!-- Cart items will be rendered here -->
                </div>
                
                <!-- Cart Footer -->
                <div class="border-t border-frost-200 p-6">
                    <div class="text-right mb-4">
                        <span class="text-2xl font-bold text-frost-900" id="cartTotal">Total: $0.00</span>
                    </div>
                    <button onclick="submitOrder()" class="w-full bg-ice-500 hover:bg-ice-600 text-white py-3 px-6 rounded-xl font-semibold transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl">
                        Submit Order
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Waiter Request Modal -->
    <div id="waiterModal" class="hidden fixed inset-0 bg-black/50 backdrop-blur-sm z-50 animate-fade-in">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-2xl shadow-2xl max-w-lg w-full max-h-[90vh] overflow-hidden animate-slide-up">
                <!-- Modal Header -->
                <div class="flex items-center justify-between p-6 border-b border-frost-200">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-winter-800 rounded-lg flex items-center justify-center">
                            <i class="fas fa-bell text-white text-sm"></i>
                        </div>
                        <h2 class="text-xl font-bold text-frost-900">Request Waiter</h2>
                    </div>
                    <button onclick="toggleWaiterModal()" class="text-frost-400 hover:text-frost-600 transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                
                <!-- Modal Content -->
                <div class="p-6">
                    <p class="text-frost-600 mb-6">Please select your table to request waiter assistance:</p>
                    
                    <div class="grid grid-cols-3 gap-3 mb-6" id="tablesGrid">
                        <?php $__currentLoopData = $tables; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $table): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <button class="table-option p-4 bg-frost-50 hover:bg-frost-100 border-2 border-frost-200 rounded-xl text-center transition-all duration-200 hover:scale-105" data-table-id="<?php echo e($table->id); ?>" onclick="selectTable(<?php echo e($table->id); ?>)">
                            <div class="text-lg font-bold text-frost-900"><?php echo e($table->table_number); ?></div>
                            <div class="text-xs text-frost-500"><?php echo e($table->area->name ?? 'No Area'); ?></div>
                        </button>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    
                    <button id="requestWaiterBtn" onclick="submitWaiterRequest()" disabled class="w-full bg-winter-800 hover:bg-winter-700 disabled:bg-frost-300 disabled:cursor-not-allowed text-white py-3 px-6 rounded-xl font-semibold transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl">
                        Request Waiter
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notificationContainer" class="fixed top-20 right-4 z-50 space-y-2"></div>

    <script>
        // Restaurant data from backend
        const restaurantData = {
            name: '<?php echo e($restaurant["name"]); ?>',
            tenantName: '<?php echo e($tenantName); ?>',
            branchId: <?php echo e($restaurant['branch_id']); ?>,
            tables: <?php echo json_encode($tables, 15, 512) ?>,
            areas: <?php echo json_encode($areas, 15, 512) ?>,
            branches: <?php echo json_encode($branches, 15, 512) ?>
        };

        // Global variables
        let cart = [];
        let currentCategory = 'all';
        let currentSlide = 0;
        let selectedTableId = null;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            startCarousel();
            setupCategoryFiltering();
            updateCartCount();
        });

        // Branch dropdown functionality
        function toggleBranchDropdown() {
            const menu = document.getElementById('branchDropdownMenu');
            const icon = document.getElementById('branchDropdownIcon');
            
            if (menu.classList.contains('hidden')) {
                menu.classList.remove('hidden');
                icon.style.transform = 'rotate(180deg)';
            } else {
                menu.classList.add('hidden');
                icon.style.transform = 'rotate(0deg)';
            }
        }

        function selectBranch(branchId, branchName, branchAddress) {
            // Update the selected branch display
            document.getElementById('selectedBranchName').textContent = branchName;
            
            // Close the dropdown
            document.getElementById('branchDropdownMenu').classList.add('hidden');
            document.getElementById('branchDropdownIcon').style.transform = 'rotate(0deg)';
            
            // Redirect to the new branch
            changeBranch(branchId);
        }

        // Branch change functionality
        function changeBranch(branchId) {
            const pathParts = window.location.pathname.split('/');
            const tenantName = pathParts[pathParts.indexOf('restaurant') + 1] || restaurantData.tenantName;
            const newUrl = `/restaurant/${tenantName}?branch_id=${branchId}`;
            window.location.href = newUrl;
        }

        // Category filtering
        function setupCategoryFiltering() {
            document.querySelectorAll('.category-item').forEach(item => {
                item.addEventListener('click', function() {
                    // Remove active class from all items
                    document.querySelectorAll('.category-item').forEach(cat => {
                        cat.classList.remove('bg-ice-500', 'text-white', 'active');
                        cat.classList.add('bg-white', 'hover:bg-frost-50', 'text-frost-700', 'border', 'border-frost-200');
                    });
                    
                    // Add active class to clicked item
                    this.classList.remove('bg-white', 'hover:bg-frost-50', 'text-frost-700', 'border', 'border-frost-200');
                    this.classList.add('bg-ice-500', 'text-white', 'active');
                    
                    currentCategory = this.dataset.category;
                    filterMenuItems();
                });
            });
        }

        function filterMenuItems() {
            const menuItems = document.querySelectorAll('.menu-item');
            menuItems.forEach(item => {
                if (currentCategory === 'all' || item.dataset.category === currentCategory) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        // Cart functions
        function addToCart(itemId, itemName, itemPrice, categoryName) {
            try {
                // Validate input parameters
                if (!itemId || !itemName || itemPrice === undefined || itemPrice === null) {
                    throw new Error('Invalid item data provided');
                }

                // Validate price is a number
                const price = parseFloat(itemPrice);
                if (isNaN(price) || price < 0) {
                    throw new Error('Invalid item price');
                }

                const existingItem = cart.find(c => c.id === itemId);

                if (existingItem) {
                    existingItem.quantity++;
                    showNotification(`${itemName} quantity updated! (${existingItem.quantity})`, 'success');
                } else {
                    cart.push({
                        id: itemId,
                        name: itemName,
                        price: price,
                        category: categoryName || 'Unknown',
                        quantity: 1,
                        addedAt: new Date().toISOString()
                    });
                    showNotification(`${itemName} added to cart!`, 'success');
                }

                updateCartCount();

            } catch (error) {
                console.error('Error adding item to cart:', error);
                showNotification('Failed to add item to cart. Please try again.', 'error');
            }
        }

        function updateCartCount() {
            const count = cart.reduce((sum, item) => sum + item.quantity, 0);
            document.getElementById('cartCount').textContent = count;
        }

        function toggleCart() {
            const modal = document.getElementById('cartModal');
            if (modal.classList.contains('hidden')) {
                renderCart();
                modal.classList.remove('hidden');
            } else {
                modal.classList.add('hidden');
            }
        }

        function renderCart() {
            const cartItems = document.getElementById('cartItems');
            const cartTotal = document.getElementById('cartTotal');
            
            if (cart.length === 0) {
                cartItems.innerHTML = `
                    <div class="text-center py-8">
                        <div class="w-16 h-16 bg-frost-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-shopping-cart text-frost-400 text-2xl"></i>
                        </div>
                        <p class="text-frost-500">Your cart is empty</p>
                    </div>
                `;
                cartTotal.textContent = 'Total: $0.00';
                return;
            }
            
            cartItems.innerHTML = cart.map(item => `
                <div class="flex items-center justify-between py-4 border-b border-frost-100 last:border-b-0">
                    <div class="flex-1">
                        <h4 class="font-semibold text-frost-900">${item.name}</h4>
                        <p class="text-sm text-frost-500">${item.category}</p>
                        <p class="text-ice-600 font-bold">$${item.price.toFixed(2)}</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button onclick="updateQuantity(${item.id}, -1)" class="w-8 h-8 bg-frost-100 hover:bg-frost-200 rounded-full flex items-center justify-center transition-colors">
                            <i class="fas fa-minus text-xs text-frost-600"></i>
                        </button>
                        <span class="font-semibold text-frost-900 min-w-[2rem] text-center">${item.quantity}</span>
                        <button onclick="updateQuantity(${item.id}, 1)" class="w-8 h-8 bg-frost-100 hover:bg-frost-200 rounded-full flex items-center justify-center transition-colors">
                            <i class="fas fa-plus text-xs text-frost-600"></i>
                        </button>
                    </div>
                </div>
            `).join('');
            
            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            cartTotal.textContent = `Total: $${total.toFixed(2)}`;
        }

        function updateQuantity(itemId, change) {
            try {
                // Validate inputs
                if (!itemId || typeof change !== 'number') {
                    throw new Error('Invalid parameters for quantity update');
                }

                const item = cart.find(c => c.id === itemId);
                if (!item) {
                    throw new Error('Item not found in cart');
                }

                const newQuantity = item.quantity + change;

                // Validate quantity limits
                if (newQuantity < 0) {
                    throw new Error('Quantity cannot be negative');
                }

                if (newQuantity > 99) {
                    showNotification('Maximum quantity is 99 per item', 'error');
                    return;
                }

                item.quantity = newQuantity;

                if (item.quantity <= 0) {
                    cart = cart.filter(c => c.id !== itemId);
                    showNotification(`${item.name} removed from cart`, 'success');
                }

                updateCartCount();
                renderCart();

            } catch (error) {
                console.error('Error updating quantity:', error);
                showNotification('Failed to update quantity. Please try again.', 'error');
            }
        }

        async function submitOrder() {
            if (cart.length === 0) {
                showNotification('Your cart is empty!', 'error');
                return;
            }

            if (!selectedTableId) {
                toggleWaiterModal();
                window.pendingAction = 'submitOrder';
                return;
            }

            const submitBtn = document.querySelector('.checkout-btn, button[onclick="submitOrder()"]');
            const originalText = submitBtn.textContent;
            submitBtn.disabled = true;
            submitBtn.textContent = 'Submitting...';

            try {
                const pathParts = window.location.pathname.split('/');
                const tenantName = pathParts[pathParts.indexOf('restaurant') + 1] || restaurantData.tenantName;
                
                const orderData = {
                    table_id: selectedTableId,
                    branch_id: restaurantData.branchId,
                    order_type: 'dine_in',
                    status: 'pending',
                    items: cart.map(item => ({
                        menu_item_id: item.id,
                        quantity: item.quantity,
                        unit_price: item.price,
                        special_instructions: null
                    })),
                    customer_name: null,
                    customer_phone: null,
                    special_instructions: 'Order submitted from public menu',
                    _token: document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                };
                
                console.log('Order submission data:', orderData);
                console.log('Available tables:', restaurantData.tables);
                console.log('Selected table details:', restaurantData.tables.find(t => t.id === selectedTableId));
                
                const response = await fetch(`/restaurant/${tenantName}/submit-order`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                    },
                    body: JSON.stringify(orderData)
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    const selectedTable = restaurantData.tables.find(t => t.id === selectedTableId);
                    const tableNumber = selectedTable ? selectedTable.table_number : selectedTableId;
                    
                    showNotification(`Order submitted successfully for table ${tableNumber}! Order ID: ${result.order_number}`, 'success');
                    
                    cart = [];
                    updateCartCount();
                    toggleCart();
                    selectedTableId = null;
                    updateTableSelection();
                } else {
                    throw new Error(result.message || result.error || 'Failed to submit order');
                }
            } catch (error) {
                console.error('Error submitting order:', error);
                showNotification('Failed to submit order. Please try again. Error: ' + error.message, 'error');
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = originalText;
            }
        }

        function callWaiter() {
            toggleWaiterModal();
        }

        function toggleWaiterModal() {
            const modal = document.getElementById('waiterModal');
            if (modal.classList.contains('hidden')) {
                modal.classList.remove('hidden');
            } else {
                modal.classList.add('hidden');
                if (!window.pendingAction) {
                    selectedTableId = null;
                    updateTableSelection();
                }
            }
        }

        function selectTable(tableId) {
            selectedTableId = tableId;
            updateTableSelection();
        }

        function updateTableSelection() {
            document.querySelectorAll('.table-option').forEach(table => {
                table.classList.remove('bg-ice-500', 'text-white', 'border-ice-500');
                table.classList.add('bg-frost-50', 'hover:bg-frost-100', 'border-frost-200', 'text-frost-900');
            });

            if (selectedTableId) {
                const selectedTable = document.querySelector(`[data-table-id="${selectedTableId}"]`);
                if (selectedTable) {
                    selectedTable.classList.remove('bg-frost-50', 'hover:bg-frost-100', 'border-frost-200', 'text-frost-900');
                    selectedTable.classList.add('bg-ice-500', 'text-white', 'border-ice-500');
                }
            }

            const requestBtn = document.getElementById('requestWaiterBtn');
            requestBtn.disabled = !selectedTableId;
        }

        async function submitWaiterRequest() {
            if (!selectedTableId) {
                showNotification('Please select a table first.', 'error');
                return;
            }

            if (window.pendingAction === 'submitOrder') {
                const tableId = selectedTableId;
                toggleWaiterModal();
                window.pendingAction = null;
                selectedTableId = tableId;
                submitOrder();
                return;
            }

            const requestBtn = document.getElementById('requestWaiterBtn');
            const originalText = requestBtn.textContent;
            requestBtn.disabled = true;
            requestBtn.textContent = 'Requesting...';

            try {
                const pathParts = window.location.pathname.split('/');
                const tenantName = pathParts[pathParts.indexOf('restaurant') + 1] || restaurantData.tenantName;
                
                const requestData = {
                    table_id: selectedTableId,
                    branch_id: restaurantData.branchId,
                    request_type: 'service',
                    notes: 'Customer requested waiter assistance'
                };
                
                console.log('Waiter request data:', requestData);
                console.log('Available tables:', restaurantData.tables);
                console.log('Selected table details:', restaurantData.tables.find(t => t.id === selectedTableId));
                
                const response = await fetch(`/restaurant/${tenantName}/request-waiter`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                    },
                    body: JSON.stringify(requestData)
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    const selectedTable = restaurantData.tables.find(t => t.id === selectedTableId);
                    const tableNumber = selectedTable ? selectedTable.table_number : selectedTableId;
                    
                    showNotification(`Waiter has been called to table ${tableNumber}!`, 'success');
                    toggleWaiterModal();
                } else {
                    throw new Error(result.message || result.error || 'Failed to request waiter');
                }
            } catch (error) {
                console.error('Error requesting waiter:', error);
                showNotification('Failed to request waiter. Please try again. Error: ' + error.message, 'error');
            } finally {
                requestBtn.disabled = false;
                requestBtn.textContent = originalText;
            }
        }

        function showNotification(message, type = 'info') {
            try {
                // Validate inputs
                if (!message || typeof message !== 'string') {
                    console.warn('Invalid notification message');
                    return;
                }

                const container = safeGetElement('notificationContainer');
                if (!container) {
                    console.error('Notification container not found');
                    return;
                }

                // Limit concurrent notifications
                const existingNotifications = container.children.length;
                if (existingNotifications >= 5) {
                    // Remove oldest notification
                    const oldest = container.firstElementChild;
                    if (oldest) {
                        oldest.remove();
                    }
                }

                const notification = document.createElement('div');

                // Enhanced type handling
                const typeConfig = {
                    success: { bg: 'bg-emerald-500', icon: 'fa-check-circle', duration: 3000 },
                    error: { bg: 'bg-red-500', icon: 'fa-exclamation-circle', duration: 6000 },
                    warning: { bg: 'bg-amber-500', icon: 'fa-exclamation-triangle', duration: 5000 },
                    info: { bg: 'bg-ice-500', icon: 'fa-info-circle', duration: 4000 }
                };

                const config = typeConfig[type] || typeConfig.info;

                notification.className = `${config.bg} text-white px-6 py-4 rounded-xl shadow-lg transform transition-all duration-300 translate-x-full opacity-0 max-w-sm`;
                notification.innerHTML = `
                    <div class="flex items-center justify-between space-x-3">
                        <div class="flex items-center space-x-3">
                            <i class="fas ${config.icon}"></i>
                            <span class="font-medium">${message}</span>
                        </div>
                        <button onclick="this.parentElement.parentElement.remove()" class="text-white/80 hover:text-white ml-2">
                            <i class="fas fa-times text-sm"></i>
                        </button>
                    </div>
                `;

                container.appendChild(notification);

                // Animate in
                setTimeout(() => {
                    notification.classList.remove('translate-x-full', 'opacity-0');
                    notification.classList.add('translate-x-0', 'opacity-100');
                }, 100);

                // Auto-remove after specified duration
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.classList.add('translate-x-full', 'opacity-0');
                        setTimeout(() => {
                            if (notification.parentNode) {
                                notification.remove();
                            }
                        }, 300);
                    }
                }, config.duration);

            } catch (error) {
                console.error('Error showing notification:', error);
                // Fallback to browser alert for critical errors
                if (type === 'error') {
                    alert(message);
                }
            }
        }

        // Carousel functionality
        function startCarousel() {
            setInterval(() => {
                nextSlide();
            }, 5000);
        }

        function nextSlide() {
            const carousel = document.getElementById('carouselInner');
            const items = carousel.children;
            const totalSlides = items.length;
            
            currentSlide = (currentSlide + 1) % totalSlides;
            carousel.style.transform = `translateX(-${currentSlide * 100}%)`;
            updateCarouselIndicators();
        }

        function prevSlide() {
            const carousel = document.getElementById('carouselInner');
            const items = carousel.children;
            const totalSlides = items.length;
            
            currentSlide = currentSlide === 0 ? totalSlides - 1 : currentSlide - 1;
            carousel.style.transform = `translateX(-${currentSlide * 100}%)`;
            updateCarouselIndicators();
        }

        function updateCarouselIndicators() {
            const indicators = document.querySelectorAll('[id^="indicator-"]');
            indicators.forEach((indicator, index) => {
                if (index === currentSlide) {
                    indicator.classList.remove('bg-white/50');
                    indicator.classList.add('bg-white');
                } else {
                    indicator.classList.remove('bg-white');
                    indicator.classList.add('bg-white/50');
                }
            });
        }

        // Close modals when clicking outside
        document.addEventListener('click', function(event) {
            const cartModal = document.getElementById('cartModal');
            const waiterModal = document.getElementById('waiterModal');
            const branchDropdown = document.getElementById('branchDropdownMenu');
            const branchDropdownBtn = document.getElementById('branchDropdownBtn');
            
            // Close cart modal when clicking outside
            if (event.target === cartModal) {
                toggleCart();
            }
            
            // Close waiter modal when clicking outside
            if (event.target === waiterModal) {
                toggleWaiterModal();
            }
            
            // Close branch dropdown when clicking outside
            if (!branchDropdownBtn.contains(event.target) && !branchDropdown.contains(event.target)) {
                branchDropdown.classList.add('hidden');
                document.getElementById('branchDropdownIcon').style.transform = 'rotate(0deg)';
            }
        });

        // Add touch/swipe support for carousel on mobile
        let touchStartX = 0;
        let touchEndX = 0;

        const carouselElement = document.getElementById('carouselInner').parentElement;
        
        carouselElement.addEventListener('touchstart', function(event) {
            touchStartX = event.changedTouches[0].screenX;
        });

        carouselElement.addEventListener('touchend', function(event) {
            touchEndX = event.changedTouches[0].screenX;
            handleSwipe();
        });

        function handleSwipe() {
            const swipeThreshold = 50;
            const diff = touchStartX - touchEndX;
            
            if (Math.abs(diff) > swipeThreshold) {
                if (diff > 0) {
                    nextSlide();
                } else {
                    prevSlide();
                }
            }
        }

        // Global error handling
        window.addEventListener('error', function(event) {
            console.error('Global error caught:', event.error);
            showNotification('An unexpected error occurred. Please refresh the page.', 'error');
        });

        window.addEventListener('unhandledrejection', function(event) {
            console.error('Unhandled promise rejection:', event.reason);
            showNotification('A network error occurred. Please check your connection.', 'error');
        });

        // Network connectivity check
        function checkNetworkStatus() {
            if (!navigator.onLine) {
                showNotification('You are currently offline. Some features may not work.', 'warning');
            }
        }

        window.addEventListener('online', function() {
            showNotification('Connection restored!', 'success');
        });

        window.addEventListener('offline', function() {
            showNotification('Connection lost. Working in offline mode.', 'warning');
        });

        // Initialize network status check
        checkNetworkStatus();

        // Graceful degradation for older browsers
        if (!window.fetch) {
            showNotification('Your browser may not support all features. Please consider updating.', 'warning');
        }

        // Performance monitoring
        if ('performance' in window) {
            window.addEventListener('load', function() {
                setTimeout(function() {
                    const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
                    if (loadTime > 5000) {
                        console.warn('Page load time is slow:', loadTime + 'ms');
                    }
                }, 0);
            });
        }

        // Input validation helpers
        function validateOrderData(orderData) {
            if (!orderData.table_id) {
                throw new Error('Table selection is required');
            }
            if (!orderData.items || orderData.items.length === 0) {
                throw new Error('Cart cannot be empty');
            }
            if (!orderData.branch_id) {
                throw new Error('Branch information is missing');
            }
            return true;
        }

        // Safe DOM manipulation
        function safeGetElement(id) {
            const element = document.getElementById(id);
            if (!element) {
                console.warn(`Element with ID '${id}' not found`);
            }
            return element;
        }

        // Safe event handling
        function safeAddEventListener(element, event, handler) {
            if (element && typeof handler === 'function') {
                element.addEventListener(event, handler);
            } else {
                console.warn('Invalid element or handler for event listener');
            }
        }
    </script>
</body>
</html><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\resources\views/public/restaurant.blade.php ENDPATH**/ ?>