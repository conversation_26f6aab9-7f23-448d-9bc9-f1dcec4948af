<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Branch;
use App\Models\User;
use App\Models\MenuCategory;
use App\Models\MenuItem;
use Modules\Kitchen\Models\Kitchen;
use Modules\Kitchen\Models\KitchenMenuItem;

class KitchenModuleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Seeding Kitchen Module data...');

        // Get existing branches and users
        $branches = Branch::all();
        $users = User::where('is_active', true)->get();
        $menuCategories = MenuCategory::where('is_active', true)->get();
        $menuItems = MenuItem::where('is_active', true)->get();

        if ($branches->isEmpty()) {
            $this->command->warn('No branches found. Please run BranchSeeder first.');
            return;
        }

        // Create kitchens for each branch
        foreach ($branches as $branch) {
            $this->createKitchensForBranch($branch, $users, $menuCategories, $menuItems);
        }

        $this->command->info('Kitchen Module seeding completed successfully!');
    }

    /**
     * Create kitchens for a specific branch
     */
    private function createKitchensForBranch($branch, $users, $menuCategories, $menuItems)
    {
        // Check if kitchens already exist for this branch
        $existingKitchens = Kitchen::where('branch_id', $branch->id)->count();
        
        if ($existingKitchens > 0) {
            $this->command->info("Kitchens already exist for branch: {$branch->name}");
            return;
        }

        // Get potential managers from users in this branch
        $branchUsers = $users->where('branch_id', $branch->id);
        $managers = $branchUsers->whereIn('role', ['manager', 'kitchen_manager', 'chef'])->take(5);

        // Define kitchen configurations
        $kitchenConfigs = [
            [
                'name' => 'Main Kitchen',
                'code' => 'MAIN-' . strtoupper(substr($branch->code, 0, 3)),
                'station_type' => 'main',
                'description' => 'Primary kitchen for all main course preparations',
                'max_concurrent_orders' => 15,
                'average_prep_time_minutes' => 20,
                'equipment_list' => ['stove', 'oven', 'grill', 'fryer', 'prep_station'],
                'operating_hours' => [
                    'monday' => ['start' => '06:00', 'end' => '23:00'],
                    'tuesday' => ['start' => '06:00', 'end' => '23:00'],
                    'wednesday' => ['start' => '06:00', 'end' => '23:00'],
                    'thursday' => ['start' => '06:00', 'end' => '23:00'],
                    'friday' => ['start' => '06:00', 'end' => '24:00'],
                    'saturday' => ['start' => '06:00', 'end' => '24:00'],
                    'sunday' => ['start' => '08:00', 'end' => '22:00'],
                ],
                'display_order' => 1,
            ],
            [
                'name' => 'Grill Station',
                'code' => 'GRILL-' . strtoupper(substr($branch->code, 0, 3)),
                'station_type' => 'grill',
                'description' => 'Specialized station for grilled items and BBQ',
                'max_concurrent_orders' => 8,
                'average_prep_time_minutes' => 15,
                'equipment_list' => ['charcoal_grill', 'gas_grill', 'salamander', 'tongs'],
                'operating_hours' => [
                    'monday' => ['start' => '11:00', 'end' => '22:00'],
                    'tuesday' => ['start' => '11:00', 'end' => '22:00'],
                    'wednesday' => ['start' => '11:00', 'end' => '22:00'],
                    'thursday' => ['start' => '11:00', 'end' => '22:00'],
                    'friday' => ['start' => '11:00', 'end' => '23:00'],
                    'saturday' => ['start' => '11:00', 'end' => '23:00'],
                    'sunday' => ['start' => '12:00', 'end' => '21:00'],
                ],
                'display_order' => 2,
            ],
            [
                'name' => 'Cold Station',
                'code' => 'COLD-' . strtoupper(substr($branch->code, 0, 3)),
                'station_type' => 'cold',
                'description' => 'Station for salads, cold appetizers, and desserts',
                'max_concurrent_orders' => 12,
                'average_prep_time_minutes' => 8,
                'equipment_list' => ['refrigerator', 'prep_tables', 'mandoline', 'mixing_bowls'],
                'operating_hours' => [
                    'monday' => ['start' => '09:00', 'end' => '22:00'],
                    'tuesday' => ['start' => '09:00', 'end' => '22:00'],
                    'wednesday' => ['start' => '09:00', 'end' => '22:00'],
                    'thursday' => ['start' => '09:00', 'end' => '22:00'],
                    'friday' => ['start' => '09:00', 'end' => '23:00'],
                    'saturday' => ['start' => '09:00', 'end' => '23:00'],
                    'sunday' => ['start' => '10:00', 'end' => '21:00'],
                ],
                'display_order' => 3,
            ],
            [
                'name' => 'Beverage Station',
                'code' => 'BEV-' . strtoupper(substr($branch->code, 0, 3)),
                'station_type' => 'beverage',
                'description' => 'Station for all hot and cold beverages',
                'max_concurrent_orders' => 20,
                'average_prep_time_minutes' => 5,
                'equipment_list' => ['espresso_machine', 'blender', 'juicer', 'ice_machine'],
                'operating_hours' => [
                    'monday' => ['start' => '07:00', 'end' => '22:00'],
                    'tuesday' => ['start' => '07:00', 'end' => '22:00'],
                    'wednesday' => ['start' => '07:00', 'end' => '22:00'],
                    'thursday' => ['start' => '07:00', 'end' => '22:00'],
                    'friday' => ['start' => '07:00', 'end' => '23:00'],
                    'saturday' => ['start' => '07:00', 'end' => '23:00'],
                    'sunday' => ['start' => '08:00', 'end' => '21:00'],
                ],
                'display_order' => 4,
            ],
        ];

        // Create kitchens
        foreach ($kitchenConfigs as $index => $config) {
            // Assign a manager if available
            $manager = $managers->count() > 0 ? $managers->get($index % $managers->count()) : null;

            $kitchen = Kitchen::create([
                'tenant_id' => 1, // Default tenant ID
                'branch_id' => $branch->id,
                'name' => $config['name'],
                'code' => $config['code'],
                'station_type' => $config['station_type'],
                'description' => $config['description'],
                'max_concurrent_orders' => $config['max_concurrent_orders'],
                'average_prep_time_minutes' => $config['average_prep_time_minutes'],
                'equipment_list' => json_encode($config['equipment_list']),
                'operating_hours' => json_encode($config['operating_hours']),
                'manager_id' => $manager ? $manager->id : null,
                'display_order' => $config['display_order'],
                'is_active' => true,
            ]);

            $this->command->info("Created kitchen: {$kitchen->name} for branch: {$branch->name}");

            // Assign menu items to kitchens based on station type
            $this->assignMenuItemsToKitchen($kitchen, $menuItems, $menuCategories);
        }
    }

    /**
     * Assign menu items to kitchen based on station type
     */
    private function assignMenuItemsToKitchen($kitchen, $menuItems, $menuCategories)
    {
        // Define which menu categories should be assigned to which station types
        $stationCategoryMapping = [
            'main' => ['main course', 'entrees', 'pasta', 'rice', 'noodles', 'curry'],
            'grill' => ['grilled', 'bbq', 'steaks', 'kebabs', 'tandoor'],
            'cold' => ['salads', 'appetizers', 'desserts', 'ice cream', 'cold drinks'],
            'beverage' => ['beverages', 'drinks', 'coffee', 'tea', 'juices', 'smoothies'],
            'fryer' => ['fried', 'snacks', 'finger food'],
            'dessert' => ['desserts', 'sweets', 'cakes', 'pastries'],
            'prep' => ['prep items', 'sauces', 'bases'],
        ];

        $relevantCategories = $stationCategoryMapping[$kitchen->station_type] ?? [];
        
        // If main kitchen, assign items from multiple categories
        if ($kitchen->station_type === 'main') {
            $relevantCategories = array_merge($relevantCategories, ['soups', 'starters']);
        }

        // Find menu items that belong to relevant categories
        $assignedCount = 0;
        foreach ($menuItems as $menuItem) {
            $shouldAssign = false;

            // Check if menu item's category matches station type
            if ($menuItem->menuCategory) {
                $categoryName = strtolower($menuItem->menuCategory->name);
                
                foreach ($relevantCategories as $relevantCategory) {
                    if (str_contains($categoryName, $relevantCategory)) {
                        $shouldAssign = true;
                        break;
                    }
                }
            }

            // For main kitchen, assign some items from other categories too
            if ($kitchen->station_type === 'main' && !$shouldAssign && rand(1, 3) === 1) {
                $shouldAssign = true;
            }

            if ($shouldAssign) {
                // Check if already assigned
                $existingAssignment = KitchenMenuItem::where('kitchen_id', $kitchen->id)
                    ->where('menu_item_id', $menuItem->id)
                    ->first();

                if (!$existingAssignment) {
                    KitchenMenuItem::create([
                        'tenant_id' => 1, // Default tenant ID
                        'kitchen_id' => $kitchen->id,
                        'menu_item_id' => $menuItem->id,
                        'prep_time_minutes' => $this->calculatePrepTime($kitchen->station_type, $menuItem),
                        'priority_level' => $this->calculatePriorityLevel($kitchen->station_type, $menuItem),
                        'is_active' => true,
                    ]);

                    $assignedCount++;
                }
            }
        }

        $this->command->info("Assigned {$assignedCount} menu items to {$kitchen->name}");
    }

    /**
     * Calculate prep time based on station type and menu item
     */
    private function calculatePrepTime($stationType, $menuItem)
    {
        $baseTimes = [
            'main' => 20,
            'grill' => 15,
            'cold' => 8,
            'beverage' => 5,
            'fryer' => 10,
            'dessert' => 12,
            'prep' => 30,
        ];

        $baseTime = $baseTimes[$stationType] ?? 15;
        
        // Add some variation based on item complexity (simulated)
        $variation = rand(-5, 10);
        
        return max(3, $baseTime + $variation);
    }

    /**
     * Calculate priority level based on station type and menu item
     */
    private function calculatePriorityLevel($stationType, $menuItem)
    {
        // Beverages and cold items typically have higher priority (faster service)
        $priorityMapping = [
            'beverage' => rand(1, 2), // High priority
            'cold' => rand(1, 3),
            'fryer' => rand(2, 4),
            'grill' => rand(3, 5),
            'main' => rand(3, 5),
            'dessert' => rand(4, 5), // Lower priority
            'prep' => 5, // Lowest priority
        ];

        return $priorityMapping[$stationType] ?? 3;
    }
}
