<?php

namespace Modules\Tenant\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\SubscriptionPlan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Yajra\DataTables\Facades\DataTables;

class PackageController extends Controller
{
    /**
     * Display a listing of packages/subscription plans
     */
    public function index()
    {
        return view('tenant::packages.index');
    }

    /**
     * Get packages data for DataTable.
     */
    public function getPackagesData(Request $request)
    {
        $query = SubscriptionPlan::select(['id', 'name', 'description', 'price', 'billing_cycle', 'max_branches', 'max_users', 'is_active', 'created_at']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active' ? 1 : 0);
        }

        if ($request->filled('billing_cycle')) {
            $query->where('billing_cycle', $request->billing_cycle);
        }

        return DataTables::of($query)
            ->addIndexColumn()
            ->addColumn('price_formatted', function ($package) {
                return number_format($package->price, 2) . ' ر.س';
            })
            ->addColumn('billing_cycle_text', function ($package) {
                $cycles = [
                    'monthly' => 'شهري',
                    'yearly' => 'سنوي',
                    'quarterly' => 'ربع سنوي'
                ];
                return $cycles[$package->billing_cycle] ?? $package->billing_cycle;
            })
            ->addColumn('package_info', function ($package) {
                $html = '<div class="text-sm">';
                $html .= '<div class="font-medium">' . $package->name . '</div>';
                if ($package->description) {
                    $html .= '<div class="text-gray-600">' . Str::limit($package->description, 50) . '</div>';
                }
                $html .= '</div>';
                return $html;
            })
            ->addColumn('limitations', function ($package) {
                $limitations = [];
                if ($package->max_branches) {
                    $limitations[] = '<div class="text-sm text-gray-600"><i class="fas fa-store text-green-500 w-4"></i> ' . $package->max_branches . ' فرع</div>';
                }
                if ($package->max_users) {
                    $limitations[] = '<div class="text-sm text-gray-600"><i class="fas fa-users text-blue-500 w-4"></i> ' . $package->max_users . ' مستخدم</div>';
                }
                return implode('', $limitations);
            })
            ->addColumn('status_badge', function ($package) {
                return $package->is_active 
                    ? '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">نشط</span>'
                    : '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">غير نشط</span>';
            })
            ->addColumn('subscriptions_count', function ($package) {
                return $package->tenantSubscriptions()->count();
            })
            ->addColumn('action', function ($package) {
                $actions = '<div class="flex items-center gap-2">';
                $actions .= '<a href="' . route('packages.show', $package) . '" class="inline-flex items-center px-3 py-1.5 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200" title="عرض"><i class="fas fa-eye"></i></a>';
                $actions .= '<a href="' . route('packages.edit', $package) . '" class="inline-flex items-center px-3 py-1.5 bg-yellow-600 text-white text-sm font-medium rounded-lg hover:bg-yellow-700 transition-colors duration-200" title="تعديل"><i class="fas fa-edit"></i></a>';
                
                if ($package->is_active) {
                    $actions .= '<button type="button" class="inline-flex items-center px-3 py-1.5 bg-orange-600 text-white text-sm font-medium rounded-lg hover:bg-orange-700 transition-colors duration-200" onclick="toggleStatus(' . $package->id . ', \'deactivate\')" title="إلغاء تفعيل"><i class="fas fa-pause"></i></button>';
                } else {
                    $actions .= '<button type="button" class="inline-flex items-center px-3 py-1.5 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors duration-200" onclick="toggleStatus(' . $package->id . ', \'activate\')" title="تفعيل"><i class="fas fa-play"></i></button>';
                }
                
                $actions .= '<button type="button" class="inline-flex items-center px-3 py-1.5 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 transition-colors duration-200" onclick="deletePackage(' . $package->id . ')" title="حذف"><i class="fas fa-trash"></i></button>';
                $actions .= '</div>';
                
                return $actions;
            })
            ->rawColumns(['package_info', 'limitations', 'status_badge', 'action'])
            ->make(true);
    }

    /**
     * Show the form for creating a new package
     */
    public function create()
    {
        return view('tenant::packages.create');
    }

    /**
     * Store a newly created package
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'billing_cycle' => 'required|in:monthly,yearly,quarterly',
            'max_branches' => 'nullable|integer|min:1',
            'max_users' => 'nullable|integer|min:1',
            'storage_limit' => 'nullable|numeric|min:0.1',
            'status' => 'required|in:1,0'
        ]);

        try {
            DB::beginTransaction();
            
            // Prepare features array
            $features = [];
            if ($request->has_pos) $features[] = 'pos';
            if ($request->has_inventory) $features[] = 'inventory';
            if ($request->has_reports) $features[] = 'reports';
            if ($request->has_delivery) $features[] = 'delivery';
            if ($request->has_hr) $features[] = 'hr';
            if ($request->has_api) $features[] = 'api';
            
            // Prepare limitations array
            $limitations = [];
            if ($request->max_branches) $limitations['max_branches'] = (int)$request->max_branches;
            if ($request->max_users) $limitations['max_users'] = (int)$request->max_users;
            if ($request->storage_limit) $limitations['storage_limit'] = (float)$request->storage_limit;
            
            $package = SubscriptionPlan::create([
                'name' => $request->name,
                'description' => $request->description,
                'price' => $request->price,
                'billing_cycle' => $request->billing_cycle,
                'features' => $features,
                'limitations' => $limitations,
                'max_branches' => $request->max_branches,
                'max_users' => $request->max_users,
                'is_active' => (bool)$request->status,
                'sort_order' => $request->sort_order ?? 0,
            ]);
            
            DB::commit();
            
            return redirect()->route('packages.index')
                ->with('success', 'Package created successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Failed to create package: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified package
     */
    public function show(SubscriptionPlan $package)
    {
        try {
            if (request()->ajax()) {
                return response()->json($package);
            }
            
            return view('tenant::packages.show', compact('package'));
        } catch (\Exception $e) {
            if (request()->ajax()) {
                return response()->json(['error' => 'Package not found'], 404);
            }
            
            return back()->with('error', 'Failed to retrieve package: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for editing the specified package
     */
    public function edit(SubscriptionPlan $package)
    {
        return view('tenant::packages.edit', compact('package'));
    }

    /**
     * Update the specified package
     */
    public function update(Request $request, SubscriptionPlan $package)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'billing_cycle' => 'required|in:monthly,yearly,quarterly',
            'max_branches' => 'nullable|integer|min:1',
            'max_users' => 'nullable|integer|min:1',
            'storage_limit' => 'nullable|numeric|min:0.1',
            'status' => 'required|in:1,0'
        ]);

        try {
            DB::beginTransaction();
            
            // Prepare features array
            $features = [];
            if ($request->has_pos) $features[] = 'pos';
            if ($request->has_inventory) $features[] = 'inventory';
            if ($request->has_reports) $features[] = 'reports';
            if ($request->has_delivery) $features[] = 'delivery';
            if ($request->has_hr) $features[] = 'hr';
            if ($request->has_api) $features[] = 'api';
            
            // Prepare limitations array
            $limitations = [];
            if ($request->max_branches) $limitations['max_branches'] = (int)$request->max_branches;
            if ($request->max_users) $limitations['max_users'] = (int)$request->max_users;
            if ($request->storage_limit) $limitations['storage_limit'] = (float)$request->storage_limit;
            
            $package->update([
                'name' => $request->name,
                'description' => $request->description,
                'price' => $request->price,
                'billing_cycle' => $request->billing_cycle,
                'features' => $features,
                'limitations' => $limitations,
                'max_branches' => $request->max_branches,
                'max_users' => $request->max_users,
                'is_active' => (bool)$request->status,
                'sort_order' => $request->sort_order ?? $package->sort_order ?? 0,
            ]);
            
            DB::commit();
            
            return redirect()->route('packages.index')
                ->with('success', 'Package updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Failed to update package: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified package from storage
     */
    public function destroy(SubscriptionPlan $package)
    {
        try {
            DB::beginTransaction();
            
            // Check if package is being used by any active subscriptions
            $activeSubscriptions = $package->tenantSubscriptions()->where('status', 'active')->count();
            
            if ($activeSubscriptions > 0) {
                return back()->with('error', 'Cannot delete package that has active subscriptions');
            }
            
            $package->delete();
            
            DB::commit();
            
            return redirect()->route('packages.index')
                ->with('success', 'Package deleted successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Failed to delete package: ' . $e->getMessage());
        }
    }

    /**
     * Activate package.
     */
    public function activate(SubscriptionPlan $package)
    {
        try {
            $package->update(['is_active' => true]);
            
            if (request()->ajax()) {
                return response()->json(['success' => true, 'message' => 'تم تفعيل الباقة بنجاح']);
            }
            
            return back()->with('success', 'تم تفعيل الباقة بنجاح');
        } catch (\Exception $e) {
            if (request()->ajax()) {
                return response()->json(['success' => false, 'message' => 'حدث خطأ أثناء تفعيل الباقة']);
            }
            
            return back()->with('error', 'حدث خطأ أثناء تفعيل الباقة: ' . $e->getMessage());
        }
    }

    /**
     * Deactivate package.
     */
    public function deactivate(SubscriptionPlan $package)
    {
        try {
            $package->update(['is_active' => false]);
            
            if (request()->ajax()) {
                return response()->json(['success' => true, 'message' => 'تم إلغاء تفعيل الباقة بنجاح']);
            }
            
            return back()->with('success', 'تم إلغاء تفعيل الباقة بنجاح');
        } catch (\Exception $e) {
            if (request()->ajax()) {
                return response()->json(['success' => false, 'message' => 'حدث خطأ أثناء إلغاء تفعيل الباقة']);
            }
            
            return back()->with('error', 'حدث خطأ أثناء إلغاء تفعيل الباقة: ' . $e->getMessage());
        }
    }
}