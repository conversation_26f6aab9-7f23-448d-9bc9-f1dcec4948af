<?php

namespace Modules\Inventory\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\BranchInventory;
use App\Models\Product;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Facades\Auth;

class ItemsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Get items data for DataTable with branch filtering
     */
    public function datatable(Request $request)
    {
        $user = Auth::user();
        
        if (!$user->branch_id) {
            return response()->json([
                'error' => 'User is not assigned to any branch'
            ], 400);
        }

        $query = BranchInventory::with(['product.unit', 'branch'])
            ->where('branch_id', $user->branch_id);

        // Apply filters
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->whereHas('product', function ($q) use ($searchTerm) {
                $q->where('name', 'like', "%{$searchTerm}%")
                  ->orWhere('code', 'like', "%{$searchTerm}%")
                  ->orWhere('barcode', 'like', "%{$searchTerm}%");
            });
        }

        if ($request->filled('category')) {
            $query->whereHas('product', function ($q) use ($request) {
                $q->where('category', $request->category);
            });
        }

        if ($request->filled('stock_status')) {
            switch ($request->stock_status) {
                case 'low':
                    $query->whereColumn('current_stock', '<=', 'minimum_level');
                    break;
                case 'out':
                    $query->where('current_stock', '<=', 0);
                    break;
                case 'normal':
                    $query->whereColumn('current_stock', '>', 'minimum_level');
                    break;
            }
        }

        return DataTables::of($query)
            ->addIndexColumn()
            ->addColumn('product_name', function ($row) {
                return $row->product->name ?? 'N/A';
            })
            ->addColumn('product_code', function ($row) {
                return $row->product->code ?? 'N/A';
            })
            ->addColumn('category', function ($row) {
                return $row->product->category ?? 'N/A';
            })
            ->addColumn('unit', function ($row) {
                return $row->product->unit->name ?? 'N/A';
            })
            ->addColumn('stock_level', function ($row) {
                $current = $row->current_stock;
                $minimum = $row->minimum_level;
                
                if ($current <= 0) {
                    $status = 'نفد من المخزون';
                    $class = 'badge-danger';
                } elseif ($current <= $minimum) {
                    $status = 'مخزون منخفض';
                    $class = 'badge-warning';
                } else {
                    $status = 'مخزون طبيعي';
                    $class = 'badge-success';
                }
                
                return "<span class='badge {$class}'>{$status}</span>";
            })
            ->addColumn('stock_info', function ($row) {
                return "
                    <div class='stock-info'>
                        <div><strong>الحالي:</strong> {$row->current_stock}</div>
                        <div><strong>الحد الأدنى:</strong> {$row->minimum_level}</div>
                        <div><strong>المتاح:</strong> {$row->available_stock}</div>
                    </div>
                ";
            })
            ->addColumn('value', function ($row) {
                $totalValue = $row->current_stock * $row->unit_cost;
                return number_format($totalValue, 2) . ' ر.س';
            })
            ->addColumn('last_movement', function ($row) {
                return $row->last_movement_at ? $row->last_movement_at->format('Y-m-d H:i') : 'لا يوجد';
            })
            ->addColumn('actions', function ($row) {
                return "
                    <div class='btn-group' role='group'>
                        <button type='button' class='btn btn-sm btn-info' onclick='viewItem({$row->id})' title='عرض'>
                            <i class='fas fa-eye'></i>
                        </button>
                        <button type='button' class='btn btn-sm btn-warning' onclick='editStock({$row->id})' title='تعديل المخزون'>
                            <i class='fas fa-edit'></i>
                        </button>
                        <button type='button' class='btn btn-sm btn-success' onclick='updateStock({$row->id})' title='تحديث المخزون'>
                            <i class='fas fa-plus'></i>
                        </button>
                        <button type='button' class='btn btn-sm btn-primary' onclick='viewMovements({$row->id})' title='الحركات'>
                            <i class='fas fa-history'></i>
                        </button>
                    </div>
                ";
            })
            ->rawColumns(['stock_level', 'stock_info', 'actions'])
            ->make(true);
    }

    /**
     * Get all items for select dropdowns
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        if (!$user->branch_id) {
            return response()->json([
                'error' => 'User is not assigned to any branch'
            ], 400);
        }

        $items = BranchInventory::with(['product'])
            ->where('branch_id', $user->branch_id)
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'name' => $item->product->name,
                    'code' => $item->product->code,
                    'current_stock' => $item->current_stock,
                    'unit' => $item->product->unit->name ?? 'N/A',
                ];
            });

        return response()->json($items);
    }

    /**
     * Get item details
     */
    public function show($id)
    {
        $user = Auth::user();
        
        $item = BranchInventory::with(['product.unit', 'branch'])
            ->where('branch_id', $user->branch_id)
            ->findOrFail($id);

        return response()->json([
            'id' => $item->id,
            'product' => $item->product,
            'current_stock' => $item->current_stock,
            'available_stock' => $item->available_stock,
            'reserved_stock' => $item->reserved_stock,
            'minimum_level' => $item->minimum_level,
            'maximum_level' => $item->maximum_level,
            'reorder_point' => $item->reorder_point,
            'unit_cost' => $item->unit_cost,
            'total_value' => $item->current_stock * $item->unit_cost,
            'last_movement_at' => $item->last_movement_at,
            'last_counted_at' => $item->last_counted_at,
            'branch' => $item->branch,
        ]);
    }

    /**
     * Update stock for an item
     */
    public function updateStock(Request $request, $id)
    {
        $user = Auth::user();
        
        $request->validate([
            'quantity' => 'required|numeric',
            'type' => 'required|in:in,out,adjustment',
            'reason' => 'required|string|max:255',
            'notes' => 'nullable|string|max:500',
            'unit_cost' => 'nullable|numeric|min:0',
        ]);

        $item = BranchInventory::where('branch_id', $user->branch_id)
            ->findOrFail($id);

        // Update stock based on type
        $oldStock = $item->current_stock;
        
        switch ($request->type) {
            case 'in':
                $item->current_stock += $request->quantity;
                break;
            case 'out':
                $item->current_stock -= $request->quantity;
                break;
            case 'adjustment':
                $item->current_stock = $request->quantity;
                break;
        }

        // Update available stock
        $item->available_stock = $item->current_stock - $item->reserved_stock;
        
        // Update unit cost if provided
        if ($request->filled('unit_cost')) {
            $item->unit_cost = $request->unit_cost;
        }

        $item->last_movement_at = now();
        $item->save();

        // Create movement record
        $item->movements()->create([
            'type' => $request->type,
            'quantity' => $request->quantity,
            'unit_cost' => $request->unit_cost ?? $item->unit_cost,
            'total_cost' => $request->quantity * ($request->unit_cost ?? $item->unit_cost),
            'reason' => $request->reason,
            'notes' => $request->notes,
            'user_id' => $user->id,
            'performed_at' => now(),
            'reference_type' => 'manual_adjustment',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث المخزون بنجاح',
            'data' => $item->fresh(['product.unit', 'branch'])
        ]);
    }

    /**
     * Get low stock items
     */
    public function lowStock()
    {
        $user = Auth::user();
        
        if (!$user->branch_id) {
            return response()->json([
                'error' => 'User is not assigned to any branch'
            ], 400);
        }

        $items = BranchInventory::with(['product'])
            ->where('branch_id', $user->branch_id)
            ->whereColumn('current_stock', '<=', 'minimum_level')
            ->get();

        return response()->json($items);
    }
}
