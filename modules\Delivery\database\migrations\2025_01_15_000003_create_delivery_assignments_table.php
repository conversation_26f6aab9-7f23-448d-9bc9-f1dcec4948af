<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
       
        
        // Create the new table with correct structure
        Schema::create('delivery_assignments', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('order_id');
            $table->unsignedBigInteger('delivery_personnel_id');
            $table->unsignedBigInteger('delivery_zone_id')->nullable();
            $table->string('external_delivery_id')->nullable();
            $table->enum('status', ['assigned', 'picked_up', 'in_transit', 'delivered', 'cancelled', 'failed'])->default('assigned');
            $table->timestamp('assigned_at')->useCurrent();
            $table->timestamp('picked_up_at')->nullable();
            $table->timestamp('delivered_at')->nullable();
            $table->text('delivery_notes')->nullable();
            $table->string('failure_reason')->nullable();
            $table->json('delivery_proof')->nullable()->comment('Photos, signatures, etc.');
            $table->decimal('distance_km', 8, 2)->nullable();
            $table->integer('estimated_duration_minutes')->nullable();
            $table->integer('actual_duration_minutes')->nullable();
            $table->decimal('delivery_fee_earned', 8, 2)->default(0.00);
            $table->timestamps();

            // Indexes
            $table->index(['delivery_personnel_id', 'status']);
            $table->index('order_id');
            $table->index('assigned_at');
            $table->index('external_delivery_id');
            $table->index('delivery_zone_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('delivery_assignments');
    }
};