<!-- Loyalty Points Management Modal -->
<div class="modal fade" id="loyaltyPointsModal" tabindex="-1" aria-labelledby="loyaltyPointsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="loyaltyPointsModalLabel">
                    <i class="fas fa-star me-2"></i>Loyalty Points Management
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Customer Info Header -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card bg-light">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <h6 class="mb-1" id="loyalty_customer_name">Customer Name</h6>
                                        <small class="text-muted" id="loyalty_customer_info">Customer Info</small>
                                    </div>
                                    <div class="col-md-6 text-md-end">
                                        <div class="loyalty-points-display">
                                            <h4 class="text-warning mb-0">
                                                <i class="fas fa-star me-2"></i>
                                                <span id="loyalty_current_points">0</span> Points
                                            </h4>
                                            <small class="text-muted">Current Balance</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Tabs -->
                <ul class="nav nav-tabs" id="loyaltyTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="add-points-tab" data-bs-toggle="tab" data-bs-target="#add-points" type="button" role="tab">
                            <i class="fas fa-plus me-2"></i>Add Points
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="redeem-points-tab" data-bs-toggle="tab" data-bs-target="#redeem-points" type="button" role="tab">
                            <i class="fas fa-minus me-2"></i>Redeem Points
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="history-tab" data-bs-toggle="tab" data-bs-target="#history" type="button" role="tab">
                            <i class="fas fa-history me-2"></i>History
                        </button>
                    </li>
                </ul>

                <div class="tab-content mt-3" id="loyaltyTabContent">
                    <!-- Add Points Tab -->
                    <div class="tab-pane fade show active" id="add-points" role="tabpanel">
                        <form id="addPointsForm">
                            <input type="hidden" id="add_customer_id" name="customer_id">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="add_points_amount" class="form-label">Points to Add <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control" id="add_points_amount" name="points" min="0.01" max="10000" step="0.01" required>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="add_order_id" class="form-label">Related Order (Optional)</label>
                                        <select class="form-select" id="add_order_id" name="order_id">
                                            <option value="">Select Order</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label for="add_reason" class="form-label">Reason</label>
                                        <textarea class="form-control" id="add_reason" name="reason" rows="3" placeholder="Reason for adding points..."></textarea>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Quick Add Buttons -->
                            <div class="mb-3">
                                <label class="form-label">Quick Add:</label>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="setQuickPoints(10)">+10</button>
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="setQuickPoints(25)">+25</button>
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="setQuickPoints(50)">+50</button>
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="setQuickPoints(100)">+100</button>
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-plus me-2"></i>Add Points
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Redeem Points Tab -->
                    <div class="tab-pane fade" id="redeem-points" role="tabpanel">
                        <form id="redeemPointsForm">
                            <input type="hidden" id="redeem_customer_id" name="customer_id">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="redeem_points_amount" class="form-label">Points to Redeem <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control" id="redeem_points_amount" name="points" min="0.01" step="0.01" required>
                                        <div class="invalid-feedback"></div>
                                        <small class="text-muted">Available: <span id="available_points">0</span> points</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="redeem_order_id" class="form-label">Related Order (Optional)</label>
                                        <select class="form-select" id="redeem_order_id" name="order_id">
                                            <option value="">Select Order</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label for="redeem_reason" class="form-label">Reason</label>
                                        <textarea class="form-control" id="redeem_reason" name="reason" rows="3" placeholder="Reason for redeeming points..."></textarea>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Discount Calculator -->
                            <div class="mb-3">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">Discount Calculator</h6>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <label for="points_to_calculate" class="form-label">Points:</label>
                                                <input type="number" class="form-control" id="points_to_calculate" placeholder="Enter points">
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">Discount Value:</label>
                                                <div class="form-control-plaintext">
                                                    <strong id="calculated_discount">$0.00</strong>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-minus me-2"></i>Redeem Points
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- History Tab -->
                    <div class="tab-pane fade" id="history" role="tabpanel">
                        <div class="table-responsive">
                            <table id="loyaltyHistoryTable" class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Type</th>
                                        <th>Points</th>
                                        <th>Balance</th>
                                        <th>Reason</th>
                                        <th>Order</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- History will be populated by JS -->
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <nav aria-label="Loyalty history pagination">
                            <ul class="pagination pagination-sm justify-content-center" id="historyPagination">
                                <!-- Pagination will be populated by JS -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Close
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentLoyaltyCustomerId = null;

// Function to open loyalty management modal
function manageLoyalty(customerId) {
    currentLoyaltyCustomerId = customerId;
    
    // Fetch customer data
    $.get(`/customers/${customerId}`, function(customer) {
        // Set customer info
        $('#loyalty_customer_name').text(customer.first_name + ' ' + customer.last_name);
        $('#loyalty_customer_info').text(`${customer.email} • ${customer.phone}`);
        $('#loyalty_current_points').text(customer.loyalty_points);
        $('#available_points').text(customer.loyalty_points);
        
        // Set customer IDs in forms
        $('#add_customer_id').val(customer.id);
        $('#redeem_customer_id').val(customer.id);
        
        // Set max redeem amount
        $('#redeem_points_amount').attr('max', customer.loyalty_points);
        
        // Load recent orders for dropdowns
        loadRecentOrders(customer.id);
        
        // Load loyalty history
        loadLoyaltyHistory(customer.id);
        
        // Show modal
        $('#loyaltyPointsModal').modal('show');
    }).fail(function() {
        toastr.error('Failed to load customer data.');
    });
}

// Load recent orders for order selection
function loadRecentOrders(customerId) {
    $.get(`/customers/${customerId}/orders/recent`, function(orders) {
        const addSelect = $('#add_order_id');
        const redeemSelect = $('#redeem_order_id');
        
        addSelect.empty().append('<option value="">Select Order</option>');
        redeemSelect.empty().append('<option value="">Select Order</option>');
        
        orders.forEach(function(order) {
            const option = `<option value="${order.id}">#${order.order_number} - $${order.total_amount}</option>`;
            addSelect.append(option);
            redeemSelect.append(option);
        });
    }).fail(function() {
        console.log('No recent orders found or failed to load.');
    });
}

// Load loyalty history
function loadLoyaltyHistory(customerId, page = 1) {
    $.get(`/customers/${customerId}/loyalty/history?page=${page}`, function(response) {
        const tbody = $('#loyaltyHistoryTable tbody');
        tbody.empty();
        
        if (response.data.length === 0) {
            tbody.append('<tr><td colspan="6" class="text-center text-muted">No loyalty transactions found</td></tr>');
            return;
        }
        
        response.data.forEach(function(transaction) {
            const typeClass = transaction.type === 'earned' ? 'text-success' : 'text-warning';
            const pointsDisplay = transaction.type === 'earned' ? `+${transaction.points}` : `-${transaction.points}`;
            
            const row = `
                <tr>
                    <td>${moment(transaction.created_at).format('MMM DD, YYYY')}</td>
                    <td><span class="${typeClass}">${transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)}</span></td>
                    <td><span class="${typeClass}">${pointsDisplay}</span></td>
                    <td>${transaction.balance_after}</td>
                    <td>${transaction.reason || '-'}</td>
                    <td>${transaction.order ? `#${transaction.order.order_number}` : '-'}</td>
                </tr>
            `;
            tbody.append(row);
        });
        
        // Update pagination
        updateHistoryPagination(response.pagination, customerId);
    }).fail(function() {
        toastr.error('Failed to load loyalty history.');
    });
}

// Update pagination
function updateHistoryPagination(pagination, customerId) {
    const paginationEl = $('#historyPagination');
    paginationEl.empty();
    
    if (pagination.last_page <= 1) return;
    
    // Previous button
    if (pagination.current_page > 1) {
        paginationEl.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadLoyaltyHistory(${customerId}, ${pagination.current_page - 1})">Previous</a>
            </li>
        `);
    }
    
    // Page numbers
    for (let i = 1; i <= pagination.last_page; i++) {
        const activeClass = i === pagination.current_page ? 'active' : '';
        paginationEl.append(`
            <li class="page-item ${activeClass}">
                <a class="page-link" href="#" onclick="loadLoyaltyHistory(${customerId}, ${i})">${i}</a>
            </li>
        `);
    }
    
    // Next button
    if (pagination.current_page < pagination.last_page) {
        paginationEl.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadLoyaltyHistory(${customerId}, ${pagination.current_page + 1})">Next</a>
            </li>
        `);
    }
}

// Set quick points amount
function setQuickPoints(amount) {
    $('#add_points_amount').val(amount);
}

// Calculate discount in real-time
$('#points_to_calculate').on('input', function() {
    const points = parseFloat($(this).val()) || 0;
    
    if (points > 0) {
        $.post('/customers/loyalty/calculate-discount', {
            points: points,
            _token: $('meta[name="csrf-token"]').attr('content')
        }, function(response) {
            $('#calculated_discount').text('$' + response.discount.toFixed(2));
        }).fail(function() {
            $('#calculated_discount').text('$0.00');
        });
    } else {
        $('#calculated_discount').text('$0.00');
    }
});

// Copy calculated points to redeem form
$('#points_to_calculate').on('change', function() {
    const points = parseFloat($(this).val()) || 0;
    if (points > 0) {
        $('#redeem_points_amount').val(points);
    }
});

$(document).ready(function() {
    // Handle add points form submission
    $('#addPointsForm').on('submit', function(e) {
        e.preventDefault();
        
        const form = $(this);
        const submitBtn = form.find('button[type="submit"]');
        const originalText = submitBtn.html();
        
        // Show loading state
        submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>Adding...').prop('disabled', true);
        
        // Clear previous errors
        form.find('.is-invalid').removeClass('is-invalid');
        form.find('.invalid-feedback').text('');
        
        $.ajax({
            url: `/customers/${currentLoyaltyCustomerId}/loyalty/add-points`,
            method: 'POST',
            data: form.serialize() + '&_token=' + $('meta[name="csrf-token"]').attr('content'),
            success: function(response) {
                // Reset form
                form[0].reset();
                
                // Update points display
                $('#loyalty_current_points').text(response.new_balance);
                $('#available_points').text(response.new_balance);
                
                // Reload history
                loadLoyaltyHistory(currentLoyaltyCustomerId);
                
                // Refresh main table if visible
                if ($.fn.DataTable.isDataTable('#customersTable')) {
                    $('#customersTable').DataTable().ajax.reload();
                }
                
                // Show success message
                toastr.success('Points added successfully!');
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    const errors = xhr.responseJSON.errors;
                    $.each(errors, function(field, messages) {
                        const input = form.find(`[name="${field}"]`);
                        input.addClass('is-invalid');
                        input.siblings('.invalid-feedback').text(messages[0]);
                    });
                } else {
                    toastr.error('An error occurred while adding points.');
                }
            },
            complete: function() {
                submitBtn.html(originalText).prop('disabled', false);
            }
        });
    });
    
    // Handle redeem points form submission
    $('#redeemPointsForm').on('submit', function(e) {
        e.preventDefault();
        
        const form = $(this);
        const submitBtn = form.find('button[type="submit"]');
        const originalText = submitBtn.html();
        
        // Show loading state
        submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>Redeeming...').prop('disabled', true);
        
        // Clear previous errors
        form.find('.is-invalid').removeClass('is-invalid');
        form.find('.invalid-feedback').text('');
        
        $.ajax({
            url: `/customers/${currentLoyaltyCustomerId}/loyalty/redeem-points`,
            method: 'POST',
            data: form.serialize() + '&_token=' + $('meta[name="csrf-token"]').attr('content'),
            success: function(response) {
                // Reset form
                form[0].reset();
                $('#points_to_calculate').val('');
                $('#calculated_discount').text('$0.00');
                
                // Update points display
                $('#loyalty_current_points').text(response.new_balance);
                $('#available_points').text(response.new_balance);
                $('#redeem_points_amount').attr('max', response.new_balance);
                
                // Reload history
                loadLoyaltyHistory(currentLoyaltyCustomerId);
                
                // Refresh main table if visible
                if ($.fn.DataTable.isDataTable('#customersTable')) {
                    $('#customersTable').DataTable().ajax.reload();
                }
                
                // Show success message
                toastr.success('Points redeemed successfully!');
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    const errors = xhr.responseJSON.errors;
                    $.each(errors, function(field, messages) {
                        const input = form.find(`[name="${field}"]`);
                        input.addClass('is-invalid');
                        input.siblings('.invalid-feedback').text(messages[0]);
                    });
                } else {
                    toastr.error('An error occurred while redeeming points.');
                }
            },
            complete: function() {
                submitBtn.html(originalText).prop('disabled', false);
            }
        });
    });
    
    // Reset forms when modal is hidden
    $('#loyaltyPointsModal').on('hidden.bs.modal', function() {
        $('#addPointsForm')[0].reset();
        $('#redeemPointsForm')[0].reset();
        $('#points_to_calculate').val('');
        $('#calculated_discount').text('$0.00');
        
        // Clear validation errors
        $('.is-invalid').removeClass('is-invalid');
        $('.invalid-feedback').text('');
        
        // Reset to first tab
        $('#add-points-tab').tab('show');
    });
});
</script>