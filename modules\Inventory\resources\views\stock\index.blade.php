@extends('layouts.master')

@section('title', 'إدارة المخزون')

@section('breadcrumb')
<div class="bg-white rounded-lg shadow-sm border border-gray-200 px-6 py-4 mb-6">
    <nav class="flex" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3 rtl:space-x-reverse">
            <li class="inline-flex items-center">
                <a href="{{ route('dashboard') }}" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                    <i class="fas fa-home ml-2"></i>
                    الرئيسية
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <i class="fas fa-chevron-left text-gray-400 mx-2"></i>
                    <a href="{{ route('inventory.dashboard') }}" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">المخزون</a>
                </div>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <i class="fas fa-chevron-left text-gray-400 mx-2"></i>
                    <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">إدارة المخزون</span>
                </div>
            </li>
        </ol>
    </nav>
</div>
@endsection

@section('content')
<div class="space-y-6">
    <!-- Page Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 flex items-center gap-3">
                        <div class="w-10 h-10 bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg flex items-center justify-center">
                            <i class="fas fa-warehouse text-white"></i>
                        </div>
                        إدارة المخزون
                    </h1>
                    <p class="mt-1 text-sm text-gray-600">مراقبة وإدارة مستويات المخزون والتنبيهات</p>
                </div>
                <div class="flex gap-2">
                    <button type="button" onclick="bulkStockUpdate()" class="px-4 py-2 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center gap-2">
                        <i class="fas fa-cube"></i>
                        تحديث مجمع
                    </button>
                    <button type="button" onclick="generateStockReport()" class="px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2">
                        <i class="fas fa-chart-bar"></i>
                        تقرير المخزون
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Stock Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Critical Stock Card -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                    </div>
                </div>
                <div class="mr-4 flex-1 text-right">
                    <p class="text-sm font-medium text-gray-600">مخزون حرج</p>
                    <p class="text-2xl font-bold text-red-600">{{ $analytics['critical_stock'] ?? 0 }}</p>
                </div>
            </div>
        </div>

        <!-- Low Stock Card -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-exclamation-circle text-yellow-600 text-xl"></i>
                    </div>
                </div>
                <div class="mr-4 flex-1 text-right">
                    <p class="text-sm font-medium text-gray-600">مخزون منخفض</p>
                    <p class="text-2xl font-bold text-yellow-600">{{ $analytics['low_stock'] ?? 0 }}</p>
                </div>
            </div>
        </div>
        </div>

        <!-- Normal Stock Card -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                </div>
                <div class="mr-4 flex-1 text-right">
                    <p class="text-sm font-medium text-gray-600">مخزون طبيعي</p>
                    <p class="text-2xl font-bold text-green-600">{{ $analytics['normal_stock'] ?? 0 }}</p>
                </div>
            </div>
        </div>

        <!-- Overstock Card -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-arrow-up text-blue-600 text-xl"></i>
                    </div>
                </div>
                <div class="mr-4 flex-1 text-right">
                    <p class="text-sm font-medium text-gray-600">مخزون زائد</p>
                    <p class="text-2xl font-bold text-blue-600">{{ $analytics['overstock'] ?? 0 }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Stock Alerts -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="fas fa-bell text-blue-600"></i>
                        تنبيهات المخزون
                    </h3>
                    <a href="{{ route('inventory.stock.alerts') }}" class="px-3 py-1 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors duration-200">
                        عرض الكل
                    </a>
                </div>
            </div>
            <div class="p-6 max-h-96 overflow-y-auto">
                @if($criticalItems && $criticalItems->count() > 0)
                    @foreach($criticalItems->take(5) as $item)
                    <div class="border-r-4 border-red-500 bg-red-50 p-4 mb-3 rounded-lg">
                        <div class="flex justify-between items-center">
                            <div>
                                <h6 class="font-medium text-red-800">{{ $item->product->name }}</h6>
                                <p class="text-sm text-red-600">{{ $item->product->sku }}</p>
                            </div>
                            <div class="text-left">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    {{ $item->current_stock }} {{ $item->product->unit->symbol ?? '' }}
                                </span>
                                <p class="text-xs text-red-600 mt-1">نفد المخزون</p>
                            </div>
                        </div>
                    </div>
                    @endforeach
                @endif

                @if($lowStockItems && $lowStockItems->count() > 0)
                    @foreach($lowStockItems->take(3) as $item)
                    <div class="border-r-4 border-yellow-500 bg-yellow-50 p-4 mb-3 rounded-lg">
                        <div class="flex justify-between items-center">
                            <div>
                                <h6 class="font-medium text-yellow-800">{{ $item->product->name }}</h6>
                                <p class="text-sm text-yellow-600">{{ $item->product->sku }}</p>
                            </div>
                            <div class="text-left">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    {{ $item->current_stock }} {{ $item->product->unit->symbol ?? '' }}
                                </span>
                                <p class="text-xs text-yellow-600 mt-1">الحد الأدنى: {{ $item->minimum_stock }}</p>
                            </div>
                        </div>
                    </div>
                    @endforeach
                @endif

                @if((!$criticalItems || $criticalItems->count() == 0) && (!$lowStockItems || $lowStockItems->count() == 0))
                <div class="text-center text-gray-500 py-8">
                    <i class="fas fa-check-circle text-4xl text-green-500 mb-3"></i>
                    <p class="text-sm">لا توجد تنبيهات مخزون حالياً</p>
                </div>
                @endif
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                    <i class="fas fa-bolt text-blue-600"></i>
                    الإجراءات السريعة
                </h3>
            </div>
            <div class="p-6 space-y-3">
                <button type="button" onclick="quickStockUpdate()" class="w-full px-4 py-3 bg-blue-50 text-blue-700 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors duration-200 flex items-center gap-3">
                    <i class="fas fa-cube"></i>
                    تحديث سريع للمخزون
                </button>

                <button type="button" onclick="stockAdjustment()" class="w-full px-4 py-3 bg-green-50 text-green-700 border border-green-200 rounded-lg hover:bg-green-100 transition-colors duration-200 flex items-center gap-3">
                    <i class="fas fa-balance-scale"></i>
                    تسوية المخزون
                </button>

                <button type="button" onclick="stockTransfer()" class="w-full px-4 py-3 bg-purple-50 text-purple-700 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors duration-200 flex items-center gap-3">
                    <i class="fas fa-exchange-alt"></i>
                    نقل المخزون
                </button>

                <button type="button" onclick="stockCount()" class="w-full px-4 py-3 bg-yellow-50 text-yellow-700 border border-yellow-200 rounded-lg hover:bg-yellow-100 transition-colors duration-200 flex items-center gap-3">
                    <i class="fas fa-clipboard-list"></i>
                    جرد المخزون
                </button>

                <button type="button" onclick="viewMovements()" class="w-full px-4 py-3 bg-gray-50 text-gray-700 border border-gray-200 rounded-lg hover:bg-gray-100 transition-colors duration-200 flex items-center gap-3">
                    <i class="fas fa-history"></i>
                    حركات المخزون
                </button>
            </div>
        </div>

        <!-- Recent Movements -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <i class="fas fa-history text-blue-600"></i>
                        آخر الحركات
                    </h3>
                    <a href="{{ route('inventory.stock.movements') }}" class="px-3 py-1 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors duration-200">
                        عرض الكل
                    </a>
                </div>
            </div>
            <div class="p-6 max-h-96 overflow-y-auto">
                @if($recentMovements && $recentMovements->count() > 0)
                    @foreach($recentMovements->take(8) as $movement)
                    <div class="flex justify-between items-center py-3 border-b border-gray-100 last:border-b-0">
                        <div>
                            <h6 class="font-medium text-gray-900">{{ $movement->branchInventory->product->name }}</h6>
                            <p class="text-sm text-gray-500">
                                @switch($movement->type)
                                    @case('in')
                                        إدخال
                                        @break
                                    @case('out')
                                        إخراج
                                        @break
                                    @case('adjustment')
                                        تعديل
                                        @break
                                    @case('transfer')
                                        نقل
                                        @break
                                    @default
                                        {{ $movement->type }}
                                @endswitch
                            </p>
                        </div>
                        <div class="text-left">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $movement->type === 'out' ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800' }}">
                                {{ $movement->type === 'out' ? '-' : '+' }}{{ $movement->quantity }}
                            </span>
                            <p class="text-xs text-gray-500 mt-1">{{ $movement->created_at->diffForHumans() }}</p>
                        </div>
                    </div>
                    @endforeach
                @else
                <div class="text-center text-gray-500 py-8">
                    <i class="fas fa-history text-4xl text-gray-400 mb-3"></i>
                    <p class="text-sm">لا توجد حركات مخزون حديثة</p>
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Stock Levels Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <div class="flex justify-between items-center">
                <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                    <i class="fas fa-list text-blue-600"></i>
                    مستويات المخزون
                </h2>
                <div class="flex gap-2">
                    <button type="button" onclick="refreshStockTable()" class="px-3 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                    <button type="button" onclick="exportStock()" class="px-3 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center gap-2">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                </div>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table id="stockTable" class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المادة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الرمز</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المخزون الحالي</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحد الأدنى</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحد الأقصى</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">مستوى المخزون</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">آخر حركة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <!-- DataTable will populate this -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modals -->
@include('inventory::stock.modals')

@endsection

@push('styles')
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
<style>
    /* Custom DataTable styling for Tailwind */
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_processing,
    .dataTables_wrapper .dataTables_paginate {
        color: #374151;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.5rem 0.75rem;
        margin: 0 0.125rem;
        border-radius: 0.375rem;
        border: 1px solid #d1d5db;
        background: white;
        color: #374151;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background: #f3f4f6;
        border-color: #9ca3af;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background: #2563eb;
        border-color: #2563eb;
        color: white;
    }

    .stock-level-indicator {
        width: 100%;
        height: 8px;
        border-radius: 4px;
        background: #e5e7eb;
        overflow: hidden;
        margin-bottom: 4px;
    }

    .stock-level-fill {
        height: 100%;
        transition: width 0.3s ease;
    }

    .stock-level-fill.critical { background: #ef4444; }
    .stock-level-fill.low { background: #f59e0b; }
    .stock-level-fill.normal { background: #10b981; }
    .stock-level-fill.high { background: #3b82f6; }
</style>
@endpush

@push('scripts')
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>

<!-- Modal Functions -->
<script>
function openModal(modalId) {
    document.getElementById(modalId).classList.remove('hidden');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

let stockTable;

$(document).ready(function() {
    stockTable = $('#stockTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("inventory.api.stock.datatable") }}',
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'product.name', name: 'product.name' },
            { data: 'product.sku', name: 'product.sku' },
            {
                data: 'current_stock',
                name: 'current_stock',
                render: function(data, type, row) {
                    return `${data} ${row.product.unit?.symbol || ''}`;
                }
            },
            { data: 'minimum_stock', name: 'minimum_stock' },
            { data: 'maximum_stock', name: 'maximum_stock' },
            {
                data: 'stock_level',
                name: 'stock_level',
                render: function(data, type, row) {
                    const percentage = getStockPercentage(row.current_stock, row.minimum_stock, row.maximum_stock);
                    const level = getStockLevel(row.current_stock, row.minimum_stock, row.maximum_stock);

                    return `
                        <div class="stock-level-indicator">
                            <div class="stock-level-fill ${level}" style="width: ${percentage}%"></div>
                        </div>
                        <span class="text-xs text-gray-500">${Math.round(percentage)}%</span>
                    `;
                }
            },
            {
                data: 'status',
                name: 'status',
                render: function(data, type, row) {
                    return getStockStatusBadge(row.current_stock, row.minimum_stock);
                }
            },
            {
                data: 'last_movement',
                name: 'last_movement',
                render: function(data) {
                    return data ? formatDate(data) : '-';
                }
            },
            {
                data: 'actions',
                name: 'actions',
                orderable: false,
                searchable: false,
                render: function(data, type, row) {
                    return `
                        <div class="flex gap-1">
                            <button type="button" class="px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700" onclick="updateStock(${row.id})" title="تحديث المخزون">
                                <i class="fas fa-cube"></i>
                            </button>
                            <button type="button" class="px-2 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700" onclick="viewMovements(${row.id})" title="الحركات">
                                <i class="fas fa-history"></i>
                            </button>
                            <button type="button" class="px-2 py-1 bg-yellow-600 text-white text-xs rounded hover:bg-yellow-700" onclick="adjustStock(${row.id})" title="تسوية">
                                <i class="fas fa-balance-scale"></i>
                            </button>
                        </div>
                    `;
                }
            }
        ],
        order: [[1, 'asc']],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json'
        },
        responsive: true,
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]]
    });
});

function getStockPercentage(current, minimum, maximum) {
    if (!maximum) return current > minimum ? 100 : (current / minimum) * 50;
    return Math.min((current / maximum) * 100, 100);
}

function getStockLevel(current, minimum, maximum) {
    if (current <= 0) return 'critical';
    if (current <= minimum) return 'low';
    if (maximum && current >= maximum * 0.8) return 'high';
    return 'normal';
}

function getStockStatusBadge(current, minimum) {
    if (current <= 0) {
        return '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">نفد المخزون</span>';
    } else if (current <= minimum) {
        return '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">مخزون منخفض</span>';
    } else {
        return '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">متوفر</span>';
    }
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('ar-SA');
}

function refreshStockTable() {
    stockTable.ajax.reload();
}

function updateStock(id) {
    $('#updateStockModal').data('item-id', id);
    openModal('updateStockModal');
}

function viewMovements(id) {
    window.location.href = `{{ route('inventory.stock.movements.item', ':id') }}`.replace(':id', id);
}

function adjustStock(id) {
    $('#adjustStockModal').data('item-id', id);
    openModal('adjustStockModal');
}

function quickStockUpdate() {
    openModal('quickUpdateModal');
}

function stockAdjustment() {
    openModal('stockAdjustmentModal');
}

function stockTransfer() {
    openModal('stockTransferModal');
}

function stockCount() {
    openModal('stockCountModal');
}

function bulkStockUpdate() {
    openModal('bulkUpdateModal');
}

function generateStockReport() {
    window.location.href = '{{ route("inventory.api.stock.report") }}';
}

function exportStock() {
    window.location.href = '{{ route("inventory.api.stock.export") }}';
}

// Toast notification function
window.showToast = function(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg text-white font-medium ${
        type === 'success' ? 'bg-green-600' :
        type === 'error' ? 'bg-red-600' :
        type === 'warning' ? 'bg-yellow-600' : 'bg-blue-600'
    }`;
    toast.textContent = message;
    document.body.appendChild(toast);

    setTimeout(() => {
        toast.remove();
    }, 3000);
};
</script>
@endpush
