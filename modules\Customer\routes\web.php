<?php

use Illuminate\Support\Facades\Route;
use Modules\Customer\Http\Controllers\Web\CustomerController;
use Modules\Customer\Http\Controllers\Web\LoyaltyController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::middleware(['auth'])->prefix('customers')->name('customers.')->group(function () {
    // Customer Management Routes
    Route::get('/', [CustomerController::class, 'index'])->name('index');
    Route::get('/create', [CustomerController::class, 'create'])->name('create');
    Route::post('/', [CustomerController::class, 'store'])->name('store');
    Route::get('/{customer}', [CustomerController::class, 'show'])->name('show');
    Route::get('/{customer}/edit', [CustomerController::class, 'edit'])->name('edit');
    Route::put('/{customer}', [CustomerController::class, 'update'])->name('update');
    Route::delete('/{customer}', [CustomerController::class, 'destroy'])->name('destroy');
    
    // DataTables and AJAX Routes
    Route::get('/data/table', [CustomerController::class, 'getCustomersData'])->name('data.table');
    Route::get('/data/statistics', [CustomerController::class, 'getStatistics'])->name('data.statistics');
    Route::get('/search', [CustomerController::class, 'search'])->name('search');
    Route::get('/active', [CustomerController::class, 'getActiveCustomers'])->name('active');
    
    // Customer Status Management
    Route::patch('/{customer}/activate', [CustomerController::class, 'activate'])->name('activate');
    Route::patch('/{customer}/deactivate', [CustomerController::class, 'deactivate'])->name('deactivate');
    Route::patch('/{customer}/update-last-visit', [CustomerController::class, 'updateLastVisit'])->name('update-last-visit');
    
    // Loyalty Management Routes
    Route::prefix('{customer}/loyalty')->name('loyalty.')->group(function () {
        Route::get('/', [LoyaltyController::class, 'index'])->name('index');
        Route::post('/add-points', [LoyaltyController::class, 'addPoints'])->name('add-points');
        Route::post('/redeem-points', [LoyaltyController::class, 'redeemPoints'])->name('redeem-points');
        Route::get('/history', [LoyaltyController::class, 'history'])->name('history');
        Route::get('/data/table', [LoyaltyController::class, 'getTransactionsData'])->name('data.table');
    });
    
    // Loyalty Calculation Routes
    Route::post('/loyalty/calculate-points', [LoyaltyController::class, 'calculatePointsForOrder'])->name('loyalty.calculate-points');
    Route::post('/loyalty/calculate-discount', [LoyaltyController::class, 'calculateDiscountForPoints'])->name('loyalty.calculate-discount');
    Route::get('/loyalty/top-customers', [LoyaltyController::class, 'getTopLoyaltyCustomers'])->name('loyalty.top-customers');
});

// // Additional routes for customer selection in other modules
// Route::middleware(['auth', 'tenant.check'])->group(function () {
//     Route::get('/api/customers/select', [CustomerController::class, 'getActiveCustomers'])->name('api.customers.select');
// });