<?php

namespace Modules\Customer\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AddLoyaltyPointsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'points' => 'required|numeric|min:0.01|max:10000',
            'reason' => 'nullable|string|max:255',
            'order_id' => 'nullable|integer|exists:orders,id',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'points.required' => 'Points amount is required.',
            'points.numeric' => 'Points must be a valid number.',
            'points.min' => 'Points must be at least 0.01.',
            'points.max' => 'Points cannot exceed 10,000.',
            'order_id.exists' => 'The selected order does not exist.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'order_id' => 'order',
        ];
    }
}