<?php

namespace App\Helpers;

use Carbon\Carbon;

class ReservationHelper
{
    /**
     * Reservation status constants.
     */
    const STATUS_PENDING = 'pending';
    const STATUS_CONFIRMED = 'confirmed';
    const STATUS_SEATED = 'seated';
    const STATUS_COMPLETED = 'completed';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_NO_SHOW = 'no_show';

    /**
     * Get all available reservation statuses.
     * 
     * @return array
     */
    public static function getReservationStatuses(): array
    {
        return [
            self::STATUS_PENDING => 'Pending',
            self::STATUS_CONFIRMED => 'Confirmed',
            self::STATUS_SEATED => 'Seated',
            self::STATUS_COMPLETED => 'Completed',
            self::STATUS_CANCELLED => 'Cancelled',
            self::STATUS_NO_SHOW => 'No Show',
        ];
    }

    /**
     * Get status color for UI display.
     * 
     * @param string $status
     * @return string
     */
    public static function getStatusColor(string $status): string
    {
        return match($status) {
            self::STATUS_PENDING => 'warning',
            self::STATUS_CONFIRMED => 'info',
            self::STATUS_SEATED => 'primary',
            self::STATUS_COMPLETED => 'success',
            self::STATUS_CANCELLED => 'danger',
            self::STATUS_NO_SHOW => 'secondary',
            default => 'light'
        };
    }

    /**
     * Check if a reservation can be modified.
     * 
     * @param string $status
     * @return bool
     */
    public static function canModifyReservation(string $status): bool
    {
        return in_array($status, [
            self::STATUS_PENDING,
            self::STATUS_CONFIRMED
        ]);
    }

    /**
     * Check if a reservation can be cancelled.
     * 
     * @param string $status
     * @return bool
     */
    public static function canCancelReservation(string $status): bool
    {
        return in_array($status, [
            self::STATUS_PENDING,
            self::STATUS_CONFIRMED
        ]);
    }

    /**
     * Format reservation time for display.
     * 
     * @param string $date
     * @param string $time
     * @return string
     */
    public static function formatReservationDateTime(string $date, string $time): string
    {
        try {
            $dateTime = Carbon::createFromFormat('Y-m-d H:i:s', $date . ' ' . $time);
            return $dateTime->format('M d, Y \a\t g:i A');
        } catch (\Exception $e) {
            return $date . ' at ' . $time;
        }
    }

    /**
     * Calculate reservation duration in minutes.
     * 
     * @param string $startTime
     * @param string $endTime
     * @return int
     */
    public static function calculateDuration(string $startTime, string $endTime): int
    {
        try {
            $start = Carbon::createFromFormat('H:i:s', $startTime);
            $end = Carbon::createFromFormat('H:i:s', $endTime);
            
            return $start->diffInMinutes($end);
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Check if reservation time is in the past.
     * 
     * @param string $date
     * @param string $time
     * @return bool
     */
    public static function isReservationPast(string $date, string $time): bool
    {
        try {
            $reservationDateTime = Carbon::createFromFormat('Y-m-d H:i:s', $date . ' ' . $time);
            return $reservationDateTime->isPast();
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get time slots for reservation booking.
     * 
     * @param string $startTime
     * @param string $endTime
     * @param int $intervalMinutes
     * @return array
     */
    public static function generateTimeSlots(string $startTime = '09:00', string $endTime = '22:00', int $intervalMinutes = 30): array
    {
        $slots = [];
        $start = Carbon::createFromFormat('H:i', $startTime);
        $end = Carbon::createFromFormat('H:i', $endTime);
        
        while ($start->lte($end)) {
            $slots[] = [
                'value' => $start->format('H:i:s'),
                'label' => $start->format('g:i A')
            ];
            $start->addMinutes($intervalMinutes);
        }
        
        return $slots;
    }

    /**
     * Validate party size.
     * 
     * @param int $partySize
     * @param int $maxPartySize
     * @return array
     */
    public static function validatePartySize(int $partySize, int $maxPartySize = 20): array
    {
        if ($partySize < 1) {
            return [
                'valid' => false,
                'message' => 'Party size must be at least 1 person.'
            ];
        }
        
        if ($partySize > $maxPartySize) {
            return [
                'valid' => false,
                'message' => "Party size cannot exceed {$maxPartySize} people."
            ];
        }
        
        return [
            'valid' => true,
            'message' => 'Party size is valid.'
        ];
    }

    /**
     * Generate reservation reference number.
     * 
     * @param int $branchId
     * @return string
     */
    public static function generateReservationReference(int $branchId): string
    {
        $date = Carbon::now()->format('Ymd');
        $random = strtoupper(substr(uniqid(), -4));
        
        return "RES-{$branchId}-{$date}-{$random}";
    }
}