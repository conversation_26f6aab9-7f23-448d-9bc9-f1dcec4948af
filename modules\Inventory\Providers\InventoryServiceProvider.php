<?php

namespace Modules\Inventory\Providers;

use Illuminate\Support\ServiceProvider;
use Modules\Inventory\Services\InventoryService;
use Modules\Inventory\Services\SupplierService;
use Modules\Inventory\Services\PurchaseOrderService;
use Modules\Inventory\Services\InventoryLogService;

class InventoryServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register services as singletons
        $this->app->singleton(InventoryService::class);
        $this->app->singleton(SupplierService::class);
        $this->app->singleton(PurchaseOrderService::class);
        $this->app->singleton(InventoryLogService::class);
        
        // Register config
        $this->mergeConfigFrom(__DIR__ . '/../config/Inventory.php', 'Inventory');
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Load routes
        $this->loadRoutesFrom(__DIR__ . '/../routes/api.php');
        $this->loadRoutesFrom(__DIR__ . '/../routes/web.php');
        
        // Load migrations
        $this->loadMigrationsFrom(__DIR__ . '/../database/migrations');
        
        // Load views
        $this->loadViewsFrom(__DIR__ . '/../resources/views', 'Inventory');
        
        // Register commands
        if ($this->app->runningInConsole()) {
            $this->commands([
                \Modules\Inventory\Console\Commands\ProcessInventoryAlertsCommand::class,
            ]);
        }
        
        // Register middleware
        $router = $this->app['router'];
        $router->aliasMiddleware('Inventory.access', \Modules\Inventory\Http\Middleware\InventoryAccessMiddleware::class);
        
        // Publish config
        $this->publishes([
            __DIR__ . '/../config/Inventory.php' => config_path('Inventory.php'),
        ], 'Inventory-config');
    }
}