<?php

namespace Modules\Menu\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Modules\Menu\Http\Requests\StoreMenuRequest;
use Modules\Menu\Http\Requests\UpdateMenuRequest;
use Modules\Menu\Http\Resources\MenuResource;
use Modules\Menu\Services\MenuService;
use App\Helpers\BranchHelper;
use App\Helpers\ResponseHelper;

class MenuController extends Controller
{
    protected $menuService;

    public function __construct(MenuService $menuService)
    {
        $this->menuService = $menuService;
    }

    /**
     * Display a listing of menus for the authenticated user's branch.
     */
    public function index(Request $request)
    {
        try {
            $validation = BranchHelper::validateCurrentUserAssignment();
            
            if (!$validation['valid']) {
                return response()->json(['error' => $validation['message']], 400);
            }

            // Convert request parameters to filters array
            $filters = $request->only([
                'is_active', 'menu_type', 'search', 'sort_by', 
                'sort_direction', 'per_page'
            ]);

            $menus = $this->menuService->getMenusForBranch($validation['branch_id'], $filters);
            
            return MenuResource::collection($menus);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to retrieve menus: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Store a newly created menu.
     */
    public function store(StoreMenuRequest $request)
    {
        try {
            $validation = BranchHelper::validateCurrentUserAssignment();
            
            if (!$validation['valid']) {
                return response()->json(['error' => $validation['message']], 400);
            }

            $data = BranchHelper::addBranchAndTenantIds($request->validated());
            $menu = $this->menuService->createMenu($data);
            
            return new MenuResource($menu);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to create menu: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified menu.
     */
    public function show($id)
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            
            if (!$branchId) {
                return response()->json(['error' => 'User is not assigned to any branch'], 400);
            }

            $menu = $this->menuService->getMenuByIdForBranch($id, $branchId);
            
            if (!$menu) {
                return response()->json(['error' => 'Menu not found'], 404);
            }
            
            return new MenuResource($menu);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to retrieve menu: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Update the specified menu.
     */
    public function update(UpdateMenuRequest $request, $id)
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            
            if (!$branchId) {
                return response()->json(['error' => 'User is not assigned to any branch'], 400);
            }

            $data = $request->validated();
            $menu = $this->menuService->updateMenuForBranch($id, $data, $branchId);
            
            if (!$menu) {
                return response()->json(['error' => 'Menu not found'], 404);
            }
            
            return new MenuResource($menu);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to update menu: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified menu.
     */
    public function destroy($id)
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            
            if (!$branchId) {
                return response()->json(['error' => 'User is not assigned to any branch'], 400);
            }

            $deleted = $this->menuService->deleteMenuForBranch($id, $branchId);
            
            if (!$deleted) {
                return response()->json(['error' => 'Menu not found'], 404);
            }
            
            return response()->json(['message' => 'Menu deleted successfully']);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to delete menu: ' . $e->getMessage()], 500);
        }
    }
}