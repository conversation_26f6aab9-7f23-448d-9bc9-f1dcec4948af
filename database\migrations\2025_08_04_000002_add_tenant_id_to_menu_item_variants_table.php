<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if tenant_id column already exists
        if (!Schema::hasColumn('menu_item_variants', 'tenant_id')) {
            Schema::table('menu_item_variants', function (Blueprint $table) {
                // Add tenant_id column as nullable first
                $table->foreignId('tenant_id')->nullable()->after('id');
            });
        }

        // Update existing records to set tenant_id based on their menu_item's tenant_id
        // First, set a default tenant_id for records that don't have a menu_item
        DB::statement('UPDATE menu_item_variants SET tenant_id = 1 WHERE tenant_id IS NULL AND menu_item_id IS NULL');

        // Then update based on menu_item's tenant_id
        DB::statement('
            UPDATE menu_item_variants miv
            JOIN menu_items mi ON miv.menu_item_id = mi.id
            SET miv.tenant_id = mi.tenant_id
            WHERE miv.tenant_id IS NULL
        ');

        // Finally, set any remaining NULL values to 1 (default tenant)
        DB::statement('UPDATE menu_item_variants SET tenant_id = 1 WHERE tenant_id IS NULL');

        Schema::table('menu_item_variants', function (Blueprint $table) {
            // Now make it non-nullable and add foreign key constraint
            $table->foreignId('tenant_id')->nullable(false)->change();

            // Add foreign key constraint
            try {
                $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
            } catch (\Exception $e) {
                // Foreign key might already exist, ignore
            }

            // Add indexes for better performance
            try {
                $table->index(['tenant_id', 'branch_id']);
            } catch (\Exception $e) {
                // Index might already exist, ignore
            }

            try {
                $table->index(['tenant_id', 'menu_item_id']);
            } catch (\Exception $e) {
                // Index might already exist, ignore
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('menu_item_variants', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['tenant_id', 'branch_id']);
            $table->dropIndex(['tenant_id', 'menu_item_id']);
            
            // Drop foreign key and column
            $table->dropForeign(['tenant_id']);
            $table->dropColumn('tenant_id');
        });
    }
};
