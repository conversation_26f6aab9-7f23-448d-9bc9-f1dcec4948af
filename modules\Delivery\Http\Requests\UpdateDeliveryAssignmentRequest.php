<?php

namespace Modules\Delivery\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class UpdateDeliveryAssignmentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // return Auth::check();
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'delivery_personnel_id' => 'nullable|exists:delivery_personnel,id',
            'pickup_time' => 'nullable|date',
            'delivery_time' => 'nullable|date|after:pickup_time',
            'special_instructions' => 'nullable|string|max:500',
            'priority' => 'nullable|in:low,normal,high,urgent',
            'delivery_notes' => 'nullable|string|max:1000',
            'estimated_duration_minutes' => 'nullable|integer|min:1|max:480',
            'delivery_fee' => 'nullable|numeric|min:0',
            'tip_amount' => 'nullable|numeric|min:0',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'delivery_personnel_id.exists' => 'The selected delivery personnel does not exist.',
            'pickup_time.date' => 'The pickup time must be a valid date.',
            'delivery_time.date' => 'The delivery time must be a valid date.',
            'delivery_time.after' => 'The delivery time must be after the pickup time.',
            'special_instructions.max' => 'Special instructions cannot exceed 500 characters.',
            'priority.in' => 'Priority must be one of: low, normal, high, urgent.',
            'delivery_notes.max' => 'Delivery notes cannot exceed 1000 characters.',
            'estimated_duration_minutes.integer' => 'Estimated duration must be a whole number.',
            'estimated_duration_minutes.min' => 'Estimated duration must be at least 1 minute.',
            'estimated_duration_minutes.max' => 'Estimated duration cannot exceed 480 minutes (8 hours).',
            'delivery_fee.numeric' => 'Delivery fee must be a valid number.',
            'delivery_fee.min' => 'Delivery fee cannot be negative.',
            'tip_amount.numeric' => 'Tip amount must be a valid number.',
            'tip_amount.min' => 'Tip amount cannot be negative.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'delivery_personnel_id' => 'delivery personnel',
            'pickup_time' => 'pickup time',
            'delivery_time' => 'delivery time',
            'special_instructions' => 'special instructions',
            'priority' => 'priority',
            'delivery_notes' => 'delivery notes',
            'estimated_duration_minutes' => 'estimated duration',
            'delivery_fee' => 'delivery fee',
            'tip_amount' => 'tip amount',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate that delivery personnel is available if being assigned
            if ($this->has('delivery_personnel_id') && $this->delivery_personnel_id) {
                $personnelId = $this->delivery_personnel_id;
                
                // Check if personnel exists and is active
                $personnel = \Modules\Delivery\Models\DeliveryPersonnel::find($personnelId);
                if ($personnel && !in_array($personnel->status, ['active', 'available'])) {
                    $validator->errors()->add('delivery_personnel_id', 'The selected delivery personnel is not available for assignments.');
                }
            }

            // Validate time constraints
            if ($this->has('pickup_time') && $this->has('delivery_time')) {
                $pickupTime = $this->pickup_time;
                $deliveryTime = $this->delivery_time;
                
                if ($pickupTime && $deliveryTime) {
                    $pickup = \Carbon\Carbon::parse($pickupTime);
                    $delivery = \Carbon\Carbon::parse($deliveryTime);
                    
                    // Ensure delivery time is at least 5 minutes after pickup
                    if ($delivery->diffInMinutes($pickup) < 5) {
                        $validator->errors()->add('delivery_time', 'Delivery time must be at least 5 minutes after pickup time.');
                    }
                }
            }
        });
    }
}