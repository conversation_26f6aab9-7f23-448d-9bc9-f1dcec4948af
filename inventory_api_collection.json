{"info": {"_postman_id": "inventory-api-collection", "name": "Inventory Management API", "description": "Complete API collection for the Inventory Management module including inventory items, suppliers, purchase orders, and logs", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://127.0.0.1:8000/api/inventory", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}], "item": [{"name": "Inventory Items", "item": [{"name": "Get All Inventory Items", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/items?search=&category=&low_stock=false&per_page=15", "host": ["{{base_url}}"], "path": ["items"], "query": [{"key": "search", "value": "", "description": "Search by product name or SKU"}, {"key": "category", "value": "", "description": "Filter by product category"}, {"key": "low_stock", "value": "false", "description": "Filter low stock items"}, {"key": "per_page", "value": "15", "description": "Items per page"}]}}}, {"name": "Create Inventory Item", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Tomatoes\",\n    \"sku\": \"TOM001\",\n    \"description\": \"Fresh red tomatoes\",\n    \"category\": \"ingredients\",\n    \"unit_id\": 1,\n    \"initial_stock\": 100,\n    \"minimum_stock\": 20,\n    \"maximum_stock\": 500,\n    \"reorder_point\": 30,\n    \"cost_per_unit\": 2.50,\n    \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/items", "host": ["{{base_url}}"], "path": ["items"]}}}, {"name": "Get Inventory Item by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/items/1", "host": ["{{base_url}}"], "path": ["items", "1"]}}}, {"name": "Update Inventory Item", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Organic Tomatoes\",\n    \"description\": \"Fresh organic red tomatoes\",\n    \"minimum_stock\": 25,\n    \"maximum_stock\": 600,\n    \"reorder_point\": 35,\n    \"cost_per_unit\": 3.00\n}"}, "url": {"raw": "{{base_url}}/items/1", "host": ["{{base_url}}"], "path": ["items", "1"]}}}, {"name": "Delete Inventory Item", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/items/1", "host": ["{{base_url}}"], "path": ["items", "1"]}}}, {"name": "Get Low Stock Items", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/items/low-stock", "host": ["{{base_url}}"], "path": ["items", "low-stock"]}}}, {"name": "Update Stock", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"quantity\": 50,\n    \"type\": \"add\",\n    \"reason\": \"Stock replenishment\"\n}"}, "url": {"raw": "{{base_url}}/items/1/update-stock", "host": ["{{base_url}}"], "path": ["items", "1", "update-stock"]}}}, {"name": "Get Item Movements", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/items/1/movements", "host": ["{{base_url}}"], "path": ["items", "1", "movements"]}}}, {"name": "Bulk Update Stock", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"items\": [\n        {\n            \"id\": 1,\n            \"quantity\": 20,\n            \"type\": \"add\",\n            \"reason\": \"Bulk replenishment\"\n        },\n        {\n            \"id\": 2,\n            \"quantity\": 15,\n            \"type\": \"subtract\",\n            \"reason\": \"Bulk usage\"\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/items/bulk-update", "host": ["{{base_url}}"], "path": ["items", "bulk-update"]}}}, {"name": "Get Analytics", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/items/analytics", "host": ["{{base_url}}"], "path": ["items", "analytics"]}}}, {"name": "Get Reorder Suggestions", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/items/reorder-suggestions", "host": ["{{base_url}}"], "path": ["items", "reorder-suggestions"]}}}]}, {"name": "Suppliers", "item": [{"name": "Get All Suppliers", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/suppliers", "host": ["{{base_url}}"], "path": ["suppliers"]}}}, {"name": "Create Supplier", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Fresh Foods Supplier\",\n    \"contact_person\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"+1234567890\",\n    \"address\": \"123 Supply Street\",\n    \"city\": \"Supply City\",\n    \"state\": \"SC\",\n    \"postal_code\": \"12345\",\n    \"country\": \"USA\",\n    \"category\": \"food_ingredients\",\n    \"payment_terms\": \"Net 30\",\n    \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/suppliers", "host": ["{{base_url}}"], "path": ["suppliers"]}}}, {"name": "Get Supplier by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/suppliers/1", "host": ["{{base_url}}"], "path": ["suppliers", "1"]}}}, {"name": "Update Supplier", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Premium Fresh Foods Supplier\",\n    \"contact_person\": \"<PERSON>.\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"+1234567891\",\n    \"payment_terms\": \"Net 15\"\n}"}, "url": {"raw": "{{base_url}}/suppliers/1", "host": ["{{base_url}}"], "path": ["suppliers", "1"]}}}, {"name": "Delete Supplier", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/suppliers/1", "host": ["{{base_url}}"], "path": ["suppliers", "1"]}}}, {"name": "Get Supplier Products", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/suppliers/1/products", "host": ["{{base_url}}"], "path": ["suppliers", "1", "products"]}}}, {"name": "Get Supplier Purchase Orders", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/suppliers/1/purchase-orders", "host": ["{{base_url}}"], "path": ["suppliers", "1", "purchase-orders"]}}}, {"name": "Get Supplier Performance", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/suppliers/1/performance", "host": ["{{base_url}}"], "path": ["suppliers", "1", "performance"]}}}, {"name": "Add Product to Supplier", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"product_id\": 1,\n    \"supplier_sku\": \"SUP-TOM001\",\n    \"cost_per_unit\": 2.25,\n    \"minimum_order_quantity\": 50,\n    \"lead_time_days\": 3\n}"}, "url": {"raw": "{{base_url}}/suppliers/1/add-product", "host": ["{{base_url}}"], "path": ["suppliers", "1", "add-product"]}}}, {"name": "Get Supplier Categories", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/suppliers/categories", "host": ["{{base_url}}"], "path": ["suppliers", "categories"]}}}]}, {"name": "Purchase Orders", "item": [{"name": "Get All Purchase Orders", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/purchase-orders", "host": ["{{base_url}}"], "path": ["purchase-orders"]}}}, {"name": "Create Purchase Order", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"supplier_id\": 1,\n    \"order_number\": \"PO-2024-001\",\n    \"order_date\": \"2024-01-15\",\n    \"expected_delivery_date\": \"2024-01-20\",\n    \"notes\": \"Urgent order for weekend rush\",\n    \"items\": [\n        {\n            \"product_id\": 1,\n            \"quantity\": 100,\n            \"unit_cost\": 2.50,\n            \"notes\": \"Fresh tomatoes\"\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/purchase-orders", "host": ["{{base_url}}"], "path": ["purchase-orders"]}}}, {"name": "Get Purchase Order by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/purchase-orders/1", "host": ["{{base_url}}"], "path": ["purchase-orders", "1"]}}}, {"name": "Update Purchase Order", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"expected_delivery_date\": \"2024-01-22\",\n    \"notes\": \"Updated delivery date due to supplier delay\"\n}"}, "url": {"raw": "{{base_url}}/purchase-orders/1", "host": ["{{base_url}}"], "path": ["purchase-orders", "1"]}}}, {"name": "Delete Purchase Order", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/purchase-orders/1", "host": ["{{base_url}}"], "path": ["purchase-orders", "1"]}}}, {"name": "Approve Purchase Order", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/purchase-orders/1/approve", "host": ["{{base_url}}"], "path": ["purchase-orders", "1", "approve"]}}}, {"name": "Reject Purchase Order", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"reason\": \"Budget constraints\"\n}"}, "url": {"raw": "{{base_url}}/purchase-orders/1/reject", "host": ["{{base_url}}"], "path": ["purchase-orders", "1", "reject"]}}}, {"name": "Receive Purchase Order", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"items\": [\n        {\n            \"id\": 1,\n            \"received_quantity\": 95,\n            \"unit_cost\": 2.50\n        }\n    ],\n    \"notes\": \"5 items were damaged during delivery\"\n}"}, "url": {"raw": "{{base_url}}/purchase-orders/1/receive", "host": ["{{base_url}}"], "path": ["purchase-orders", "1", "receive"]}}}, {"name": "Cancel Purchase Order", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"reason\": \"Supplier unable to fulfill order\"\n}"}, "url": {"raw": "{{base_url}}/purchase-orders/1/cancel", "host": ["{{base_url}}"], "path": ["purchase-orders", "1", "cancel"]}}}, {"name": "Get Purchase Order Items", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/purchase-orders/1/items", "host": ["{{base_url}}"], "path": ["purchase-orders", "1", "items"]}}}, {"name": "Add Item to Purchase Order", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"product_id\": 2,\n    \"quantity\": 50,\n    \"unit_cost\": 1.75,\n    \"notes\": \"Additional item\"\n}"}, "url": {"raw": "{{base_url}}/purchase-orders/1/items", "host": ["{{base_url}}"], "path": ["purchase-orders", "1", "items"]}}}, {"name": "Update Purchase Order Item", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"quantity\": 75,\n    \"unit_cost\": 1.80\n}"}, "url": {"raw": "{{base_url}}/purchase-orders/1/items/1", "host": ["{{base_url}}"], "path": ["purchase-orders", "1", "items", "1"]}}}, {"name": "Re<PERSON><PERSON> from Purchase Order", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/purchase-orders/1/items/1", "host": ["{{base_url}}"], "path": ["purchase-orders", "1", "items", "1"]}}}, {"name": "Generate Purchase Order PDF", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/pdf"}], "url": {"raw": "{{base_url}}/purchase-orders/1/pdf", "host": ["{{base_url}}"], "path": ["purchase-orders", "1", "pdf"]}}}]}, {"name": "Inventory Logs", "item": [{"name": "Get All Logs", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/logs?start_date=2024-01-01&end_date=2024-01-31", "host": ["{{base_url}}"], "path": ["logs"], "query": [{"key": "start_date", "value": "2024-01-01"}, {"key": "end_date", "value": "2024-01-31"}]}}}, {"name": "Get Item Logs", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/logs/item/1", "host": ["{{base_url}}"], "path": ["logs", "item", "1"]}}}, {"name": "Get User Logs", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/logs/user/1", "host": ["{{base_url}}"], "path": ["logs", "user", "1"]}}}, {"name": "Get Movements Summary", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/logs/movements/summary?start_date=2024-01-01&end_date=2024-01-31&movement_type=add", "host": ["{{base_url}}"], "path": ["logs", "movements", "summary"], "query": [{"key": "start_date", "value": "2024-01-01"}, {"key": "end_date", "value": "2024-01-31"}, {"key": "movement_type", "value": "add"}]}}}, {"name": "Get Stock History", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/logs/stock-history/1?interval=day", "host": ["{{base_url}}"], "path": ["logs", "stock-history", "1"], "query": [{"key": "interval", "value": "day"}]}}}, {"name": "Get Valuation History", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/logs/valuation-history?interval=week", "host": ["{{base_url}}"], "path": ["logs", "valuation-history"], "query": [{"key": "interval", "value": "week"}]}}}, {"name": "Get Low Stock Alerts", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/logs/low-stock-alerts", "host": ["{{base_url}}"], "path": ["logs", "low-stock-alerts"]}}}, {"name": "Get Waste Report", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/logs/waste-report?start_date=2024-01-01&end_date=2024-01-31", "host": ["{{base_url}}"], "path": ["logs", "waste-report"], "query": [{"key": "start_date", "value": "2024-01-01"}, {"key": "end_date", "value": "2024-01-31"}]}}}, {"name": "Export Logs", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/logs/export?format=csv", "host": ["{{base_url}}"], "path": ["logs", "export"], "query": [{"key": "format", "value": "csv"}]}}}, {"name": "Get Audit Trail", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/logs/audit-trail/1", "host": ["{{base_url}}"], "path": ["logs", "audit-trail", "1"]}}}, {"name": "Get Discrepancy Report", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/logs/discrepancy-report?threshold=5", "host": ["{{base_url}}"], "path": ["logs", "discrepancy-report"], "query": [{"key": "threshold", "value": "5"}]}}}]}, {"name": "Utilities", "item": [{"name": "Get Categories", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/utilities/categories", "host": ["{{base_url}}"], "path": ["utilities", "categories"]}}}, {"name": "Get Units", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/utilities/units", "host": ["{{base_url}}"], "path": ["utilities", "units"]}}}, {"name": "Get Dashboard Stats", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/utilities/dashboard-stats", "host": ["{{base_url}}"], "path": ["utilities", "dashboard-stats"]}}}, {"name": "Import Inventory", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": []}, {"key": "format", "value": "csv", "type": "text"}]}, "url": {"raw": "{{base_url}}/utilities/import", "host": ["{{base_url}}"], "path": ["utilities", "import"]}}}, {"name": "Export Inventory", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/utilities/export?format=excel", "host": ["{{base_url}}"], "path": ["utilities", "export"], "query": [{"key": "format", "value": "excel"}]}}}]}]}