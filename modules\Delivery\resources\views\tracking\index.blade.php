@extends('layouts.master')

@section('content')
<!-- <PERSON> Header -->
<div class="mb-6">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="mb-4 sm:mb-0">
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-map-marker-alt mr-3"></i>
                تتبع التوصيل
            </h1>
            <p class="text-sm text-gray-600 mt-1">تتبع حالة طلبات التوصيل في الوقت الفعلي</p>
        </div>
        <div>
            <button type="button" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors duration-200" id="refresh-btn">
                <i class="fas fa-sync-alt mr-2"></i>
                تحديث
            </button>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">فلاتر البحث</h3>
    </div>
    <div class="p-6">
        <!-- Filters -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">موظف التوصيل</label>
                <select id="personnel-filter" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">جميع موظفي التوصيل</option>
                    @foreach($deliveryPersonnel as $personnel)
                        <option value="{{ $personnel->id }}">{{ $personnel->name }}</option>
                    @endforeach
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                <select id="status-filter" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">جميع الحالات</option>
                    <option value="assigned">مُعيّن</option>
                    <option value="picked_up">تم الاستلام</option>
                    <option value="in_transit">في الطريق</option>
                    <option value="delivered">تم التوصيل</option>
                    <option value="failed">فشل</option>
                    <option value="cancelled">ملغي</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">من تاريخ</label>
                <input type="date" id="date-from" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">إلى تاريخ</label>
                <input type="date" id="date-to" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
            <div class="flex items-end space-x-2">
                <button type="button" id="filter-btn" class="flex-1 inline-flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors duration-200">
                    <i class="fas fa-filter mr-2"></i>
                    فلتر
                </button>
                <button type="button" id="reset-btn" class="flex-1 inline-flex items-center justify-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-md transition-colors duration-200">
                    <i class="fas fa-undo mr-2"></i>
                    إعادة تعيين
                </button>
            </div>
        </div>

                    <!-- DataTable -->
                    <div class="table-responsive">
                        <table id="tracking-table" class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>رقم الطلب</th>
                                    <th>موظف التوصيل</th>
                                    <th>الحالة</th>
                                    <th>الموقع</th>
                                    <th>معلومات التوقيت</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Real-time Tracking Modal -->
<div class="modal fade" id="trackingModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تتبع الطلب في الوقت الفعلي</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="tracking-info">
                    <!-- Tracking information will be loaded here -->
                </div>
                <div id="map-container" style="height: 400px; margin-top: 20px;">
                    <!-- Map will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
                <button type="button" id="refresh-tracking" class="btn btn-primary">
                    <i class="fa fa-refresh"></i> تحديث الموقع
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Initialize DataTable
    var table = $('#tracking-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("delivery.tracking.data") }}',
            data: function(d) {
                d.delivery_personnel_id = $('#personnel-filter').val();
                d.status = $('#status-filter').val();
                d.date_from = $('#date-from').val();
                d.date_to = $('#date-to').val();
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'order_number', name: 'order.order_number' },
            { data: 'delivery_personnel_name', name: 'deliveryPersonnel.name' },
            { data: 'status_badge', name: 'status' },
            { data: 'location', name: 'location', orderable: false, searchable: false },
            { data: 'timing_info', name: 'timing_info', orderable: false, searchable: false },
            { data: 'action', name: 'action', orderable: false, searchable: false }
        ],
        order: [[0, 'desc']],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json'
        },
        autoWidth: false,
        responsive: true
    });

    // Filter functionality
    $('#filter-btn').click(function() {
        table.draw();
    });

    $('#reset-btn').click(function() {
        $('#personnel-filter').val('');
        $('#status-filter').val('');
        $('#date-from').val('');
        $('#date-to').val('');
        table.draw();
    });

    // Refresh functionality
    $('#refresh-btn').click(function() {
        table.draw(false);
    });

    // Real-time tracking functionality
    window.showRealTimeTracking = function(orderId) {
        $('#trackingModal').modal('show');
        loadTrackingData(orderId);
        
        $('#refresh-tracking').off('click').on('click', function() {
            loadTrackingData(orderId);
        });
    };

    function loadTrackingData(orderId) {
        $.ajax({
            url: '{{ route("delivery.tracking.order", ":id") }}'.replace(':id', orderId),
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    var data = response.data;
                    var trackingHtml = `
                        <div class="row">
                            <div class="col-md-6">
                                <h6>معلومات الطلب</h6>
                                <p><strong>رقم الطلب:</strong> #${data.order_number}</p>
                                <p><strong>موظف التوصيل:</strong> ${data.delivery_personnel}</p>
                                <p><strong>الحالة:</strong> ${getStatusLabel(data.status)}</p>
                            </div>
                            <div class="col-md-6">
                                <h6>معلومات التوقيت</h6>
                                <p><strong>الوقت المتوقع:</strong> ${data.estimated_arrival_time || 'غير محدد'}</p>
                                <p><strong>الوقت الفعلي:</strong> ${data.actual_arrival_time || 'لم يصل بعد'}</p>
                                <p><strong>آخر تحديث:</strong> ${data.last_updated}</p>
                            </div>
                        </div>
                    `;
                    
                    $('#tracking-info').html(trackingHtml);
                    
                    // Load map if coordinates are available
                    if (data.latitude && data.longitude) {
                        loadMap(data.latitude, data.longitude);
                    } else {
                        $('#map-container').html('<div class="alert alert-info">لا توجد بيانات موقع متاحة</div>');
                    }
                } else {
                    $('#tracking-info').html('<div class="alert alert-danger">' + response.message + '</div>');
                }
            },
            error: function() {
                $('#tracking-info').html('<div class="alert alert-danger">حدث خطأ أثناء تحميل بيانات التتبع</div>');
            }
        });
    }

    function getStatusLabel(status) {
        var labels = {
            'assigned': 'مُعيّن',
            'picked_up': 'تم الاستلام',
            'in_transit': 'في الطريق',
            'delivered': 'تم التوصيل',
            'failed': 'فشل',
            'cancelled': 'ملغي'
        };
        return labels[status] || status;
    }

    function loadMap(latitude, longitude) {
        var mapHtml = `
            <div class="text-center">
                <p><strong>الموقع الحالي:</strong></p>
                <p>خط العرض: ${latitude}</p>
                <p>خط الطول: ${longitude}</p>
                <a href="https://maps.google.com/?q=${latitude},${longitude}" target="_blank" class="btn btn-primary">
                    <i class="fa fa-map-marker"></i> عرض على خرائط جوجل
                </a>
            </div>
        `;
        $('#map-container').html(mapHtml);
    }

    // Auto-refresh every 30 seconds for active tracking
    setInterval(function() {
        if ($('#trackingModal').hasClass('show')) {
            var orderId = $('#refresh-tracking').data('order-id');
            if (orderId) {
                loadTrackingData(orderId);
            }
        }
    }, 30000);
});
</script>
@endpush