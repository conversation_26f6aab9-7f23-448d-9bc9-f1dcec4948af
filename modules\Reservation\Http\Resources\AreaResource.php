<?php

namespace Modules\Reservation\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AreaResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'branch_id' => $this->branch_id,
            'name' => $this->name,
            'description' => $this->description,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            
            // Relationships
            'branch' => $this->whenLoaded('branch', function () {
                return [
                    'id' => $this->branch->id,
                    'name' => $this->branch->name,
                ];
            }),
            
            'tables' => $this->whenLoaded('tables', function () {
                return $this->tables->map(function ($table) {
                    return [
                        'id' => $table->id,
                        'table_number' => $table->table_number,
                        'table_name' => $table->table_name,
                        'seating_capacity' => $table->seating_capacity,
                        'section' => $table->section,
                        'status' => $table->status,
                        'is_active' => $table->is_active,
                    ];
                });
            }),
            
            // Computed attributes
            'tables_count' => $this->whenCounted('tables'),
            'total_capacity' => $this->when(
                $this->relationLoaded('tables'),
                function () {
                    return $this->tables->sum('seating_capacity');
                }
            ),
        ];
    }
}