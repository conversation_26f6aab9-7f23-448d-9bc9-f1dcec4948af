<!-- Edit Customer Modal -->
<div class="modal fade" id="editCustomerModal" tabindex="-1" aria-labelledby="editCustomerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editCustomerModalLabel">
                    <i class="fas fa-user-edit me-2"></i>Edit Customer
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editCustomerForm" method="POST">
                @csrf
                @method('PUT')
                <input type="hidden" id="edit_customer_id" name="customer_id">
                
                <div class="modal-body">
                    <div class="row">
                        <!-- Personal Information -->
                        <div class="col-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-user me-2"></i>Personal Information
                            </h6>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_first_name" class="form-label">First Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="edit_first_name" name="first_name" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_last_name" class="form-label">Last Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="edit_last_name" name="last_name" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="edit_email" name="email">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_phone" class="form-label">Phone <span class="text-danger">*</span></label>
                                <input type="tel" class="form-control" id="edit_phone" name="phone" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_date_of_birth" class="form-label">Date of Birth</label>
                                <input type="date" class="form-control" id="edit_date_of_birth" name="date_of_birth">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_gender" class="form-label">Gender</label>
                                <select class="form-select" id="edit_gender" name="gender">
                                    <option value="">Select Gender</option>
                                    <option value="male">Male</option>
                                    <option value="female">Female</option>
                                    <option value="other">Other</option>
                                </select>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        
                        <!-- Address Information -->
                        <div class="col-12 mt-3">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-map-marker-alt me-2"></i>Address Information
                            </h6>
                        </div>
                        
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="edit_address" class="form-label">Address</label>
                                <textarea class="form-control" id="edit_address" name="address" rows="2"></textarea>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_city" class="form-label">City</label>
                                <input type="text" class="form-control" id="edit_city" name="city">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_postal_code" class="form-label">Postal Code</label>
                                <input type="text" class="form-control" id="edit_postal_code" name="postal_code">
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        
                        <!-- Loyalty Information -->
                        <div class="col-12 mt-3">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-star me-2"></i>Loyalty Information
                            </h6>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_loyalty_points" class="form-label">Loyalty Points</label>
                                <input type="number" class="form-control" id="edit_loyalty_points" name="loyalty_points" readonly>
                                <small class="text-muted">Use the loyalty management to modify points</small>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_tier" class="form-label">Customer Tier</label>
                                <input type="text" class="form-control" id="edit_tier" readonly>
                            </div>
                        </div>
                        
                        <!-- Additional Information -->
                        <div class="col-12 mt-3">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-info-circle me-2"></i>Additional Information
                            </h6>
                        </div>
                        
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="edit_notes" class="form-label">Notes</label>
                                <textarea class="form-control" id="edit_notes" name="notes" rows="3"></textarea>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active" value="1">
                                <label class="form-check-label" for="edit_is_active">
                                    Active Customer
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Update Customer
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Function to open edit modal
function editCustomer(customerId) {
    // Fetch customer data
    $.get(`/customers/${customerId}`, function(customer) {
        // Populate form fields
        $('#edit_customer_id').val(customer.id);
        $('#edit_first_name').val(customer.first_name);
        $('#edit_last_name').val(customer.last_name);
        $('#edit_email').val(customer.email);
        $('#edit_phone').val(customer.phone);
        $('#edit_date_of_birth').val(customer.date_of_birth);
        $('#edit_gender').val(customer.gender);
        $('#edit_address').val(customer.address);
        $('#edit_city').val(customer.city);
        $('#edit_postal_code').val(customer.postal_code);
        $('#edit_loyalty_points').val(customer.loyalty_points);
        $('#edit_tier').val(customer.tier);
        $('#edit_notes').val(customer.notes);
        $('#edit_is_active').prop('checked', customer.is_active);
        
        // Set form action
        $('#editCustomerForm').attr('action', `/customers/${customer.id}`);
        
        // Show modal
        $('#editCustomerModal').modal('show');
    }).fail(function() {
        toastr.error('Failed to load customer data.');
    });
}

$(document).ready(function() {
    // Handle form submission
    $('#editCustomerForm').on('submit', function(e) {
        e.preventDefault();
        
        const form = $(this);
        const submitBtn = form.find('button[type="submit"]');
        const originalText = submitBtn.html();
        
        // Show loading state
        submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>Updating...').prop('disabled', true);
        
        // Clear previous errors
        form.find('.is-invalid').removeClass('is-invalid');
        form.find('.invalid-feedback').text('');
        
        $.ajax({
            url: form.attr('action'),
            method: 'POST',
            data: form.serialize(),
            success: function(response) {
                // Close modal
                $('#editCustomerModal').modal('hide');
                
                // Refresh DataTable
                $('#customersTable').DataTable().ajax.reload();
                
                // Reload statistics
                loadStatistics();
                
                // Show success message
                toastr.success('Customer updated successfully!');
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    // Validation errors
                    const errors = xhr.responseJSON.errors;
                    
                    $.each(errors, function(field, messages) {
                        const input = form.find(`[name="${field}"]`);
                        input.addClass('is-invalid');
                        input.siblings('.invalid-feedback').text(messages[0]);
                    });
                } else {
                    toastr.error('An error occurred while updating the customer.');
                }
            },
            complete: function() {
                // Restore button state
                submitBtn.html(originalText).prop('disabled', false);
            }
        });
    });
    
    // Reset form when modal is hidden
    $('#editCustomerModal').on('hidden.bs.modal', function() {
        const form = $('#editCustomerForm');
        form.find('.is-invalid').removeClass('is-invalid');
        form.find('.invalid-feedback').text('');
    });
    
    // Phone number formatting
    $('#edit_phone').on('input', function() {
        let value = $(this).val().replace(/\D/g, '');
        if (value.length >= 6) {
            value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
        } else if (value.length >= 3) {
            value = value.replace(/(\d{3})(\d{0,3})/, '($1) $2');
        }
        $(this).val(value);
    });
});
</script>