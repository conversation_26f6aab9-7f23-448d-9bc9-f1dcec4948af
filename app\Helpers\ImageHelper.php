<?php

namespace App\Helpers;

class ImageHelper
{
    /**
     * Store an uploaded image file
     */
    public static function storeImage($file, string $directory = 'images'): ?string
    {
        if (!$file || !$file->isValid()) {
            return null;
        }

        try {
            return $file->store($directory, 'public');
        } catch (\Exception $e) {
            \Log::error('Image upload failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Delete an image file
     */
    public static function deleteImage(?string $imagePath): bool
    {
        if (!$imagePath) {
            return true;
        }

        try {
            return \Storage::disk('public')->delete($imagePath);
        } catch (\Exception $e) {
            \Log::error('Image deletion failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get the full URL for an image
     */
    public static function getImageUrl(?string $imagePath): ?string
    {
        if (!$imagePath) {
            return null;
        }

        return \Storage::disk('public')->url($imagePath);
    }

    /**
     * Validate image file
     */
    public static function validateImage($file, int $maxSizeKB = 2048): array
    {
        if (!$file) {
            return ['valid' => true];
        }

        $allowedMimes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif'];
        $maxSizeBytes = $maxSizeKB * 1024;

        if (!in_array($file->getMimeType(), $allowedMimes)) {
            return [
                'valid' => false,
                'message' => 'Invalid image format. Only JPEG, PNG, JPG, and GIF are allowed.'
            ];
        }

        if ($file->getSize() > $maxSizeBytes) {
            return [
                'valid' => false,
                'message' => "Image size must be less than {$maxSizeKB}KB."
            ];
        }

        return ['valid' => true];
    }

    /**
     * Generate image thumbnail HTML for DataTables
     */
    public static function generateThumbnailHtml(?string $imagePath, string $altText = '', string $placeholderIcon = 'fas fa-image'): string
    {
        if ($imagePath) {
            $imageUrl = self::getImageUrl($imagePath);
            return '<img src="' . $imageUrl . '" class="menu-item-thumbnail" alt="' . $altText . '" onerror="this.style.display=\'none\'; this.nextElementSibling.style.display=\'flex\';">
                    <div class="menu-item-thumbnail-placeholder" style="display:none;"><i class="' . $placeholderIcon . '"></i></div>';
        } else {
            return '<div class="menu-item-thumbnail-placeholder"><i class="' . $placeholderIcon . '"></i></div>';
        }
    }
}