<?php

namespace App\Services;

use App\Models\Setting;
use App\Models\Settings\SystemSetting;
use App\Models\Settings\BranchSetting;
use App\Models\Settings\PrinterSetting;
use App\Models\Settings\PaymentSetting;
use App\Models\Settings\SecuritySetting;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class SettingsService
{
    protected $cachePrefix = 'settings';
    protected $cacheTtl = 3600; // 1 hour

    /**
     * Get setting value with hierarchical fallback
     * Priority: Branch -> Tenant -> System -> Default
     */
    public function get($key, $tenantId = null, $branchId = null, $category = 'general', $default = null)
    {
        $cacheKey = $this->getCacheKey($key, $tenantId, $branchId, $category);
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($key, $tenantId, $branchId, $category, $default) {
            // 1. Try branch-specific setting
            if ($branchId && $tenantId) {
                $value = BranchSetting::get($key, $tenantId, $branchId, $category);
                if ($value !== null) {
                    return $value;
                }
            }
            
            // 2. Try tenant-specific setting (legacy settings table)
            if ($tenantId) {
                $setting = Setting::where('tenant_id', $tenantId)
                    ->where('category', $category)
                    ->where('key', $key)
                    ->first();
                
                if ($setting) {
                    return $this->castValue($setting->value, $setting->data_type);
                }
            }
            
            // 3. Try system-wide setting
            $systemValue = SystemSetting::get($key);
            if ($systemValue !== null) {
                return $systemValue;
            }
            
            // 4. Return default
            return $default;
        });
    }

    /**
     * Set setting value
     */
    public function set($key, $value, $tenantId = null, $branchId = null, $category = 'general', $description = null)
    {
        try {
            if ($branchId && $tenantId) {
                // Branch-specific setting
                $result = BranchSetting::set($key, $value, $tenantId, $branchId, $category, $description);
            } elseif ($tenantId) {
                // Tenant-specific setting (legacy table)
                $result = Setting::updateOrCreate(
                    [
                        'tenant_id' => $tenantId,
                        'category' => $category,
                        'key' => $key,
                    ],
                    [
                        'value' => is_array($value) ? json_encode($value) : $value,
                        'data_type' => $this->detectDataType($value),
                        'description' => $description,
                    ]
                );
            } else {
                // System-wide setting
                $result = SystemSetting::set($key, $value, null, $description);
            }
            
            // Clear cache
            $this->clearCache($key, $tenantId, $branchId, $category);
            
            return $result;
        } catch (\Exception $e) {
            Log::error('Failed to set setting', [
                'key' => $key,
                'value' => $value,
                'tenant_id' => $tenantId,
                'branch_id' => $branchId,
                'category' => $category,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get all settings for a category
     */
    public function getByCategory($category, $tenantId = null, $branchId = null)
    {
        $cacheKey = $this->getCacheKey("category_{$category}", $tenantId, $branchId);
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($category, $tenantId, $branchId) {
            $settings = [];
            
            // Get branch settings
            if ($branchId && $tenantId) {
                $branchSettings = BranchSetting::getByCategory($category, $tenantId, $branchId);
                $settings = array_merge($settings, $branchSettings);
            }
            
            // Get tenant settings (fill gaps)
            if ($tenantId) {
                $tenantSettings = Setting::where('tenant_id', $tenantId)
                    ->where('category', $category)
                    ->get()
                    ->pluck('value', 'key')
                    ->toArray();
                
                foreach ($tenantSettings as $key => $value) {
                    if (!isset($settings[$key])) {
                        $settings[$key] = $value;
                    }
                }
            }
            
            // Get system settings (fill remaining gaps)
            $systemSettings = SystemSetting::all()->pluck('value', 'key')->toArray();
            foreach ($systemSettings as $key => $value) {
                if (!isset($settings[$key])) {
                    $settings[$key] = $value;
                }
            }
            
            return $settings;
        });
    }

    /**
     * Bulk update settings
     */
    public function bulkUpdate($settings, $tenantId = null, $branchId = null, $category = 'general')
    {
        $results = [];
        
        foreach ($settings as $key => $value) {
            try {
                $results[$key] = $this->set($key, $value, $tenantId, $branchId, $category);
            } catch (\Exception $e) {
                $results[$key] = ['error' => $e->getMessage()];
            }
        }
        
        return $results;
    }

    /**
     * Delete setting
     */
    public function delete($key, $tenantId = null, $branchId = null, $category = 'general')
    {
        if ($branchId && $tenantId) {
            $setting = BranchSetting::where('tenant_id', $tenantId)
                ->where('branch_id', $branchId)
                ->where('category', $category)
                ->where('key', $key)
                ->first();
        } elseif ($tenantId) {
            $setting = Setting::where('tenant_id', $tenantId)
                ->where('category', $category)
                ->where('key', $key)
                ->first();
        } else {
            $setting = SystemSetting::where('key', $key)->first();
        }
        
        if ($setting) {
            $setting->delete();
            $this->clearCache($key, $tenantId, $branchId, $category);
            return true;
        }
        
        return false;
    }

    /**
     * Get printer settings
     */
    public function getPrinterSettings($tenantId, $branchId = null, $type = null)
    {
        $query = PrinterSetting::where('tenant_id', $tenantId)
            ->where('is_active', true);
        
        if ($branchId) {
            $query->where('branch_id', $branchId);
        }
        
        if ($type) {
            $query->where('type', $type);
        }
        
        return $query->orderBy('display_order')->get();
    }

    /**
     * Get payment settings
     */
    public function getPaymentSettings($tenantId, $branchId = null)
    {
        $query = PaymentSetting::where('tenant_id', $tenantId)
            ->where('is_enabled', true);
        
        if ($branchId) {
            $query->where('branch_id', $branchId);
        }
        
        return $query->orderBy('display_order')->get();
    }

    /**
     * Get security settings
     */
    public function getSecuritySettings($tenantId)
    {
        return SecuritySetting::where('tenant_id', $tenantId)->first() 
            ?? new SecuritySetting(SecuritySetting::getDefaults($tenantId));
    }

    /**
     * Initialize default settings for tenant
     */
    public function initializeTenantSettings($tenantId, $branchId = null)
    {
        // Initialize system defaults
        $this->initializeSystemDefaults();
        
        // Initialize payment settings
        $paymentDefaults = PaymentSetting::getDefaultSettings($tenantId, $branchId);
        foreach ($paymentDefaults as $paymentSetting) {
            PaymentSetting::create($paymentSetting);
        }
        
        // Initialize security settings
        SecuritySetting::create(SecuritySetting::getDefaults($tenantId));
        
        // Initialize branch settings if branch provided
        if ($branchId) {
            $this->initializeBranchDefaults($tenantId, $branchId);
        }
    }

    /**
     * Initialize system defaults
     */
    protected function initializeSystemDefaults()
    {
        $defaults = SystemSetting::getDefaults();
        
        foreach ($defaults as $key => $value) {
            SystemSetting::firstOrCreate(
                ['key' => $key],
                [
                    'value' => is_array($value) ? json_encode($value) : $value,
                    'data_type' => $this->detectDataType($value),
                    'description' => "Default {$key} setting",
                ]
            );
        }
    }

    /**
     * Initialize branch defaults
     */
    protected function initializeBranchDefaults($tenantId, $branchId)
    {
        $categories = BranchSetting::getDefaultCategories();
        
        foreach ($categories as $category => $settings) {
            foreach ($settings as $key => $description) {
                BranchSetting::firstOrCreate(
                    [
                        'tenant_id' => $tenantId,
                        'branch_id' => $branchId,
                        'category' => $category,
                        'key' => $key,
                    ],
                    [
                        'value' => $this->getDefaultValue($key),
                        'data_type' => 'string',
                        'description' => $description,
                    ]
                );
            }
        }
    }

    /**
     * Helper methods
     */
    protected function getCacheKey($key, $tenantId = null, $branchId = null, $category = 'general')
    {
        return "{$this->cachePrefix}_{$key}_{$tenantId}_{$branchId}_{$category}";
    }

    protected function clearCache($key, $tenantId = null, $branchId = null, $category = 'general')
    {
        $cacheKey = $this->getCacheKey($key, $tenantId, $branchId, $category);
        Cache::forget($cacheKey);
        
        // Also clear category cache
        $categoryCacheKey = $this->getCacheKey("category_{$category}", $tenantId, $branchId);
        Cache::forget($categoryCacheKey);
    }

    protected function castValue($value, $dataType)
    {
        switch ($dataType) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'integer':
                return (int) $value;
            case 'float':
                return (float) $value;
            case 'json':
                return json_decode($value, true);
            case 'array':
                return is_string($value) ? json_decode($value, true) : $value;
            default:
                return $value;
        }
    }

    protected function detectDataType($value)
    {
        if (is_bool($value)) {
            return 'boolean';
        } elseif (is_int($value)) {
            return 'integer';
        } elseif (is_float($value)) {
            return 'float';
        } elseif (is_array($value)) {
            return 'json';
        } else {
            return 'string';
        }
    }

    protected function getDefaultValue($key)
    {
        $defaults = [
            'restaurant_name' => 'مطعم جديد',
            'default_language' => 'ar',
            'currency' => 'SAR',
            'timezone' => 'Asia/Riyadh',
            'date_format' => 'Y-m-d',
            'time_format' => 'H:i',
            'show_tax_in_invoice' => true,
            'minimum_order_amount' => 0,
            'sound_notification' => true,
            'auto_print_receipt' => true,
            'default_tax_rate' => 15,
            'tax_inclusive' => false,
        ];
        
        return $defaults[$key] ?? '';
    }
}
