# 🚚 **Delivery Personnel API Documentation**

## **Overview**
This document provides comprehensive API documentation for the Delivery Personnel endpoints in the Restaurant POS system. These endpoints handle delivery personnel management, including CRUD operations, location tracking, performance metrics, and availability management.

---

## **🔗 Base URL**
```
/api/delivery/personnel
```

---

## **🔐 Authentication**
All endpoints require authentication using Bearer token:
```http
Authorization: Bearer {your_access_token}
```

---

## **📋 API Endpoints**

### **1. Get All Delivery Personnel**
```http
GET /api/delivery/personnel
Authorization: Bearer {token}
```

**Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `status` | string | No | Filter by status: `active`, `inactive`, `on_break`, `off_duty` |
| `branch_id` | string | No | Filter by branch ID |
| `vehicle_type` | string | No | Filter by vehicle type |
| `is_available` | boolean | No | Filter by availability |
| `per_page` | integer | No | Number of items per page (default: 15) |

**Example Request:**
```http
GET /api/delivery/personnel?status=active&branch_id=branch-uuid-123&per_page=10
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "personnel-uuid-123",
      "user_id": "user-uuid-456",
      "branch_id": "branch-uuid-123",
      "vehicle_type": "motorcycle",
      "vehicle_model": "Honda CBR 150",
      "vehicle_plate_number": "B1234XYZ",
      "license_number": "DL123456789",
      "phone_number": "+**********",
      "emergency_contact": "John Doe",
      "emergency_phone": "+**********",
      "max_concurrent_deliveries": 3,
      "delivery_radius": 15.00,
      "hourly_rate": 12.50,
      "commission_rate": 15.00,
      "status": "active",
      "current_latitude": 40.7128,
      "current_longitude": -74.0060,
      "last_location_update": "2024-12-15 14:30:22",
      "working_hours": {
        "monday": {"enabled": true, "start": "09:00", "end": "18:00"},
        "tuesday": {"enabled": true, "start": "09:00", "end": "18:00"},
        "wednesday": {"enabled": true, "start": "09:00", "end": "18:00"},
        "thursday": {"enabled": true, "start": "09:00", "end": "18:00"},
        "friday": {"enabled": true, "start": "09:00", "end": "18:00"},
        "saturday": {"enabled": false, "start": null, "end": null},
        "sunday": {"enabled": false, "start": null, "end": null}
      },
      "is_verified": true,
      "verification_date": "2024-12-01 10:00:00",
      "created_at": "2024-12-01 09:00:00",
      "updated_at": "2024-12-15 14:30:22",
      "user": {
        "id": "user-uuid-456",
        "name": "Mike Johnson",
        "email": "<EMAIL>",
        "avatar": "https://example.com/avatars/mike.jpg",
        "created_at": "2024-11-15 08:00:00"
      },
      "branch": {
        "id": "branch-uuid-123",
        "name": "Downtown Branch",
        "address": "123 Main Street, Downtown",
        "phone": "+1234567892",
        "latitude": 40.7589,
        "longitude": -73.9851
      },
      "status_label": "Active",
      "vehicle_info": "motorcycle - Honda CBR 150 - B1234XYZ",
      "is_available": true,
      "is_online": true,
      "current_load": 2,
      "can_accept_delivery": true,
      "distance_from_branch": 5.2,
      "working_today": true,
      "current_shift": "09:00 - 18:00",
      "performance_rating": 4.8,
      "total_deliveries_today": 8,
      "earnings_today": 125.50
    }
  ],
  "meta": {
    "current_page": 1,
    "last_page": 3,
    "per_page": 10,
    "total": 25
  }
}
```

---

### **2. Create New Delivery Personnel**
```http
POST /api/delivery/personnel
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "user_id": "user-uuid-789",
  "branch_id": "branch-uuid-123",
  "vehicle_type": "motorcycle",
  "vehicle_plate_number": "B5678ABC",
  "license_number": "DL987654321",
  "license_expiry_date": "2026-12-31",
  "phone_number": "+**********",
  "emergency_contact_name": "Jane Smith",
  "emergency_contact_phone": "+**********",
  "max_concurrent_deliveries": 3,
  "delivery_radius_km": 12.5,
  "hourly_rate": 15.00,
  "commission_rate": 20.00,
  "status": "active",
  "working_hours": [
    {
      "day": "monday",
      "start_time": "08:00",
      "end_time": "17:00",
      "is_working": true
    },
    {
      "day": "tuesday",
      "start_time": "08:00",
      "end_time": "17:00",
      "is_working": true
    },
    {
      "day": "wednesday",
      "start_time": "08:00",
      "end_time": "17:00",
      "is_working": true
    },
    {
      "day": "thursday",
      "start_time": "08:00",
      "end_time": "17:00",
      "is_working": true
    },
    {
      "day": "friday",
      "start_time": "08:00",
      "end_time": "17:00",
      "is_working": true
    },
    {
      "day": "saturday",
      "start_time": "09:00",
      "end_time": "15:00",
      "is_working": true
    },
    {
      "day": "sunday",
      "start_time": "09:00",
      "end_time": "15:00",
      "is_working": false
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Delivery personnel created successfully",
  "data": {
    "id": "personnel-uuid-789",
    "user_id": "user-uuid-789",
    "branch_id": "branch-uuid-123",
    "vehicle_type": "motorcycle",
    "vehicle_plate_number": "B5678ABC",
    "license_number": "DL987654321",
    "phone_number": "+**********",
    "emergency_contact_name": "Jane Smith",
    "emergency_contact_phone": "+**********",
    "max_concurrent_deliveries": 3,
    "delivery_radius_km": 12.5,
    "hourly_rate": 15.00,
    "commission_rate": 20.00,
    "status": "active",
    "is_verified": false,
    "created_at": "2024-12-15 15:00:00",
    "updated_at": "2024-12-15 15:00:00"
  }
}
```

---

### **3. Get Specific Delivery Personnel**
```http
GET /api/delivery/personnel/{id}
Authorization: Bearer {token}
```

**Example Request:**
```http
GET /api/delivery/personnel/personnel-uuid-123
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "personnel-uuid-123",
    "user_id": "user-uuid-456",
    "branch_id": "branch-uuid-123",
    "vehicle_type": "motorcycle",
    "vehicle_model": "Honda CBR 150",
    "vehicle_plate_number": "B1234XYZ",
    "license_number": "DL123456789",
    "phone_number": "+**********",
    "emergency_contact": "John Doe",
    "emergency_phone": "+**********",
    "max_concurrent_deliveries": 3,
    "delivery_radius": 15.00,
    "hourly_rate": 12.50,
    "commission_rate": 15.00,
    "status": "active",
    "current_latitude": 40.7128,
    "current_longitude": -74.0060,
    "last_location_update": "2024-12-15 14:30:22",
    "working_hours": {
      "monday": {"enabled": true, "start": "09:00", "end": "18:00"},
      "tuesday": {"enabled": true, "start": "09:00", "end": "18:00"},
      "wednesday": {"enabled": true, "start": "09:00", "end": "18:00"},
      "thursday": {"enabled": true, "start": "09:00", "end": "18:00"},
      "friday": {"enabled": true, "start": "09:00", "end": "18:00"},
      "saturday": {"enabled": false, "start": null, "end": null},
      "sunday": {"enabled": false, "start": null, "end": null}
    },
    "is_verified": true,
    "verification_date": "2024-12-01 10:00:00",
    "user": {
      "id": "user-uuid-456",
      "name": "Mike Johnson",
      "email": "<EMAIL>",
      "avatar": "https://example.com/avatars/mike.jpg"
    },
    "branch": {
      "id": "branch-uuid-123",
      "name": "Downtown Branch",
      "address": "123 Main Street, Downtown",
      "phone": "+1234567892"
    },
    "active_assignments": [
      {
        "id": "assignment-uuid-101",
        "order_id": "order-uuid-201",
        "status": "in_transit",
        "pickup_address": "Restaurant Address",
        "delivery_address": "Customer Address",
        "estimated_delivery_time": "2024-12-15 15:30:00"
      }
    ]
  }
}
```

---

### **4. Update Delivery Personnel**
```http
PUT /api/delivery/personnel/{id}
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "vehicle_type": "car",
  "vehicle_plate_number": "B9999XYZ",
  "phone_number": "+1234567895",
  "max_concurrent_deliveries": 5,
  "delivery_radius": 20.0,
  "hourly_rate": 18.00,
  "commission_rate": 25.00,
  "status": "active",
  "working_hours": [
    {
      "day": "monday",
      "start_time": "07:00",
      "end_time": "19:00",
      "is_working": true
    },
    {
      "day": "tuesday",
      "start_time": "07:00",
      "end_time": "19:00",
      "is_working": true
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Delivery personnel updated successfully",
  "data": {
    "id": "personnel-uuid-123",
    "vehicle_type": "car",
    "vehicle_plate_number": "B9999XYZ",
    "phone_number": "+1234567895",
    "max_concurrent_deliveries": 5,
    "delivery_radius": 20.0,
    "hourly_rate": 18.00,
    "commission_rate": 25.00,
    "status": "active",
    "updated_at": "2024-12-15 15:15:00"
  }
}
```

---

### **5. Delete Delivery Personnel**
```http
DELETE /api/delivery/personnel/{id}
Authorization: Bearer {token}
```

**Example Request:**
```http
DELETE /api/delivery/personnel/personnel-uuid-123
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

**Response:**
```json
{
  "success": true,
  "message": "Delivery personnel deleted successfully"
}
```

---

### **6. Get Available Personnel**
```http
GET /api/delivery/personnel/available/list
Authorization: Bearer {token}
```

**Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `latitude` | float | No | Customer latitude for distance calculation |
| `longitude` | float | No | Customer longitude for distance calculation |
| `branch_id` | string | No | Filter by specific branch |

**Example Request:**
```http
GET /api/delivery/personnel/available/list?latitude=40.7128&longitude=-74.0060&branch_id=branch-uuid-123
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "personnel-uuid-123",
      "user": {
        "name": "Mike Johnson",
        "avatar": "https://example.com/avatars/mike.jpg"
      },
      "vehicle_type": "motorcycle",
      "vehicle_info": "motorcycle - Honda CBR 150 - B1234XYZ",
      "current_latitude": 40.7128,
      "current_longitude": -74.0060,
      "distance_from_customer": 2.5,
      "current_load": 1,
      "max_concurrent_deliveries": 3,
      "performance_rating": 4.8,
      "estimated_arrival_time": "15 minutes",
      "is_online": true,
      "last_location_update": "2024-12-15 14:30:22"
    },
    {
      "id": "personnel-uuid-456",
      "user": {
        "name": "Sarah Wilson",
        "avatar": "https://example.com/avatars/sarah.jpg"
      },
      "vehicle_type": "car",
      "vehicle_info": "car - Toyota Camry - B5678DEF",
      "current_latitude": 40.7589,
      "current_longitude": -73.9851,
      "distance_from_customer": 4.2,
      "current_load": 0,
      "max_concurrent_deliveries": 5,
      "performance_rating": 4.9,
      "estimated_arrival_time": "20 minutes",
      "is_online": true,
      "last_location_update": "2024-12-15 14:28:15"
    }
  ]
}
```

---

### **7. Get Personnel Performance Metrics**
```http
GET /api/delivery/personnel/{id}/performance
Authorization: Bearer {token}
```

**Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `date_from` | date | No | Start date for metrics (YYYY-MM-DD) |
| `date_to` | date | No | End date for metrics (YYYY-MM-DD) |

**Example Request:**
```http
GET /api/delivery/personnel/personnel-uuid-123/performance?date_from=2024-12-01&date_to=2024-12-15
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

**Response:**
```json
{
  "success": true,
  "data": {
    "personnel_id": "personnel-uuid-123",
    "period": {
      "from": "2024-12-01",
      "to": "2024-12-15"
    },
    "metrics": {
      "total_deliveries": 125,
      "completed_deliveries": 120,
      "cancelled_deliveries": 5,
      "completion_rate": 96.0,
      "average_delivery_time": 28.5,
      "total_distance_km": 450.2,
      "total_earnings": 1875.50,
      "average_rating": 4.8,
      "total_tips": 245.75,
      "on_time_deliveries": 115,
      "on_time_rate": 95.8,
      "late_deliveries": 5,
      "customer_complaints": 2,
      "working_hours": 112.5,
      "deliveries_per_hour": 1.07
    },
    "daily_breakdown": [
      {
        "date": "2024-12-01",
        "deliveries": 8,
        "earnings": 125.50,
        "working_hours": 8.0,
        "average_rating": 4.9
      },
      {
        "date": "2024-12-02",
        "deliveries": 10,
        "earnings": 156.25,
        "working_hours": 8.5,
        "average_rating": 4.7
      }
    ],
    "ratings_distribution": {
      "5_star": 85,
      "4_star": 25,
      "3_star": 8,
      "2_star": 2,
      "1_star": 0
    }
  }
}
```

---

### **8. Verify Personnel**
```http
POST /api/delivery/personnel/{id}/verify
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "verification_status": "verified",
  "notes": "All documents verified successfully. License and vehicle registration are valid."
}
```

**Response:**
```json
{
  "success": true,
  "message": "Personnel verification updated successfully",
  "data": {
    "id": "personnel-uuid-123",
    "verification_status": "verified",
    "verification_notes": "All documents verified successfully. License and vehicle registration are valid.",
    "verified_at": "2024-12-15 15:30:00",
    "is_verified": true
  }
}
```

---

### **9. Update Personnel Status**
```http
PUT /api/delivery/personnel/{id}/status
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "status": "on_break"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Status updated successfully",
  "data": {
    "id": "personnel-uuid-123",
    "status": "on_break",
    "updated_at": "2024-12-15 15:45:00"
  }
}
```

---

### **10. Update Personnel Location**
```http
POST /api/delivery/personnel/{id}/location
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "latitude": 40.7589,
  "longitude": -73.9851
}
```

**Response:**
```json
{
  "success": true,
  "message": "Location updated successfully",
  "data": {
    "id": "personnel-uuid-123",
    "current_latitude": 40.7589,
    "current_longitude": -73.9851,
    "last_location_update": "2024-12-15 15:50:00"
  }
}
```

---

## **📊 Admin Endpoints**

### **11. Suspend Personnel**
```http
POST /api/delivery/admin/personnel/{id}/suspend
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "reason": "Multiple customer complaints about late deliveries",
  "suspension_until": "2024-12-22"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Personnel suspended successfully",
  "data": {
    "id": "personnel-uuid-123",
    "status": "suspended",
    "suspension_reason": "Multiple customer complaints about late deliveries",
    "suspension_until": "2024-12-22",
    "suspended_at": "2024-12-15 16:00:00"
  }
}
```

---

### **12. Activate Personnel**
```http
POST /api/delivery/admin/personnel/{id}/activate
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "message": "Personnel activated successfully",
  "data": {
    "id": "personnel-uuid-123",
    "status": "active",
    "activated_at": "2024-12-15 16:15:00"
  }
}
```

---

## **📱 Personnel App Endpoints**

### **13. Get My Assignments (Personnel App)**
```http
GET /api/delivery/personnel-app/my-assignments
Authorization: Bearer {personnel_token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "active_assignments": [
      {
        "id": "assignment-uuid-101",
        "order_id": "order-uuid-201",
        "order_number": "ORD20241215001",
        "status": "assigned",
        "pickup_address": "123 Restaurant Street",
        "delivery_address": "456 Customer Avenue",
        "customer_phone": "+**********",
        "estimated_prep_time": "15 minutes",
        "estimated_delivery_time": "2024-12-15 16:30:00",
        "delivery_fee": 5.50,
        "distance_km": 3.2,
        "special_instructions": "Ring doorbell twice"
      }
    ],
    "completed_today": 8,
    "earnings_today": 125.50,
    "current_shift": {
      "start_time": "09:00",
      "end_time": "18:00",
      "hours_worked": 7.5
    }
  }
}
```

---

### **14. Get My Performance (Personnel App)**
```http
GET /api/delivery/personnel-app/my-performance
Authorization: Bearer {personnel_token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "today": {
      "deliveries": 8,
      "earnings": 125.50,
      "tips": 25.75,
      "average_rating": 4.8,
      "on_time_rate": 100.0
    },
    "this_week": {
      "deliveries": 45,
      "earnings": 675.25,
      "tips": 145.50,
      "average_rating": 4.7,
      "on_time_rate": 95.6
    },
    "this_month": {
      "deliveries": 180,
      "earnings": 2850.75,
      "tips": 580.25,
      "average_rating": 4.8,
      "on_time_rate": 96.7
    },
    "rankings": {
      "delivery_count_rank": 3,
      "rating_rank": 2,
      "earnings_rank": 4,
      "total_personnel": 25
    }
  }
}
```

---

## **🚨 Error Responses**

### **Validation Error (422)**
```json
{
  "success": false,
  "message": "The given data was invalid.",
  "errors": {
    "vehicle_plate_number": [
      "The vehicle plate number has already been taken."
    ],
    "license_number": [
      "The license number field is required."
    ],
    "phone_number": [
      "The phone number field is required."
    ]
  }
}
```

### **Not Found Error (404)**
```json
{
  "success": false,
  "message": "Delivery personnel not found"
}
```

### **Server Error (500)**
```json
{
  "success": false,
  "message": "Failed to create delivery personnel",
  "error": "Database connection failed"
}
```

---

## **📝 Field Descriptions**

### **Vehicle Types**
- `bike` - Bicycle
- `motorcycle` - Motorcycle/Motorbike
- `car` - Car
- `van` - Van
- `truck` - Truck
- `bicycle` - Bicycle
- `scooter` - Electric Scooter

### **Status Types**
- `active` - Available for deliveries
- `inactive` - Not available for deliveries
- `on_break` - Currently on break
- `off_duty` - Off duty/shift ended
- `suspended` - Suspended by admin

### **Verification Status**
- `pending` - Awaiting verification
- `verified` - Documents verified and approved
- `rejected` - Documents rejected

---

## **🔄 Status Flow**
```
pending → verified/rejected
active ↔ inactive
active ↔ on_break
active ↔ off_duty
any_status → suspended (admin only)
suspended → active (admin only)
```

---

## **📊 Performance Metrics**
- **Completion Rate**: Percentage of successfully completed deliveries
- **On-Time Rate**: Percentage of deliveries completed on time
- **Average Rating**: Customer rating average (1-5 stars)
- **Deliveries per Hour**: Efficiency metric
- **Total Distance**: Cumulative distance covered
- **Earnings**: Total earnings including base pay and tips

---

## **🎯 Best Practices**

1. **Location Updates**: Update personnel location every 30-60 seconds during active deliveries
2. **Status Management**: Always update status when personnel goes on break or off duty
3. **Performance Monitoring**: Regularly check performance metrics to identify training needs
4. **Verification**: Ensure all personnel are verified before allowing deliveries
5. **Working Hours**: Set realistic working hours to prevent burnout
6. **Load Balancing**: Monitor concurrent deliveries to optimize performance

---

## **🔗 Related Endpoints**
- [Delivery Assignments API](./DELIVERY_ASSIGNMENTS_API.md)
- [Delivery Tracking API](./DELIVERY_TRACKING_API.md)
- [Delivery Analytics API](./DELIVERY_ANALYTICS_API.md)

---

*Last Updated: December 15, 2024*