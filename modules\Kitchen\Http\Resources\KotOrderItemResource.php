<?php

namespace Modules\Kitchen\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class KotOrderItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'kot_order_id' => $this->kot_order_id,
            'order_item_id' => $this->order_item_id,
            'menu_item_id' => $this->menu_item_id,
            'quantity' => $this->quantity,
            'status' => $this->status,
            'status_label' => ucfirst($this->status),
            'special_instructions' => $this->special_instructions,
            'modifications' => $this->modifications,
            'prep_time_minutes' => $this->prep_time_minutes,
            'started_at' => $this->started_at?->toISOString(),
            'completed_at' => $this->completed_at?->toISOString(),
            'notes' => $this->notes,
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),

            // Relationships
            'kot_order' => $this->whenLoaded('kotOrder', function () {
                return [
                    'id' => $this->kotOrder->id,
                    'kot_number' => $this->kotOrder->kot_number,
                    'status' => $this->kotOrder->status,
                ];
            }),

            'order_item' => $this->whenLoaded('orderItem', function () {
                return [
                    'id' => $this->orderItem->id,
                    'quantity' => $this->orderItem->quantity,
                    'unit_price' => $this->orderItem->unit_price,
                    'total_price' => $this->orderItem->total_price,
                    'special_instructions' => $this->orderItem->special_instructions,
                ];
            }),

            'menu_item' => $this->whenLoaded('menuItem', function () {
                return [
                    'id' => $this->menuItem->id,
                    'name' => $this->menuItem->name,
                    'description' => $this->menuItem->description,
                    'price' => $this->menuItem->base_price,
                    'category' => $this->menuItem->category?->name,
                    'image_url' => $this->menuItem->image_url,
                ];
            }),

            // Computed attributes
            'elapsed_time_minutes' => $this->getElapsedTimeMinutes(),
            'is_overdue' => $this->isOverdue(),

            // Status badge for UI
            'status_badge' => [
                'class' => $this->getStatusBadgeClass(),
                'text' => ucfirst($this->status),
            ],

            // Time display
            'time_display' => [
                'elapsed' => $this->getElapsedTimeMinutes() . ' min',
                'prep_time' => $this->prep_time_minutes ? 
                    $this->prep_time_minutes . ' min' : 'N/A',
            ],

            // Timestamps formatted for display
            'formatted_timestamps' => [
                'created_at' => $this->created_at?->format('M d, Y H:i'),
                'started_at' => $this->started_at?->format('M d, Y H:i'),
                'completed_at' => $this->completed_at?->format('M d, Y H:i'),
            ],
        ];
    }

    /**
     * Get status badge CSS class.
     */
    private function getStatusBadgeClass(): string
    {
        $classes = [
            'pending' => 'badge-warning',
            'preparing' => 'badge-info',
            'ready' => 'badge-success',
            'completed' => 'badge-primary',
            'cancelled' => 'badge-danger',
        ];

        return $classes[$this->status] ?? 'badge-secondary';
    }
}
