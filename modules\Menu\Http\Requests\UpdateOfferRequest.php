<?php

namespace Modules\Menu\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * Update Offer Request
 * 
 * Handles validation for updating existing offers in the restaurant system.
 * Validates offer details, discount settings, conditions, scheduling,
 * and usage limits for updates.
 * 
 * @package Modules\Menu\Http\Requests
 * <AUTHOR> POS Team
 * @version 1.0.0
 */
class UpdateOfferRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $offerId = $this->route('offer')->id ?? $this->route('id');
        
        return [
            'tenant_id' => 'sometimes|integer|exists:tenants,id',
            'branch_id' => 'sometimes|integer|exists:branches,id',
            'name' => 'sometimes|string|max:255',
            'code' => [
                'sometimes',
                'string',
                'max:50',
                Rule::unique('offers', 'code')->ignore($offerId)
            ],
            'description' => 'nullable|string',
            'offer_type' => 'sometimes|string|in:percentage,fixed_amount,buy_x_get_y,combo,free_item,free_delivery,cashback',
            'discount_type' => 'sometimes|string|in:percentage,fixed_amount,free_item,free_delivery',
            'discount_value' => 'sometimes|numeric|min:0',
            'max_discount_amount' => 'nullable|numeric|min:0',
            'min_order_amount' => 'nullable|numeric|min:0',
            'buy_quantity' => 'nullable|integer|min:1',
            'get_quantity' => 'nullable|integer|min:1',
            'applicable_items' => 'nullable|array',
            'applicable_items.*' => 'integer|exists:menu_items,id',
            'excluded_items' => 'nullable|array',
            'excluded_items.*' => 'integer|exists:menu_items,id',
            'applicable_categories' => 'nullable|array',
            'applicable_categories.*' => 'integer|exists:menu_categories,id',
            'excluded_categories' => 'nullable|array',
            'excluded_categories.*' => 'integer|exists:menu_categories,id',
            'combo_items' => 'nullable|array',
            'combo_price' => 'nullable|numeric|min:0',
            'promo_code' => [
                'nullable',
                'string',
                'max:50',
                Rule::unique('offers', 'promo_code')->ignore($offerId)
            ],
            'requires_promo_code' => 'boolean',
            'start_date' => 'sometimes|date',
            'end_date' => 'sometimes|date|after_or_equal:start_date',
            'start_time' => 'nullable|date_format:H:i',
            'end_time' => 'nullable|date_format:H:i',
            'available_days' => 'nullable|array',
            'available_days.*' => 'string|in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'customer_eligibility' => 'nullable|array',
            'usage_limit_per_customer' => 'nullable|integer|min:1',
            'total_usage_limit' => 'nullable|integer|min:1',
            'is_stackable' => 'boolean',
            'stackable_with' => 'nullable|array',
            'stackable_with.*' => 'integer|exists:offers,id',
            'priority' => 'sometimes|string|in:low,medium,high,urgent',
            'terms_conditions' => 'nullable|string',
            'image_url' => 'nullable|string|url',
            'banner_image' => 'nullable|string|url',
            'notification_settings' => 'nullable|array',
            'auto_apply' => 'boolean',
            'auto_apply_conditions' => 'nullable|array',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'is_public' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'analytics_data' => 'nullable|array',
            'metadata' => 'nullable|array',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'tenant_id.exists' => 'Selected tenant does not exist.',
            'branch_id.exists' => 'Selected branch does not exist.',
            'name.max' => 'Offer name cannot exceed 255 characters.',
            'code.max' => 'Offer code cannot exceed 50 characters.',
            'code.unique' => 'This offer code is already in use.',
            'offer_type.in' => 'Invalid offer type selected.',
            'discount_type.in' => 'Invalid discount type selected.',
            'discount_value.numeric' => 'Discount value must be a valid number.',
            'discount_value.min' => 'Discount value cannot be negative.',
            'max_discount_amount.numeric' => 'Maximum discount amount must be a valid number.',
            'max_discount_amount.min' => 'Maximum discount amount cannot be negative.',
            'min_order_amount.numeric' => 'Minimum order amount must be a valid number.',
            'min_order_amount.min' => 'Minimum order amount cannot be negative.',
            'buy_quantity.integer' => 'Buy quantity must be a valid number.',
            'buy_quantity.min' => 'Buy quantity must be at least 1.',
            'get_quantity.integer' => 'Get quantity must be a valid number.',
            'get_quantity.min' => 'Get quantity must be at least 1.',
            'applicable_items.*.exists' => 'One or more selected menu items do not exist.',
            'excluded_items.*.exists' => 'One or more excluded menu items do not exist.',
            'applicable_categories.*.exists' => 'One or more selected categories do not exist.',
            'excluded_categories.*.exists' => 'One or more excluded categories do not exist.',
            'combo_price.numeric' => 'Combo price must be a valid number.',
            'combo_price.min' => 'Combo price cannot be negative.',
            'promo_code.max' => 'Promo code cannot exceed 50 characters.',
            'promo_code.unique' => 'This promo code is already in use.',
            'start_date.date' => 'Start date must be a valid date.',
            'end_date.date' => 'End date must be a valid date.',
            'end_date.after_or_equal' => 'End date must be after or equal to start date.',
            'start_time.date_format' => 'Start time must be in HH:MM format.',
            'end_time.date_format' => 'End time must be in HH:MM format.',
            'available_days.*.in' => 'Invalid day selected.',
            'usage_limit_per_customer.integer' => 'Usage limit per customer must be a valid number.',
            'usage_limit_per_customer.min' => 'Usage limit per customer must be at least 1.',
            'total_usage_limit.integer' => 'Total usage limit must be a valid number.',
            'total_usage_limit.min' => 'Total usage limit must be at least 1.',
            'stackable_with.*.exists' => 'One or more stackable offers do not exist.',
            'priority.in' => 'Invalid priority level selected.',
            'image_url.url' => 'Image URL must be a valid URL.',
            'banner_image.url' => 'Banner image URL must be a valid URL.',
            'sort_order.min' => 'Sort order cannot be negative.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'tenant_id' => 'tenant',
            'branch_id' => 'branch',
            'offer_type' => 'offer type',
            'discount_type' => 'discount type',
            'discount_value' => 'discount value',
            'max_discount_amount' => 'maximum discount amount',
            'min_order_amount' => 'minimum order amount',
            'buy_quantity' => 'buy quantity',
            'get_quantity' => 'get quantity',
            'applicable_items' => 'applicable items',
            'excluded_items' => 'excluded items',
            'applicable_categories' => 'applicable categories',
            'excluded_categories' => 'excluded categories',
            'combo_items' => 'combo items',
            'combo_price' => 'combo price',
            'promo_code' => 'promo code',
            'requires_promo_code' => 'requires promo code',
            'start_date' => 'start date',
            'end_date' => 'end date',
            'start_time' => 'start time',
            'end_time' => 'end time',
            'available_days' => 'available days',
            'customer_eligibility' => 'customer eligibility',
            'usage_limit_per_customer' => 'usage limit per customer',
            'total_usage_limit' => 'total usage limit',
            'is_stackable' => 'stackable',
            'stackable_with' => 'stackable with',
            'terms_conditions' => 'terms and conditions',
            'image_url' => 'image URL',
            'banner_image' => 'banner image',
            'notification_settings' => 'notification settings',
            'auto_apply' => 'auto apply',
            'auto_apply_conditions' => 'auto apply conditions',
            'is_featured' => 'featured status',
            'is_active' => 'active status',
            'is_public' => 'public status',
            'sort_order' => 'sort order',
            'analytics_data' => 'analytics data',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'requires_promo_code' => $this->boolean('requires_promo_code'),
            'is_stackable' => $this->boolean('is_stackable'),
            'auto_apply' => $this->boolean('auto_apply'),
            'is_featured' => $this->boolean('is_featured'),
            'is_active' => $this->boolean('is_active'),
            'is_public' => $this->boolean('is_public'),
        ]);
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Validate percentage discount
            if ($this->has('discount_type') && $this->discount_type === 'percentage' && 
                $this->has('discount_value') && $this->discount_value > 100) {
                $validator->errors()->add('discount_value', 'Percentage discount cannot exceed 100%.');
            }

            // Validate buy_x_get_y offer type
            if ($this->has('offer_type') && $this->offer_type === 'buy_x_get_y') {
                if ($this->has('buy_quantity') && !$this->buy_quantity) {
                    $validator->errors()->add('buy_quantity', 'Buy quantity is required for Buy X Get Y offers.');
                }
                if ($this->has('get_quantity') && !$this->get_quantity) {
                    $validator->errors()->add('get_quantity', 'Get quantity is required for Buy X Get Y offers.');
                }
            }

            // Validate combo offer type
            if ($this->has('offer_type') && $this->offer_type === 'combo') {
                if ($this->has('combo_items') && empty($this->combo_items)) {
                    $validator->errors()->add('combo_items', 'Combo items are required for combo offers.');
                }
                if ($this->has('combo_price') && !$this->combo_price) {
                    $validator->errors()->add('combo_price', 'Combo price is required for combo offers.');
                }
            }

            // Validate promo code requirement
            if ($this->has('requires_promo_code') && $this->requires_promo_code && 
                $this->has('promo_code') && !$this->promo_code) {
                $validator->errors()->add('promo_code', 'Promo code is required when promo code is mandatory.');
            }

            // Validate time consistency
            if ($this->has('start_time') && $this->has('end_time') && $this->start_time && $this->end_time) {
                try {
                    $startTime = \Carbon\Carbon::createFromFormat('H:i', $this->start_time);
                    $endTime = \Carbon\Carbon::createFromFormat('H:i', $this->end_time);
                    
                    if ($endTime->lte($startTime)) {
                        $validator->errors()->add('end_time', 'End time must be after start time.');
                    }
                } catch (\Exception $e) {
                    // Time format validation will catch this
                }
            }

            // Validate stackable offers
            if ($this->has('is_stackable') && $this->is_stackable && 
                $this->has('stackable_with') && !empty($this->stackable_with)) {
                $offerId = $this->route('offer')->id ?? $this->route('id');
                if (in_array($offerId, $this->stackable_with)) {
                    $validator->errors()->add('stackable_with', 'An offer cannot be stackable with itself.');
                }
            }

            // Validate applicable vs excluded items/categories
            if ($this->has('applicable_items') && $this->has('excluded_items') && 
                !empty($this->applicable_items) && !empty($this->excluded_items)) {
                $overlap = array_intersect($this->applicable_items, $this->excluded_items);
                if (!empty($overlap)) {
                    $validator->errors()->add('excluded_items', 'Items cannot be both applicable and excluded.');
                }
            }

            if ($this->has('applicable_categories') && $this->has('excluded_categories') && 
                !empty($this->applicable_categories) && !empty($this->excluded_categories)) {
                $overlap = array_intersect($this->applicable_categories, $this->excluded_categories);
                if (!empty($overlap)) {
                    $validator->errors()->add('excluded_categories', 'Categories cannot be both applicable and excluded.');
                }
            }

            // Validate auto apply conditions
            if ($this->has('auto_apply') && $this->auto_apply && 
                $this->has('auto_apply_conditions') && empty($this->auto_apply_conditions)) {
                $validator->errors()->add('auto_apply_conditions', 'Auto apply conditions are required when auto apply is enabled.');
            }

            // Validate usage limits consistency
            if ($this->has('usage_limit_per_customer') && $this->has('total_usage_limit') && 
                $this->usage_limit_per_customer && $this->total_usage_limit) {
                if ($this->usage_limit_per_customer > $this->total_usage_limit) {
                    $validator->errors()->add('usage_limit_per_customer', 'Usage limit per customer cannot exceed total usage limit.');
                }
            }

            // Validate free item/delivery offers
            if ($this->has('discount_type') && in_array($this->discount_type, ['free_item', 'free_delivery']) && 
                $this->has('discount_value') && $this->discount_value != 0) {
                $validator->errors()->add('discount_value', 'Discount value should be 0 for free item/delivery offers.');
            }

            // Validate cashback offers
            if ($this->has('offer_type') && $this->offer_type === 'cashback' && 
                $this->has('min_order_amount') && !$this->min_order_amount) {
                $validator->errors()->add('min_order_amount', 'Minimum order amount is required for cashback offers.');
            }

            // Validate date consistency for updates
            if ($this->has('start_date') && !$this->has('end_date')) {
                // If updating start_date, ensure it's not after existing end_date
                $offer = $this->route('offer');
                if ($offer && $offer->end_date) {
                    $startDate = \Carbon\Carbon::parse($this->start_date);
                    $existingEndDate = \Carbon\Carbon::parse($offer->end_date);
                    
                    if ($startDate->gt($existingEndDate)) {
                        $validator->errors()->add('start_date', 'Start date cannot be after the existing end date.');
                    }
                }
            }

            if ($this->has('end_date') && !$this->has('start_date')) {
                // If updating end_date, ensure it's not before existing start_date
                $offer = $this->route('offer');
                if ($offer && $offer->start_date) {
                    $endDate = \Carbon\Carbon::parse($this->end_date);
                    $existingStartDate = \Carbon\Carbon::parse($offer->start_date);
                    
                    if ($endDate->lt($existingStartDate)) {
                        $validator->errors()->add('end_date', 'End date cannot be before the existing start date.');
                    }
                }
            }

            // Validate usage limit updates don't go below current usage
            $offer = $this->route('offer');
            if ($offer) {
                if ($this->has('total_usage_limit') && $this->total_usage_limit < $offer->current_usage_count) {
                    $validator->errors()->add('total_usage_limit', 'Total usage limit cannot be less than current usage count.');
                }
            }
        });
    }
}