<?php

namespace Modules\Kitchen\Services;

use Modules\Kitchen\Models\KotOrder;
use Modules\Kitchen\Models\KotOrderItem;
use Modules\Kitchen\Models\Kitchen;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class KotService
{
    /**
     * Get KOT orders with filters and pagination.
     */
    public function getKotOrders(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = KotOrder::with(['kitchen', 'order', 'assignedTo', 'kotOrderItems.menuItem'])
            ->ordered();

        // Apply filters
        if (!empty($filters['kitchen_id'])) {
            $query->where('kitchen_id', $filters['kitchen_id']);
        }

        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (!empty($filters['priority'])) {
            $query->where('priority', $filters['priority']);
        }

        if (!empty($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        if (!empty($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('kot_number', 'like', '%' . $filters['search'] . '%')
                  ->orWhereHas('order', function ($orderQuery) use ($filters) {
                      $orderQuery->where('order_number', 'like', '%' . $filters['search'] . '%');
                  });
            });
        }

        return $query->paginate($perPage);
    }

    /**
     * Get KOT order by ID.
     */
    public function getKotOrderById(int $id): ?KotOrder
    {
        return KotOrder::with([
            'kitchen',
            'order.customer',
            'assignedTo',
            'creator',
            'kotOrderItems.menuItem',
            'kotOrderItems.orderItem'
        ])->find($id);
    }

    /**
     * Get active KOTs for kitchen display.
     */
    public function getActiveKotsForDisplay(int $kitchenId = null): Collection
    {
        $query = KotOrder::with(['order', 'kotOrderItems.menuItem'])
            ->whereIn('status', ['pending', 'preparing', 'ready'])
            ->ordered();

        if ($kitchenId) {
            $query->where('kitchen_id', $kitchenId);
        }

        return $query->get();
    }

    /**
     * Start KOT preparation.
     */
    public function startKot(KotOrder $kotOrder, ?int $userId = null): KotOrder
    {
        try {
            DB::beginTransaction();

            $kotOrder->start($userId);

            // Start all items in the KOT
            $kotOrder->kotOrderItems()->update([
                'status' => 'preparing',
                'started_at' => now(),
            ]);

            DB::commit();

            Log::info('KOT started', [
                'kot_id' => $kotOrder->id,
                'kot_number' => $kotOrder->kot_number,
                'assigned_to' => $userId
            ]);

            return $kotOrder->fresh();

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to start KOT', [
                'kot_id' => $kotOrder->id,
                'error' => $e->getMessage()
            ]);
            
            throw $e;
        }
    }

    /**
     * Mark KOT as ready.
     */
    public function markKotReady(KotOrder $kotOrder): KotOrder
    {
        try {
            DB::beginTransaction();

            $kotOrder->markReady();

            // Mark all items as ready
            $kotOrder->kotOrderItems()->update([
                'status' => 'ready',
            ]);

            DB::commit();

            Log::info('KOT marked as ready', [
                'kot_id' => $kotOrder->id,
                'kot_number' => $kotOrder->kot_number
            ]);

            return $kotOrder->fresh();

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to mark KOT as ready', [
                'kot_id' => $kotOrder->id,
                'error' => $e->getMessage()
            ]);
            
            throw $e;
        }
    }

    /**
     * Complete KOT.
     */
    public function completeKot(KotOrder $kotOrder): KotOrder
    {
        try {
            DB::beginTransaction();

            $kotOrder->complete();

            // Complete all items
            $kotOrder->kotOrderItems()->update([
                'status' => 'completed',
                'completed_at' => now(),
            ]);

            DB::commit();

            Log::info('KOT completed', [
                'kot_id' => $kotOrder->id,
                'kot_number' => $kotOrder->kot_number
            ]);

            return $kotOrder->fresh();

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to complete KOT', [
                'kot_id' => $kotOrder->id,
                'error' => $e->getMessage()
            ]);
            
            throw $e;
        }
    }

    /**
     * Cancel KOT.
     */
    public function cancelKot(KotOrder $kotOrder, string $reason = null): KotOrder
    {
        try {
            DB::beginTransaction();

            $kotOrder->cancel($reason);

            // Cancel all items
            $kotOrder->kotOrderItems()->update([
                'status' => 'cancelled',
                'notes' => $reason ? "Cancelled: {$reason}" : 'Cancelled',
            ]);

            DB::commit();

            Log::info('KOT cancelled', [
                'kot_id' => $kotOrder->id,
                'kot_number' => $kotOrder->kot_number,
                'reason' => $reason
            ]);

            return $kotOrder->fresh();

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to cancel KOT', [
                'kot_id' => $kotOrder->id,
                'error' => $e->getMessage()
            ]);
            
            throw $e;
        }
    }

    /**
     * Update KOT priority.
     */
    public function updateKotPriority(KotOrder $kotOrder, string $priority): KotOrder
    {
        $kotOrder->update(['priority' => $priority]);

        Log::info('KOT priority updated', [
            'kot_id' => $kotOrder->id,
            'kot_number' => $kotOrder->kot_number,
            'new_priority' => $priority
        ]);

        return $kotOrder->fresh();
    }

    /**
     * Assign KOT to user.
     */
    public function assignKot(KotOrder $kotOrder, int $userId): KotOrder
    {
        $kotOrder->update(['assigned_to' => $userId]);

        Log::info('KOT assigned', [
            'kot_id' => $kotOrder->id,
            'kot_number' => $kotOrder->kot_number,
            'assigned_to' => $userId
        ]);

        return $kotOrder->fresh();
    }

    /**
     * Get KOT statistics for dashboard.
     */
    public function getKotStatistics(int $kitchenId = null): array
    {
        $query = KotOrder::query();

        if ($kitchenId) {
            $query->where('kitchen_id', $kitchenId);
        }

        $today = now()->startOfDay();

        return [
            'total_today' => $query->clone()->whereDate('created_at', $today)->count(),
            'pending' => $query->clone()->where('status', 'pending')->count(),
            'preparing' => $query->clone()->where('status', 'preparing')->count(),
            'ready' => $query->clone()->where('status', 'ready')->count(),
            'completed_today' => $query->clone()->where('status', 'completed')->whereDate('completed_at', $today)->count(),
            'overdue' => $this->getOverdueKotsCount($kitchenId),
            'average_prep_time' => $this->getAveragePrepTime($kitchenId),
        ];
    }

    /**
     * Get overdue KOTs count.
     */
    private function getOverdueKotsCount(int $kitchenId = null): int
    {
        $query = KotOrder::whereIn('status', ['pending', 'preparing'])
            ->whereNotNull('estimated_prep_time_minutes');

        if ($kitchenId) {
            $query->where('kitchen_id', $kitchenId);
        }

        $overdueCount = 0;
        $kots = $query->get();

        foreach ($kots as $kot) {
            if ($kot->isOverdue()) {
                $overdueCount++;
            }
        }

        return $overdueCount;
    }

    /**
     * Get average prep time for completed KOTs.
     */
    private function getAveragePrepTime(int $kitchenId = null): float
    {
        $query = KotOrder::where('status', 'completed')
            ->whereNotNull('actual_prep_time_minutes')
            ->whereDate('completed_at', '>=', now()->subDays(7)); // Last 7 days

        if ($kitchenId) {
            $query->where('kitchen_id', $kitchenId);
        }

        return $query->avg('actual_prep_time_minutes') ?? 0;
    }
}
