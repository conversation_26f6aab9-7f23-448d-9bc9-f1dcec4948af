<?php

namespace Modules\Kitchen\Models;

use App\Models\MenuItem;
use App\Models\OrderItem;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class KotOrderItem extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'kot_order_id',
        'order_item_id',
        'menu_item_id',
        'quantity',
        'status',
        'special_instructions',
        'modifications',
        'prep_time_minutes',
        'started_at',
        'completed_at',
        'notes',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'modifications' => 'array',
            'quantity' => 'integer',
            'prep_time_minutes' => 'integer',
            'started_at' => 'datetime',
            'completed_at' => 'datetime',
        ];
    }

    /**
     * Get the KOT order that owns the item.
     */
    public function kotOrder(): BelongsTo
    {
        return $this->belongsTo(KotOrder::class);
    }

    /**
     * Get the original order item.
     */
    public function orderItem(): BelongsTo
    {
        return $this->belongsTo(OrderItem::class);
    }

    /**
     * Get the menu item.
     */
    public function menuItem(): BelongsTo
    {
        return $this->belongsTo(MenuItem::class);
    }

    /**
     * Scope to get items by status.
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get pending items.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get preparing items.
     */
    public function scopePreparing($query)
    {
        return $query->where('status', 'preparing');
    }

    /**
     * Scope to get ready items.
     */
    public function scopeReady($query)
    {
        return $query->where('status', 'ready');
    }

    /**
     * Scope to get completed items.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Start preparing the item.
     */
    public function start(): void
    {
        $this->update([
            'status' => 'preparing',
            'started_at' => now(),
        ]);
    }

    /**
     * Mark the item as ready.
     */
    public function markReady(): void
    {
        $this->update([
            'status' => 'ready',
        ]);
    }

    /**
     * Complete the item.
     */
    public function complete(): void
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
        ]);
    }

    /**
     * Cancel the item.
     */
    public function cancel(string $reason = null): void
    {
        $this->update([
            'status' => 'cancelled',
            'notes' => $reason ? "Cancelled: {$reason}" : 'Cancelled',
        ]);
    }

    /**
     * Check if item is overdue.
     */
    public function isOverdue(): bool
    {
        if (!$this->prep_time_minutes || $this->status === 'completed') {
            return false;
        }

        $expectedCompletionTime = $this->created_at->addMinutes($this->prep_time_minutes);
        return now()->gt($expectedCompletionTime);
    }

    /**
     * Get the time elapsed since item preparation started.
     */
    public function getElapsedTimeMinutes(): int
    {
        $startTime = $this->started_at ?? $this->created_at;
        return $startTime->diffInMinutes(now());
    }
}
