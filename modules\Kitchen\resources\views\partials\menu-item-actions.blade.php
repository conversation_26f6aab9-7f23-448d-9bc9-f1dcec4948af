<div class="btn-group" role="group">
    <button type="button" class="btn btn-sm btn-primary edit-menu-item" 
            data-kitchen-id="{{ $kitchen->id }}"
            data-menu-item-id="{{ $kitchenMenuItem->menu_item_id }}"
            data-prep-time="{{ $kitchenMenuItem->prep_time_minutes }}"
            data-priority="{{ $kitchenMenuItem->priority_level }}"
            data-instructions="{{ $kitchenMenuItem->special_instructions }}"
            data-active="{{ $kitchenMenuItem->is_active ? 'true' : 'false' }}"
            title="Edit Assignment">
        <i class="fas fa-edit"></i>
    </button>
    
    <button type="button" class="btn btn-sm {{ $kitchenMenuItem->is_active ? 'btn-warning' : 'btn-success' }} toggle-menu-item-status" 
            data-kitchen-id="{{ $kitchen->id }}"
            data-menu-item-id="{{ $kitchenMenuItem->menu_item_id }}"
            data-current-status="{{ $kitchenMenuItem->is_active ? 'active' : 'inactive' }}"
            title="{{ $kitchenMenuItem->is_active ? 'Deactivate' : 'Activate' }}">
        <i class="fas fa-{{ $kitchenMenuItem->is_active ? 'pause' : 'play' }}"></i>
    </button>
    
    <button type="button" class="btn btn-sm btn-danger remove-menu-item" 
            data-kitchen-id="{{ $kitchen->id }}"
            data-menu-item-id="{{ $kitchenMenuItem->menu_item_id }}"
            data-menu-item-name="{{ $kitchenMenuItem->menuItem?->name ?? 'Unknown Item' }}"
            title="Remove from Kitchen">
        <i class="fas fa-trash"></i>
    </button>
</div>