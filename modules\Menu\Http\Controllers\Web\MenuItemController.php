<?php

namespace Modules\Menu\Http\Controllers\Web;

use App\Models\MenuItem;
use Illuminate\Http\Request;
use App\Helpers\BranchHelper;
use App\Helpers\ResponseHelper;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Modules\Menu\Services\MenuService;
use Yajra\DataTables\Facades\DataTables;

use Modules\Menu\Services\CategoryService;
use Modules\Menu\Services\MenuItemService;
use Modules\Menu\Http\Requests\StoreMenuItemRequest;
use Modules\Menu\Http\Requests\UpdateMenuItemRequest;

class MenuItemController extends Controller
{
    protected $menuItemService;
    protected $categoryService;
    protected $menuService;

    public function __construct(MenuItemService $menuItemService, CategoryService $categoryService, MenuService $menuService)
    {
        $this->menuItemService = $menuItemService;
        $this->categoryService = $categoryService;
        $this->menuService = $menuService;
    }

    /**
     * Display menu items datatable
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $branchId = BranchHelper::getCurrentBranchId();
            
            // Get menu items query for DataTable
            $query = MenuItem::with(['menu:id,name', 'category:id,name'])
                ->select('id', 'menu_id', 'category_id', 'name', 'code', 'base_price', 'image', 'is_active', 'is_featured', 'created_at')
                ->whereHas('menu', function ($q) use ($branchId) {
                    $q->where('branch_id', $branchId);
                });

            // Apply filters
            if ($request->filled('menu_id')) {
                $query->where('menu_id', $request->menu_id);
            }

            if ($request->filled('category_id')) {
                $query->where('category_id', $request->category_id);
            }

            if ($request->filled('status')) {
                if ($request->status === 'active') {
                    $query->where('is_active', true);
                } elseif ($request->status === 'inactive') {
                    $query->where('is_active', false);
                }
            }

            if ($request->filled('featured')) {
                if ($request->featured === 'yes') {
                    $query->where('is_featured', true);
                } elseif ($request->featured === 'no') {
                    $query->where('is_featured', false);
                }
            }

            return DataTables::of($query)
                ->addIndexColumn()
                ->addColumn('action', function($row) {
                    $btn = '<div class="flex gap-2">';
                    $btn .= '<button type="button" class="show-item bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded-md text-sm transition-colors duration-200" data-id="'.$row->id.'" title="عرض"><i class="fas fa-eye"></i></button>';
                    $btn .= '<button type="button" class="edit-item bg-yellow-500 hover:bg-yellow-600 text-white px-3 py-1 rounded-md text-sm transition-colors duration-200" data-id="'.$row->id.'" title="تعديل"><i class="fas fa-edit"></i></button>';
                    $btn .= '<button type="button" class="delete-item bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded-md text-sm transition-colors duration-200" data-id="'.$row->id.'" title="حذف"><i class="fas fa-trash"></i></button>';
                    $btn .= '</div>';
                    return $btn;
                })
                ->editColumn('is_active', function($row) {
                    return $row->is_active ? '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">نشط</span>' : '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">غير نشط</span>';
                })
                ->editColumn('is_featured', function($row) {
                    return $row->is_featured ? '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"><i class="fas fa-star mr-1"></i>مميز</span>' : '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">عادي</span>';
                })
                ->editColumn('image', function($row) {
                    if ($row->image) {
                        return '<img src="'.$row->image.'" alt="'.$row->name.'" class="menu-item-thumbnail">';
                    }
                    return '<div class="menu-item-thumbnail-placeholder"><i class="fas fa-utensils"></i></div>';
                })
                ->editColumn('name', function($row) {
                    return '<span class="font-medium text-gray-900">' . $row->name . '</span>';
                })
                ->editColumn('code', function($row) {
                    return '<span class="font-mono text-sm bg-gray-100 px-2 py-1 rounded">' . $row->code . '</span>';
                })
                ->editColumn('base_price', function($row) {
                    return '<span class="font-medium text-gray-900">' . number_format($row->base_price, 2) . ' ر.س</span>';
                })
                ->editColumn('created_at', function($row) {
                    return $row->created_at ? '<span class="text-sm text-gray-500">' . $row->created_at->format('Y-m-d H:i') . '</span>' : '-';
                })
                ->addColumn('category_name', function($row) {
                    return $row->category ? '<span class="text-sm text-gray-700">' . $row->category->name . '</span>' : '<span class="text-sm text-gray-400">-</span>';
                })
                ->addColumn('menu_name', function($row) {
                    return $row->menu ? '<span class="text-sm text-gray-700">' . $row->menu->name . '</span>' : '<span class="text-sm text-gray-400">-</span>';
                })
        
                ->rawColumns(['action', 'is_active', 'is_featured', 'image', 'name', 'code', 'base_price', 'created_at', 'category_name', 'menu_name'])
                ->make(true);
        }

        return view('menu::menu-items');
    }

    /**
     * Store a newly created menu item
     */
    public function store(StoreMenuItemRequest $request): JsonResponse
    {
        try {
            $data = BranchHelper::addBranchAndTenantIds($request->validated());
            $menuItem = $this->menuItemService->createMenuItemForWeb($data);
            
            return ResponseHelper::success($menuItem, 'تم إضافة عنصر القائمة بنجاح');
        } catch (\Exception $e) {
            return ResponseHelper::error('فشل في إضافة عنصر القائمة: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified menu item
     */
    public function show($id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $menuItem = $this->menuItemService->getMenuItemByIdForBranch($id, $branchId);
            
            if (!$menuItem) {
                return ResponseHelper::notFound('عنصر القائمة غير موجود');
            }
            
            return ResponseHelper::success($menuItem);
        } catch (\Exception $e) {
            return ResponseHelper::error('فشل في استرجاع عنصر القائمة: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for editing the specified menu item
     */
    public function edit($id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $menuItem = $this->menuItemService->getMenuItemByIdForBranch($id, $branchId);
            
            if (!$menuItem) {
                return ResponseHelper::notFound('عنصر القائمة غير موجود');
            }
            
            return ResponseHelper::success($menuItem);
        } catch (\Exception $e) {
            return ResponseHelper::error('فشل في استرجاع عنصر القائمة للتعديل: ' . $e->getMessage());
        }
    }

    /**
     * Update the specified menu item
     */
    public function update(UpdateMenuItemRequest $request, $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $data = BranchHelper::addBranchAndTenantIds($request->validated());
            
            $menuItem = $this->menuItemService->updateMenuItemForWeb($id, $data, $branchId);
            
            if (!$menuItem) {
                return ResponseHelper::notFound('عنصر القائمة غير موجود');
            }
            
            return ResponseHelper::success($menuItem, 'تم تحديث عنصر القائمة بنجاح');
        } catch (\Exception $e) {
            return ResponseHelper::error('فشل في تحديث عنصر القائمة: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified menu item
     */
    public function destroy($id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            
            // First check if the menu item exists and belongs to the branch
            $menuItem = $this->menuItemService->getMenuItemByIdForBranch($id, $branchId);
            if (!$menuItem) {
                return ResponseHelper::notFound('عنصر القائمة غير موجود');
            }
            
            $deleted = $this->menuItemService->deleteMenuItem($id);
            
            if (!$deleted) {
                return ResponseHelper::error('فشل في حذف عنصر القائمة');
            }
            
            return ResponseHelper::success(null, 'تم حذف عنصر القائمة بنجاح');
        } catch (\Exception $e) {
            return ResponseHelper::error('فشل في حذف عنصر القائمة: ' . $e->getMessage());
        }
    }

    /**
     * Get menu items list for dropdowns
     */
    public function getMenuItemsList(Request $request): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $categoryId = $request->get('category_id');
            $menuItems = $this->menuItemService->getMenuItemsListForBranch($branchId, $categoryId);
            
            return ResponseHelper::success($menuItems);
        } catch (\Exception $e) {
            return ResponseHelper::error('فشل في استرجاع قائمة عناصر القائمة: ' . $e->getMessage());
        }
    }

    /**
     * Get categories list for dropdowns
     */
    public function getCategoriesList(): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $categories = $this->categoryService->getCategoriesListForBranch($branchId);
            
            return ResponseHelper::success($categories);
        } catch (\Exception $e) {
            return ResponseHelper::error('فشل في استرجاع قائمة الفئات: ' . $e->getMessage());
        }
    }

    /**
     * Get menus list for dropdowns
     */
    public function getMenusList(): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $menus = $this->menuService->getMenusListForBranch($branchId);
            
            return ResponseHelper::success($menus);
        } catch (\Exception $e) {
            return ResponseHelper::error('فشل في استرجاع قائمة القوائم: ' . $e->getMessage());
        }
    }
}