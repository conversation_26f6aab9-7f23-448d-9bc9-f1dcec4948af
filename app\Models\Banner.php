<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Banner Model
 * 
 * Manages promotional banners, advertisements, and visual content
 * displayed across the restaurant's digital platforms.
 * 
 * @package App\Models
 * <AUTHOR> POS Team
 * @version 1.0.0
 */
class Banner extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'branch_id',
        'title',
        'subtitle',
        'description',
        'banner_type',
        'position',
        'display_location',
        'image_url',
        'mobile_image_url',
        'thumbnail_url',
        'link_url',
        'link_text',
        'link_target',
        'call_to_action',
        'start_date',
        'end_date',
        'start_time',
        'end_time',
        'target_audience',
        'display_rules',
        'priority',
        'max_impressions',
        'current_impressions',
        'max_clicks',
        'current_clicks',
        'click_through_rate',
        'is_animated',
        'animation_type',
        'animation_duration',
        'auto_hide',
        'auto_hide_delay',
        'is_dismissible',
        'is_responsive',
        'responsive_settings',
        'background_color',
        'text_color',
        'custom_css',
        'is_active',
        'is_featured',
        'sort_order',
        'analytics_data',
        'metadata',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'call_to_action' => 'array',
            'start_date' => 'datetime',
            'end_date' => 'datetime',
            'start_time' => 'datetime:H:i',
            'end_time' => 'datetime:H:i',
            'target_audience' => 'array',
            'display_rules' => 'array',
            'priority' => 'integer',
            'max_impressions' => 'integer',
            'current_impressions' => 'integer',
            'max_clicks' => 'integer',
            'current_clicks' => 'integer',
            'click_through_rate' => 'decimal:2',
            'is_animated' => 'boolean',
            'animation_duration' => 'integer',
            'auto_hide' => 'boolean',
            'auto_hide_delay' => 'integer',
            'is_dismissible' => 'boolean',
            'is_responsive' => 'boolean',
            'responsive_settings' => 'array',
            'custom_css' => 'array',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'sort_order' => 'integer',
            'analytics_data' => 'array',
            'metadata' => 'array',
        ];
    }

    // Relationships

    /**
     * Get the tenant that owns the banner.
     */
    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the branch that owns the banner.
     */
    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    // Scopes

    /**
     * Scope to get banners for a specific branch
     */
    public function scopeForBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * Scope to get active banners
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get featured banners
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope to get current banners (should be displayed now)
     */
    public function scopeCurrent($query)
    {
        $now = now();
        return $query->where('start_date', '<=', $now)
                    ->where('end_date', '>=', $now)
                    ->where('is_active', true);
    }

    /**
     * Scope to get banners by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('banner_type', $type);
    }

    /**
     * Scope to get banners by position
     */
    public function scopeByPosition($query, $position)
    {
        return $query->where('position', $position);
    }

    /**
     * Scope to get banners by display location
     */
    public function scopeByLocation($query, $location)
    {
        return $query->where('display_location', $location)
                    ->orWhere('display_location', 'all');
    }

    /**
     * Scope to order by priority
     */
    public function scopeByPriority($query)
    {
        return $query->orderBy('priority', 'desc')
                    ->orderBy('sort_order', 'asc');
    }

    // Helper Methods

    /**
     * Check if banner should be displayed now
     */
    public function shouldDisplay()
    {
        if (!$this->is_active) {
            return false;
        }

        $now = now();
        
        // Check date range
        if ($now < $this->start_date || $now > $this->end_date) {
            return false;
        }

        // Check time range if specified
        if ($this->start_time && $this->end_time) {
            $currentTime = $now->format('H:i');
            if ($currentTime < $this->start_time->format('H:i') || 
                $currentTime > $this->end_time->format('H:i')) {
                return false;
            }
        }

        // Check impression limits
        if ($this->max_impressions && $this->current_impressions >= $this->max_impressions) {
            return false;
        }

        // Check click limits
        if ($this->max_clicks && $this->current_clicks >= $this->max_clicks) {
            return false;
        }

        return true;
    }

    /**
     * Record an impression
     */
    public function recordImpression()
    {
        $this->increment('current_impressions');
        
        // Update analytics data
        $analytics = $this->analytics_data ?? [];
        $today = now()->format('Y-m-d');
        
        if (!isset($analytics['daily_impressions'])) {
            $analytics['daily_impressions'] = [];
        }
        
        if (!isset($analytics['daily_impressions'][$today])) {
            $analytics['daily_impressions'][$today] = 0;
        }
        
        $analytics['daily_impressions'][$today]++;
        $this->update(['analytics_data' => $analytics]);
    }

    /**
     * Record a click
     */
    public function recordClick()
    {
        $this->increment('current_clicks');
        
        // Update click-through rate
        if ($this->current_impressions > 0) {
            $ctr = ($this->current_clicks / $this->current_impressions) * 100;
            $this->update(['click_through_rate' => round($ctr, 2)]);
        }
        
        // Update analytics data
        $analytics = $this->analytics_data ?? [];
        $today = now()->format('Y-m-d');
        
        if (!isset($analytics['daily_clicks'])) {
            $analytics['daily_clicks'] = [];
        }
        
        if (!isset($analytics['daily_clicks'][$today])) {
            $analytics['daily_clicks'][$today] = 0;
        }
        
        $analytics['daily_clicks'][$today]++;
        $this->update(['analytics_data' => $analytics]);
    }

    /**
     * Get banner status
     */
    public function getStatus()
    {
        if (!$this->is_active) {
            return 'inactive';
        }

        $now = now();
        
        if ($now < $this->start_date) {
            return 'scheduled';
        }
        
        if ($now > $this->end_date) {
            return 'expired';
        }
        
        if ($this->max_impressions && $this->current_impressions >= $this->max_impressions) {
            return 'impression_limit_reached';
        }
        
        if ($this->max_clicks && $this->current_clicks >= $this->max_clicks) {
            return 'click_limit_reached';
        }
        
        return 'active';
    }

    /**
     * Get performance metrics
     */
    public function getPerformanceMetrics()
    {
        return [
            'impressions' => $this->current_impressions,
            'clicks' => $this->current_clicks,
            'click_through_rate' => $this->click_through_rate,
            'impression_limit' => $this->max_impressions,
            'click_limit' => $this->max_clicks,
            'status' => $this->getStatus(),
        ];
    }

    /**
     * Check if banner has reached its limits
     */
    public function hasReachedLimits()
    {
        if ($this->max_impressions && $this->current_impressions >= $this->max_impressions) {
            return true;
        }
        
        if ($this->max_clicks && $this->current_clicks >= $this->max_clicks) {
            return true;
        }
        
        return false;
    }
}