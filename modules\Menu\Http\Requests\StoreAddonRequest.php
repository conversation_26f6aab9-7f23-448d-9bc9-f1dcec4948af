<?php

namespace Modules\Menu\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Helpers\BranchHelper;

class StoreAddonRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $branchId = BranchHelper::getCurrentBranchId();

        return [
            'menu_item_id' => [
                'required',
                'integer',
                function ($attribute, $value, $fail) use ($branchId) {
                    // If no branch ID, fail validation
                    if (!$branchId) {
                        $fail('المستخدم غير مرتبط بأي فرع');
                        return;
                    }

                    // Check if menu item exists and belongs to the user's branch
                    $menuItem = \App\Models\MenuItem::whereHas('menu', function ($q) use ($branchId) {
                        $q->where('branch_id', $branchId);
                    })->where('id', $value)->where('is_active', true)->first();

                    if (!$menuItem) {
                        $fail('عنصر القائمة المحدد غير موجود أو غير متاح لهذا الفرع');
                    }
                }
            ],
            'addon_group_name' => 'nullable|string|max:100',
            'name' => 'required|string|max:100',
            'code' => 'nullable|string|max:50',
            'price' => 'required|numeric|min:0',
            'cost' => 'nullable|numeric|min:0',
            'is_required' => 'boolean',
            'max_quantity' => 'integer|min:1',
            'sort_order' => 'integer|min:0',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'menu_item_id.required' => 'معرف عنصر القائمة مطلوب',
            'menu_item_id.exists' => 'عنصر القائمة المحدد غير موجود',
            'addon_group_name.string' => 'اسم مجموعة الإضافة يجب أن يكون نص',
            'addon_group_name.max' => 'اسم مجموعة الإضافة يجب ألا يتجاوز 100 حرف',
            'name.required' => 'اسم الإضافة مطلوب',
            'name.string' => 'اسم الإضافة يجب أن يكون نص',
            'name.max' => 'اسم الإضافة يجب ألا يتجاوز 100 حرف',
            'code.required' => 'كود الإضافة مطلوب',
            'code.string' => 'كود الإضافة يجب أن يكون نص',
            'code.max' => 'كود الإضافة يجب ألا يتجاوز 50 حرف',
            'price.required' => 'سعر الإضافة مطلوب',
            'price.numeric' => 'سعر الإضافة يجب أن يكون رقم',
            'price.min' => 'سعر الإضافة يجب أن يكون على الأقل 0',
            'cost.numeric' => 'تكلفة الإضافة يجب أن تكون رقم',
            'cost.min' => 'تكلفة الإضافة يجب أن تكون على الأقل 0',
            'max_quantity.integer' => 'الحد الأقصى للكمية يجب أن يكون رقم صحيح',
            'max_quantity.min' => 'الحد الأقصى للكمية يجب أن يكون على الأقل 1',
            'sort_order.integer' => 'ترتيب العرض يجب أن يكون رقم صحيح',
            'sort_order.min' => 'ترتيب العرض يجب أن يكون على الأقل 0',
        ];
    }
}