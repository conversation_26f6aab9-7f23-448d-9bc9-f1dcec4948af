@extends('layouts.master')

@section('content')
<!-- <PERSON> Header -->
<div class="mb-6">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="mb-4 sm:mb-0">
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-truck mr-3"></i>
                تعيينات التوصيل
            </h1>
            <p class="text-sm text-gray-600 mt-1">إدارة تعيينات الطلبات لموظفي التوصيل</p>
        </div>
        <div class="flex space-x-2">
            <button type="button" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors duration-200" id="refreshStats">
                <i class="fas fa-sync-alt mr-2"></i>
                تحديث
            </button>
            <button type="button" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-md transition-colors duration-200" onclick="openModal('createAssignmentModal')">
                <i class="fas fa-plus mr-2"></i>
                تعيين جديد
            </button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6" id="statisticsCards">
    <div class="bg-blue-500 rounded-lg shadow-sm p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-2xl font-bold" id="totalAssignments">0</h3>
                <p class="text-blue-100">إجمالي التعيينات</p>
            </div>
            <div class="text-blue-200">
                <i class="fas fa-clipboard-list text-3xl"></i>
            </div>
        </div>
    </div>
    <div class="bg-yellow-500 rounded-lg shadow-sm p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-2xl font-bold" id="pendingAssignments">0</h3>
                <p class="text-yellow-100">في الانتظار</p>
            </div>
            <div class="text-yellow-200">
                <i class="fas fa-clock text-3xl"></i>
            </div>
        </div>
    </div>
    <div class="bg-indigo-500 rounded-lg shadow-sm p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-2xl font-bold" id="inTransitAssignments">0</h3>
                <p class="text-indigo-100">في الطريق</p>
            </div>
            <div class="text-indigo-200">
                <i class="fas fa-shipping-fast text-3xl"></i>
            </div>
        </div>
    </div>
    <div class="bg-green-500 rounded-lg shadow-sm p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-2xl font-bold" id="deliveredAssignments">0</h3>
                <p class="text-green-100">تم التوصيل</p>
            </div>
            <div class="text-green-200">
                <i class="fas fa-check-circle text-3xl"></i>
            </div>
        </div>
    </div>
    <div class="bg-red-500 rounded-lg shadow-sm p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-2xl font-bold" id="cancelledAssignments">0</h3>
                <p class="text-red-100">ملغية/فاشلة</p>
            </div>
            <div class="text-red-200">
                <i class="fas fa-times-circle text-3xl"></i>
            </div>
        </div>
    </div>
    <div class="bg-gray-500 rounded-lg shadow-sm p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-2xl font-bold" id="completionRate">0%</h3>
                <p class="text-gray-100">معدل الإنجاز</p>
            </div>
            <div class="text-gray-200">
                <i class="fas fa-chart-pie text-3xl"></i>
            </div>
        </div>
    </div>
</div>

    <!-- Filters -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Filters</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <form id="filtersForm">
                        <div class="row">
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="filterBranch">Branch</label>
                                    <select class="form-control" id="filterBranch" name="branch_id">
                                        <option value="">All Branches</option>
                                        @foreach($branches as $branch)
                                            <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="filterPersonnel">Personnel</label>
                                    <select class="form-control" id="filterPersonnel" name="personnel_id">
                                        <option value="">All Personnel</option>
                                        @foreach($personnel as $person)
                                            <option value="{{ $person->id }}">{{ $person->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="filterStatus">Status</label>
                                    <select class="form-control" id="filterStatus" name="status">
                                        <option value="">All Statuses</option>
                                        <option value="pending">Pending</option>
                                        <option value="assigned">Assigned</option>
                                        <option value="picked_up">Picked Up</option>
                                        <option value="in_transit">In Transit</option>
                                        <option value="delivered">Delivered</option>
                                        <option value="cancelled">Cancelled</option>
                                        <option value="failed">Failed</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="filterDateFrom">Date From</label>
                                    <input type="date" class="form-control" id="filterDateFrom" name="date_from">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="filterDateTo">Date To</label>
                                    <input type="date" class="form-control" id="filterDateTo" name="date_to">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="button" class="btn btn-primary btn-block" id="applyFilters">
                                            <i class="fas fa-filter"></i> Apply
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <button type="button" class="btn btn-secondary btn-sm" id="clearFilters">
                                    <i class="fas fa-times"></i> Clear Filters
                                </button>
                                <button type="button" class="btn btn-info btn-sm" id="exportData">
                                    <i class="fas fa-download"></i> Export
                                </button>
                                <button type="button" class="btn btn-warning btn-sm" id="bulkActions" disabled>
                                    <i class="fas fa-tasks"></i> Bulk Actions (<span id="selectedCount">0</span>)
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Assignments List</h3>
                    <div class="card-tools">
                        <div class="input-group input-group-sm" style="width: 250px;">
                            <input type="text" name="table_search" class="form-control float-right" placeholder="Search assignments..." id="tableSearch">
                            <div class="input-group-append">
                                <button type="submit" class="btn btn-default">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body table-responsive p-0">
                    <table class="table table-hover text-nowrap" id="assignmentsTable">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="selectAll">
                                </th>
                                <th>ID</th>
                                <th>Order #</th>
                                <th>Customer</th>
                                <th>Phone</th>
                                <th>Personnel</th>
                                <th>Branch</th>
                                <th>Address</th>
                                <th>Status</th>
                                <th>Estimated</th>
                                <th>Delivered</th>
                                <th>Fee</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data will be loaded via DataTables -->
                        </tbody>
                    </table>
                </div>
                <div class="card-footer">
                    <div class="row">
                        <div class="col-sm-12 col-md-5">
                            <div class="dataTables_info" id="assignmentsTable_info" role="status" aria-live="polite"></div>
                        </div>
                        <div class="col-sm-12 col-md-7">
                            <div class="dataTables_paginate paging_simple_numbers" id="assignmentsTable_paginate"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include Modals -->
@include('delivery::assignments.partials.modals')

@endsection

@push('styles')
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.bootstrap4.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/select/1.3.4/css/select.bootstrap4.min.css">
@endpush

@push('scripts')
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.bootstrap4.min.js"></script>
<script src="https://cdn.datatables.net/select/1.3.4/js/dataTables.select.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable
    let table = $('#assignmentsTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("delivery.assignments.data") }}',
            data: function(d) {
                d.branch_id = $('#filterBranch').val();
                d.personnel_id = $('#filterPersonnel').val();
                d.status = $('#filterStatus').val();
                d.date_from = $('#filterDateFrom').val();
                d.date_to = $('#filterDateTo').val();
            }
        },
        columns: [
            { data: 'id', name: 'id', orderable: false, searchable: false, render: function(data) {
                return '<input type="checkbox" class="row-select" value="' + data + '">';
            }},
            { data: 'id', name: 'id' },
            { data: 'order_number', name: 'order_number' },
            { data: 'customer_name', name: 'customer_name' },
            { data: 'customer_phone', name: 'customer_phone' },
            { data: 'personnel_name', name: 'personnel_name' },
            { data: 'branch_name', name: 'branch_name' },
            { data: 'delivery_address', name: 'delivery_address' },
            { data: 'status_badge', name: 'status', orderable: false },
            { data: 'estimated_delivery', name: 'estimated_delivery_time' },
            { data: 'actual_delivery', name: 'delivered_at' },
            { data: 'delivery_fee', name: 'delivery_fee' },
            { data: 'actions', name: 'actions', orderable: false, searchable: false }
        ],
        order: [[1, 'desc']],
        pageLength: 25,
        responsive: true,
        dom: 'Bfrtip',
        buttons: [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ]
    });

    // Load statistics
    function loadStatistics() {
        $.get('{{ route("delivery.assignments.statistics") }}', {
            date_from: $('#filterDateFrom').val(),
            date_to: $('#filterDateTo').val()
        })
        .done(function(data) {
            $('#totalAssignments').text(data.total_assignments);
            $('#pendingAssignments').text(data.pending_assignments);
            $('#inTransitAssignments').text(data.in_transit_assignments);
            $('#deliveredAssignments').text(data.delivered_assignments);
            $('#cancelledAssignments').text(data.cancelled_assignments);
            $('#completionRate').text(data.completion_rate + '%');
        });
    }

    // Initial load
    loadStatistics();

    // Apply filters
    $('#applyFilters').click(function() {
        table.ajax.reload();
        loadStatistics();
    });

    // Clear filters
    $('#clearFilters').click(function() {
        $('#filtersForm')[0].reset();
        table.ajax.reload();
        loadStatistics();
    });

    // Refresh stats
    $('#refreshStats').click(function() {
        loadStatistics();
        table.ajax.reload();
    });

    // Select all checkbox
    $('#selectAll').change(function() {
        $('.row-select').prop('checked', this.checked);
        updateBulkActionsButton();
    });

    // Individual row selection
    $(document).on('change', '.row-select', function() {
        updateBulkActionsButton();
    });

    function updateBulkActionsButton() {
        let selectedCount = $('.row-select:checked').length;
        $('#selectedCount').text(selectedCount);
        $('#bulkActions').prop('disabled', selectedCount === 0);
    }

    // Bulk actions
    $('#bulkActions').click(function() {
        let selectedIds = $('.row-select:checked').map(function() {
            return this.value;
        }).get();
        
        if (selectedIds.length > 0) {
            $('#bulkActionsModal').modal('show');
        }
    });

    // Assign personnel modal
    $(document).on('click', '.assign-personnel', function() {
        let assignmentId = $(this).data('assignment-id');
        $('#assignmentId').val(assignmentId);
        loadAvailablePersonnel('#personnelSelect');
        $('#assignPersonnelModal').modal('show');
    });

    // Update status modal
    $(document).on('click', '.update-status', function() {
        let assignmentId = $(this).data('assignment-id');
        $('#statusAssignmentId').val(assignmentId);
        $('#updateStatusModal').modal('show');
    });

    // Track assignment modal
    $(document).on('click', '.track-assignment', function() {
        let assignmentId = $(this).data('assignment-id');
        loadTrackingData(assignmentId);
        $('#trackAssignmentModal').modal('show');
    });

    // Confirm assign personnel
    $('#confirmAssign').click(function() {
        let formData = $('#assignPersonnelForm').serialize();
        let assignmentId = $('#assignmentId').val();
        
        $.post('{{ route("delivery.assignments.assign-personnel", ":id") }}'.replace(':id', assignmentId), formData)
        .done(function(response) {
            if (response.success) {
                $('#assignPersonnelModal').modal('hide');
                table.ajax.reload();
                showAlert('success', response.message);
            } else {
                showAlert('error', response.message);
            }
        })
        .fail(function() {
            showAlert('error', 'Failed to assign personnel');
        });
    });

    // Confirm status update
    $('#confirmStatusUpdate').click(function() {
        let formData = $('#updateStatusForm').serialize();
        let assignmentId = $('#statusAssignmentId').val();
        
        $.post('{{ route("delivery.assignments.update-status", ":id") }}'.replace(':id', assignmentId), formData)
        .done(function(response) {
            if (response.success) {
                $('#updateStatusModal').modal('hide');
                table.ajax.reload();
                loadStatistics();
                showAlert('success', response.message);
            } else {
                showAlert('error', response.message);
            }
        })
        .fail(function() {
            showAlert('error', 'Failed to update status');
        });
    });

    // Load available personnel
    function loadAvailablePersonnel(selector) {
        $.get('{{ route("delivery.assignments.available-personnel") }}')
        .done(function(data) {
            let options = '<option value="">Select personnel...</option>';
            data.forEach(function(person) {
                options += `<option value="${person.id}">${person.name} (${person.vehicle_type})</option>`;
            });
            $(selector).html(options);
        });
    }

    // Load tracking data
    function loadTrackingData(assignmentId) {
        // This would load real tracking data from the server
        $('#trackingStatus').text('Loading...');
        $('#trackingPersonnel').text('Loading...');
        $('#trackingLastUpdate').text('Loading...');
        $('#trackingEstimated').text('Loading...');
    }

    // Show alert function
    function showAlert(type, message) {
        let alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        let alert = `<div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>`;
        
        $('.container-fluid').prepend(alert);
        
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    }

    // Table search
    $('#tableSearch').on('keyup', function() {
        table.search(this.value).draw();
    });
});
</script>
@endpush