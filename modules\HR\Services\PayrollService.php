<?php

namespace Modules\HR\Services;

use App\Models\User;
use App\Models\PayPeriod;
use App\Models\Payslip;
use App\Models\StaffAttendance;
use App\Models\StaffPenalty;

class PayrollService
{
    private AttendanceService $attendanceService;

    public function __construct(AttendanceService $attendanceService)
    {
        $this->attendanceService = $attendanceService;
    }

    /**
     * Calculate staff salary for a pay period
     */
    public function calculateSalary(int $userId, PayPeriod $payPeriod): array
    {
        $user = User::findOrFail($userId);
        $workingHours = $this->getStaffWorkingHours(
            $user->tenant_id,
            $userId,
            $payPeriod->start_date->format('Y-m-d'),
            $payPeriod->end_date->format('Y-m-d')
        );

        $baseSalary = $user->base_salary ?? 0;
        $hourlyRate = $user->hourly_rate ?? 0;
        
        $regularPay = $workingHours['regular_hours'] * $hourlyRate;
        $overtimePay = $workingHours['overtime_hours'] * ($hourlyRate * 1.5);
        
        $latePenalty = $this->calculateLatePenalty($userId, $payPeriod);
        $absencePenalty = $this->calculateAbsencePenalty($userId, $payPeriod);
        $performanceBonus = $this->calculatePerformanceBonus($userId, $payPeriod);
        
        $grossSalary = $baseSalary + $regularPay + $overtimePay + $performanceBonus;
        $totalPenalties = $latePenalty + $absencePenalty;
        $netSalary = $grossSalary - $totalPenalties;

        return [
            'base_salary' => $baseSalary,
            'regular_pay' => $regularPay,
            'overtime_pay' => $overtimePay,
            'performance_bonus' => $performanceBonus,
            'gross_salary' => $grossSalary,
            'late_penalty' => $latePenalty,
            'absence_penalty' => $absencePenalty,
            'total_penalties' => $totalPenalties,
            'net_salary' => $netSalary,
            'working_hours' => $workingHours,
        ];
    }

    /**
     * Generate payslip
     */
    public function generatePayslip(int $userId, PayPeriod $payPeriod): Payslip
    {
        $salaryData = $this->calculateSalary($userId, $payPeriod);
        $user = User::findOrFail($userId);

        return Payslip::create([
            'tenant_id' => $user->tenant_id,
            'user_id' => $userId,
            'pay_period_id' => $payPeriod->id,
            'base_salary' => $salaryData['base_salary'],
            'regular_pay' => $salaryData['regular_pay'],
            'overtime_pay' => $salaryData['overtime_pay'],
            'bonuses' => $salaryData['performance_bonus'],
            'gross_salary' => $salaryData['gross_salary'],
            'deductions' => $salaryData['total_penalties'],
            'net_salary' => $salaryData['net_salary'],
            'working_hours' => $salaryData['working_hours']['total_hours'],
            'overtime_hours' => $salaryData['working_hours']['overtime_hours'],
            'generated_at' => now(),
            'status' => 'pending',
        ]);
    }

    /**
     * Get staff working hours for payroll - Optimized with database aggregation
     */
    public function getStaffWorkingHours(int $tenantId, int $userId, string $startDate, string $endDate): array
    {
        // Use database aggregation instead of PHP loops for better performance
        $stats = StaffAttendance::where('tenant_id', $tenantId)
                                ->where('user_id', $userId)
                                ->whereBetween('date', [$startDate, $endDate])
                                ->where('status', 'present')
                                ->whereNotNull('check_in_time')
                                ->whereNotNull('check_out_time')
                                ->selectRaw('
                                    COUNT(*) as working_days,
                                    SUM(late_minutes) as total_late_minutes,
                                    SUM(overtime_minutes) as total_overtime_minutes,
                                    SUM(
                                        TIMESTAMPDIFF(MINUTE, check_in_time, check_out_time) / 60
                                    ) as total_hours
                                ')
                                ->first();

        $totalHours = $stats->total_hours ?? 0;
        $workingDays = $stats->working_days ?? 0;
        $totalLateMinutes = $stats->total_late_minutes ?? 0;
        $overtimeHours = ($stats->total_overtime_minutes ?? 0) / 60;

        // Calculate regular hours (assuming 8 hours per day max for regular time)
        $regularHours = max(0, $totalHours - $overtimeHours);

        return [
            'total_hours' => round($totalHours, 2),
            'regular_hours' => round($regularHours, 2),
            'overtime_hours' => round($overtimeHours, 2),
            'total_late_minutes' => $totalLateMinutes,
            'working_days' => $workingDays,
        ];
    }

    /**
     * Calculate late penalty
     */
    private function calculateLatePenalty(int $userId, PayPeriod $payPeriod): float
    {
        $lateMinutes = StaffAttendance::where('user_id', $userId)
            ->whereBetween('date', [$payPeriod->start_date, $payPeriod->end_date])
            ->sum('late_minutes');

        $penaltyPerMinute = config('hr.penalty_per_minute', 1.0);
        return $lateMinutes * $penaltyPerMinute;
    }

    /**
     * Calculate absence penalty
     */
    private function calculateAbsencePenalty(int $userId, PayPeriod $payPeriod): float
    {
        $user = User::findOrFail($userId);
        $absentDays = StaffAttendance::where('user_id', $userId)
            ->whereBetween('date', [$payPeriod->start_date, $payPeriod->end_date])
            ->where('status', 'absent')
            ->count();

        $workingDaysPerMonth = config('hr.working_days_per_month', 30);
        $dailyRate = ($user->base_salary ?? 0) / $workingDaysPerMonth;
        return $absentDays * $dailyRate;
    }

    /**
     * Calculate performance bonus
     */
    private function calculatePerformanceBonus(int $userId, PayPeriod $payPeriod): float
    {
        $attendanceRate = $this->getStaffAttendanceRate($userId, $payPeriod);
        $user = User::findOrFail($userId);
        
        $bonusRates = config('hr.performance_bonus_rates', [
            95 => 0.1,  // 10% bonus for 95%+ attendance
            90 => 0.05, // 5% bonus for 90%+ attendance
        ]);

        foreach ($bonusRates as $threshold => $rate) {
            if ($attendanceRate >= $threshold) {
                return ($user->base_salary ?? 0) * $rate;
            }
        }
        
        return 0;
    }

    /**
     * Get staff attendance rate for a pay period
     */
    private function getStaffAttendanceRate(int $userId, PayPeriod $payPeriod): float
    {
        $totalDays = StaffAttendance::where('user_id', $userId)
            ->whereBetween('date', [$payPeriod->start_date, $payPeriod->end_date])
            ->count();

        $presentDays = StaffAttendance::where('user_id', $userId)
            ->whereBetween('date', [$payPeriod->start_date, $payPeriod->end_date])
            ->where('status', 'present')
            ->count();

        return $totalDays > 0 ? ($presentDays / $totalDays) * 100 : 0;
    }
}