<?php

// Test script for Orders API
echo "Testing Orders API...\n\n";

// Step 1: Login to get token
echo "1. Getting authentication token...\n";
$loginData = json_encode([
    'email' => '<EMAIL>',
    'password' => 'password'
]);

$loginContext = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => "Content-Type: application/json\r\n",
        'content' => $loginData
    ]
]);

$loginResponse = file_get_contents('http://localhost:8000/api/auth/login', false, $loginContext);
if ($loginResponse === false) {
    echo "❌ Failed to login\n";
    exit(1);
}

$loginResult = json_decode($loginResponse, true);
if (!isset($loginResult['data']['token'])) {
    echo "❌ No token in login response\n";
    echo "Response: " . $loginResponse . "\n";
    exit(1);
}

$token = $loginResult['data']['token'];
echo "✅ Token obtained: " . substr($token, 0, 20) . "...\n\n";

// Step 2: Test orders list endpoint
echo "2. Testing orders list endpoint...\n";
$ordersContext = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => "Authorization: Bearer $token\r\nAccept: application/json\r\n"
    ]
]);

$ordersResponse = file_get_contents('http://localhost:8000/api/orders', false, $ordersContext);
if ($ordersResponse === false) {
    echo "❌ Failed to get orders\n";
    $error = error_get_last();
    echo "Error: " . $error['message'] . "\n";
    exit(1);
}

echo "✅ Orders endpoint responded successfully\n";
echo "Response: " . $ordersResponse . "\n\n";

// Step 3: Test order statuses endpoint (public)
echo "3. Testing order statuses endpoint...\n";
$statusesResponse = file_get_contents('http://localhost:8000/api/order-statuses');
if ($statusesResponse === false) {
    echo "❌ Failed to get order statuses\n";
} else {
    echo "✅ Order statuses endpoint responded successfully\n";
    echo "Response: " . $statusesResponse . "\n\n";
}

// Step 4: Test order types endpoint (public)
echo "4. Testing order types endpoint...\n";
$typesResponse = file_get_contents('http://localhost:8000/api/order-types');
if ($typesResponse === false) {
    echo "❌ Failed to get order types\n";
} else {
    echo "✅ Order types endpoint responded successfully\n";
    echo "Response: " . $typesResponse . "\n\n";
}

echo "Orders API testing completed!\n";