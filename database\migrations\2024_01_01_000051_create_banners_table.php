<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('banners', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained()->onDelete('cascade');
            $table->foreignId('branch_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('title');
            $table->string('subtitle')->nullable();
            $table->text('description')->nullable();
            $table->string('banner_type')->default('promotional'); // promotional, informational, event, offer, seasonal
            $table->string('position')->default('header'); // header, footer, sidebar, popup, inline
            $table->string('display_location')->default('all'); // all, home, menu, checkout, specific_page
            $table->string('image_url');
            $table->string('mobile_image_url')->nullable();
            $table->string('thumbnail_url')->nullable();
            $table->string('link_url')->nullable();
            $table->string('link_text')->nullable();
            $table->string('link_target')->default('_self'); // _self, _blank
            $table->json('call_to_action')->nullable(); // Button text, URL, style
            $table->datetime('start_date');
            $table->datetime('end_date');
            $table->time('start_time')->nullable();
            $table->time('end_time')->nullable();
            $table->json('target_audience')->nullable(); // customer_types, age_groups, etc.
            $table->json('display_rules')->nullable(); // Conditions for showing banner
            $table->integer('priority')->default(0); // Higher number = higher priority
            $table->integer('max_impressions')->nullable();
            $table->integer('current_impressions')->default(0);
            $table->integer('max_clicks')->nullable();
            $table->integer('current_clicks')->default(0);
            $table->decimal('click_through_rate', 5, 2)->default(0);
            $table->boolean('is_animated')->default(false);
            $table->string('animation_type')->nullable(); // fade, slide, bounce, etc.
            $table->integer('animation_duration')->nullable(); // in milliseconds
            $table->boolean('auto_hide')->default(false);
            $table->integer('auto_hide_delay')->nullable(); // in seconds
            $table->boolean('is_dismissible')->default(true);
            $table->boolean('is_responsive')->default(true);
            $table->json('responsive_settings')->nullable();
            $table->string('background_color')->nullable();
            $table->string('text_color')->nullable();
            $table->json('custom_css')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('is_featured')->default(false);
            $table->integer('sort_order')->default(0);
            $table->json('analytics_data')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['tenant_id', 'branch_id']);
            $table->index(['start_date', 'end_date']);
            $table->index(['is_active', 'priority']);
            $table->index('banner_type');
            $table->index('position');
            $table->index('display_location');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('banners');
    }
};