<?php

namespace Modules\Menu\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Modules\Menu\Services\EventService;
use Modules\Menu\Http\Requests\StoreEventRequest;
use Modules\Menu\Http\Requests\UpdateEventRequest;
use App\Helpers\BranchHelper;
use App\Helpers\ResponseHelper;

/**
 * Event API Controller
 * 
 * Handles API requests for event management in the restaurant system.
 * Provides endpoints for CRUD operations, event registration, and analytics.
 * 
 * @package Modules\Menu\Http\Controllers\Api
 * <AUTHOR> POS Team
 * @version 1.0.0
 */
class EventController extends Controller
{
    protected EventService $eventService;

    public function __construct(EventService $eventService)
    {
        $this->eventService = $eventService;
    }

    /**
     * Display a listing of events
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            
            $filters = $request->only([
                'event_type', 'status', 'is_featured', 'date_from', 'date_to',
                'search', 'sort_by', 'sort_order'
            ]);

            $perPage = $request->get('per_page', 15);
            $events = $this->eventService->getEventsForBranch($branchId, $filters, $perPage);

            return ResponseHelper::success('Events retrieved successfully', $events);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve events: ' . $e->getMessage());
        }
    }

    /**
     * Store a newly created event
     *
     * @param StoreEventRequest $request
     * @return JsonResponse
     */
    public function store(StoreEventRequest $request): JsonResponse
    {
        try {
            $data = BranchHelper::addBranchAndTenantIds($request->validated());
            $event = $this->eventService->createEvent($data);

            return ResponseHelper::success('Event created successfully', $event, 201);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to create event: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified event
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $event = $this->eventService->getEventByIdForBranch($id, $branchId);

            if (!$event) {
                return ResponseHelper::notFound('Event not found');
            }

            return ResponseHelper::success('Event retrieved successfully', $event);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve event: ' . $e->getMessage());
        }
    }

    /**
     * Update the specified event
     *
     * @param UpdateEventRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(UpdateEventRequest $request, int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $event = $this->eventService->updateEventForBranch($id, $request->validated(), $branchId);

            if (!$event) {
                return ResponseHelper::notFound('Event not found');
            }

            return ResponseHelper::success('Event updated successfully', $event);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to update event: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified event
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $deleted = $this->eventService->deleteEventForBranch($id, $branchId);

            if (!$deleted) {
                return ResponseHelper::notFound('Event not found');
            }

            return ResponseHelper::success('Event deleted successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to delete event: ' . $e->getMessage());
        }
    }

    /**
     * Get featured events
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function featured(Request $request): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $limit = $request->get('limit', 10);
            
            $events = $this->eventService->getFeaturedEventsForBranch($branchId, $limit);

            return ResponseHelper::success('Featured events retrieved successfully', $events);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve featured events: ' . $e->getMessage());
        }
    }

    /**
     * Get upcoming events
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function upcoming(Request $request): JsonResponse
    {
        try {
            $branchId = BranchHelper::getCurrentBranchId();
            $limit = $request->get('limit', 10);
            
            $events = $this->eventService->getUpcomingEventsForBranch($branchId, $limit);

            return ResponseHelper::success('Upcoming events retrieved successfully', $events);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to retrieve upcoming events: ' . $e->getMessage());
        }
    }

    /**
     * Register for an event
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function register(Request $request, int $id): JsonResponse
    {
        try {
            $request->validate([
                'customer_name' => 'required|string|max:255',
                'customer_email' => 'required|email|max:255',
                'customer_phone' => 'nullable|string|max:20',
                'party_size' => 'required|integer|min:1|max:50',
                'special_requests' => 'nullable|string|max:1000'
            ]);

            $branchId = BranchHelper::getCurrentBranchId();
            $registration = $this->eventService->registerForEvent($id, $request->all(), $branchId);

            if (!$registration) {
                return ResponseHelper::error('Failed to register for event');
            }

            return ResponseHelper::success('Successfully registered for event', $registration, 201);
        } catch (\Exception $e) {
            return ResponseHelper::error('Failed to register for event: ' . $e->getMessage());
        }
    }
}