<!-- Add Staff Modal -->
<div id="addStaffModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <!-- Modal Header -->
        <div class="bg-gradient-to-r from-amber-500 to-orange-600 -m-5 mb-4 px-6 py-4 rounded-t-md">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-white flex items-center">
                    <i class="fas fa-user-plus ml-2"></i>
                    إضافة موظف جديد
                </h3>
                <button onclick="closeModal('addStaffModal')" class="text-white hover:text-gray-200 transition-colors duration-200">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Modal Body -->
        <form id="addStaffForm" class="space-y-6">
            @csrf
            
            <!-- Personal Information Section -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-md font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-user ml-2 text-amber-600"></i>
                    المعلومات الشخصية
                </h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Full Name -->
                    <div>
                        <label for="add_name" class="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل *</label>
                        <input type="text" id="add_name" name="name" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="add_name_error"></div>
                    </div>

                    <!-- Email -->
                    <div>
                        <label for="add_email" class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني *</label>
                        <input type="email" id="add_email" name="email" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="add_email_error"></div>
                    </div>

                    <!-- Phone -->
                    <div>
                        <label for="add_phone" class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف *</label>
                        <input type="tel" id="add_phone" name="phone" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="add_phone_error"></div>
                    </div>

                    <!-- National ID -->
                    <div>
                        <label for="add_national_id" class="block text-sm font-medium text-gray-700 mb-2">رقم الهوية</label>
                        <input type="text" id="add_national_id" name="national_id" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="add_national_id_error"></div>
                    </div>

                    <!-- Date of Birth -->
                    <div>
                        <label for="add_date_of_birth" class="block text-sm font-medium text-gray-700 mb-2">تاريخ الميلاد</label>
                        <input type="date" id="add_date_of_birth" name="date_of_birth" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="add_date_of_birth_error"></div>
                    </div>

                    <!-- Gender -->
                    <div>
                        <label for="add_gender" class="block text-sm font-medium text-gray-700 mb-2">الجنس</label>
                        <select id="add_gender" name="gender" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500">
                            <option value="">اختر الجنس</option>
                            <option value="male">ذكر</option>
                            <option value="female">أنثى</option>
                        </select>
                        <div class="text-red-500 text-sm mt-1 hidden" id="add_gender_error"></div>
                    </div>
                </div>

                <!-- Address -->
                <div class="mt-4">
                    <label for="add_address" class="block text-sm font-medium text-gray-700 mb-2">العنوان</label>
                    <textarea id="add_address" name="address" rows="2" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500"></textarea>
                    <div class="text-red-500 text-sm mt-1 hidden" id="add_address_error"></div>
                </div>
            </div>

            <!-- Employment Information Section -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-md font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-briefcase ml-2 text-amber-600"></i>
                    معلومات التوظيف
                </h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Role -->
                    <div>
                        <label for="add_role" class="block text-sm font-medium text-gray-700 mb-2">الدور الوظيفي *</label>
                        <select id="add_role" name="role" required 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500">
                            <option value="">اختر الدور</option>
                            <option value="manager">مدير</option>
                            <option value="cashier">كاشير</option>
                            <option value="waiter">نادل</option>
                            <option value="chef">طباخ</option>
                            <option value="delivery">توصيل</option>
                        </select>
                        <div class="text-red-500 text-sm mt-1 hidden" id="add_role_error"></div>
                    </div>

                    <!-- Branch -->
                    <div>
                        <label for="add_branch_id" class="block text-sm font-medium text-gray-700 mb-2">الفرع *</label>
                        <select id="add_branch_id" name="branch_id" required 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500">
                            <option value="">اختر الفرع</option>
                            @foreach($branches ?? [] as $branch)
                                <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                            @endforeach
                        </select>
                        <div class="text-red-500 text-sm mt-1 hidden" id="add_branch_id_error"></div>
                    </div>

                    <!-- Hire Date -->
                    <div>
                        <label for="add_hire_date" class="block text-sm font-medium text-gray-700 mb-2">تاريخ التوظيف *</label>
                        <input type="date" id="add_hire_date" name="hire_date" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="add_hire_date_error"></div>
                    </div>

                    <!-- Salary -->
                    <div>
                        <label for="add_salary" class="block text-sm font-medium text-gray-700 mb-2">الراتب الأساسي</label>
                        <input type="number" id="add_salary" name="salary" step="0.01" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="add_salary_error"></div>
                    </div>

                    <!-- Hourly Rate -->
                    <div>
                        <label for="add_hourly_rate" class="block text-sm font-medium text-gray-700 mb-2">الأجر بالساعة</label>
                        <input type="number" id="add_hourly_rate" name="hourly_rate" step="0.01" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="add_hourly_rate_error"></div>
                    </div>

                    <!-- Status -->
                    <div>
                        <label for="add_status" class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                        <select id="add_status" name="status" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500">
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                            <option value="suspended">معلق</option>
                        </select>
                        <div class="text-red-500 text-sm mt-1 hidden" id="add_status_error"></div>
                    </div>
                </div>
            </div>

            <!-- Account Information Section -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-md font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-key ml-2 text-amber-600"></i>
                    معلومات الحساب
                </h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Password -->
                    <div>
                        <label for="add_password" class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور *</label>
                        <input type="password" id="add_password" name="password" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="add_password_error"></div>
                    </div>

                    <!-- Confirm Password -->
                    <div>
                        <label for="add_password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">تأكيد كلمة المرور *</label>
                        <input type="password" id="add_password_confirmation" name="password_confirmation" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-amber-500 focus:border-amber-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="add_password_confirmation_error"></div>
                    </div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="flex items-center justify-end space-x-2 space-x-reverse pt-4 border-t border-gray-200">
                <button type="button" onclick="closeModal('addStaffModal')" 
                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors duration-200">
                    إلغاء
                </button>
                <button type="submit" 
                        class="px-4 py-2 bg-gradient-to-r from-amber-500 to-orange-600 text-white rounded-md hover:from-amber-600 hover:to-orange-700 transition-all duration-200 flex items-center">
                    <i class="fas fa-save ml-2"></i>
                    حفظ الموظف
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Edit Staff Modal -->
<div id="editStaffModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <!-- Modal Header -->
        <div class="bg-gradient-to-r from-blue-500 to-blue-600 -m-5 mb-4 px-6 py-4 rounded-t-md">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-white flex items-center">
                    <i class="fas fa-user-edit ml-2"></i>
                    تعديل بيانات الموظف
                </h3>
                <button onclick="closeModal('editStaffModal')" class="text-white hover:text-gray-200 transition-colors duration-200">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Modal Body -->
        <form id="editStaffForm" class="space-y-6">
            @csrf
            @method('PUT')
            <input type="hidden" id="edit_staff_id" name="staff_id">
            
            <!-- Personal Information Section -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-md font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-user ml-2 text-blue-600"></i>
                    المعلومات الشخصية
                </h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Full Name -->
                    <div>
                        <label for="edit_name" class="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل *</label>
                        <input type="text" id="edit_name" name="name" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="edit_name_error"></div>
                    </div>

                    <!-- Email -->
                    <div>
                        <label for="edit_email" class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني *</label>
                        <input type="email" id="edit_email" name="email" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="edit_email_error"></div>
                    </div>

                    <!-- Phone -->
                    <div>
                        <label for="edit_phone" class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف *</label>
                        <input type="tel" id="edit_phone" name="phone" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="edit_phone_error"></div>
                    </div>

                    <!-- National ID -->
                    <div>
                        <label for="edit_national_id" class="block text-sm font-medium text-gray-700 mb-2">رقم الهوية</label>
                        <input type="text" id="edit_national_id" name="national_id" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="edit_national_id_error"></div>
                    </div>

                    <!-- Date of Birth -->
                    <div>
                        <label for="edit_date_of_birth" class="block text-sm font-medium text-gray-700 mb-2">تاريخ الميلاد</label>
                        <input type="date" id="edit_date_of_birth" name="date_of_birth" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="edit_date_of_birth_error"></div>
                    </div>

                    <!-- Gender -->
                    <div>
                        <label for="edit_gender" class="block text-sm font-medium text-gray-700 mb-2">الجنس</label>
                        <select id="edit_gender" name="gender" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            <option value="">اختر الجنس</option>
                            <option value="male">ذكر</option>
                            <option value="female">أنثى</option>
                        </select>
                        <div class="text-red-500 text-sm mt-1 hidden" id="edit_gender_error"></div>
                    </div>
                </div>

                <!-- Address -->
                <div class="mt-4">
                    <label for="edit_address" class="block text-sm font-medium text-gray-700 mb-2">العنوان</label>
                    <textarea id="edit_address" name="address" rows="2" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"></textarea>
                    <div class="text-red-500 text-sm mt-1 hidden" id="edit_address_error"></div>
                </div>
            </div>

            <!-- Employment Information Section -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-md font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-briefcase ml-2 text-blue-600"></i>
                    معلومات التوظيف
                </h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Role -->
                    <div>
                        <label for="edit_role" class="block text-sm font-medium text-gray-700 mb-2">الدور الوظيفي *</label>
                        <select id="edit_role" name="role" required 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            <option value="">اختر الدور</option>
                            <option value="manager">مدير</option>
                            <option value="cashier">كاشير</option>
                            <option value="waiter">نادل</option>
                            <option value="chef">طباخ</option>
                            <option value="delivery">توصيل</option>
                        </select>
                        <div class="text-red-500 text-sm mt-1 hidden" id="edit_role_error"></div>
                    </div>

                    <!-- Branch -->
                    <div>
                        <label for="edit_branch_id" class="block text-sm font-medium text-gray-700 mb-2">الفرع *</label>
                        <select id="edit_branch_id" name="branch_id" required 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            <option value="">اختر الفرع</option>
                            @foreach($branches ?? [] as $branch)
                                <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                            @endforeach
                        </select>
                        <div class="text-red-500 text-sm mt-1 hidden" id="edit_branch_id_error"></div>
                    </div>

                    <!-- Hire Date -->
                    <div>
                        <label for="edit_hire_date" class="block text-sm font-medium text-gray-700 mb-2">تاريخ التوظيف *</label>
                        <input type="date" id="edit_hire_date" name="hire_date" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="edit_hire_date_error"></div>
                    </div>

                    <!-- Salary -->
                    <div>
                        <label for="edit_salary" class="block text-sm font-medium text-gray-700 mb-2">الراتب الأساسي</label>
                        <input type="number" id="edit_salary" name="salary" step="0.01" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="edit_salary_error"></div>
                    </div>

                    <!-- Hourly Rate -->
                    <div>
                        <label for="edit_hourly_rate" class="block text-sm font-medium text-gray-700 mb-2">الأجر بالساعة</label>
                        <input type="number" id="edit_hourly_rate" name="hourly_rate" step="0.01" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <div class="text-red-500 text-sm mt-1 hidden" id="edit_hourly_rate_error"></div>
                    </div>

                    <!-- Status -->
                    <div>
                        <label for="edit_status" class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                        <select id="edit_status" name="status" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                            <option value="suspended">معلق</option>
                        </select>
                        <div class="text-red-500 text-sm mt-1 hidden" id="edit_status_error"></div>
                    </div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="flex items-center justify-end space-x-2 space-x-reverse pt-4 border-t border-gray-200">
                <button type="button" onclick="closeModal('editStaffModal')" 
                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors duration-200">
                    إلغاء
                </button>
                <button type="submit" 
                        class="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-md hover:from-blue-600 hover:to-blue-700 transition-all duration-200 flex items-center">
                    <i class="fas fa-save ml-2"></i>
                    حفظ التعديلات
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Form submission handlers
$(document).ready(function() {
    // Add Staff Form
    $('#addStaffForm').on('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        
        $.ajax({
            url: '{{ route("hr.staff.store") }}',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    closeModal('addStaffModal');
                    $('#addStaffForm')[0].reset();
                    $('#staff-table').DataTable().ajax.reload();
                    showToast('تم إضافة الموظف بنجاح', 'success');
                }
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    const errors = xhr.responseJSON.errors;
                    Object.keys(errors).forEach(key => {
                        $(`#add_${key}_error`).text(errors[key][0]).removeClass('hidden');
                    });
                } else {
                    showToast('حدث خطأ أثناء إضافة الموظف', 'error');
                }
            }
        });
    });

    // Edit Staff Form
    $('#editStaffForm').on('submit', function(e) {
        e.preventDefault();
        
        const staffId = $('#edit_staff_id').val();
        const formData = new FormData(this);
        
        $.ajax({
            url: `{{ route("hr.staff.update", ":id") }}`.replace(':id', staffId),
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    closeModal('editStaffModal');
                    $('#staff-table').DataTable().ajax.reload();
                    showToast('تم تحديث بيانات الموظف بنجاح', 'success');
                }
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    const errors = xhr.responseJSON.errors;
                    Object.keys(errors).forEach(key => {
                        $(`#edit_${key}_error`).text(errors[key][0]).removeClass('hidden');
                    });
                } else {
                    showToast('حدث خطأ أثناء تحديث بيانات الموظف', 'error');
                }
            }
        });
    });
});

// Clear form errors when modal is closed
function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
    // Clear all error messages
    document.querySelectorAll('.text-red-500').forEach(el => {
        el.classList.add('hidden');
        el.textContent = '';
    });
}
</script>
