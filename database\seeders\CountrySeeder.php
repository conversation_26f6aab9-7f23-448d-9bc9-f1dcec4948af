<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Country;

class CountrySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $countries = [
            ['name' => 'United States', 'code' => 'US', 'currency_code' => 'USD', 'tax_rate' => 0.0875],
            ['name' => 'Canada', 'code' => 'CA', 'currency_code' => 'CAD', 'tax_rate' => 0.13],
            ['name' => 'United Kingdom', 'code' => 'GB', 'currency_code' => 'GBP', 'tax_rate' => 0.20],
            ['name' => 'Germany', 'code' => 'DE', 'currency_code' => 'EUR', 'tax_rate' => 0.19],
            ['name' => 'France', 'code' => 'FR', 'currency_code' => 'EUR', 'tax_rate' => 0.20],
            ['name' => 'Italy', 'code' => 'IT', 'currency_code' => 'EUR', 'tax_rate' => 0.22],
            ['name' => 'Spain', 'code' => 'ES', 'currency_code' => 'EUR', 'tax_rate' => 0.21],
            ['name' => 'Netherlands', 'code' => 'NL', 'currency_code' => 'EUR', 'tax_rate' => 0.21],
            ['name' => 'Belgium', 'code' => 'BE', 'currency_code' => 'EUR', 'tax_rate' => 0.21],
            ['name' => 'Switzerland', 'code' => 'CH', 'currency_code' => 'CHF', 'tax_rate' => 0.077],
            ['name' => 'Austria', 'code' => 'AT', 'currency_code' => 'EUR', 'tax_rate' => 0.20],
            ['name' => 'Sweden', 'code' => 'SE', 'currency_code' => 'SEK', 'tax_rate' => 0.25],
            ['name' => 'Norway', 'code' => 'NO', 'currency_code' => 'NOK', 'tax_rate' => 0.25],
            ['name' => 'Denmark', 'code' => 'DK', 'currency_code' => 'DKK', 'tax_rate' => 0.25],
            ['name' => 'Finland', 'code' => 'FI', 'currency_code' => 'EUR', 'tax_rate' => 0.24],
            ['name' => 'Australia', 'code' => 'AU', 'currency_code' => 'AUD', 'tax_rate' => 0.10],
            ['name' => 'New Zealand', 'code' => 'NZ', 'currency_code' => 'NZD', 'tax_rate' => 0.15],
            ['name' => 'Japan', 'code' => 'JP', 'currency_code' => 'JPY', 'tax_rate' => 0.10],
            ['name' => 'South Korea', 'code' => 'KR', 'currency_code' => 'KRW', 'tax_rate' => 0.10],
            ['name' => 'China', 'code' => 'CN', 'currency_code' => 'CNY', 'tax_rate' => 0.13],
            ['name' => 'India', 'code' => 'IN', 'currency_code' => 'INR', 'tax_rate' => 0.18],
            ['name' => 'Brazil', 'code' => 'BR', 'currency_code' => 'BRL', 'tax_rate' => 0.17],
            ['name' => 'Mexico', 'code' => 'MX', 'currency_code' => 'MXN', 'tax_rate' => 0.16],
            ['name' => 'Argentina', 'code' => 'AR', 'currency_code' => 'ARS', 'tax_rate' => 0.21],
            ['name' => 'Chile', 'code' => 'CL', 'currency_code' => 'CLP', 'tax_rate' => 0.19],
            ['name' => 'Colombia', 'code' => 'CO', 'currency_code' => 'COP', 'tax_rate' => 0.19],
            ['name' => 'Peru', 'code' => 'PE', 'currency_code' => 'PEN', 'tax_rate' => 0.18],
            ['name' => 'South Africa', 'code' => 'ZA', 'currency_code' => 'ZAR', 'tax_rate' => 0.15],
            ['name' => 'Egypt', 'code' => 'EG', 'currency_code' => 'EGP', 'tax_rate' => 0.14],
            ['name' => 'Nigeria', 'code' => 'NG', 'currency_code' => 'NGN', 'tax_rate' => 0.075],
            ['name' => 'Kenya', 'code' => 'KE', 'currency_code' => 'KES', 'tax_rate' => 0.16],
            ['name' => 'Morocco', 'code' => 'MA', 'currency_code' => 'MAD', 'tax_rate' => 0.20],
            ['name' => 'Turkey', 'code' => 'TR', 'currency_code' => 'TRY', 'tax_rate' => 0.18],
            ['name' => 'Russia', 'code' => 'RU', 'currency_code' => 'RUB', 'tax_rate' => 0.20],
            ['name' => 'Poland', 'code' => 'PL', 'currency_code' => 'PLN', 'tax_rate' => 0.23],
            ['name' => 'Czech Republic', 'code' => 'CZ', 'currency_code' => 'CZK', 'tax_rate' => 0.21],
            ['name' => 'Hungary', 'code' => 'HU', 'currency_code' => 'HUF', 'tax_rate' => 0.27],
            ['name' => 'Romania', 'code' => 'RO', 'currency_code' => 'RON', 'tax_rate' => 0.19],
            ['name' => 'Bulgaria', 'code' => 'BG', 'currency_code' => 'BGN', 'tax_rate' => 0.20],
            ['name' => 'Croatia', 'code' => 'HR', 'currency_code' => 'EUR', 'tax_rate' => 0.25],
            ['name' => 'Slovenia', 'code' => 'SI', 'currency_code' => 'EUR', 'tax_rate' => 0.22],
            ['name' => 'Slovakia', 'code' => 'SK', 'currency_code' => 'EUR', 'tax_rate' => 0.20],
            ['name' => 'Estonia', 'code' => 'EE', 'currency_code' => 'EUR', 'tax_rate' => 0.20],
            ['name' => 'Latvia', 'code' => 'LV', 'currency_code' => 'EUR', 'tax_rate' => 0.21],
            ['name' => 'Lithuania', 'code' => 'LT', 'currency_code' => 'EUR', 'tax_rate' => 0.21],
            ['name' => 'Portugal', 'code' => 'PT', 'currency_code' => 'EUR', 'tax_rate' => 0.23],
            ['name' => 'Greece', 'code' => 'GR', 'currency_code' => 'EUR', 'tax_rate' => 0.24],
            ['name' => 'Cyprus', 'code' => 'CY', 'currency_code' => 'EUR', 'tax_rate' => 0.19],
            ['name' => 'Malta', 'code' => 'MT', 'currency_code' => 'EUR', 'tax_rate' => 0.18],
            ['name' => 'Luxembourg', 'code' => 'LU', 'currency_code' => 'EUR', 'tax_rate' => 0.17],
            ['name' => 'Ireland', 'code' => 'IE', 'currency_code' => 'EUR', 'tax_rate' => 0.23],
            ['name' => 'Iceland', 'code' => 'IS', 'currency_code' => 'ISK', 'tax_rate' => 0.24],
            ['name' => 'Israel', 'code' => 'IL', 'currency_code' => 'ILS', 'tax_rate' => 0.17],
            ['name' => 'United Arab Emirates', 'code' => 'AE', 'currency_code' => 'AED', 'tax_rate' => 0.05],
            ['name' => 'Saudi Arabia', 'code' => 'SA', 'currency_code' => 'SAR', 'tax_rate' => 0.15],
            ['name' => 'Qatar', 'code' => 'QA', 'currency_code' => 'QAR', 'tax_rate' => 0.00],
            ['name' => 'Kuwait', 'code' => 'KW', 'currency_code' => 'KWD', 'tax_rate' => 0.00],
            ['name' => 'Bahrain', 'code' => 'BH', 'currency_code' => 'BHD', 'tax_rate' => 0.05],
            ['name' => 'Oman', 'code' => 'OM', 'currency_code' => 'OMR', 'tax_rate' => 0.05],
            ['name' => 'Jordan', 'code' => 'JO', 'currency_code' => 'JOD', 'tax_rate' => 0.16],
            ['name' => 'Lebanon', 'code' => 'LB', 'currency_code' => 'LBP', 'tax_rate' => 0.11],
            ['name' => 'Syria', 'code' => 'SY', 'currency_code' => 'SYP', 'tax_rate' => 0.00],
            ['name' => 'Iraq', 'code' => 'IQ', 'currency_code' => 'IQD', 'tax_rate' => 0.00],
            ['name' => 'Iran', 'code' => 'IR', 'currency_code' => 'IRR', 'tax_rate' => 0.09],
            ['name' => 'Afghanistan', 'code' => 'AF', 'currency_code' => 'AFN', 'tax_rate' => 0.00],
            ['name' => 'Pakistan', 'code' => 'PK', 'currency_code' => 'PKR', 'tax_rate' => 0.17],
            ['name' => 'Bangladesh', 'code' => 'BD', 'currency_code' => 'BDT', 'tax_rate' => 0.15],
            ['name' => 'Sri Lanka', 'code' => 'LK', 'currency_code' => 'LKR', 'tax_rate' => 0.15],
            ['name' => 'Nepal', 'code' => 'NP', 'currency_code' => 'NPR', 'tax_rate' => 0.13],
            ['name' => 'Bhutan', 'code' => 'BT', 'currency_code' => 'BTN', 'tax_rate' => 0.00],
            ['name' => 'Maldives', 'code' => 'MV', 'currency_code' => 'MVR', 'tax_rate' => 0.06],
            ['name' => 'Thailand', 'code' => 'TH', 'currency_code' => 'THB', 'tax_rate' => 0.07],
            ['name' => 'Vietnam', 'code' => 'VN', 'currency_code' => 'VND', 'tax_rate' => 0.10],
            ['name' => 'Cambodia', 'code' => 'KH', 'currency_code' => 'KHR', 'tax_rate' => 0.10],
            ['name' => 'Laos', 'code' => 'LA', 'currency_code' => 'LAK', 'tax_rate' => 0.10],
            ['name' => 'Myanmar', 'code' => 'MM', 'currency_code' => 'MMK', 'tax_rate' => 0.05],
            ['name' => 'Malaysia', 'code' => 'MY', 'currency_code' => 'MYR', 'tax_rate' => 0.06],
            ['name' => 'Singapore', 'code' => 'SG', 'currency_code' => 'SGD', 'tax_rate' => 0.07],
            ['name' => 'Indonesia', 'code' => 'ID', 'currency_code' => 'IDR', 'tax_rate' => 0.10],
            ['name' => 'Philippines', 'code' => 'PH', 'currency_code' => 'PHP', 'tax_rate' => 0.12],
            ['name' => 'Brunei', 'code' => 'BN', 'currency_code' => 'BND', 'tax_rate' => 0.00],
            ['name' => 'East Timor', 'code' => 'TL', 'currency_code' => 'USD', 'tax_rate' => 0.00],
        ];

        foreach ($countries as $country) {
            Country::updateOrCreate(
                ['code' => $country['code']],
                $country
            );
        }
    }
}