<?php

namespace Modules\Customer\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class CustomerCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
            'meta' => [
                'total_customers' => $this->total(),
                'active_customers' => $this->collection->where('is_active', true)->count(),
                'inactive_customers' => $this->collection->where('is_active', false)->count(),
                'total_loyalty_points' => $this->collection->sum('loyalty_points'),
                'average_loyalty_points' => $this->collection->avg('loyalty_points'),
            ],
        ];
    }

    /**
     * Get additional data that should be returned with the resource array.
     *
     * @return array<string, mixed>
     */
    public function with(Request $request): array
    {
        return [
            'success' => true,
            'message' => 'Customers retrieved successfully',
        ];
    }
}