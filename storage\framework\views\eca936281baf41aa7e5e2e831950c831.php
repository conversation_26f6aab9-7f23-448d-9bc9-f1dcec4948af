<?php $__env->startSection('title', 'إدارة الفئات'); ?>

<?php $__env->startPush('styles'); ?>
<meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<!-- SweetAlert2 -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
<style>
    /* Custom DataTables Tailwind Styling */
    .dataTables_wrapper {
        font-family: inherit;
    }
    
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_processing,
    .dataTables_wrapper .dataTables_paginate {
        color: #374151;
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }
    .dataTables_processing{
        margin: auto
    }
    .dataTables_wrapper .dataTables_length select,
    .dataTables_wrapper .dataTables_filter input {
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
    
    .dataTables_wrapper .dataTables_filter input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.5rem 0.75rem !important;
        margin: 0 0.125rem !important;
        border-radius: 0.375rem !important;
        border: 1px solid #d1d5db !important;
        background: white !important;
        color: #374151 !important;
        text-decoration: none !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background: #f3f4f6 !important;
        border-color: #9ca3af !important;
        color: #374151 !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background: #3b82f6 !important;
        border-color: #3b82f6 !important;
        color: white !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
        background: #f9fafb !important;
        color: #9ca3af !important;
        border-color: #e5e7eb !important;
    }
    
    table.dataTable {
        border-collapse: separate !important;
        border-spacing: 0 !important;
    }
    
    table.dataTable thead th {
        background-color: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
        font-weight: 600;
        color: #374151;
        padding: 0.75rem;
    }
    
    table.dataTable tbody td {
        padding: 0.75rem;
        border-bottom: 1px solid #f3f4f6;
    }
    
    table.dataTable tbody tr:hover {
        background-color: #f9fafb;
    }
    
    .select2-container--default .select2-selection--single {
        height: 42px;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        padding: 0.5rem;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 26px;
        padding-left: 0;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 40px;
    }
    
    .modal-backdrop {
        background-color: rgba(0, 0, 0, 0.5);
    }
    
    .animate-fadeIn {
        animation: fadeIn 0.3s ease-in-out;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: scale(0.95); }
        to { opacity: 1; transform: scale(1); }
    }
    
    .animate-slideDown {
        animation: slideDown 0.3s ease-in-out;
    }
    
    @keyframes slideDown {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid" dir="rtl">
    <!-- Page Header -->
    <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 mb-2">إدارة الفئات</h1>
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="inline-flex items-center space-x-1 md:space-x-3">
                        <li class="inline-flex items-center">
                            <a href="<?php echo e(route('dashboard')); ?>" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                                <i class="fas fa-home ml-2"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <i class="fas fa-chevron-left text-gray-400 mx-2"></i>
                                <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">الفئات</span>
                            </div>
                        </li>
                    </ol>
                </nav>
            </div>
            <div class="mt-4 md:mt-0">
                <button type="button" id="addCategoryBtn" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg shadow-sm transition-colors duration-200">
                    <i class="fas fa-plus ml-2"></i>
                    إضافة فئة جديدة
                </button>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
                <label for="filter_menu" class="block text-sm font-medium text-gray-700 mb-2">القائمة</label>
                <select id="filter_menu" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">جميع القوائم</option>
                </select>
            </div>
            <div>
                <label for="filter_status" class="block text-sm font-medium text-gray-700 mb-2">حالة الفئة</label>
                <select id="filter_status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">جميع الحالات</option>
                    <option value="1">نشط</option>
                    <option value="0">غير نشط</option>
                </select>
            </div>
            <div>
                <label for="filter_featured" class="block text-sm font-medium text-gray-700 mb-2">مميز</label>
                <select id="filter_featured" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">الكل</option>
                    <option value="1">مميز</option>
                    <option value="0">عادي</option>
                </select>
            </div>
            <div class="flex items-end">
                <button type="button" id="resetFilters" class="w-full px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                    <i class="fas fa-undo ml-2"></i>
                    إعادة تعيين
                </button>
            </div>
        </div>
    </div>

    <!-- DataTable Section -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
        <div class="p-6">
            <div class="overflow-x-auto">
                <table id="categoriesTable" class="w-full text-sm text-right text-gray-500">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3">#</th>
                            <th scope="col" class="px-6 py-3">اسم الفئة</th>
                            <th scope="col" class="px-6 py-3">القائمة</th>
                            <th scope="col" class="px-6 py-3">الوصف</th>
                            <th scope="col" class="px-6 py-3">ترتيب العرض</th>
                            <th scope="col" class="px-6 py-3">الحالة</th>
                            <th scope="col" class="px-6 py-3">مميز</th>
                            <th scope="col" class="px-6 py-3">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Category Modal -->
<div id="categoryModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white animate-fadeIn">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between p-4 border-b">
                <h3 class="text-lg font-semibold text-gray-900" id="modalTitle">إضافة فئة جديدة</h3>
                <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center" id="closeModal">
                    <i class="fas fa-times w-5 h-5"></i>
                </button>
            </div>
            
            <!-- Modal Body -->
            <div class="p-6">
                <form id="categoryForm" class="space-y-6">
                    <input type="hidden" id="categoryId" name="id">
                    <input type="hidden" id="code" name="code">
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Category Name -->
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">اسم الفئة *</label>
                            <input type="text" id="name" name="name" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="أدخل اسم الفئة">
                            <div class="text-red-500 text-sm mt-1 hidden" id="name_error"></div>
                        </div>
                        
                        <!-- Menu Selection -->
                        <div>
                            <label for="menu_id" class="block text-sm font-medium text-gray-700 mb-2">القائمة *</label>
                            <select id="menu_id" name="menu_id" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">اختر القائمة</option>
                            </select>
                            <div class="text-red-500 text-sm mt-1 hidden" id="menu_id_error"></div>
                        </div>
                    </div>
                    
                    <!-- Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                        <textarea id="description" name="description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="أدخل وصف الفئة"></textarea>
                        <div class="text-red-500 text-sm mt-1 hidden" id="description_error"></div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Sort Order -->
                        <div>
                            <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-2">ترتيب العرض</label>
                            <input type="number" id="sort_order" name="sort_order" min="0" value="0" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <div class="text-red-500 text-sm mt-1 hidden" id="sort_order_error"></div>
                        </div>
                        
                        <!-- Color -->
                        <div>
                            <label for="color" class="block text-sm font-medium text-gray-700 mb-2">اللون</label>
                            <input type="color" id="color" name="color" value="#3b82f6" class="w-full h-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <div class="text-red-500 text-sm mt-1 hidden" id="color_error"></div>
                        </div>
                    </div>
                    
                    <!-- Status Checkboxes -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="flex items-center">
                            <input type="checkbox" id="is_active" name="is_active" value="1" checked class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <label for="is_active" class="mr-2 text-sm font-medium text-gray-700">فئة نشطة</label>
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" id="is_featured" name="is_featured" value="1" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <label for="is_featured" class="mr-2 text-sm font-medium text-gray-700">فئة مميزة</label>
                        </div>
                    </div>
                </form>
            </div>
            
            <!-- Modal Footer -->
            <div class="flex items-center justify-end p-6 border-t border-gray-200 space-x-2">
                <button type="button" id="cancelBtn" class="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                    إلغاء
                </button>
                <button type="button" id="saveBtn" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                    <i class="fas fa-save ml-2"></i>
                    حفظ
                </button>
            </div>
        </div>
    </div>
</div>

<!-- View Category Modal -->
<div id="viewCategoryModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white animate-fadeIn">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between p-4 border-b">
                <h3 class="text-lg font-semibold text-gray-900">تفاصيل الفئة</h3>
                <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center" id="closeViewModal">
                    <i class="fas fa-times w-5 h-5"></i>
                </button>
            </div>
            
            <!-- Modal Body -->
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">اسم الفئة</label>
                        <p class="text-gray-900" id="view_name"></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">القائمة</label>
                        <p class="text-gray-900" id="view_menu"></p>
                    </div>
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">الوصف</label>
                        <p class="text-gray-900" id="view_description"></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">ترتيب العرض</label>
                        <p class="text-gray-900" id="view_sort_order"></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">اللون</label>
                        <div class="flex items-center">
                            <div id="view_color_box" class="w-6 h-6 rounded border border-gray-300 ml-2"></div>
                            <span id="view_color"></span>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">الحالة</label>
                        <p id="view_is_active"></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">مميز</label>
                        <p id="view_is_featured"></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">تاريخ الإنشاء</label>
                        <p class="text-gray-900" id="view_created_at"></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">تاريخ التحديث</label>
                        <p class="text-gray-900" id="view_updated_at"></p>
                    </div>
                </div>
            </div>
            
            <!-- Modal Footer -->
            <div class="flex items-center justify-end p-6 border-t border-gray-200">
                <button type="button" id="closeViewModalBtn" class="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                    إغلاق
                </button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
$(document).ready(function() {
    // Setup CSRF token for AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Initialize Select2
    $('#filter_menu, #menu_id').select2({
        placeholder: 'اختر القائمة',
        allowClear: true
    });

    // Load menus for filters and form
    loadMenus();

    // Initialize DataTable
    let table = $('#categoriesTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: "<?php echo e(route('categories.index')); ?>",
            data: function(d) {
                // Only send filter parameters if they have values
                let menuId = $('#filter_menu').val();
                let status = $('#filter_status').val();
                let featured = $('#filter_featured').val();
                
                if (menuId && menuId !== '') {
                    d.menu_id = menuId;
                }
                if (status && status !== '') {
                    d.status = status;
                }
                if (featured && featured !== '') {
                    d.featured = featured;
                }
            }
        },
        columns: [
            { data: 'DT_RowIndex', name: 'DT_RowIndex', orderable: false, searchable: false },
            { data: 'name', name: 'name' },
            { data: 'menu_name', name: 'menu.name' },
            { data: 'description', name: 'description', orderable: false },
            { data: 'sort_order', name: 'sort_order' },
            { data: 'status', name: 'is_active', orderable: false },
            { data: 'featured', name: 'is_featured', orderable: false },
            { data: 'actions', name: 'actions', orderable: false, searchable: false }
        ],
        order: [[4, 'asc']], // Sort by sort_order
        responsive: true,
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json'
        },
        drawCallback: function() {
            // Re-initialize tooltips or other UI elements if needed
        }
    });

    // Filter change events
    $('#filter_menu, #filter_status, #filter_featured').change(function() {
        table.draw();
    });

    // Reset filters
    $('#resetFilters').click(function() {
        $('#filter_menu').val('').trigger('change');
        $('#filter_status').val('');
        $('#filter_featured').val('');
        table.draw();
    });

    // Load menus function
    function loadMenus() {
        $.ajax({
            url: "<?php echo e(route('categories.menus-list')); ?>",
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    let options = '<option value="">اختر القائمة</option>';
                    response.data.forEach(function(menu) {
                        options += `<option value="${menu.id}">${menu.name}</option>`;
                    });
                    $('#filter_menu, #menu_id').html(options);
                }
            },
            error: function(xhr) {
                console.error('Failed to load menus:', xhr.responseJSON?.message);
            }
        });
    }

    // Modal functions
    function openModal() {
        $('#categoryModal').removeClass('hidden');
        $('body').addClass('overflow-hidden');
    }

    function closeModal() {
        $('#categoryModal').addClass('hidden');
        $('body').removeClass('overflow-hidden');
        resetForm();
    }

    function openViewModal() {
        $('#viewCategoryModal').removeClass('hidden');
        $('body').addClass('overflow-hidden');
    }

    function closeViewModal() {
        $('#viewCategoryModal').addClass('hidden');
        $('body').removeClass('overflow-hidden');
    }

    function resetForm() {
        $('#categoryForm')[0].reset();
        $('#categoryId').val('');
        $('#modalTitle').text('إضافة فئة جديدة');
        $('#menu_id').val('').trigger('change');
        $('#color').val('#3b82f6');
        $('#is_active').prop('checked', true);
        $('#is_featured').prop('checked', false);
        clearErrors();
    }

    function clearErrors() {
        $('.text-red-500').addClass('hidden');
        $('.border-red-500').removeClass('border-red-500');
    }

    function showErrors(errors) {
        clearErrors();
        for (let field in errors) {
            $(`#${field}_error`).text(errors[field][0]).removeClass('hidden');
            $(`#${field}`).addClass('border-red-500');
        }
    }

    // Auto-generate category code from name
    $('#name').on('input', function() {
        let name = $(this).val();
        let code = generateCategoryCode(name);
        $('#code').val(code);
    });

    // Function to generate category code
    function generateCategoryCode(name) {
        if (!name) return '';
        
        // Remove Arabic diacritics and special characters
        let code = name
            .replace(/[\u064B-\u065F\u0670\u06D6-\u06ED]/g, '') // Remove Arabic diacritics
            .replace(/[^\u0600-\u06FF\w\s]/g, '') // Keep only Arabic letters, numbers, and spaces
            .trim()
            .replace(/\s+/g, '_') // Replace spaces with underscores
            .toUpperCase();
        
        // If the result is empty or too short, use a fallback
        if (code.length < 2) {
            code = 'CAT_' + Date.now().toString().slice(-6);
        }
        
        // Limit to 50 characters as per validation rules
        return code.substring(0, 50);
    }

    // Event handlers
    $('#addCategoryBtn').click(function() {
        resetForm();
        openModal();
    });

    $('#closeModal, #cancelBtn').click(closeModal);
    $('#closeViewModal, #closeViewModalBtn').click(closeViewModal);

    // Close modal when clicking outside
    $('#categoryModal, #viewCategoryModal').click(function(e) {
        if (e.target === this) {
            closeModal();
            closeViewModal();
        }
    });

    // Save category
    $('#saveBtn').click(function() {
        let formData = new FormData($('#categoryForm')[0]);
        let categoryId = $('#categoryId').val();
        let url = categoryId ? `/menu/categories/${categoryId}` : "<?php echo e(route('categories.store')); ?>";
        let method = categoryId ? 'PUT' : 'POST';

        // Handle checkboxes
        formData.set('is_active', $('#is_active').is(':checked') ? '1' : '0');
        formData.set('is_featured', $('#is_featured').is(':checked') ? '1' : '0');

        if (method === 'PUT') {
            formData.append('_method', 'PUT');
        }

        $.ajax({
            url: url,
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'نجح!',
                        text: response.message,
                        timer: 2000,
                        showConfirmButton: false
                    });
                    closeModal();
                    table.draw();
                }
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    showErrors(xhr.responseJSON.errors);
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ!',
                        text: xhr.responseJSON?.message || 'حدث خطأ غير متوقع'
                    });
                }
            }
        });
    });

    // Show category details
    $(document).on('click', '.show-category', function() {
        let id = $(this).data('id');
        
        $.ajax({
            url: `/menu/categories/${id}/show`,
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    let category = response.data;
                    $('#view_name').text(category.name || '-');
                    $('#view_menu').text(category.menu?.name || '-');
                    $('#view_description').text(category.description || '-');
                    $('#view_sort_order').text(category.sort_order || '0');
                    $('#view_color').text(category.color || '#3b82f6');
                    $('#view_color_box').css('background-color', category.color || '#3b82f6');
                    $('#view_is_active').html(category.is_active ? '<span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">نشط</span>' : '<span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded">غير نشط</span>');
                    $('#view_is_featured').html(category.is_featured ? '<span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">مميز</span>' : '<span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">عادي</span>');
                    $('#view_created_at').text(category.created_at || '-');
                    $('#view_updated_at').text(category.updated_at || '-');
                    
                    openViewModal();
                }
            },
            error: function(xhr) {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ!',
                    text: xhr.responseJSON?.message || 'فشل في استرجاع بيانات الفئة'
                });
            }
        });
    });

    // Edit category
    $(document).on('click', '.edit-category', function() {
        let id = $(this).data('id');
        
        $.ajax({
            url: `/menu/categories/${id}/edit`,
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    let category = response.data;
                    $('#categoryId').val(category.id);
                    $('#name').val(category.name);
                    $('#code').val(category.code || generateCategoryCode(category.name));
                    $('#menu_id').val(category.menu_id).trigger('change');
                    $('#description').val(category.description);
                    $('#sort_order').val(category.sort_order || 0);
                    $('#color').val(category.color || '#3b82f6');
                    $('#is_active').prop('checked', category.is_active);
                    $('#is_featured').prop('checked', category.is_featured);
                    
                    $('#modalTitle').text('تعديل الفئة');
                    openModal();
                }
            },
            error: function(xhr) {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ!',
                    text: xhr.responseJSON?.message || 'فشل في استرجاع بيانات الفئة للتعديل'
                });
            }
        });
    });

    // Delete category
    $(document).on('click', '.delete-category', function() {
        let id = $(this).data('id');
        
        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: 'لن تتمكن من التراجع عن هذا الإجراء!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'نعم، احذف!',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: `/menu/categories/${id}`,
                    method: 'DELETE',
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'تم الحذف!',
                                text: response.message,
                                timer: 2000,
                                showConfirmButton: false
                            });
                            table.draw();
                        }
                    },
                    error: function(xhr) {
                        Swal.fire({
                            icon: 'error',
                            title: 'خطأ!',
                            text: xhr.responseJSON?.message || 'فشل في حذف الفئة'
                        });
                    }
                });
            }
        });
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\my dehive work\resturant-pos - Copy\epis - Copy\modules\Menu\Providers/../resources/views/categories.blade.php ENDPATH**/ ?>