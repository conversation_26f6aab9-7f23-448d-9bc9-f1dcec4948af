<?php

namespace Modules\Reservation\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateReservationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'customer_id' => 'nullable|integer|exists:customers,id',
            'table_id' => 'nullable|integer|exists:tables,id',
            'name' => 'sometimes|required|string|max:255',
            'phone' => 'sometimes|required|string|max:20',
            'email' => 'nullable|email|max:255',
            'party_size' => 'sometimes|required|integer|min:1|max:50',
            'reservation_datetime' => 'sometimes|required|date',
            'duration_minutes' => 'nullable|integer|min:30|max:480',
            'special_requests' => 'nullable|string|max:1000',
            'notes' => 'nullable|string|max:1000',
            'area_id' => 'nullable|integer|exists:areas,id',
            'reservation_status_id' => 'nullable|integer|exists:reservation_statuses,id',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'customer_id.exists' => 'Selected customer does not exist.',
            'table_id.exists' => 'Selected table does not exist.',
            'name.required' => 'Customer name is required.',
            'name.max' => 'Customer name cannot exceed 255 characters.',
            'phone.required' => 'Phone number is required.',
            'phone.max' => 'Phone number cannot exceed 20 characters.',
            'email.email' => 'Please provide a valid email address.',
            'party_size.required' => 'Party size is required.',
            'party_size.min' => 'Party size must be at least 1.',
            'party_size.max' => 'Party size cannot exceed 50.',
            'reservation_datetime.required' => 'Reservation date and time is required.',
            'duration_minutes.min' => 'Reservation duration must be at least 30 minutes.',
            'duration_minutes.max' => 'Reservation duration cannot exceed 8 hours.',
            'special_requests.max' => 'Special requests cannot exceed 1000 characters.',
            'notes.max' => 'Notes cannot exceed 1000 characters.',
            'area_id.exists' => 'Selected area does not exist.',
            'reservation_status_id.exists' => 'Selected reservation status does not exist.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'customer_id' => 'customer',
            'table_id' => 'table',
            'party_size' => 'party size',
            'reservation_datetime' => 'reservation date and time',
            'duration_minutes' => 'duration',
            'special_requests' => 'special requests',
            'area_id' => 'area',
            'reservation_status_id' => 'reservation status',
        ];
    }
}