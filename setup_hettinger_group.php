<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Tenant;
use App\Models\Branch;
use App\Models\Area;
use App\Models\Table;

// Create or find the tenant
$tenant = Tenant::where('name', 'Hettinger Group')->first();
if (!$tenant) {
    $tenant = Tenant::create([
        'name' => 'Hettinger Group',
        'code' => 'hettinger-group-1234',
        'business_type' => 'Restaurant',
        'primary_contact_name' => '<PERSON>',
        'contact_email' => '<EMAIL>',
        'contact_phone' => '555-0123',
        'business_address' => '123 Restaurant Ave',
        'timezone' => 'UTC',
        'currency_code' => 'USD',
        'language_code' => 'en',
        'status' => 'active',
    ]);
    echo "Tenant created: {$tenant->name} (ID: {$tenant->id})\n";
} else {
    echo "Tenant found: {$tenant->name} (ID: {$tenant->id})\n";
}

// Create a branch
$branch = $tenant->branches()->where('status', 'active')->first();
if (!$branch) {
    $branch = Branch::create([
        'tenant_id' => $tenant->id,
        'name' => 'Main Branch',
        'code' => 'MAIN',
        'address' => '123 Restaurant Ave',
        'phone' => '555-0123',
        'email' => '<EMAIL>',
        'manager_name' => 'John Manager',
        'seating_capacity' => 50,
        'status' => 'active',
        'is_delivery_enabled' => true,
        'is_takeaway_enabled' => true,
        'is_dine_in_enabled' => true,
        'is_online_ordering_enabled' => true,
    ]);
    echo "Branch created: {$branch->name} (ID: {$branch->id})\n";
} else {
    echo "Branch found: {$branch->name} (ID: {$branch->id})\n";
}

// Create an area
$area = Area::where('branch_id', $branch->id)->first();
if (!$area) {
    $area = Area::create([
        'branch_id' => $branch->id,
        'name' => 'Main Dining Area',
        'description' => 'Main dining area with tables',
        'is_active' => true,
    ]);
    echo "Area created: {$area->name} (ID: {$area->id})\n";
} else {
    echo "Area found: {$area->name} (ID: {$area->id})\n";
}

// Create some tables
for ($i = 1; $i <= 5; $i++) {
    $table = Table::where('area_id', $area->id)->where('table_number', $i)->first();
    if (!$table) {
        $table = Table::create([
            'branch_id' => $branch->id,  // Added branch_id
            'area_id' => $area->id,
            'table_number' => $i,
            'seating_capacity' => 4,
            'status' => 'available',
            'qr_code' => 'QR' . str_pad($i, 3, '0', STR_PAD_LEFT),
            'is_active' => true,
        ]);
        echo "Table created: Table {$table->table_number} (ID: {$table->id})\n";
    }
}

echo "\nSetup complete! You can now access: http://127.0.0.1:8000/menu/restaurant/Hettinger%20Group\n";