<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

try {
    echo "Testing addon creation...\n";
    
    // Get a user and login
    $user = App\Models\User::first();
    if (!$user) {
        echo "No user found\n";
        exit(1);
    }
    
    auth()->login($user);
    echo "Logged in as user: " . $user->name . "\n";
    
    // Get a menu item
    $menuItem = App\Models\MenuItem::first();
    if (!$menuItem) {
        echo "No menu item found\n";
        exit(1);
    }
    
    echo "Using menu item: " . $menuItem->name . " (ID: " . $menuItem->id . ")\n";
    
    // Test addon creation
    $data = [
        'menu_item_id' => $menuItem->id,
        'name' => 'Test Addon',
        'price' => 2.50
    ];
    
    $addonService = app(Modules\Menu\Services\AddonService::class);
    $addon = $addonService->createAddon($data);
    
    echo "Addon created successfully!\n";
    echo "ID: " . $addon->id . "\n";
    echo "Name: " . $addon->name . "\n";
    echo "Code: " . $addon->code . "\n";
    echo "Price: " . $addon->price . "\n";
    echo "Tenant ID: " . $addon->tenant_id . "\n";
    echo "Branch ID: " . $addon->branch_id . "\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}