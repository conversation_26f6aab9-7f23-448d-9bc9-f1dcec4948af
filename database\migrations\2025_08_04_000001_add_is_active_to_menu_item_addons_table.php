<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('menu_item_addons', function (Blueprint $table) {
            // Add is_active column if it doesn't exist
            if (!Schema::hasColumn('menu_item_addons', 'is_active')) {
                $table->boolean('is_active')->default(true)->after('sort_order');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('menu_item_addons', function (Blueprint $table) {
            if (Schema::hasColumn('menu_item_addons', 'is_active')) {
                $table->dropColumn('is_active');
            }
        });
    }
};
