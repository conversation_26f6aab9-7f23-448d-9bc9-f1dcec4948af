@extends('layouts.master')

@section('css')
<link href="{{URL::asset('assets/plugins/datatable/css/dataTables.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/buttons.bootstrap4.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.bootstrap4.min.css')}}" rel="stylesheet" />
<link href="{{URL::asset('assets/plugins/datatable/css/jquery.dataTables.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/datatable/css/responsive.dataTables.min.css')}}" rel="stylesheet">
<link href="{{URL::asset('assets/plugins/select2/css/select2.min.css')}}" rel="stylesheet">
<style>
.stats-card {
    border-radius: 10px;
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
}
.stats-card:hover {
    transform: translateY(-2px);
}
.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}
.low-stock-item {
    border-left: 4px solid #dc3545;
    background: #fff5f5;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 5px;
}
.reorder-item {
    border-left: 4px solid #ffc107;
    background: #fffbf0;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 5px;
}
</style>
@endsection

@section('page-header')
<!-- breadcrumb -->
<div class="breadcrumb-header justify-content-between">
    <div class="my-auto">
        <div class="d-flex">
            <h4 class="content-title mb-0 my-auto">المخزون</h4>
            <span class="text-muted mt-1 tx-13 mr-2 mb-0">/ لوحة التحكم</span>
        </div>
    </div>
    <div class="d-flex my-xl-auto right-content">
        <div class="pr-1 mb-3 mb-xl-0">
            <a href="{{ route('inventory.items.create') }}" class="btn btn-primary">
                <i class="mdi mdi-plus"></i> إضافة مادة جديدة
            </a>
        </div>
    </div>
</div>
<!-- breadcrumb -->
@endsection

@section('content')
<!-- row -->
<div class="row row-sm">
    <!-- Statistics Cards -->
    <div class="col-xl-3 col-lg-6 col-md-6 col-xm-12">
        <div class="card stats-card">
            <div class="card-body">
                <div class="d-flex">
                    <div class="stats-icon bg-primary">
                        <i class="mdi mdi-package-variant"></i>
                    </div>
                    <div class="mr-auto">
                        <div class="text-right">
                            <h2 class="mb-1 tx-20 font-weight-bold">{{ $analytics['total_items'] ?? 0 }}</h2>
                            <p class="text-muted mb-0 tx-11">إجمالي المواد</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6 col-xm-12">
        <div class="card stats-card">
            <div class="card-body">
                <div class="d-flex">
                    <div class="stats-icon bg-warning">
                        <i class="mdi mdi-alert-circle"></i>
                    </div>
                    <div class="mr-auto">
                        <div class="text-right">
                            <h2 class="mb-1 tx-20 font-weight-bold">{{ $analytics['low_stock_items'] ?? 0 }}</h2>
                            <p class="text-muted mb-0 tx-11">مخزون منخفض</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6 col-xm-12">
        <div class="card stats-card">
            <div class="card-body">
                <div class="d-flex">
                    <div class="stats-icon bg-danger">
                        <i class="mdi mdi-package-variant-closed"></i>
                    </div>
                    <div class="mr-auto">
                        <div class="text-right">
                            <h2 class="mb-1 tx-20 font-weight-bold">{{ $analytics['out_of_stock_items'] ?? 0 }}</h2>
                            <p class="text-muted mb-0 tx-11">نفد المخزون</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6 col-xm-12">
        <div class="card stats-card">
            <div class="card-body">
                <div class="d-flex">
                    <div class="stats-icon bg-success">
                        <i class="mdi mdi-currency-usd"></i>
                    </div>
                    <div class="mr-auto">
                        <div class="text-right">
                            <h2 class="mb-1 tx-20 font-weight-bold">{{ number_format($analytics['total_value'] ?? 0, 2) }}</h2>
                            <p class="text-muted mb-0 tx-11">قيمة المخزون</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Low Stock Items -->
@if($lowStockItems && $lowStockItems->count() > 0)
<div class="row row-sm">
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">المواد منخفضة المخزون</h4>
                <div class="card-options">
                    <a href="{{ route('inventory.stock.low') }}" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                </div>
            </div>
            <div class="card-body">
                @foreach($lowStockItems->take(5) as $item)
                <div class="low-stock-item">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">{{ $item->product->name }}</h6>
                            <small class="text-muted">{{ $item->product->sku }}</small>
                        </div>
                        <div class="text-right">
                            <span class="badge badge-danger">{{ $item->current_stock }} {{ $item->product->unit->symbol ?? '' }}</span>
                            <small class="text-muted d-block">الحد الأدنى: {{ $item->minimum_stock }}</small>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>
    
    <!-- Reorder Suggestions -->
    <div class="col-xl-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">اقتراحات إعادة الطلب</h4>
            </div>
            <div class="card-body">
                @foreach($reorderSuggestions->take(5) as $suggestion)
                <div class="reorder-item">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">{{ $suggestion['product'] }}</h6>
                            <small class="text-muted">{{ $suggestion['sku'] }}</small>
                        </div>
                        <div class="text-right">
                            <span class="badge badge-warning">اطلب {{ $suggestion['suggested_quantity'] }}</span>
                            <small class="text-muted d-block">المخزون: {{ $suggestion['current_stock'] }}</small>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>
</div>
@endif

<!-- Quick Actions -->
<div class="row row-sm">
    <div class="col-xl-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">الإجراءات السريعة</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ route('inventory.items.index') }}" class="btn btn-outline-primary btn-block">
                            <i class="mdi mdi-package-variant mr-2"></i>إدارة المواد
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ route('inventory.stock.index') }}" class="btn btn-outline-success btn-block">
                            <i class="mdi mdi-cube-outline mr-2"></i>إدارة المخزون
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ route('inventory.suppliers.index') }}" class="btn btn-outline-info btn-block">
                            <i class="mdi mdi-truck mr-2"></i>إدارة الموردين
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ route('inventory.purchase-orders.index') }}" class="btn btn-outline-warning btn-block">
                            <i class="mdi mdi-cart-plus mr-2"></i>أوامر الشراء
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- row closed -->
@endsection

@section('js')
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.dataTables.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/jquery.dataTables.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.bootstrap4.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.buttons.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.bootstrap4.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/jszip.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/pdfmake.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/vfs_fonts.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.html5.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.print.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/buttons.colVis.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/dataTables.responsive.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/datatable/js/responsive.bootstrap4.min.js')}}"></script>
<script src="{{URL::asset('assets/plugins/select2/js/select2.min.js')}}"></script>
@endsection
