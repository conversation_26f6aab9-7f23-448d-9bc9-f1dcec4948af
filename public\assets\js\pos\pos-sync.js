/**
 * POS Sync Module - Handles synchronization between offline and online data
 * Manages background sync, conflict resolution, and data consistency
 */

class POSSync {
    constructor(storage) {
        this.storage = storage;
        this.isOnline = navigator.onLine;
        this.syncInProgress = false;
        this.syncQueue = [];
        this.maxRetries = 3;
        this.retryDelay = 5000; // 5 seconds
        
        this.setupEventListeners();
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Listen for online/offline events
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.syncAll();
        });
        
        window.addEventListener('offline', () => {
            this.isOnline = false;
        });
    }

    /**
     * Sync all pending data
     */
    async syncAll() {
        if (!this.isOnline || this.syncInProgress) {
            console.log('Sync skipped - offline or already in progress');
            return;
        }

        this.syncInProgress = true;
        console.log('Starting sync process...');

        try {
            // Sync pending orders first (highest priority)
            await this.syncPendingOrders();
            
            // Sync other queued items
            await this.syncQueuedItems();
            
            // Update local data from server
            await this.updateLocalData();
            
            console.log('Sync completed successfully');
            this.showSyncStatus('success', 'Data synchronized successfully');
        } catch (error) {
            console.error('Sync failed:', error);
            this.showSyncStatus('error', 'Sync failed: ' + error.message);
        } finally {
            this.syncInProgress = false;
        }
    }

    /**
     * Sync pending orders
     */
    async syncPendingOrders() {
        const pendingOrders = await this.storage.getPendingOrders();
        
        if (!pendingOrders || pendingOrders.length === 0) {
            console.log('No pending orders to sync');
            return;
        }

        console.log(`Syncing ${pendingOrders.length} pending orders...`);

        for (const order of pendingOrders) {
            try {
                await this.syncOrder(order);
                await this.storage.deletePendingOrder(order.id);
                console.log(`Order ${order.id} synced successfully`);
            } catch (error) {
                console.error(`Failed to sync order ${order.id}:`, error);
                // Update retry count
                order.sync_attempts = (order.sync_attempts || 0) + 1;
                order.last_sync_attempt = new Date().toISOString();
                
                if (order.sync_attempts >= this.maxRetries) {
                    console.error(`Order ${order.id} exceeded max retry attempts`);
                    order.status = 'sync_failed';
                }
                
                await this.storage.savePendingOrder(order);
            }
        }
    }

    /**
     * Sync individual order
     */
    async syncOrder(order) {
        const response = await fetch('/pos/orders/store', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(order)
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || 'Failed to sync order');
        }

        return await response.json();
    }

    /**
     * Sync queued items
     */
    async syncQueuedItems() {
        const queueItems = await this.storage.getSyncQueue();
        
        if (!queueItems || queueItems.length === 0) {
            console.log('No queued items to sync');
            return;
        }

        console.log(`Syncing ${queueItems.length} queued items...`);

        // Sort by priority (higher priority first)
        queueItems.sort((a, b) => (b.priority || 1) - (a.priority || 1));

        for (const item of queueItems) {
            try {
                await this.syncQueueItem(item);
                await this.storage.removeSyncQueueItem(item.id);
                console.log(`Queue item ${item.id} synced successfully`);
            } catch (error) {
                console.error(`Failed to sync queue item ${item.id}:`, error);
                
                // Update retry count
                item.attempts = (item.attempts || 0) + 1;
                item.last_attempt = new Date().toISOString();
                
                if (item.attempts >= this.maxRetries) {
                    console.error(`Queue item ${item.id} exceeded max retry attempts`);
                    await this.storage.removeSyncQueueItem(item.id);
                } else {
                    await this.storage.saveToStore('syncQueue', item);
                }
            }
        }
    }

    /**
     * Sync individual queue item
     */
    async syncQueueItem(item) {
        switch (item.type) {
            case 'customer_update':
                return await this.syncCustomerUpdate(item.data);
            case 'table_status':
                return await this.syncTableStatus(item.data);
            case 'inventory_update':
                return await this.syncInventoryUpdate(item.data);
            default:
                throw new Error(`Unknown sync item type: ${item.type}`);
        }
    }

    /**
     * Sync customer update
     */
    async syncCustomerUpdate(data) {
        const response = await fetch(`/api/customers/${data.id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(data)
        });

        if (!response.ok) {
            throw new Error('Failed to sync customer update');
        }

        return await response.json();
    }

    /**
     * Sync table status
     */
    async syncTableStatus(data) {
        const response = await fetch(`/api/tables/${data.id}/status`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ status: data.status })
        });

        if (!response.ok) {
            throw new Error('Failed to sync table status');
        }

        return await response.json();
    }

    /**
     * Sync inventory update
     */
    async syncInventoryUpdate(data) {
        const response = await fetch(`/api/inventory/${data.id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(data)
        });

        if (!response.ok) {
            throw new Error('Failed to sync inventory update');
        }

        return await response.json();
    }

    /**
     * Update local data from server
     */
    async updateLocalData() {
        console.log('Updating local data from server...');

        try {
            // Update menu items
            const menuResponse = await fetch('/api/pos/form-data', {
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                }
            });
            if (menuResponse.ok) {
                const data = await menuResponse.json();
                const menuItems = data.data?.menu_items || [];
                await this.storage.saveMenuItems(menuItems);
                console.log('Menu items updated');
            }

            // Update customers
            const customersResponse = await fetch('/api/customers', {
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                }
            });
            if (customersResponse.ok) {
                const customers = await customersResponse.json();
                await this.storage.saveCustomers(customers);
                console.log('Customers updated');
            }

            // Update tables
            const tablesResponse = await fetch('/api/reservation/tables', {
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                }
            });
            if (tablesResponse.ok) {
                const tables = await tablesResponse.json();
                await this.storage.saveTables(tables);
                console.log('Tables updated');
            }

        } catch (error) {
            console.error('Failed to update local data:', error);
            // Don't throw error here as this is not critical
        }
    }

    /**
     * Add item to sync queue
     */
    async addToQueue(type, data, priority = 1) {
        const queueItem = {
            type: type,
            data: data,
            priority: priority,
            created_at: new Date().toISOString(),
            attempts: 0
        };

        await this.storage.addToSyncQueue(queueItem);
        console.log(`Added ${type} to sync queue`);

        // Try to sync immediately if online
        if (this.isOnline && !this.syncInProgress) {
            setTimeout(() => this.syncAll(), 1000);
        }
    }

    /**
     * Force sync now
     */
    async forcSync() {
        if (this.syncInProgress) {
            console.log('Sync already in progress');
            return;
        }

        if (!this.isOnline) {
            throw new Error('Cannot sync while offline');
        }

        await this.syncAll();
    }

    /**
     * Get sync status
     */
    async getSyncStatus() {
        const pendingOrders = await this.storage.getPendingOrders();
        const queueItems = await this.storage.getSyncQueue();
        
        return {
            isOnline: this.isOnline,
            syncInProgress: this.syncInProgress,
            pendingOrders: pendingOrders ? pendingOrders.length : 0,
            queueItems: queueItems ? queueItems.length : 0,
            lastSync: await this.storage.getSetting('last_sync_time')
        };
    }

    /**
     * Show sync status to user
     */
    showSyncStatus(type, message) {
        // Update last sync time
        this.storage.saveSetting('last_sync_time', new Date().toISOString());
        
        // Show notification to user
        if (window.posCore && window.posCore.ui) {
            if (type === 'success') {
                window.posCore.ui.showSuccess(message);
            } else {
                window.posCore.ui.showError(message);
            }
        }

        // Dispatch custom event
        window.dispatchEvent(new CustomEvent('pos-sync-status', {
            detail: { type, message }
        }));
    }

    /**
     * Clear all sync data
     */
    async clearSyncData() {
        await this.storage.clearStore('pendingOrders');
        await this.storage.clearStore('syncQueue');
        console.log('Sync data cleared');
    }
}
